import { NextRequest, NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { getUserFromToken } from "@/lib/auth/token"

export async function GET(request: NextRequest) {
  try {
    // Get user from token
    const userToken = await getUserFromToken(request)
    if (!userToken) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin()

    // Get user details to check admin role
    const { data: user, error: userError } = await supabaseAdmin
      .from("users")
      .select(`
        *,
        role_info:role_id(id, role_name, description, permissions)
      `)
      .eq("id", userToken.userId)
      .single()

    if (userError || !user) {
      console.error("Error fetching user:", userError)
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Check if user has admin role (only "admin" role has full access)
    const userRole = user.role_name || user.role_info?.role_name || user.role
    if (userRole !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 403 }
      )
    }

    // Fetch subscription statistics
    const [
      plansResult,
      subscriptionsResult,
      certificatesResult,
      attendanceResult,
      webhooksResult
    ] = await Promise.all([
      // Total and active plans
      supabaseAdmin
        .from('subscription_plans')
        .select('id, is_active'),

      // Active subscriptions and revenue
      supabaseAdmin
        .from('user_subscription')
        .select(`
          id,
          subscription_type,
          is_active,
          users!inner(subscription_plan)
        `)
        .eq('is_active', true),

      // Certificates issued (mock for now)
      Promise.resolve({ data: [], error: null }),

      // Attendance tracked (mock for now)
      Promise.resolve({ data: [], error: null }),

      // Webhook calls (mock for now)
      Promise.resolve({ data: [], error: null })
    ])

    if (plansResult.error) {
      console.error("Error fetching plans:", plansResult.error)
      return NextResponse.json(
        { error: "Failed to fetch subscription statistics" },
        { status: 500 }
      )
    }

    if (subscriptionsResult.error) {
      console.error("Error fetching subscriptions:", subscriptionsResult.error)
      return NextResponse.json(
        { error: "Failed to fetch subscription statistics" },
        { status: 500 }
      )
    }

    // Calculate statistics
    const totalPlans = plansResult.data?.length || 0
    const activePlans = plansResult.data?.filter(plan => plan.is_active).length || 0
    const totalSubscribers = subscriptionsResult.data?.length || 0

    // Calculate monthly revenue (simplified calculation)
    const monthlyRevenue = subscriptionsResult.data?.reduce((total, sub) => {
      // This is a simplified calculation - in reality you'd need to fetch plan prices
      const planPrices = {
        'free': 0,
        'basic': 29,
        'pro': 99,
        'enterprise': 299
      }
      const planType = sub.subscription_type?.toLowerCase() || 'free'
      return total + (planPrices[planType as keyof typeof planPrices] || 0)
    }, 0) || 0

    const stats = {
      totalPlans,
      activePlans,
      totalSubscribers,
      monthlyRevenue,
      certificatesIssued: 89, // Mock data
      attendanceTracked: 234, // Mock data
      webhookCalls: 45, // Mock data
      analyticsViews: 1234 // Mock data
    }

    return NextResponse.json({ stats })
  } catch (error: any) {
    console.error("Error in subscription stats API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
