-- Update certificate templates table to include new fields for enhanced editor
ALTER TABLE certificate_templates
ADD COLUMN IF NOT EXISTS html_template TEXT,
ADD COLUMN IF NOT EXISTS css_styles TEXT,
ADD COLUMN IF NOT EXISTS background_image_url TEXT,
ADD COLUMN IF NOT EXISTS fields JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS is_premium BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES users(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS is_shared BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS orientation VARCHAR(20) DEFAULT 'landscape';

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_certificate_templates_created_by ON certificate_templates(created_by);
CREATE INDEX IF NOT EXISTS idx_certificate_templates_is_active ON certificate_templates(is_active);
CREATE INDEX IF NOT EXISTS idx_certificate_templates_is_premium ON certificate_templates(is_premium);

-- Update existing templates with default HTML and CSS and mark as default
UPDATE certificate_templates
SET
  is_default = true,
  html_template = CASE
    WHEN name = 'Completion Certificate' THEN
      '<div class="certificate">
        <div class="certificate-content">
          <h1 class="title">Certificate of Completion</h1>
          <p class="subtitle">This is to certify that</p>
          <h2 class="participant-name">{{participant_name}}</h2>
          <p class="description">has successfully completed</p>
          <h3 class="event-name">{{event_name}}</h3>
          <p class="date">on {{completion_date}}</p>
          <div class="signature-section">
            <div class="signature">
              <div class="signature-line"></div>
              <p>Organizer Signature</p>
            </div>
          </div>
          <div class="qr-code">
            <img src="{{qr_code_url}}" alt="Verification QR Code" />
          </div>
        </div>
      </div>'
    WHEN name = 'Attendance Certificate' THEN
      '<div class="certificate modern">
        <div class="certificate-content">
          <h1 class="title">Certificate of Attendance</h1>
          <p class="subtitle">This certifies that</p>
          <h2 class="participant-name">{{participant_name}}</h2>
          <p class="description">attended</p>
          <h3 class="event-name">{{event_name}}</h3>
          <p class="date">on {{completion_date}}</p>
          <div class="qr-code">
            <img src="{{qr_code_url}}" alt="Verification QR Code" />
          </div>
        </div>
      </div>'
    ELSE
      '<div class="certificate elegant">
        <div class="certificate-content">
          <h1 class="title">Certificate of Achievement</h1>
          <p class="subtitle">Awarded to</p>
          <h2 class="participant-name">{{participant_name}}</h2>
          <p class="description">for outstanding achievement in</p>
          <h3 class="event-name">{{event_name}}</h3>
          <p class="date">{{completion_date}}</p>
          <div class="qr-code">
            <img src="{{qr_code_url}}" alt="Verification QR Code" />
          </div>
        </div>
      </div>'
  END,
  css_styles = CASE
    WHEN name = 'Completion Certificate' THEN
      '.certificate {
        width: 800px;
        height: 600px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: 8px solid #4a5568;
        position: relative;
        font-family: "Georgia", serif;
        color: #2d3748;
      }
      .certificate-content {
        background: white;
        margin: 20px;
        padding: 40px;
        height: calc(100% - 40px);
        text-align: center;
        position: relative;
        border: 2px solid #e2e8f0;
      }
      .title {
        font-size: 36px;
        font-weight: bold;
        color: #2b6cb0;
        margin-bottom: 20px;
        text-transform: uppercase;
        letter-spacing: 2px;
      }
      .participant-name {
        font-size: 32px;
        font-weight: bold;
        color: #2d3748;
        margin: 20px 0;
        border-bottom: 2px solid #2b6cb0;
        display: inline-block;
        padding-bottom: 5px;
      }
      .qr-code {
        position: absolute;
        bottom: 20px;
        right: 20px;
        width: 80px;
        height: 80px;
      }'
    WHEN name = 'Attendance Certificate' THEN
      '.certificate.modern {
        width: 800px;
        height: 600px;
        background: linear-gradient(45deg, #f8f9fa 0%, #e9ecef 100%);
        border: 4px solid #333333;
        position: relative;
        font-family: "Arial", sans-serif;
        color: #333333;
      }
      .certificate-content {
        background: white;
        margin: 15px;
        padding: 30px;
        height: calc(100% - 30px);
        text-align: center;
        position: relative;
        border: 1px solid #dee2e6;
      }
      .title {
        font-size: 32px;
        font-weight: bold;
        color: #495057;
        margin-bottom: 20px;
      }
      .participant-name {
        font-size: 28px;
        font-weight: bold;
        color: #212529;
        margin: 20px 0;
      }'
    ELSE
      '.certificate.elegant {
        width: 800px;
        height: 600px;
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border: 6px solid #856404;
        position: relative;
        font-family: "Georgia", serif;
        color: #856404;
      }
      .certificate-content {
        background: #fffbf0;
        margin: 25px;
        padding: 35px;
        height: calc(100% - 50px);
        text-align: center;
        position: relative;
        border: 2px solid #d4a574;
      }
      .title {
        font-size: 34px;
        font-weight: bold;
        color: #856404;
        margin-bottom: 20px;
        text-transform: uppercase;
      }
      .participant-name {
        font-size: 30px;
        font-weight: bold;
        color: #6c5ce7;
        margin: 20px 0;
      }'
  END
WHERE html_template IS NULL OR html_template = '';
