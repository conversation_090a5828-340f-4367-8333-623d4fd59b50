// Test script to verify slug routes work correctly
console.log('🧪 Testing Slug Routes');

async function testSlugRoutes() {
  try {
    // First, get the list of events to find actual slugs
    console.log('📋 Fetching events from API...');
    const eventsResponse = await fetch('http://localhost:3001/api/events');
    
    if (!eventsResponse.ok) {
      throw new Error(`Events API failed: ${eventsResponse.status}`);
    }
    
    const eventsData = await eventsResponse.json();
    console.log('📊 API Response:', typeof eventsData, eventsData);

    // Handle different response formats
    const events = Array.isArray(eventsData) ? eventsData : (eventsData.data || eventsData.events || []);
    console.log(`✅ Found ${events.length} events`);

    if (!Array.isArray(events) || events.length === 0) {
      console.log('❌ No events found to test with');
      console.log('Raw response:', eventsData);
      return;
    }

    // Get the first published event
    const testEvent = events.find(event => event.is_published || event.status === 'published');
    
    if (!testEvent) {
      console.log('❌ No published events found to test with');
      return;
    }
    
    console.log(`🎯 Testing with event: "${testEvent.title}" (slug: ${testEvent.slug})`);
    
    // Test 1: Full events route
    console.log('\n📝 Test 1: Full events route (/events/slug)');
    const fullRouteResponse = await fetch(`http://localhost:3001/events/${testEvent.slug}`);
    console.log(`   Status: ${fullRouteResponse.status}`);
    
    if (fullRouteResponse.ok) {
      console.log('   ✅ Full route works');
    } else {
      console.log('   ❌ Full route failed');
    }
    
    // Test 2: Short slug route
    console.log('\n📝 Test 2: Short slug route (/slug)');
    const shortRouteResponse = await fetch(`http://localhost:3001/${testEvent.slug}`);
    console.log(`   Status: ${shortRouteResponse.status}`);
    
    if (shortRouteResponse.ok) {
      console.log('   ✅ Short route works');
    } else {
      console.log('   ❌ Short route failed');
    }
    
    // Test 3: Compare content (if both work)
    if (fullRouteResponse.ok && shortRouteResponse.ok) {
      console.log('\n📝 Test 3: Comparing content');
      const fullContent = await fullRouteResponse.text();
      const shortContent = await shortRouteResponse.text();
      
      // Simple check - both should contain the event title
      const fullHasTitle = fullContent.includes(testEvent.title);
      const shortHasTitle = shortContent.includes(testEvent.title);
      
      if (fullHasTitle && shortHasTitle) {
        console.log('   ✅ Both routes display event content correctly');
      } else {
        console.log('   ❌ Content mismatch between routes');
        console.log(`   Full route has title: ${fullHasTitle}`);
        console.log(`   Short route has title: ${shortHasTitle}`);
      }
    }
    
    // Test 4: Invalid slug
    console.log('\n📝 Test 4: Invalid slug route');
    const invalidResponse = await fetch('http://localhost:3001/invalidslug123');
    console.log(`   Status: ${invalidResponse.status}`);
    
    if (invalidResponse.status === 302 || invalidResponse.url.includes('/events')) {
      console.log('   ✅ Invalid slug correctly redirects');
    } else {
      console.log('   ❌ Invalid slug handling failed');
    }
    
    console.log('\n🎉 Slug route testing completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testSlugRoutes();
