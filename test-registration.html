<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Registration Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .ticket-selection {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .ticket-type {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: white;
        }
        .ticket-info h3 {
            margin: 0 0 5px 0;
            color: #333;
        }
        .ticket-info p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        .ticket-price {
            font-weight: bold;
            color: #2563eb;
            font-size: 18px;
        }
        .quantity-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .quantity-control button {
            width: 30px;
            height: 30px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        .quantity-control button:hover {
            background: #f0f0f0;
        }
        .quantity-control input {
            width: 50px;
            text-align: center;
            border: 1px solid #ddd;
            padding: 5px;
            border-radius: 4px;
        }
        .summary {
            margin-top: 20px;
            padding: 20px;
            background: #f0f9ff;
            border-radius: 8px;
            border: 1px solid #bae6fd;
        }
        .summary h3 {
            margin: 0 0 15px 0;
            color: #0369a1;
        }
        .summary-line {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .summary-total {
            font-weight: bold;
            font-size: 18px;
            border-top: 1px solid #0369a1;
            padding-top: 10px;
            margin-top: 10px;
        }
        .btn {
            background: #2563eb;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 20px;
            width: 100%;
        }
        .btn:hover {
            background: #1d4ed8;
        }
        .btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            font-weight: bold;
        }
        .status.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        .status.error {
            background: #fef2f2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Event Registration Form</h1>
        <p>This page simulates ticket selection and then redirects to the registration form with mock data.</p>
        
        <div class="ticket-selection">
            <h2>Select Tickets</h2>
            
            <div class="ticket-type">
                <div class="ticket-info">
                    <h3>Early Bird</h3>
                    <p>Limited time offer with special pricing</p>
                </div>
                <div class="ticket-price">RM 120.00</div>
                <div class="quantity-control">
                    <button onclick="updateQuantity('early-bird', -1)">-</button>
                    <input type="number" id="early-bird-qty" value="1" min="0" max="5" readonly>
                    <button onclick="updateQuantity('early-bird', 1)">+</button>
                </div>
            </div>
            
            <div class="ticket-type">
                <div class="ticket-info">
                    <h3>Standard</h3>
                    <p>Regular admission ticket</p>
                </div>
                <div class="ticket-price">RM 150.00</div>
                <div class="quantity-control">
                    <button onclick="updateQuantity('standard', -1)">-</button>
                    <input type="number" id="standard-qty" value="1" min="0" max="10" readonly>
                    <button onclick="updateQuantity('standard', 1)">+</button>
                </div>
            </div>
            
            <div class="ticket-type">
                <div class="ticket-info">
                    <h3>VIP</h3>
                    <p>Premium experience with exclusive benefits</p>
                </div>
                <div class="ticket-price">RM 225.00</div>
                <div class="quantity-control">
                    <button onclick="updateQuantity('vip', -1)">-</button>
                    <input type="number" id="vip-qty" value="1" min="0" max="3" readonly>
                    <button onclick="updateQuantity('vip', 1)">+</button>
                </div>
            </div>
            
            <div class="summary">
                <h3>Order Summary</h3>
                <div id="summary-content">
                    <!-- Summary will be populated by JavaScript -->
                </div>
            </div>
            
            <button class="btn" onclick="proceedToRegistration()" id="proceed-btn">
                Proceed to Registration
            </button>
        </div>
        
        <div id="status"></div>
    </div>

    <script>
        // Ticket types configuration
        const ticketTypes = {
            'early-bird': {
                id: 'early-bird',
                name: 'Early Bird',
                description: 'Limited time offer with special pricing',
                price: 120,
                maxQuantity: 5,
                availableQuantity: 25,
                features: ['Event access', 'Digital materials', 'Early bird discount'],
                isPopular: true
            },
            'standard': {
                id: 'standard',
                name: 'Standard',
                description: 'Regular admission ticket',
                price: 150,
                maxQuantity: 10,
                availableQuantity: 150,
                features: ['Event access', 'Digital materials', 'Networking session']
            },
            'vip': {
                id: 'vip',
                name: 'VIP',
                description: 'Premium experience with exclusive benefits',
                price: 225,
                maxQuantity: 3,
                availableQuantity: 20,
                features: ['Event access', 'Digital materials', 'VIP seating', 'Meet & greet', 'Premium lunch']
            }
        };

        function updateQuantity(ticketId, change) {
            const input = document.getElementById(ticketId + '-qty');
            const currentValue = parseInt(input.value);
            const newValue = Math.max(0, Math.min(ticketTypes[ticketId].maxQuantity, currentValue + change));
            input.value = newValue;
            updateSummary();
        }

        function updateSummary() {
            const summaryContent = document.getElementById('summary-content');
            let totalAmount = 0;
            let totalTickets = 0;
            let summaryHTML = '';

            Object.keys(ticketTypes).forEach(ticketId => {
                const quantity = parseInt(document.getElementById(ticketId + '-qty').value);
                if (quantity > 0) {
                    const ticketType = ticketTypes[ticketId];
                    const subtotal = quantity * ticketType.price;
                    totalAmount += subtotal;
                    totalTickets += quantity;
                    
                    summaryHTML += `
                        <div class="summary-line">
                            <span>${ticketType.name} x ${quantity}</span>
                            <span>RM ${subtotal.toFixed(2)}</span>
                        </div>
                    `;
                }
            });

            summaryHTML += `
                <div class="summary-line">
                    <span>Total Tickets:</span>
                    <span>${totalTickets}</span>
                </div>
                <div class="summary-line summary-total">
                    <span>Total Amount:</span>
                    <span>RM ${totalAmount.toFixed(2)}</span>
                </div>
            `;

            summaryContent.innerHTML = summaryHTML;

            // Enable/disable proceed button
            const proceedBtn = document.getElementById('proceed-btn');
            proceedBtn.disabled = totalTickets === 0;
        }

        function proceedToRegistration() {
            const selectedTickets = [];
            
            Object.keys(ticketTypes).forEach(ticketId => {
                const quantity = parseInt(document.getElementById(ticketId + '-qty').value);
                if (quantity > 0) {
                    selectedTickets.push({
                        ticketType: ticketTypes[ticketId],
                        quantity: quantity
                    });
                }
            });

            if (selectedTickets.length === 0) {
                showStatus('Please select at least one ticket.', 'error');
                return;
            }

            // Store selected tickets in session storage
            sessionStorage.setItem('selectedTickets', JSON.stringify(selectedTickets));
            
            showStatus('Tickets selected! Redirecting to registration form...', 'success');
            
            // Redirect to registration form after a short delay
            setTimeout(() => {
                window.location.href = 'http://localhost:3001/events/5nEh/register';
            }, 1500);
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = message;
            statusDiv.className = 'status ' + type;
        }

        // Initialize summary on page load
        updateSummary();
    </script>
</body>
</html>
