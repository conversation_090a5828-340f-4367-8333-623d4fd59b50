-- Add ic_reg column to registrations table for IC/Registration number storage
-- This field stores the IC (Identity Card) or registration number for attendees

DO $$ 
BEGIN
  -- Add ic_reg column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'registrations' AND column_name = 'ic_reg') THEN
    ALTER TABLE registrations ADD COLUMN ic_reg VARCHAR(50);
  END IF;
END $$;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_registrations_ic_reg ON registrations(ic_reg);

-- Add comment to document the purpose
COMMENT ON COLUMN registrations.ic_reg IS 'IC (Identity Card) or registration number for the attendee';
