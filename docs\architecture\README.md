# Architecture Documentation

This directory contains technical architecture documentation for the mTicket.my platform.

## Contents

### Core Architecture
- [Application Structure](./application-structure.md) - Overall application architecture and organization
- [Database Schema](./database-schema.md) - Complete database schema and relationships
- [Performance Optimization](./performance-optimization.md) - Performance strategies and optimizations

### Security & Access Control
- [Role-Based Access Control](./rbac-system.md) - Comprehensive RBAC implementation
- [Row Level Security](./rls-implementation.md) - Database-level security policies

### System Components
- [Payment System](./payment-system.md) - Modular payment gateway architecture
- [Certificate Management](./certificate-system.md) - Certificate generation and management
- [Activity Logging](./activity-logging.md) - Comprehensive audit trail system

### Data Management
- [Server-Side Data Fetching](./data-fetching.md) - Data fetching patterns and strategies
- [Invoice & Receipt System](./invoice-system.md) - Financial document management

## Quick Navigation

- **Getting Started**: See [../guides/](../guides/) for setup and development guides
- **API Reference**: See [../api/](../api/) for API documentation
- **User Guides**: See [../guides/user/](../guides/user/) for end-user documentation
- **Developer Guides**: See [../guides/developer/](../guides/developer/) for development documentation
