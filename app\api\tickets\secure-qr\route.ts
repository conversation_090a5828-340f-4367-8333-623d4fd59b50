import { NextResponse } from "next/server"
import { generateSecureQRData } from "@/lib/security-token"
import { getJWTTokenFromRequest, verifyJWTToken } from "@/lib/auth"

/**
 * POST /api/tickets/secure-qr
 * Generate a secure, time-based QR code for a ticket
 * Requires authentication
 */
export async function POST(request: Request) {
  try {
    // Get JWT token from request
    const token = getJWTTokenFromRequest(request)
    
    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Verify the JWT token and get user data
    const authResult = await verifyJWTToken(token)
    if (!authResult?.user) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { ticketData } = body

    // Validate ticket data
    if (!ticketData || !ticketData.id || !ticketData.event_id || !ticketData.guest_name) {
      return NextResponse.json(
        { error: "Invalid ticket data. Required fields: id, event_id, guest_name" },
        { status: 400 }
      )
    }

    // Get base URL from request
    const url = new URL(request.url)
    const baseUrl = `${url.protocol}//${url.host}`

    // Generate secure QR code data
    const qrData = generateSecureQRData(ticketData, baseUrl)

    return NextResponse.json({
      success: true,
      qrData,
      ticketId: ticketData.id,
      eventId: ticketData.event_id,
      timestamp: new Date().toISOString(),
    })

  } catch (error: any) {
    console.error("Error generating secure QR code:", error)
    return NextResponse.json(
      { error: error.message || "Failed to generate secure QR code" },
      { status: 500 }
    )
  }
}
