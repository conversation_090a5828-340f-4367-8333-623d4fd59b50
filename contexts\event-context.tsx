"use client"

import type React from "react"

import { createContext, useContext, useState, useEffect, useCallback } from "react"
import { supabase } from "@/lib/supabase"

// Types
export interface EventImage {
  url: string
  alt_text?: string
  order?: number
  is_primary?: boolean
}

// Custom Field Interface
export interface CustomField {
  id: string
  label: string
  type: 'text' | 'email' | 'phone' | 'number' | 'select' | 'checkbox' | 'textarea'
  required: boolean
  placeholder?: string
  options?: string[]
  validation?: {
    minLength?: number
    maxLength?: number
    pattern?: string
  }
  order: number
}

// Ticket Type Interface
export interface TicketType {
  id: string
  name: string
  description: string
  price: number
  maxQuantity: number
  availableQuantity: number
  features: string[]
  isPopular?: boolean
  saleStartDate?: string // When this ticket type becomes available for sale
  saleEndDate?: string // When this ticket type stops being available for sale (deadline)
}

// Event Ticket Interface
export interface EventTicket {
  ticketType: TicketType
  quantity: number
}

export type EventType = {
  id: string
  title: string
  slug: string // Random Base62 shortlink (e.g., "aB3x", "9Kp2", "mN8Q")
  description: string
  description_html?: string // Rich HTML content for WYSIWYG
  short_description?: string
  location: string
  venue_details?: string
  start_date: string
  end_date: string
  registration_deadline?: string
  price: number | null // Legacy field, use tickets instead
  max_participants: number | null
  current_participants: number
  image_url: string | null
  images?: EventImage[]
  tickets?: EventTicket[] // JSONB array of ticket configurations
  custom_fields?: CustomField[] // JSONB array of custom field configurations
  status: "draft" | "published" | "cancelled" | "completed"
  created_by: string
  created_at: string
  updated_at: string
  payment_gateway_id: string | null
  category_id: string | null
  is_featured: boolean
  is_published?: boolean // Database field for published status
  enable_certificates: boolean
  enable_attendance: boolean
  is_public: boolean
  event_category?: {
    id: string
    name: string
    color: string
    icon: string
  }
  organizations?: {
    id: string
    name: string
    ssm_number: string
    pic_name: string
    pic_phone: string
    pic_email: string
    logo_url?: string
    address?: string
    city?: string
    state?: string
    postal_code?: string
    country?: string
    website?: string
  }
}

type EventContextType = {
  events: EventType[]
  featuredEvents: EventType[]
  loading: boolean
  error: string | null
  getEventBySlug: (slug: string) => Promise<EventType | null>
  createEvent: (eventData: Partial<EventType>) => Promise<EventType | null>
  updateEvent: (id: string, eventData: Partial<EventType>) => Promise<EventType | null>
  deleteEvent: (id: string) => Promise<boolean>
  refreshEvents: () => Promise<void>
}

const EventContext = createContext<EventContextType | undefined>(undefined)

export function EventProvider({ children }: { children: React.ReactNode }) {
  const [events, setEvents] = useState<EventType[]>([])
  const [featuredEvents, setFeaturedEvents] = useState<EventType[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch events from the database
  const fetchEvents = useCallback(async () => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from("events")
        .select(`
          *,
          event_categories (
            id,
            name,
            color,
            icon
          )
        `)
        .order("created_at", { ascending: false })

      if (error) {
        console.error("Supabase error details:", {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        })
        throw error
      }

      console.log("Successfully fetched events:", data?.length || 0)
      setEvents(data as EventType[])

      // Filter featured events (only published events)
      const featured = data.filter((event: EventType) => event.is_featured && event.is_published)
      setFeaturedEvents(featured as EventType[])

      setError(null)
    } catch (err: any) {
      console.error("Error fetching events:", err)
      setError(err.message || "Failed to fetch events")
    } finally {
      setLoading(false)
    }
  }, [])

  // Fetch events on mount
  useEffect(() => {
    fetchEvents()
  }, [fetchEvents])

  // Get event by slug
  const getEventBySlug = useCallback(async (slug: string): Promise<EventType | null> => {
    try {
      console.log(`Fetching event with slug: ${slug}`)

      if (!slug) {
        console.error("No slug provided")
        return null
      }

      // Directly fetch the event with all fields including images and organizer data
      const { data, error } = await supabase
        .from("events")
        .select(`
          *,
          event_categories!category_id (
            id,
            name,
            color,
            icon
          ),
          organizations:organization_id (
            id,
            name,
            ssm_number,
            pic_name,
            pic_phone,
            pic_email,
            logo_url,
            address,
            city,
            state,
            postal_code,
            country,
            website
          )
        `)
        .eq("slug", slug)
        .maybeSingle()

      if (error) {
        console.error("Error fetching event by slug:", error)
        console.error("Error details:", {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        })
        // Don't throw, return null and log the error
        return null
      }

      if (!data) {
        console.log(`No event found with slug: ${slug}`)

        // If no event found, try to fetch mock data for development
        console.log("Attempting to fetch mock data for development")

        // This is a fallback for development - in production you'd remove this
        const mockEvents = [
          {
            id: "1",
            title: "Tech Conference 2023",
            slug: "aB3x", // Random Base62 shortlink
            description: "Join us for the biggest tech conference in the region.",
            location: "Kuala Lumpur Convention Centre",
            start_date: new Date("2023-12-15").toISOString(),
            end_date: new Date("2023-12-16").toISOString(),
            price: 150.0,
            max_participants: 500,
            current_participants: 320,
            image_url: null,
            status: "published",
            created_by: "system",
            created_at: new Date("2023-11-01").toISOString(),
            updated_at: new Date("2023-11-01").toISOString(),
            payment_gateway_id: null,
            category: "Technology",
            is_featured: true,
            enable_certificates: false,
            enable_attendance: true,
            is_public: true,
          },
          {
            id: "2",
            title: "Music Festival 2023",
            slug: "9Kp2", // Random Base62 shortlink
            description: "A weekend of amazing music performances.",
            location: "Bukit Jalil Stadium",
            start_date: new Date("2023-11-25").toISOString(),
            end_date: new Date("2023-11-26").toISOString(),
            price: 200.0,
            max_participants: 1000,
            current_participants: 750,
            image_url: null,
            status: "published",
            created_by: "system",
            created_at: new Date("2023-10-15").toISOString(),
            updated_at: new Date("2023-10-15").toISOString(),
            payment_gateway_id: null,
            category: "Entertainment",
            is_featured: true,
            enable_certificates: false,
            enable_attendance: true,
            is_public: true,
          },
        ]

        const mockEvent = mockEvents.find((event) => event.slug === slug)
        if (mockEvent) {
          console.log("Using mock event data:", mockEvent)
          return mockEvent as EventType
        }

        return null
      }

      console.log(`Successfully fetched event:`, data)
      console.log("Organization data:", data.organizations)
      console.log("Organization ID:", data.organization_id)
      return data as EventType
    } catch (err: any) {
      console.error(`Error fetching event by slug (${slug}):`, err)
      // Return null instead of throwing to prevent app crashes
      return null
    }
  }, [])



  // Create a new event
  const createEvent = async (eventData: Partial<EventType>): Promise<EventType | null> => {
    try {
      console.log("Creating event with data:", eventData)

      // Clean and map the eventData to match database schema
      const cleanEventData: any = {}

      // Map fields that exist in the database
      if (eventData.title !== undefined) cleanEventData.title = eventData.title
      if (eventData.description !== undefined) cleanEventData.description = eventData.description
      if (eventData.short_description !== undefined) cleanEventData.short_description = eventData.short_description
      if (eventData.location !== undefined) cleanEventData.location = eventData.location
      if (eventData.venue_details !== undefined) cleanEventData.venue_details = eventData.venue_details
      if (eventData.start_date !== undefined) cleanEventData.start_date = eventData.start_date
      if (eventData.end_date !== undefined) cleanEventData.end_date = eventData.end_date
      if (eventData.registration_deadline !== undefined) cleanEventData.registration_deadline = eventData.registration_deadline
      if (eventData.image_url !== undefined) cleanEventData.image_url = eventData.image_url
      if (eventData.images !== undefined) cleanEventData.images = eventData.images
      if (eventData.tickets !== undefined) cleanEventData.tickets = eventData.tickets
      if (eventData.max_participants !== undefined) cleanEventData.max_participants = eventData.max_participants
      if (eventData.price !== undefined) cleanEventData.price = eventData.price
      if (eventData.category_id !== undefined) cleanEventData.category_id = eventData.category_id
      if (eventData.is_featured !== undefined) cleanEventData.is_featured = eventData.is_featured
      if (eventData.enable_certificates !== undefined) cleanEventData.enable_certificates = eventData.enable_certificates
      if (eventData.enable_attendance !== undefined) cleanEventData.enable_attendance = eventData.enable_attendance

      // Map status to is_published
      if (eventData.status !== undefined) {
        cleanEventData.is_published = eventData.status === 'published'
      }

      // Map created_by to event_manager_id
      if (eventData.created_by !== undefined) {
        cleanEventData.event_manager_id = eventData.created_by
      }

      console.log("Cleaned event data for creation:", cleanEventData)

      const { data, error } = await supabase.from("events").insert([cleanEventData]).select().single()

      if (error) {
        console.error("Supabase create error:", error)
        throw error
      }

      // Update local state
      setEvents((prevEvents) => [data as EventType, ...prevEvents])

      // Log the event creation activity
      try {
        await supabase.from("activity_logs").insert([
          {
            user_id: eventData.created_by,
            action: "create_event",
            entity_type: "event",
            entity_id: data.id,
            category: "event",
            details: {
              event_name: data.title,
              event_status: data.status,
              event_location: data.location,
              event_start_date: data.start_date,
              event_end_date: data.end_date,
            },
            created_at: new Date().toISOString(),
          },
        ])
      } catch (logError) {
        // Don't fail event creation if logging fails
        console.error("Error logging event creation activity:", logError)
      }

      return data as EventType
    } catch (err: any) {
      console.error("Error creating event:", err)
      setError(err.message || "Failed to create event")
      return null
    }
  }

  // Update an existing event
  const updateEvent = async (id: string, eventData: Partial<EventType>): Promise<EventType | null> => {
    try {
      console.log("Updating event with ID:", id)
      console.log("Event data being sent:", eventData)

      // Get the original event data for logging changes
      const { data: originalEvent } = await supabase.from("events").select("*").eq("id", id).single()

      // Clean and map the eventData to match database schema
      const cleanEventData: any = {}

      // Map fields that exist in the database
      if (eventData.title !== undefined) cleanEventData.title = eventData.title
      if (eventData.description !== undefined) cleanEventData.description = eventData.description
      if (eventData.short_description !== undefined) cleanEventData.short_description = eventData.short_description
      if (eventData.location !== undefined) cleanEventData.location = eventData.location
      if (eventData.venue_details !== undefined) cleanEventData.venue_details = eventData.venue_details
      if (eventData.start_date !== undefined) cleanEventData.start_date = eventData.start_date
      if (eventData.end_date !== undefined) cleanEventData.end_date = eventData.end_date
      if (eventData.registration_deadline !== undefined) cleanEventData.registration_deadline = eventData.registration_deadline
      if (eventData.image_url !== undefined) cleanEventData.image_url = eventData.image_url
      if (eventData.images !== undefined) cleanEventData.images = eventData.images
      if (eventData.tickets !== undefined) cleanEventData.tickets = eventData.tickets
      if (eventData.max_participants !== undefined) cleanEventData.max_participants = eventData.max_participants
      if (eventData.price !== undefined) cleanEventData.price = eventData.price
      if (eventData.category_id !== undefined) cleanEventData.category_id = eventData.category_id
      if (eventData.is_featured !== undefined) cleanEventData.is_featured = eventData.is_featured
      if (eventData.enable_certificates !== undefined) cleanEventData.enable_certificates = eventData.enable_certificates
      if (eventData.enable_attendance !== undefined) cleanEventData.enable_attendance = eventData.enable_attendance

      // Map status to is_published
      if (eventData.status !== undefined) {
        cleanEventData.is_published = eventData.status === 'published'
      }

      // Map created_by to event_manager_id (only for new events)
      if (eventData.created_by !== undefined && !originalEvent) {
        cleanEventData.event_manager_id = eventData.created_by
      }

      console.log("Cleaned event data:", cleanEventData)

      // Update the event
      const { data, error } = await supabase.from("events").update(cleanEventData).eq("id", id).select().single()

      if (error) {
        console.error("Supabase update error:", error)
        console.error("Error details:", {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        })
        throw error
      }

      // Update local state
      setEvents((prevEvents) => prevEvents.map((event) => (event.id === id ? { ...event, ...data } : event)))

      // Log the event update activity
      try {
        // Determine which fields were updated
        const fieldsUpdated = Object.keys(eventData)

        // Create a record of previous values for changed fields
        const previousValues: Record<string, any> = {}
        if (originalEvent) {
          fieldsUpdated.forEach(field => {
            previousValues[field] = originalEvent[field as keyof EventType]
          })
        }

        await supabase.from("activity_logs").insert([
          {
            user_id: eventData.created_by || originalEvent?.created_by,
            action: "update_event",
            entity_type: "event",
            entity_id: id,
            category: "event",
            details: {
              event_name: data.title,
              fields_updated: fieldsUpdated,
              previous_values: previousValues,
              new_values: fieldsUpdated.reduce((acc, field) => {
                acc[field] = eventData[field as keyof Partial<EventType>]
                return acc
              }, {} as Record<string, any>),
              status_change: eventData.status !== originalEvent?.status
                ? `${originalEvent?.status} → ${eventData.status}`
                : undefined,
            },
            created_at: new Date().toISOString(),
          },
        ])
      } catch (logError) {
        // Don't fail event update if logging fails
        console.error("Error logging event update activity:", logError)
      }

      return data as EventType
    } catch (err: any) {
      console.error("Error updating event:", err)
      console.error("Error type:", typeof err)
      console.error("Error keys:", Object.keys(err))
      console.error("Error message:", err?.message)
      console.error("Error details:", err?.details)
      console.error("Error hint:", err?.hint)
      console.error("Error code:", err?.code)

      const errorMessage = err?.message || err?.details || "Failed to update event"
      setError(errorMessage)
      return null
    }
  }

  // Delete an event
  const deleteEvent = async (id: string): Promise<boolean> => {
    try {
      // Get the event data for logging
      const { data: eventToDelete } = await supabase.from("events").select("*").eq("id", id).single()

      // Delete the event
      const { error } = await supabase.from("events").delete().eq("id", id)

      if (error) throw error

      // Update local state
      setEvents((prevEvents) => prevEvents.filter((event) => event.id !== id))

      // Log the event deletion activity
      if (eventToDelete) {
        try {
          await supabase.from("activity_logs").insert([
            {
              user_id: eventToDelete.created_by,
              action: "delete_event",
              entity_type: "event",
              entity_id: id,
              category: "event",
              details: {
                event_name: eventToDelete.title,
                event_status: eventToDelete.status,
                event_location: eventToDelete.location,
                event_start_date: eventToDelete.start_date,
                event_end_date: eventToDelete.end_date,
              },
              created_at: new Date().toISOString(),
            },
          ])
        } catch (logError) {
          // Don't fail event deletion if logging fails
          console.error("Error logging event deletion activity:", logError)
        }
      }

      return true
    } catch (err: any) {
      console.error("Error deleting event:", err)
      setError(err.message || "Failed to delete event")
      return false
    }
  }

  // Refresh events
  const refreshEvents = async (): Promise<void> => {
    await fetchEvents()
  }

  const value = {
    events,
    featuredEvents,
    loading,
    error,
    getEventBySlug,
    createEvent,
    updateEvent,
    deleteEvent,
    refreshEvents,
  }

  return <EventContext.Provider value={value}>{children}</EventContext.Provider>
}

export function useEvents() {
  const context = useContext(EventContext)
  if (context === undefined) {
    throw new Error("useEvents must be used within an EventProvider")
  }
  return context
}
