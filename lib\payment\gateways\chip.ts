import type { PaymentGateway, PaymentGatewayConfig, PaymentRequest, PaymentResponse } from "../types"

export class ChipGateway implements PaymentGateway {
  async createPayment(
    config: PaymentGatewayConfig,
    paymentRequest: PaymentRequest,
  ): Promise<PaymentResponse> {
    try {
      // In a real implementation, this would make an API call to Chip
      // For demo purposes, we'll simulate a successful response

      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Generate a fake transaction ID
      const transactionId = `chip_${Math.random().toString(36).substring(2, 15)}`

      // Generate a fake payment URL
      const paymentUrl = `https://gate.chip-in.asia/checkout/${transactionId}`

      return {
        success: true,
        payment_url: paymentUrl,
        transaction_id: transactionId,
        payment_gateway_id: paymentRequest.payment_gateway_id,
      }
    } catch (error: any) {
      console.error("Error creating Chip payment:", error)
      return {
        success: false,
        error: error.message || "Failed to create payment",
      }
    }
  }

  async verifyPayment(transactionId: string): Promise<boolean> {
    try {
      // In a real implementation, this would make an API call to Chip
      // For demo purposes, we'll simulate a successful verification

      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500))

      // Always return true for demo
      return true
    } catch (error) {
      console.error("Error verifying Chip payment:", error)
      return false
    }
  }
}

// Legacy function for backward compatibility
export async function createChipPayment(
  config: PaymentGatewayConfig,
  paymentRequest: PaymentRequest,
): Promise<PaymentResponse> {
  const gateway = new ChipGateway()
  return gateway.createPayment(config, paymentRequest)
}
