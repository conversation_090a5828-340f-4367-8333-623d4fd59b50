import { NextRequest, NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { logActivity } from "@/lib/activity-logger"

// Type for payment gateway from database
export type PaymentGatewaySetting = {
  id: string
  gateway_name: string
  is_enabled: boolean
  is_test_mode: boolean
  configuration: Record<string, any>
  test_configuration: Record<string, any>
  live_configuration: Record<string, any>
  display_order: number
  created_at: string
  updated_at: string
  created_by?: string
  updated_by?: string
}

// GET: Fetch all payment gateways
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    // Check if user is authenticated and has admin role
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }
    
    // Get user role from session
    const userRole = session.user.role
    
    // Check if user has admin or manager role
    if (userRole !== "admin" && userRole !== "manager") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }
    
    const supabaseAdmin = getSupabaseAdmin()
    
    // Get all payment gateways
    const { data, error } = await supabaseAdmin
      .from("payment_gateway_settings")
      .select("*")
      .order("display_order", { ascending: true })
    
    if (error) {
      console.error("Error fetching payment gateways:", error)
      return NextResponse.json({ error: "Failed to fetch payment gateways" }, { status: 500 })
    }
    
    return NextResponse.json(data)
  } catch (error) {
    console.error("Error in payment gateways API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// POST: Create a new payment gateway
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    // Check if user is authenticated and has admin role
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }
    
    // Get user role from session
    const userRole = session.user.role
    
    // Check if user has admin role
    if (userRole !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }
    
    const supabaseAdmin = getSupabaseAdmin()
    const body = await request.json()
    
    // Validate required fields
    if (!body.gateway_name) {
      return NextResponse.json({ error: "Gateway name is required" }, { status: 400 })
    }
    
    // Get the highest display_order value
    const { data: maxOrderData, error: maxOrderError } = await supabaseAdmin
      .from("payment_gateway_settings")
      .select("display_order")
      .order("display_order", { ascending: false })
      .limit(1)
    
    if (maxOrderError) {
      console.error("Error getting max display order:", maxOrderError)
      return NextResponse.json({ error: "Failed to create payment gateway" }, { status: 500 })
    }
    
    const nextDisplayOrder = maxOrderData && maxOrderData.length > 0 ? maxOrderData[0].display_order + 1 : 1
    
    // Create new payment gateway
    const newGateway = {
      gateway_name: body.gateway_name,
      is_enabled: body.is_enabled ?? false,
      is_test_mode: body.is_test_mode ?? true,
      configuration: body.configuration ?? {},
      test_configuration: body.test_configuration ?? {},
      live_configuration: body.live_configuration ?? {},
      display_order: nextDisplayOrder,
      created_by: session.user.id,
      updated_by: session.user.id,
    }
    
    const { data, error } = await supabaseAdmin
      .from("payment_gateway_settings")
      .insert([newGateway])
      .select()
    
    if (error) {
      console.error("Error creating payment gateway:", error)
      return NextResponse.json({ error: "Failed to create payment gateway" }, { status: 500 })
    }
    
    // Log activity
    await logActivity({
      user_id: session.user.id,
      action: "create",
      entity_type: "payment_gateway",
      entity_id: data[0].id,
      details: { gateway_name: data[0].gateway_name },
      category: "settings",
    })
    
    return NextResponse.json(data[0], { status: 201 })
  } catch (error) {
    console.error("Error in payment gateways API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// PUT: Update a payment gateway
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    // Check if user is authenticated and has admin role
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }
    
    // Get user role from session
    const userRole = session.user.role
    
    // Check if user has admin role
    if (userRole !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }
    
    const supabaseAdmin = getSupabaseAdmin()
    const body = await request.json()
    
    // Validate required fields
    if (!body.id || !body.gateway_name) {
      return NextResponse.json({ error: "Gateway ID and name are required" }, { status: 400 })
    }
    
    // Update payment gateway
    const updates = {
      gateway_name: body.gateway_name,
      is_enabled: body.is_enabled,
      is_test_mode: body.is_test_mode,
      configuration: body.configuration,
      test_configuration: body.test_configuration,
      live_configuration: body.live_configuration,
      display_order: body.display_order,
      updated_by: session.user.id,
      updated_at: new Date().toISOString(),
    }
    
    const { data, error } = await supabaseAdmin
      .from("payment_gateway_settings")
      .update(updates)
      .eq("id", body.id)
      .select()
    
    if (error) {
      console.error("Error updating payment gateway:", error)
      return NextResponse.json({ error: "Failed to update payment gateway" }, { status: 500 })
    }
    
    if (!data || data.length === 0) {
      return NextResponse.json({ error: "Payment gateway not found" }, { status: 404 })
    }
    
    // Log activity
    await logActivity({
      user_id: session.user.id,
      action: "update",
      entity_type: "payment_gateway",
      entity_id: data[0].id,
      details: { gateway_name: data[0].gateway_name },
      category: "settings",
    })
    
    return NextResponse.json(data[0])
  } catch (error) {
    console.error("Error in payment gateways API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// DELETE: Delete a payment gateway
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    // Check if user is authenticated and has admin role
    if (!session || !session.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }
    
    // Get user role from session
    const userRole = session.user.role
    
    // Check if user has admin role
    if (userRole !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }
    
    const supabaseAdmin = getSupabaseAdmin()
    const url = new URL(request.url)
    const id = url.searchParams.get("id")
    
    if (!id) {
      return NextResponse.json({ error: "Payment gateway ID is required" }, { status: 400 })
    }
    
    // Get the gateway before deleting for logging
    const { data: gatewayData, error: getError } = await supabaseAdmin
      .from("payment_gateway_settings")
      .select("gateway_name")
      .eq("id", id)
      .single()
    
    if (getError) {
      console.error("Error getting payment gateway:", getError)
      return NextResponse.json({ error: "Failed to delete payment gateway" }, { status: 500 })
    }
    
    // Delete payment gateway
    const { error } = await supabaseAdmin
      .from("payment_gateway_settings")
      .delete()
      .eq("id", id)
    
    if (error) {
      console.error("Error deleting payment gateway:", error)
      return NextResponse.json({ error: "Failed to delete payment gateway" }, { status: 500 })
    }
    
    // Log activity
    await logActivity({
      user_id: session.user.id,
      action: "delete",
      entity_type: "payment_gateway",
      entity_id: id,
      details: { gateway_name: gatewayData.gateway_name },
      category: "settings",
    })
    
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error in payment gateways API:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
