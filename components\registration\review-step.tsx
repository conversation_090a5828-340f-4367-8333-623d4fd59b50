"use client"

import React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { type EventType } from "@/contexts/event-context"
import { type ParticipantType } from "@/components/registration-form"
import { type SelectedTicket } from "@/types/ticket-types"
import {
  ShoppingCart,
  Calendar,
  MapPin,
  User,
  Users,
  Ticket,
  CheckCircle,
  Edit,
  CreditCard
} from "lucide-react"

interface ReviewStepProps {
  event: EventType
  participants: ParticipantType[]
  mainContact: any
  selectedTickets: SelectedTicket[]
  totalAmount: number
  onBackToForm: () => void
  onProceedToPayment: () => void
}

export function ReviewStep({
  event,
  participants,
  mainContact,
  selectedTickets,
  totalAmount,
  onBackToForm,
  onProceedToPayment
}: ReviewStepProps) {
  const totalTickets = selectedTickets.reduce((total, ticket) => total + ticket.quantity, 0)
  const isFreeEvent = totalAmount <= 0

  return (
    <div className="space-y-6">
      {/* Step Header */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 text-white rounded-full mb-4">
          <ShoppingCart className="h-8 w-8" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Review Your Registration</h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Please review all the details below before proceeding. You can go back to make changes if needed.
        </p>
      </div>

      {/* Event Summary */}
      <Card className="shadow-lg border-0 bg-white">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <CardTitle className="flex items-center gap-2 text-xl text-gray-900">
            <Calendar className="h-5 w-5 text-blue-600" />
            Event Details
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-700">Event Name</p>
                <p className="text-gray-900 font-semibold">{event.title}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">Date & Time</p>
                <p className="text-gray-900">
                  {new Date(event.start_date).toLocaleDateString()} {new Date(event.start_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} -{" "}
                  {new Date(event.end_date).toLocaleDateString()} {new Date(event.end_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </p>
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-700">Location</p>
                <p className="text-gray-900 flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
                  {event.location}
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Contact */}
      <Card className="shadow-lg border-0 bg-white">
        <CardHeader className="bg-gradient-to-r from-purple-50 to-pink-50 border-b">
          <CardTitle className="flex items-center gap-2 text-xl text-gray-900">
            <User className="h-5 w-5 text-purple-600" />
            Main Contact Person
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid gap-4 md:grid-cols-3">
            <div>
              <p className="text-sm font-medium text-gray-700">Name</p>
              <p className="text-gray-900 font-semibold">{mainContact?.name}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700">Email</p>
              <p className="text-gray-900">{mainContact?.email}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-gray-700">Phone</p>
              <p className="text-gray-900">{mainContact?.phone}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Participants */}
      <Card className="shadow-lg border-0 bg-white">
        <CardHeader className="bg-gradient-to-r from-green-50 to-teal-50 border-b">
          <CardTitle className="flex items-center justify-between text-xl text-gray-900">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-green-600" />
              Participants ({participants.length})
            </div>
            <Button variant="outline" size="sm" onClick={onBackToForm}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="space-y-4">
            {participants.map((participant, index) => (
              <div key={index} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      Participant {index + 1}
                    </Badge>
                    {participant.ticketTypeName && (
                      <Badge variant="outline" className="border-green-300 text-green-700">
                        <Ticket className="h-3 w-3 mr-1" />
                        {participant.ticketTypeName}
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-sm">
                  <div>
                    <p className="font-medium text-gray-700">Name</p>
                    <p className="text-gray-900">{participant.name}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">Email</p>
                    <p className="text-gray-900">{participant.email}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">Phone</p>
                    <p className="text-gray-900">{participant.phone}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-700">IC/Passport</p>
                    <p className="text-gray-900">{participant.ic}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Payment Summary */}
      <Card className="shadow-lg border-0 bg-white">
        <CardHeader className="bg-gradient-to-r from-yellow-50 to-orange-50 border-b">
          <CardTitle className="flex items-center gap-2 text-xl text-gray-900">
            <CreditCard className="h-5 w-5 text-yellow-600" />
            Payment Summary
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg p-4 border border-gray-200">
            <div className="space-y-3">
              {selectedTickets.map((ticket) => (
                <div key={ticket.ticketType.id} className="flex justify-between items-center">
                  <div className="text-gray-700">
                    {ticket.ticketType.name} × {ticket.quantity}
                  </div>
                  <div className="font-semibold text-gray-900">
                    RM {(ticket.ticketType.price * ticket.quantity).toFixed(2)}
                  </div>
                </div>
              ))}
              <Separator />
              <div className="flex justify-between items-center text-lg font-bold">
                <div className="text-gray-900">Total Amount</div>
                <div className="text-gray-900">RM {totalAmount.toFixed(2)}</div>
              </div>
              <div className="flex justify-between items-center text-sm text-gray-600">
                <div>Total Tickets</div>
                <div>{totalTickets} ticket{totalTickets !== 1 ? 's' : ''}</div>
              </div>
            </div>
          </div>

          {isFreeEvent && (
            <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-green-800">
                <CheckCircle className="h-5 w-5" />
                <span className="font-medium">Free Event - No Payment Required</span>
              </div>
            </div>
          )}
        </CardContent>
        <CardFooter className="bg-gray-50 border-t p-6">
          <div className="flex justify-between w-full gap-4">
            <Button variant="outline" onClick={onBackToForm} className="flex-1">
              <Edit className="h-4 w-4 mr-2" />
              Back to Edit
            </Button>
            <Button onClick={onProceedToPayment} className="flex-1 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700">
              {isFreeEvent ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Complete Registration
                </>
              ) : (
                <>
                  <CreditCard className="h-4 w-4 mr-2" />
                  Proceed to Payment (RM {totalAmount.toFixed(2)})
                </>
              )}
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
