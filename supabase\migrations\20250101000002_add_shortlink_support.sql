-- Add random slug support to events table
-- This migration updates slug generation to use random Base62 characters

-- Remove the slug_id column if it exists (we don't need sequential IDs)
ALTER TABLE events DROP COLUMN IF EXISTS slug_id;

-- Create a function to generate random slugs
CREATE OR REPLACE FUNCTION generate_random_slug()
RETURNS TEXT AS $$
DECLARE
    base62_chars TEXT := '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    result TEXT := '';
    char_index INTEGER;
    i INTEGER;
    slug_length INTEGER := 4; -- Start with 4 characters
    max_attempts INTEGER := 1000;
    attempt INTEGER := 0;
    existing_count INTEGER;
BEGIN
    -- Check if we should use 5 characters (when 4-char space is getting full)
    SELECT COUNT(*) INTO existing_count FROM events WHERE LENGTH(slug) = 4;

    -- Switch to 5 characters when we have used 90% of 4-character space
    IF existing_count >= (POWER(62, 4) * 0.9) THEN
        slug_length := 5;
    END IF;

    LOOP
        result := '';

        -- Generate random slug
        FOR i IN 1..slug_length LOOP
            char_index := floor(random() * 62) + 1;
            result := result || substr(base62_chars, char_index, 1);
        END LOOP;

        -- Check if slug already exists
        IF NOT EXISTS (SELECT 1 FROM events WHERE slug = result) THEN
            RETURN result;
        END IF;

        attempt := attempt + 1;
        IF attempt >= max_attempts THEN
            -- If we can't find a unique 4-char slug, try 5-char
            IF slug_length = 4 THEN
                slug_length := 5;
                attempt := 0;
            ELSE
                RAISE EXCEPTION 'Unable to generate unique slug after % attempts', max_attempts;
            END IF;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger function to automatically generate random slug on insert
CREATE OR REPLACE FUNCTION update_event_slug()
RETURNS TRIGGER AS $$
BEGIN
    -- Generate random slug if slug is not provided or is invalid format
    IF NEW.slug IS NULL OR NEW.slug = '' OR LENGTH(NEW.slug) > 5 OR LENGTH(NEW.slug) < 4 THEN
        NEW.slug := generate_random_slug();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically generate slug on insert/update
DROP TRIGGER IF EXISTS trigger_generate_slug ON events;
CREATE TRIGGER trigger_generate_slug
    BEFORE INSERT OR UPDATE ON events
    FOR EACH ROW
    EXECUTE FUNCTION update_event_slug();

-- Update existing events to have random slugs
-- This will generate new random slugs for existing events
DO $$
DECLARE
    event_record RECORD;
BEGIN
    -- Update existing events that have old-format slugs
    FOR event_record IN
        SELECT id FROM events
        WHERE LENGTH(slug) > 5 OR LENGTH(slug) < 4
        ORDER BY created_at ASC
    LOOP
        UPDATE events
        SET slug = generate_random_slug()
        WHERE id = event_record.id;
    END LOOP;
END $$;
