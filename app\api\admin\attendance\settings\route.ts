import { NextRequest, NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { getUserFromToken } from "@/lib/auth/token"

export async function GET(request: NextRequest) {
  try {
    // Get user from token
    const userToken = await getUserFromToken(request)
    if (!userToken) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin()

    // Get user details to check admin role
    const { data: user, error: userError } = await supabaseAdmin
      .from("users")
      .select(`
        *,
        role_info:role_id(id, role_name, description, permissions)
      `)
      .eq("id", userToken.userId)
      .single()

    if (userError || !user) {
      console.error("Error fetching user:", userError)
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Check if user has admin role
    const isAdmin = user.role === "admin" || user.role_info?.role_name === "admin"
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 403 }
      )
    }

    // Fetch attendance settings
    const { data: settings, error } = await supabaseAdmin
      .from('attendance_settings')
      .select('*')
      .single()

    if (error) {
      console.error("Error fetching attendance settings:", error)
      return NextResponse.json(
        { error: "Failed to fetch attendance settings" },
        { status: 500 }
      )
    }

    return NextResponse.json({ settings })
  } catch (error: any) {
    console.error("Error in attendance settings API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Get user from token
    const userToken = await getUserFromToken(request)
    if (!userToken) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin()

    // Get user details to check admin role
    const { data: user, error: userError } = await supabaseAdmin
      .from("users")
      .select(`
        *,
        role_info:role_id(id, role_name, description, permissions)
      `)
      .eq("id", userToken.userId)
      .single()

    if (userError || !user) {
      console.error("Error fetching user:", userError)
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Check if user has admin role (only "admin" role has full access)
    const userRole = user.role_name || user.role_info?.role_name || user.role
    if (userRole !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      qr_enabled,
      manual_checkin_enabled,
      auto_certificate_generation,
      attendance_notifications,
      real_time_updates,
      bulk_checkin_enabled
    } = body

    // Update attendance settings
    const { data: settings, error } = await supabaseAdmin
      .from('attendance_settings')
      .update({
        qr_enabled: Boolean(qr_enabled),
        manual_checkin_enabled: Boolean(manual_checkin_enabled),
        auto_certificate_generation: Boolean(auto_certificate_generation),
        attendance_notifications: Boolean(attendance_notifications),
        real_time_updates: Boolean(real_time_updates),
        bulk_checkin_enabled: Boolean(bulk_checkin_enabled),
        updated_at: new Date().toISOString(),
      })
      .eq('id', (await supabaseAdmin.from('attendance_settings').select('id').single()).data?.id)
      .select()
      .single()

    if (error) {
      console.error("Error updating attendance settings:", error)
      return NextResponse.json(
        { error: "Failed to update attendance settings" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      settings
    })
  } catch (error: any) {
    console.error("Error in attendance settings update API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
