import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";

/**
 * POST /api/certificates/generate-sample
 * Generates sample certificates for testing purposes
 * This is a temporary endpoint for development/testing
 */
export async function POST(request: Request) {
  try {
    const supabaseAdmin = getSupabaseAdmin();

    // Get all registrations that don't have certificates yet
    const { data: registrations, error: regError } = await supabaseAdmin
      .from("registrations")
      .select(`
        id,
        event_id,
        user_id,
        attendee_name,
        attendee_email,
        checked_in,
        event:event_id (
          id,
          title,
          end_date
        )
      `)
      .eq("checked_in", true);

    if (regError) {
      console.error("Error fetching registrations:", regError);
      return NextResponse.json({ error: "Failed to fetch registrations" }, { status: 500 });
    }

    if (!registrations || registrations.length === 0) {
      return NextResponse.json({
        message: "No checked-in registrations found to generate certificates for",
        certificates: []
      });
    }

    // Check which registrations already have certificates
    const registrationIds = registrations.map(reg => reg.id);
    const { data: existingCerts } = await supabaseAdmin
      .from("certificates")
      .select("registration_id")
      .in("registration_id", registrationIds);

    const existingCertRegIds = existingCerts?.map(cert => cert.registration_id) || [];
    const registrationsNeedingCerts = registrations.filter(
      reg => !existingCertRegIds.includes(reg.id)
    );

    if (registrationsNeedingCerts.length === 0) {
      return NextResponse.json({
        message: "All checked-in registrations already have certificates",
        certificates: []
      });
    }

    // Generate certificates for registrations that need them
    const certificatesToInsert = registrationsNeedingCerts.map(registration => {
      const verificationCode = Math.random().toString(36).substring(2, 10).toUpperCase();

      return {
        id: crypto.randomUUID(),
        event_id: registration.event_id,
        registration_id: registration.id,
        user_id: registration.user_id,
        participant_id: registration.id, // Using registration ID as participant ID
        participant_name: registration.attendee_name,
        template_id: "default-template",
        issued_at: new Date().toISOString(),
        certificate_url: `https://mticket.my/certificates/verify/${verificationCode}`,
        verification_code: verificationCode,
        is_revoked: false
      };
    });

    // Insert the certificates
    const { data: insertedCertificates, error: insertError } = await supabaseAdmin
      .from("certificates")
      .insert(certificatesToInsert)
      .select();

    if (insertError) {
      console.error("Error inserting certificates:", insertError);
      return NextResponse.json({ error: "Failed to generate certificates" }, { status: 500 });
    }

    return NextResponse.json({
      message: `Successfully generated ${insertedCertificates?.length || 0} certificates`,
      certificates: insertedCertificates || []
    });

  } catch (error) {
    console.error("Error in generate-sample certificates API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
