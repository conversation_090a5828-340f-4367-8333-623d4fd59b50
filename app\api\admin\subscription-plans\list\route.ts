import { NextRequest, NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { getUserFromToken } from "@/lib/auth/token"

export async function GET(request: NextRequest) {
  try {
    // Get user from token
    const userToken = await getUserFromToken(request)
    if (!userToken) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin()

    // Get user details to check admin role
    const { data: user, error: userError } = await supabaseAdmin
      .from("users")
      .select(`
        *,
        role_info:role_id(id, role_name, description, permissions)
      `)
      .eq("id", userToken.userId)
      .single()

    if (userError || !user) {
      console.error("Error fetching user:", userError)
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Check if user has admin role
    const isAdmin = user.role === "admin" || user.role_info?.role_name === "admin"
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 403 }
      )
    }

    // Fetch all subscription plans (including inactive ones for admin)
    const { data: plans, error } = await supabaseAdmin
      .from('subscription_plans')
      .select('*')
      .order('price', { ascending: true })

    if (error) {
      console.error("Error fetching subscription plans:", error)
      return NextResponse.json(
        { error: "Failed to fetch subscription plans" },
        { status: 500 }
      )
    }

    // Transform the data to match the expected format
    const transformedPlans = plans.map(plan => ({
      id: plan.id,
      name: plan.name,
      price: parseFloat(plan.price.toString()),
      description: plan.description,
      features: Array.isArray(plan.features) ? plan.features : [],
      max_events: plan.max_events,
      max_attendees_per_event: plan.max_attendees_per_event,
      is_popular: plan.is_popular,
      is_active: plan.is_active,
      certificates_enabled: plan.certificates_enabled,
      attendance_enabled: plan.attendance_enabled,
      webhooks_enabled: plan.webhooks_enabled,
      analytics_enabled: plan.analytics_enabled,
      reports_enabled: plan.reports_enabled,
      created_at: plan.created_at,
      updated_at: plan.updated_at,
    }))

    return NextResponse.json({ plans: transformedPlans })
  } catch (error: any) {
    console.error("Error in subscription plans list API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
