<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Payment Callback Data Storage</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .test-section h2 {
            color: #667eea;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #667eea;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #5a67d8;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Payment Callback Data Storage</h1>
        <p>This page tests the payment return callback functionality to ensure ToyyibPay callback data is properly stored in both transactions and registrations tables.</p>

        <div class="test-section">
            <h2>Simulate ToyyibPay Return Callback</h2>
            <p>Test the payment return URL with sample ToyyibPay callback parameters:</p>

            <div class="form-group">
                <label for="status_id">Status ID (1=success, 2=pending, 3=failed):</label>
                <input type="text" id="status_id" value="1">
            </div>

            <div class="form-group">
                <label for="billcode">Bill Code:</label>
                <input type="text" id="billcode" value="b2t7zbtp">
            </div>

            <div class="form-group">
                <label for="order_id">Order ID:</label>
                <input type="text" id="order_id" value="payment_1748317741715_xt49zr">
            </div>

            <div class="form-group">
                <label for="msg">Message:</label>
                <input type="text" id="msg" value="ok">
            </div>

            <div class="form-group">
                <label for="transaction_id">Transaction ID:</label>
                <input type="text" id="transaction_id" value="TP2505274276991261">
            </div>

            <button onclick="testPaymentReturn()" id="testBtn">Test Payment Return</button>
            <button onclick="openPaymentReturnPage()" id="openBtn">Open Payment Return Page</button>

            <div id="result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>Test Public Receipt Access</h2>
            <p>Test the public receipt functionality (requires a valid receipt token):</p>

            <div class="form-group">
                <label for="receipt_token">Receipt Token:</label>
                <input type="text" id="receipt_token" placeholder="Enter receipt token from payment return response">
            </div>

            <button onclick="testPublicReceipt()" id="receiptBtn">Test Public Receipt</button>

            <div id="receiptResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        async function testPaymentReturn() {
            const testBtn = document.getElementById('testBtn');
            const resultDiv = document.getElementById('result');

            testBtn.disabled = true;
            testBtn.textContent = 'Testing...';

            try {
                const paymentData = {
                    status_id: document.getElementById('status_id').value,
                    billcode: document.getElementById('billcode').value,
                    order_id: document.getElementById('order_id').value,
                    msg: document.getElementById('msg').value,
                    transaction_id: document.getElementById('transaction_id').value,
                    timestamp: new Date().toISOString(),
                    url_params: {
                        status_id: document.getElementById('status_id').value,
                        billcode: document.getElementById('billcode').value,
                        order_id: document.getElementById('order_id').value,
                        msg: document.getElementById('msg').value,
                        transaction_id: document.getElementById('transaction_id').value
                    }
                };

                const response = await fetch('/api/payments/save-return-data', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(paymentData)
                });

                const result = await response.json();

                resultDiv.style.display = 'block';
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `SUCCESS:\n${JSON.stringify(result, null, 2)}`;

                    // Store receipt token for testing
                    if (result.receipt_token) {
                        document.getElementById('receipt_token').value = result.receipt_token;
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `ERROR:\n${JSON.stringify(result, null, 2)}`;
                }

            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.textContent = `ERROR:\n${error.message}`;
            }

            testBtn.disabled = false;
            testBtn.textContent = 'Test Payment Return';
        }

        function openPaymentReturnPage() {
            const status_id = document.getElementById('status_id').value;
            const billcode = document.getElementById('billcode').value;
            const order_id = document.getElementById('order_id').value;
            const msg = document.getElementById('msg').value;
            const transaction_id = document.getElementById('transaction_id').value;

            const url = `/payment/return?status_id=${status_id}&billcode=${billcode}&order_id=${order_id}&msg=${msg}&transaction_id=${transaction_id}`;
            window.open(url, '_blank');
        }

        function testPublicReceipt() {
            const receiptBtn = document.getElementById('receiptBtn');
            const resultDiv = document.getElementById('receiptResult');
            const token = document.getElementById('receipt_token').value;

            if (!token) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.textContent = 'ERROR: Please enter a receipt token';
                return;
            }

            receiptBtn.disabled = true;
            receiptBtn.textContent = 'Testing...';

            try {
                const url = `/api/receipts/public?token=${token}`;
                window.open(url, '_blank');

                resultDiv.style.display = 'block';
                resultDiv.className = 'result success';
                resultDiv.textContent = 'SUCCESS: Public receipt opened in new tab';

            } catch (error) {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result error';
                resultDiv.textContent = `ERROR: ${error.message}`;
            }

            receiptBtn.disabled = false;
            receiptBtn.textContent = 'Test Public Receipt';
        }
    </script>
</body>
</html>
