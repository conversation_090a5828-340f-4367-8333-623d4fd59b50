/**
 * Supabase Configuration Utility
 * 
 * This utility provides access to Supabase project configuration
 * for use with Supabase MCP and other integrations.
 */

import fs from 'fs';
import path from 'path';

interface SupabaseConfig {
  project_id: string;
  project_name: string;
  region: string;
}

/**
 * Get the Supabase project configuration
 * @returns The Supabase project configuration
 */
export function getSupabaseConfig(): SupabaseConfig {
  try {
    // Read the configuration file
    const configPath = path.join(process.cwd(), 'config', 'supabase.json');
    const configData = fs.readFileSync(configPath, 'utf8');
    return JSON.parse(configData) as SupabaseConfig;
  } catch (error) {
    console.error('Error loading Supabase configuration:', error);
    // Return default configuration as fallback
    return {
      project_id: "bivslxeghhebmkelieue",
      project_name: "supabase-mticketz",
      region: "ap-southeast-1"
    };
  }
}

/**
 * Get the Supabase project ID
 * @returns The Supabase project ID
 */
export function getSupabaseProjectId(): string {
  return getSupabaseConfig().project_id;
}