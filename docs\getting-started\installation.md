# Installation Guide

This guide covers setting up mTicket.my for development and production environments.

## Prerequisites

### System Requirements
- **Node.js**: Version 18.0 or higher
- **Package Manager**: pnpm (recommended), npm, or yarn
- **Database**: PostgreSQL (via Supabase)
- **Git**: For version control

### Development Tools
- **Code Editor**: VS Code (recommended)
- **Browser**: Chrome, Firefox, or Safari
- **Terminal**: Command line access

## Development Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd mticketz
```

### 2. Install Dependencies

```bash
# Using pnpm (recommended)
pnpm install

# Or using npm
npm install

# Or using yarn
yarn install
```

### 3. Environment Configuration

1. **Copy Environment File**
   ```bash
   cp .env.example .env.local
   ```

2. **Configure Environment Variables**
   ```env
   # Database
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

   # Authentication
   JWT_SECRET=your_jwt_secret
   NEXTAUTH_SECRET=your_nextauth_secret
   NEXTAUTH_URL=http://localhost:3000

   # Payment Gateways
   TOYYIBPAY_USER_SECRET_KEY=your_toyyibpay_key
   BILLPLZ_API_KEY=your_billplz_key
   CHIP_SECRET_KEY=your_chip_key
   STRIPE_SECRET_KEY=your_stripe_key

   # Email (Optional)
   SMTP_HOST=your_smtp_host
   SMTP_PORT=587
   SMTP_USER=your_email
   SMTP_PASS=your_password
   ```

### 4. Database Setup

1. **Create Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create new project
   - Copy URL and keys to `.env.local`

2. **Run Migrations**
   ```bash
   # If using Supabase CLI
   supabase db reset

   # Or manually run SQL files in order
   ```

3. **Seed Database (Optional)**
   ```bash
   pnpm run db:seed
   ```

### 5. Start Development Server

```bash
pnpm dev
```

Visit `http://localhost:3000` to see your application.

## Production Deployment

### Vercel Deployment (Recommended)

1. **Connect Repository**
   - Import project to Vercel
   - Connect your Git repository

2. **Configure Environment Variables**
   - Add all production environment variables
   - Ensure database URLs point to production

3. **Deploy**
   - Vercel will automatically build and deploy
   - Set up custom domain if needed

### Manual Deployment

1. **Build Application**
   ```bash
   pnpm build
   ```

2. **Start Production Server**
   ```bash
   pnpm start
   ```

### Docker Deployment

1. **Build Docker Image**
   ```bash
   docker build -t mticket .
   ```

2. **Run Container**
   ```bash
   docker run -p 3000:3000 --env-file .env.local mticket
   ```

## Configuration

### Payment Gateways

Each payment gateway requires specific configuration:

1. **ToyyibPay**
   - Get User Secret Key from ToyyibPay dashboard
   - Configure webhook URL: `your-domain/api/webhooks/toyyibpay`

2. **Billplz**
   - Get API key from Billplz dashboard
   - Set up webhook endpoint

3. **Stripe**
   - Get publishable and secret keys
   - Configure webhook endpoints

### File Storage

Configure Supabase Storage:
1. Create buckets: `certificates`, `events`, `profiles`
2. Set up RLS policies for secure access
3. Configure CORS for file uploads

### Email Configuration

For email notifications:
1. Set up SMTP credentials
2. Configure email templates
3. Test email delivery

## Verification

### Health Checks

1. **Application Health**
   ```bash
   curl http://localhost:3000/api/health
   ```

2. **Database Connection**
   ```bash
   curl http://localhost:3000/api/check-env
   ```

3. **Payment Gateway Test**
   - Test each configured gateway
   - Verify webhook endpoints

### Testing

1. **Run Tests**
   ```bash
   pnpm test
   ```

2. **Build Test**
   ```bash
   pnpm build
   ```

3. **Type Check**
   ```bash
   pnpm type-check
   ```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check Supabase URL and keys
   - Verify network connectivity
   - Check RLS policies

2. **Payment Gateway Errors**
   - Verify API credentials
   - Check webhook URLs
   - Test in sandbox mode first

3. **Build Failures**
   - Clear `.next` directory
   - Delete `node_modules` and reinstall
   - Check TypeScript errors

### Getting Help

- [Architecture Documentation](../architecture/)
- [API Reference](../api/)
- [Developer Guide](../guides/developer-guide.md)
- [Troubleshooting Guide](../guides/developer-guide.md#troubleshooting)

## Next Steps

After successful installation:
1. [Create your first event](./quick-start.md)
2. [Configure payment processing](../architecture/payment-system.md)
3. [Set up certificates](../features/certificate-system.md)
4. [Review security settings](../security/)

---

**Need help?** Check our [Developer Guide](../guides/developer-guide.md) or contact support.
