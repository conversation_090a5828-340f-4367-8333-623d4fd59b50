import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { getAuthSession } from "@/lib/auth";
import { logActivity } from "@/lib/activity-logger";

export async function POST(request: Request) {
  try {
    // Get the current session to verify admin access
    const session = await getAuthSession();

    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === "development";

    // In production, check if the user is authenticated and has admin role
    // In development, allow access without authentication for easier testing
    if (!isDevelopment && (!session?.user?.id || session.user.role !== "admin")) {
      console.log("Unauthorized access attempt to delete payment gateway. User role:", session?.user?.role);
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 403 }
      );
    }

    // Log access in development mode
    if (isDevelopment) {
      console.log("Development mode: Allowing access to delete payment gateway without authentication");
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin();
    const body = await request.json();
    
    // Validate required fields
    if (!body.id) {
      return NextResponse.json(
        { error: "Payment gateway ID is required" },
        { status: 400 }
      );
    }
    
    // Get the gateway before deleting for logging
    const { data: gatewayData, error: getError } = await supabaseAdmin
      .from("payment_gateway_settings")
      .select("gateway_name")
      .eq("id", body.id)
      .single();
    
    if (getError) {
      console.error("Error getting payment gateway:", getError);
      return NextResponse.json(
        { error: "Failed to delete payment gateway" },
        { status: 500 }
      );
    }
    
    // Delete payment gateway
    const { error } = await supabaseAdmin
      .from("payment_gateway_settings")
      .delete()
      .eq("id", body.id);
    
    if (error) {
      console.error("Error deleting payment gateway:", error);
      return NextResponse.json(
        { error: "Failed to delete payment gateway" },
        { status: 500 }
      );
    }
    
    // Log activity
    if (session?.user?.id) {
      await logActivity({
        user_id: session.user.id,
        action: "delete",
        entity_type: "payment_gateway",
        entity_id: body.id,
        details: { gateway_name: gatewayData.gateway_name },
        category: "settings",
      });
    }
    
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error("Error in payment gateways delete API:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
