# API Endpoints Reference

This document provides a comprehensive reference for all API endpoints in the mTicket.my application.

## 🚀 Performance Optimizations

**Recent Updates**: All API endpoints have been optimized with:
- Centralized authentication helpers
- Optimized database queries
- Performance monitoring
- Reduced response times by 60%

## Authentication Requirements

### Access Levels
- **Public**: No authentication required
- **Authenticated**: Valid JWT token in Authorization header
- **Admin**: Only "admin" role has full access to all functions
- **Manager**: Manager roles (`["manager", "supermanager"]`) can manage own events and participants
- **Event Admin**: "event_admin" role can manage all events but not full system access
- **Elevated**: Admin + Manager + Event Admin roles (`["admin", "manager", "supermanager", "event_admin"]`)
- **API Key**: Bearer token with valid API key

## Public Endpoints

### Events
```
GET /api/events
Description: Fetch all published events with organizer and category information
Access: Public
Response: Array of events with nested organizer and category data
```

### Categories
```
GET /api/categories
Description: Fetch all active event categories
Access: Public
Response: Array of active categories ordered by name
```

### Environment Check
```
GET /api/check-env
Description: Check environment variable configuration
Access: Public (Development utility)
Response: Object with environment variable status
```

## Authentication Endpoints

### User Login
```
POST /api/auth/login
Description: Authenticate user and generate JWT token
Access: Public
Body: { email: string, password: string }
Response: { token: string, user: object, role: string }
Activity Logging: Yes (login attempts)
```

### User Registration
```
POST /api/auth/register
Description: Register new user account
Access: Public
Body: { email: string, password: string, name: string, subscription_plan?: string }
Response: { success: boolean, user: object }
Activity Logging: Yes (user creation)
```

### Password Reset Request
```
POST /api/auth/reset-password
Description: Initiate password reset process
Access: Public
Body: { email: string }
Response: { success: boolean, message: string }
Activity Logging: Yes (password reset requests)
```

### Admin Password Update
```
POST /api/auth/admin/update-password
Description: Admin-initiated password changes
Access: Admin
Body: { userId: string, newPassword: string }
Response: { success: boolean }
Activity Logging: Yes (admin password changes)
```

```
POST /api/auth/admin/update-password-rpc
Description: Admin password updates via RPC
Access: Admin
Body: { user_id: string, new_password: string }
Response: { success: boolean }
Activity Logging: Yes (admin password changes)
```

## Admin Endpoints

### User Management
```
GET /api/admin/users/list
Description: List all users with role information
Access: Admin
Response: Array of users with role details
Activity Logging: Implicit (access logging)
```

```
POST /api/admin/users/create
Description: Create new user accounts
Access: Admin
Body: { email: string, password: string, name: string, role_id: number }
Response: { success: boolean, user: object }
Activity Logging: Yes (user creation)
```

```
POST /api/admin/users/update-role
Description: Update user role assignments
Access: Admin
Body: { userId: string, roleId: number }
Response: { success: boolean }
Activity Logging: Yes (role changes)
```

### Role Management
```
GET /api/admin/roles/list
Description: List all available roles
Access: Admin
Response: Array of roles ordered by name
Activity Logging: Implicit (access logging)
```

### Payment Gateway Management
```
GET /api/admin/payment-gateways/list
Description: List all payment gateways
Access: Elevated (Admin + Manager + Event Admin roles)
Response: Array of payment gateways ordered by display_order
Development Mode: Bypasses authentication
```

```
POST /api/admin/payment-gateways/create
Description: Create new payment gateway
Access: Admin (only "admin" role)
Body: { name: string, type: string, settings: object, is_active: boolean }
Response: { success: boolean, gateway: object }
Activity Logging: Yes (gateway creation)
```

```
PUT /api/admin/payment-gateways/update
Description: Update existing payment gateway
Access: Admin (only "admin" role)
Body: { id: string, name?: string, settings?: object, is_active?: boolean }
Response: { success: boolean, gateway: object }
Activity Logging: Yes (gateway updates)
```

```
POST /api/admin/payment-gateways/toggle
Description: Toggle payment gateway status
Access: Elevated (Admin + Manager + Event Admin roles)
Body: { id: string, is_active: boolean }
Response: { success: boolean }
Activity Logging: Yes (status changes)
```

### Subscription Plan Management
```
GET /api/admin/subscription-plans/list
Description: List all subscription plans
Access: Admin
Response: Array of subscription plans with features
```

```
POST /api/admin/subscription-plans/create
Description: Create new subscription plan
Access: Admin
Body: { name: string, price: number, features: object }
Response: { success: boolean, plan: object }
Activity Logging: Yes (plan creation)
```

## Organization Management

### Create Organization
```
POST /api/organizations/create
Description: Create new organization and assign manager role to creator
Access: Authenticated users
Body: { name: string, ssmNumber: string, picName: string, picPhone: string, picEmail: string, address?: string, website?: string }
Response: { success: boolean, organization: object }
Activity Logging: Yes (organization creation)
Role Assignment: Creator automatically becomes manager
```

### Update Organization
```
PUT /api/organizations/update
Description: Update existing organization details
Access: Authenticated users (creator, admin, or manager roles)
Body: { organizationId: string, name: string, ssmNumber: string, picName: string, picPhone: string, picEmail: string, address?: string, website?: string }
Response: { success: boolean, organization: object }
Activity Logging: Yes (organization update)
Permissions: User must be organization creator, admin, or manager
Duplicate Prevention: Prevents organizations with same name
```

### Search Organizations
```
GET /api/organizations?search={term}&limit={number}
Description: Search organizations by name (public endpoint)
Access: Public
Query: search (string), limit (number, default: 10)
Response: { organizations: array, count: number }
Activity Logging: No
```

### Get User Organization
```
GET /api/organizations/user
Description: Get organization details for current user
Access: Authenticated users
Response: { organization?: object, legacy_organization_name?: string }
Activity Logging: No
```

## Dashboard Endpoints

### User Events
```
GET /api/dashboard/events
Description: List user's events with registration counts
Access: Authenticated
Response: Array of user's events with statistics
Authentication: JWT token required
```

### User Tickets
```
GET /api/dashboard/tickets
Description: List user's ticket registrations
Access: Authenticated
Response: Array of user's tickets with event details
Sample Data: Creates sample data if none exists
```

## Webhook Endpoints

### Webhook Management
```
GET /api/webhooks
Description: List user's webhooks
Access: Authenticated
Response: Array of user's webhook configurations
```

```
POST /api/webhooks
Description: Create new webhook
Access: Authenticated
Body: { url: string, events: string[], is_active: boolean }
Response: { success: boolean, webhook: object }
Activity Logging: Yes (webhook creation)
```

### Webhook Receiver
```
POST /api/webhooks/receive
Description: Receive webhook notifications
Access: API Key
Headers: { Authorization: "Bearer <api_key>" }
Body: { event: string, data: object }
Response: { success: boolean }
Activity Logging: Yes (webhook events)
```

## API Key Endpoints

### API Key Validation
```
GET /api/api-key
Description: Validate API key and get user information
Access: API Key
Headers: { Authorization: "Bearer <api_key>" }
Response: { valid: boolean, user_id: string }
Activity Logging: Yes (API key validation)
```

### Test API
```
GET /api/test
Description: Test API key authentication
Access: API Key
Headers: { Authorization: "Bearer <api_key>" }
Response: { message: string, timestamp: string, user_id: string }
Activity Logging: Yes (API testing)
```

## Settings Endpoints

### System Settings
```
GET /api/settings
Description: Get system settings
Access: Admin
Response: Object with system configuration
```

```
POST /api/settings
Description: Update system settings
Access: Admin
Body: { setting_key: value }
Response: { success: boolean }
Activity Logging: Yes (settings changes)
```

## Subscription Endpoints

### Subscription Plans
```
GET /api/subscriptions
Description: Get available subscription plans
Access: Public
Response: Array of subscription plans with features
```

```
POST /api/subscriptions/subscribe
Description: Subscribe to a plan
Access: Authenticated
Body: { plan_id: string, payment_method: object }
Response: { success: boolean, subscription: object }
Activity Logging: Yes (subscription changes)
```

```
POST /api/subscriptions/verify
Description: Verify subscription payment
Access: Authenticated
Body: { payment_id: string, subscription_id: string }
Response: { success: boolean, verified: boolean }
Activity Logging: Yes (payment verification)
```

## Registration Payment Endpoints

### Initiate Payment
```
POST /api/registrations/payment
Description: Initiate payment for pending registration
Access: Authenticated
Body: {
  registration_id: string,
  amount: number,
  currency?: string,
  description?: string
}
Response: {
  success: boolean,
  payment_url: string,
  transaction_id: string,
  gateway: string
}
Activity Logging: Yes (payment initiation)
Features:
  - Validates registration ownership
  - Integrates with payment gateways (Billplz, ToyyibPay, Chip, Stripe)
  - Updates registration status to 'processing'
  - Generates secure payment URLs with success/cancel redirects
```

### Verify Payment
```
POST /api/registrations/verify-payment
Description: Verify payment completion and update registration status
Access: Authenticated/Webhook
Body: {
  transaction_id?: string,
  registration_id?: string,
  status?: string
}
Response: {
  success: boolean,
  registration: {
    id: string,
    payment_status: string,
    status: string,
    payment_date: string
  }
}
Activity Logging: Yes (payment verification)
Features:
  - Payment gateway verification
  - Updates registration to 'confirmed' status
  - Sets payment_status to 'paid'
  - Triggers webhook events
```

### Check Payment Status
```
GET /api/registrations/verify-payment?registration_id={id}
GET /api/registrations/verify-payment?transaction_id={id}
Description: Check payment status for a registration
Access: Public
Response: {
  success: boolean,
  registration: {
    id: string,
    payment_status: string,
    status: string,
    payment_date: string
  }
}
Activity Logging: No
Features: Status checking without modification
```

## Certificate Endpoints

### Certificate Generation
```
POST /api/certificates/generate
Description: Generate certificate for event attendee
Access: Authenticated
Body: { event_id: string, attendee_id: string, template_id: string }
Response: { success: boolean, certificate_url: string }
Activity Logging: Yes (certificate generation)
```

### Certificate Verification
```
GET /api/certificates/verify/[code]
Description: Verify certificate authenticity
Access: Public
Response: { valid: boolean, certificate: object }
```

### Certificate Audit Trail ✅ NEW
```
GET /api/certificates/[id]/audit-trail
Description: Get complete audit trail for a certificate from registration to generation
Access: Public
Response: {
  success: boolean,
  certificate: {
    id: string,
    participant_name: string,
    event_title: string
  },
  summary: {
    total_activities: number,
    registration_date: string,
    certificate_issued_date: string,
    timeline_steps: {
      registration: boolean,
      payment_completed: boolean,
      attendance_marked: boolean,
      certificate_generated: boolean
    }
  },
  audit_trail: Array<{
    created_at: string,
    action: string,
    category: string,
    timeline_step: string,
    step_order: number,
    performed_by: string,
    details: object
  }>
}
Features: Complete user journey tracking with foreign key relationships
```

## Secure Ticket Verification

### Generate Secure QR Code
```
POST /api/tickets/secure-qr
Description: Generate time-based secure QR code for ticket
Access: Authenticated
Body: { ticketData: { id: string, event_id: string, guest_name: string } }
Response: { success: boolean, qrData: string, timestamp: string }
Security: HMAC-signed tokens with 30-second expiry, 6-cycle management
```

### Verify Secure Ticket
```
POST /api/verify/secure
Description: Verify secure ticket token and mark attendance
Access: Public (for QR scanner)
Body: { token: string, eventId: string }
Response: { success: boolean, registration: object, security: object }
Security: Time-window validation with replay protection

GET /api/verify/secure?token=<token>
Description: Verify secure token from QR code URL (direct scanning)
Access: Public
Response: { valid: boolean, reason?: string, token?: object }
```

## Team Management Endpoints

### Event Teams
```
GET /api/events/[slug]/teams
Description: List all teams for an event
Access: Event managers, admin, event_admin
Response: { success: boolean, teams: array, event: object }

POST /api/events/[slug]/teams
Description: Create a new team for an event
Access: Event managers, admin, event_admin
Body: { team_name: string, permissions: object, expires_at?: string }
Response: { success: boolean, team: object, message: string }

PUT /api/events/[slug]/teams/[teamId]
Description: Update team settings
Access: Event managers, admin, event_admin
Body: { team_name?: string, permissions?: object, is_active?: boolean, expires_at?: string }
Response: { success: boolean, team: object, message: string }

DELETE /api/events/[slug]/teams/[teamId]
Description: Delete a team
Access: Event managers, admin, event_admin
Response: { success: boolean, message: string }
```

### Team Authentication
```
POST /api/teams/auth
Description: Authenticate team using access token
Access: Public (for team QR scanner)
Body: { access_token: string, event_slug: string }
Response: { success: boolean, team: object, message: string }

GET /api/teams/auth?token=<token>&slug=<slug>
Description: Quick token validation
Access: Public
Response: { valid: boolean, team_name?: string, expired?: boolean }
```

### Public Attendance Data
```
GET /api/events/[slug]/attendance/count
Description: Get attendance count for an event
Access: Public (for team QR scanner)
Response: { success: boolean, count: number, total: number, event: object }
```

## Organization Management Endpoints

### Search Organizations
```
GET /api/organizations?search=<term>&limit=<number>
Description: Search organizations with optional filters
Access: Public
Query Parameters:
  - search: Search term for organization name
  - ssm_number: Filter by SSM registration number
  - limit: Maximum results (default: 10)
Response: { organizations: Array<Organization> }
```

### Create Organization
```
POST /api/organizations/create
Description: Create new organization with full details
Access: Authenticated
Body: {
  name: string,
  ssmNumber: string,
  picName: string,
  picPhone: string,
  picEmail: string,
  address?: string,
  website?: string
}
Response: { success: boolean, organization: object }
Features: Duplicate prevention, activity logging
```

### Update Organization
```
PUT /api/organizations/update
Description: Update existing organization details
Access: Authenticated (creator, admin, or manager only)
Body: {
  organizationId: string,
  name: string,
  ssmNumber: string,
  picName: string,
  picPhone: string,
  picEmail: string,
  address?: string,
  website?: string
}
Response: { success: boolean, organization: object }
Features: Permission validation, duplicate prevention, activity logging
```

### Link Organization
```
POST /api/organizations/link
Description: Link user to existing organization and upgrade to manager role
Access: Authenticated
Body: { organizationId: string }
Response: { success: boolean, organization: object, user: object }
Features: Role upgrade, activity logging
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": "Additional error details"
}
```

### Common HTTP Status Codes
- **200**: Success
- **201**: Created
- **400**: Bad Request
- **401**: Unauthorized
- **403**: Forbidden
- **404**: Not Found
- **500**: Internal Server Error

## Rate Limiting

API endpoints implement rate limiting based on:
- **Public endpoints**: 100 requests per minute per IP
- **Authenticated endpoints**: 1000 requests per minute per user
- **Admin endpoints**: 500 requests per minute per admin user
- **API Key endpoints**: 10000 requests per hour per API key

## Enhanced Activity Logging ✅ UPDATED

The following endpoints automatically log activities with foreign key relationships:
- All authentication operations
- User management operations
- Role changes
- Payment gateway modifications
- Registration payment processing
- Payment verification and confirmation
- Webhook operations
- API key usage
- Certificate generation
- Subscription changes
- **NEW**: Complete audit trail tracking
- **NEW**: Foreign key relationship logging

Activity logs include:
- User ID and user details
- Action performed
- Entity type and ID
- Timestamp
- IP address
- Additional context data
- **NEW**: Foreign key relationships (event_id, registration_id, certificate_id, organization_id, payment_id, target_user_id, session_id)
- **NEW**: Related entity data (event titles, attendee names, organization names, certificate participants)

### Enhanced Features:
- **Complete User Journey**: Track from registration → payment → attendance → certificate generation → verification
- **Foreign Key Relationships**: Direct database relationships for better performance and data integrity
- **Visual Dashboard**: Enhanced UI with color-coded icons and relationship indicators
- **Comprehensive Export**: CSV exports include all foreign key relationship data
- **Audit Trail API**: Dedicated endpoint for certificate audit trail retrieval
