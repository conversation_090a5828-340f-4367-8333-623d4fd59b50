"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { compressProfileImage, isValidImageFile, formatFileSize, createImagePreview } from "@/lib/image-compression"

interface ProfileData {
  fullName: string
  email: string
  phone: string
  bio: string
  profileImage: string
}

interface ProfileTabProps {
  profileData: ProfileData
  setProfileData: (data: ProfileData) => void
  isSubmitting: boolean
  setIsSubmitting: (submitting: boolean) => void
}

export function ProfileTab({
  profileData,
  setProfileData,
  isSubmitting,
  setIsSubmitting
}: ProfileTabProps) {
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(profileData.profileImage)
  const [isCompressing, setIsCompressing] = useState(false)
  const { user, updateProfile } = useAuth()
  const { toast } = useToast()

  // Sync image preview with user's profile image when user changes
  useEffect(() => {
    if (user?.profile_image_url && !selectedImage) {
      setImagePreview(user.profile_image_url);
    }
  }, [user?.profile_image_url, selectedImage]);

  // Get role badge color based on role name
  const getRoleBadgeColor = (role: string) => {
    switch (role?.toLowerCase()) {
      case "admin":
      case "super_admin":
        return "bg-red-100 text-red-800"
      case "supermanager":
      case "event_admin":
        return "bg-purple-100 text-purple-800"
      case "manager":
        return "bg-blue-100 text-blue-800"
      case "paid":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // Get formatted role name
  const getFormattedRoleName = (role: string) => {
    if (!role) return "Free User"
    return role.charAt(0).toUpperCase() + role.slice(1).replace(/_/g, " ")
  }

  // Helper function to get cookie value
  const getCookie = (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      let profileImageUrl = profileData.profileImage

      // Upload profile image if selected
      if (selectedImage) {
        const token = getCookie('auth_token');
        if (!token) {
          throw new Error("Not authenticated");
        }

        // Store the old profile image URL for cleanup
        const oldProfileImageUrl = user?.profile_image_url;

        // Compress the image before uploading
        toast({
          title: "Compressing image...",
          description: "Please wait while we optimize your image",
        });

        let imageToUpload = selectedImage;
        try {
          imageToUpload = await compressProfileImage(selectedImage);
          console.log(`Image compressed: ${formatFileSize(selectedImage.size)} → ${formatFileSize(imageToUpload.size)}`);
        } catch (compressionError) {
          console.warn("Image compression failed, uploading original:", compressionError);
          // Continue with original image if compression fails
        }

        const formData = new FormData();
        formData.append('file', imageToUpload);

        // Include old image URL for cleanup if it exists
        if (oldProfileImageUrl) {
          formData.append('oldImageUrl', oldProfileImageUrl);
        }

        const uploadResponse = await fetch('/api/upload/profile-image', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
          body: formData,
        });

        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json();
          throw new Error(errorData.error || 'Failed to upload image');
        }

        const uploadData = await uploadResponse.json();
        profileImageUrl = uploadData.url;

        // Show success message for image upload
        toast({
          title: "Image uploaded",
          description: "Profile picture uploaded successfully. Updating profile...",
        });
      }

      // Update user profile
      const updatedUser = await updateProfile({
        full_name: profileData.fullName,
        profile_image_url: profileImageUrl,
        phone: profileData.phone,
        bio: profileData.bio,
      })

      if (updatedUser) {
        // Update the profile data state to reflect the changes
        setProfileData({
          ...profileData,
          profileImage: updatedUser.profile_image_url || profileImageUrl,
          fullName: updatedUser.full_name || profileData.fullName,
          phone: updatedUser.phone || profileData.phone,
          bio: updatedUser.bio || profileData.bio,
        });

        // Clear the selected image
        setSelectedImage(null);
        setImagePreview(updatedUser.profile_image_url || profileImageUrl);

        // Log success for debugging
        console.log("Profile updated - sidebar should refresh with new image:", updatedUser.profile_image_url);

        toast({
          title: "Success",
          description: selectedImage ? "Profile updated successfully." : "Profile updated successfully",
        })
      }
    } catch (error: any) {
      console.error("Error updating profile:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to update profile",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Check file type first
    if (!isValidImageFile(file)) {
      toast({
        title: "Error",
        description: "Please upload a valid image file (JPEG, PNG, WebP, or GIF)",
        variant: "destructive",
      })
      return
    }

    // Check file size (max 2MB for original, will be compressed)
    if (file.size > 2 * 1024 * 1024) {
      toast({
        title: "Error",
        description: "Image size should be less than 2MB",
        variant: "destructive",
      })
      return
    }

    setIsCompressing(true)

    try {
      // Show file size info
      toast({
        title: "Image selected",
        description: `Original size: ${formatFileSize(file.size)}. Preparing for upload...`,
      })

      setSelectedImage(file)

      // Create preview
      const previewUrl = await createImagePreview(file)
      setImagePreview(previewUrl)

    } catch (error) {
      console.error("Error processing image:", error)
      toast({
        title: "Error",
        description: "Failed to process image",
        variant: "destructive",
      })
    } finally {
      setIsCompressing(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Personal Information</CardTitle>
        <CardDescription>Update your personal details and profile picture</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleProfileSubmit} className="space-y-6">
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex flex-col items-center space-y-4">
              <Avatar className="h-32 w-32" key={`${imagePreview}-${user?.profile_updated_at || 'no-timestamp'}`}>
                <AvatarImage src={imagePreview || "/placeholder.svg?height=128&width=128"} />
                <AvatarFallback className="text-2xl">
                  {profileData.fullName
                    .split(" ")
                    .map((n) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div className="flex flex-col items-center">
                <Label htmlFor="profile-image" className="cursor-pointer text-sm text-primary hover:underline">
                  {isCompressing ? "Processing..." : "Change profile picture"}
                </Label>
                <Input
                  id="profile-image"
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageChange}
                  disabled={isCompressing || isSubmitting}
                />
                <p className="text-xs text-muted-foreground mt-1 text-center w-full">
                  Max size: 2MB
                </p>
                {selectedImage && (
                  <p className="text-xs text-green-600 mt-1">
                    ✓ Image ready for upload ({formatFileSize(selectedImage.size)})
                  </p>
                )}
              </div>
            </div>
            <div className="flex-1 space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="full-name">Full Name</Label>
                <Input
                  id="full-name"
                  value={profileData.fullName}
                  onChange={(e) => setProfileData({ ...profileData, fullName: e.target.value })}
                  placeholder="Enter your full name"
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" value={profileData.email} readOnly disabled className="bg-muted" />
                <p className="text-xs text-muted-foreground">
                  Email cannot be changed. Contact support if you need to update your email.
                </p>
              </div>
              <div className="grid gap-2">
                <Label>Account Information</Label>
                <div className="rounded-lg border p-3 bg-muted/30 space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Role:</span>
                    <Badge
                      className={getRoleBadgeColor(user?.role_name || "")}
                      variant="outline"
                    >
                      {getFormattedRoleName(user?.role_name || "")}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Member since:</span>
                    <span className="text-sm text-muted-foreground">
                      {user?.created_at ? new Date(user.created_at).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      }) : 'N/A'}
                    </span>
                  </div>
                  {user?.subscription_status && user.subscription_status !== 'none' && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Subscription:</span>
                      <Badge
                        variant={user.subscription_status === 'active' ? 'default' : 'secondary'}
                      >
                        {user.subscription_status.charAt(0).toUpperCase() + user.subscription_status.slice(1)}
                      </Badge>
                    </div>
                  )}
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  value={profileData.phone}
                  onChange={(e) => setProfileData({ ...profileData, phone: e.target.value })}
                  placeholder="Enter your phone number"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  value={profileData.bio}
                  onChange={(e) => setProfileData({ ...profileData, bio: e.target.value })}
                  placeholder="Tell us a bit about yourself"
                  rows={4}
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end">
            <Button type="submit" disabled={isSubmitting || isCompressing}>
              {isSubmitting ? "Saving..." : isCompressing ? "Processing..." : "Save Changes"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
