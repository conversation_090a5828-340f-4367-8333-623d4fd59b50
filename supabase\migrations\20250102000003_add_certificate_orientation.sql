-- Add orientation field to certificate_templates table
ALTER TABLE certificate_templates 
ADD COLUMN IF NOT EXISTS orientation VARCHAR(10) DEFAULT 'landscape' CHECK (orientation IN ('landscape', 'portrait'));

-- Update existing templates to have default landscape orientation
UPDATE certificate_templates 
SET orientation = 'landscape' 
WHERE orientation IS NULL;

-- Add comment to the column
COMMENT ON COLUMN certificate_templates.orientation IS 'Certificate template orientation: landscape (800x600) or portrait (600x800)';
