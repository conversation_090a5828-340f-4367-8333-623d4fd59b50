import { PageLayout } from "@/components/page-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Shield, Lock, Eye, Server, Key, AlertTriangle } from "lucide-react"

export const metadata = {
  title: "Security | mTicket.my - Event Management Platform",
  description: "Learn about our security measures and data protection practices",
}

export default function SecurityPage() {
  return (
    <PageLayout>
      {/* Corporate Hero Section */}
      <section className="relative w-full py-20 md:py-32 lg:py-40 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}
          ></div>
        </div>

        <div className="container relative px-4 md:px-6">
          <div className="flex flex-col items-center space-y-8 text-center">
            {/* Main Heading */}
            <div className="space-y-4 max-w-4xl">
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl lg:text-7xl">
                Security &
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> Trust</span>
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-gray-300 md:text-2xl leading-relaxed">
                Your data security and privacy are our top priorities. Learn about the measures we take to protect your information.
              </p>
            </div>
          </div>
        </div>
      </section>

      <div className="container px-4 md:px-6 py-12">
        <div className="mx-auto max-w-7xl">
          {/* Security Features Grid */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-12">
            <Card>
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2">
                  <Lock className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Data Encryption</CardTitle>
                <CardDescription>
                  All data is encrypted in transit and at rest using industry-standard encryption
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• TLS 1.3 for data in transit</li>
                  <li>• AES-256 encryption at rest</li>
                  <li>• End-to-end encryption for sensitive data</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2">
                  <Shield className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Access Control</CardTitle>
                <CardDescription>
                  Strict access controls and authentication mechanisms protect your account
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• Multi-factor authentication</li>
                  <li>• Role-based access control</li>
                  <li>• Regular access reviews</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2">
                  <Server className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Infrastructure Security</CardTitle>
                <CardDescription>
                  Our infrastructure is hosted on secure, compliant cloud platforms
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• SOC 2 compliant hosting</li>
                  <li>• Regular security audits</li>
                  <li>• 24/7 monitoring and alerts</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2">
                  <Eye className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Privacy Protection</CardTitle>
                <CardDescription>
                  We follow strict privacy practices and comply with data protection regulations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• GDPR compliant</li>
                  <li>• Data minimization practices</li>
                  <li>• Regular privacy assessments</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2">
                  <Key className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>API Security</CardTitle>
                <CardDescription>
                  Our APIs are secured with modern authentication and rate limiting
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• OAuth 2.0 authentication</li>
                  <li>• Rate limiting and throttling</li>
                  <li>• API key management</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2">
                  <AlertTriangle className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Incident Response</CardTitle>
                <CardDescription>
                  We have comprehensive incident response procedures in place
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-1 text-sm text-muted-foreground">
                  <li>• 24/7 security monitoring</li>
                  <li>• Rapid incident response</li>
                  <li>• Transparent communication</li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Compliance Section */}
          <div className="bg-card p-8 rounded-lg border mb-12">
            <h2 className="text-2xl font-bold mb-4">Compliance & Certifications</h2>
            <p className="text-muted-foreground mb-6">
              We maintain compliance with industry standards and regulations to ensure the highest level of security and trust.
            </p>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="text-center">
                <h3 className="font-semibold mb-2">GDPR</h3>
                <p className="text-sm text-muted-foreground">
                  General Data Protection Regulation compliant
                </p>
              </div>
              <div className="text-center">
                <h3 className="font-semibold mb-2">SOC 2</h3>
                <p className="text-sm text-muted-foreground">
                  Service Organization Control 2 certified
                </p>
              </div>
              <div className="text-center">
                <h3 className="font-semibold mb-2">ISO 27001</h3>
                <p className="text-sm text-muted-foreground">
                  Information security management certified
                </p>
              </div>
            </div>
          </div>

          {/* Security Best Practices */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6">Security Best Practices for Users</h2>
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Account Security</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• Use strong, unique passwords</li>
                    <li>• Enable two-factor authentication</li>
                    <li>• Regularly review account activity</li>
                    <li>• Log out from shared devices</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Data Protection</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• Only share necessary information</li>
                    <li>• Regularly update your contact details</li>
                    <li>• Be cautious with public Wi-Fi</li>
                    <li>• Report suspicious activity immediately</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Contact Section */}
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">Security Questions?</h2>
            <p className="text-muted-foreground mb-6">
              If you have any security concerns or questions about our practices, please don't hesitate to contact us.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90"
              >
                Contact Security Team
              </a>
              <a
                href="/contact"
                className="inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground"
              >
                General Contact
              </a>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  )
}
