import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"

/**
 * GET /api/categories
 * Fetches all active event categories from the database
 * This is a public endpoint - no authentication required
 */
export async function GET(request: Request) {
  try {
    console.log("Categories API: Fetching active categories");

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin();

    // Fetch all active categories, ordered by name
    const { data, error } = await supabaseAdmin
      .from("event_categories")
      .select("*")
      .eq("is_active", true)
      .order("name", { ascending: true });

    if (error) {
      console.error("Error fetching categories:", error);
      return NextResponse.json(
        { error: "Failed to fetch categories" },
        { status: 500 }
      );
    }

    console.log(`Categories API: Successfully fetched ${data?.length || 0} categories`);

    return NextResponse.json({
      categories: data || [],
      count: data?.length || 0
    });
  } catch (error: any) {
    console.error("Error in categories API:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
