import { NextRequest, NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { generateReceiptToken } from "@/app/api/receipts/public/route";
import { generateTicketToken } from "@/app/api/tickets/public/route";
import { createUser } from "@/lib/auth";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { status_id, billcode, order_id, msg, transaction_id, timestamp, url_params } = body;

    console.log("Saving payment return data:", body);

    const supabaseAdmin = getSupabaseAdmin();

    // Find the transaction by billcode (which should match gateway_transaction_id)
    const { data: transaction, error: findError } = await supabaseAdmin
      .from("transactions")
      .select("*")
      .eq("gateway_transaction_id", billcode)
      .single();

    if (findError) {
      console.error("Error finding transaction:", findError);
      // If transaction not found, we'll still save the return data for debugging
    }

    // Prepare the return data to save in gateway_response
    const returnData = {
      status_id,
      billcode,
      order_id,
      msg,
      transaction_id,
      timestamp,
      url_params,
      return_type: "toyyibpay_return"
    };

    if (transaction) {
      // Update existing transaction with return data
      const updatedGatewayResponse = {
        ...transaction.gateway_response,
        return_data: returnData
      };

      // Update transaction status based on status_id
      let newStatus = transaction.status;
      switch (status_id) {
        case '1':
          newStatus = 'paid';
          break;
        case '2':
          newStatus = 'processing';
          break;
        case '3':
          newStatus = 'failed';
          break;
        default:
          newStatus = 'failed';
      }

      const { error: updateError } = await supabaseAdmin
        .from("transactions")
        .update({
          status: newStatus,
          gateway_response: updatedGatewayResponse,
          processed_at: status_id === '1' ? new Date().toISOString() : null,
          updated_at: new Date().toISOString()
        })
        .eq("id", transaction.id);

      if (updateError) {
        console.error("Error updating transaction:", updateError);
        return NextResponse.json(
          { error: "Failed to update transaction" },
          { status: 500 }
        );
      }

      console.log(`Transaction ${transaction.id} updated with status: ${newStatus}`);

      // Handle user creation and registration creation for successful payments
      let registrationIds: string[] = [];
      let userId: string | null = transaction.user_id;

      if (status_id === '1') {
        // Check if registrations already exist
        if (!transaction.registration_id && transaction.metadata) {
          console.log("Payment successful, creating registrations and user if needed...");

          const registrationData = transaction.metadata;
          const { event_id, participants, main_contact, is_public_registration } = registrationData;

          // For public registrations, check if user exists or create new user
          if (is_public_registration && main_contact) {
            console.log("Processing public registration, checking if user exists...");

            // Check if user with main contact email already exists
            const { data: existingUser, error: userCheckError } = await supabaseAdmin
              .from("users")
              .select("id")
              .eq("email", main_contact.email)
              .single();

            if (existingUser) {
              console.log("User already exists:", existingUser.id);
              userId = existingUser.id;
            } else {
              console.log("Creating new user account for main contact...");
              try {
                // Generate a temporary password for the new user
                const tempPassword = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

                // Create new user account
                const newUser = await createUser(main_contact.email, tempPassword, main_contact.name, "user");
                userId = newUser.id;
                console.log("Created new user:", userId);

                // Update transaction with user_id
                await supabaseAdmin
                  .from("transactions")
                  .update({ user_id: userId })
                  .eq("id", transaction.id);

              } catch (createUserError) {
                console.error("Error creating user:", createUserError);
                // Continue with registration even if user creation fails
                userId = null;
              }
            }
          }

          // Create registrations if we have participants
          if (participants && participants.length > 0) {
            const registrations = participants.map((participant: any) => {
              const registrationId = crypto.randomUUID();
              registrationIds.push(registrationId);

              return {
                id: registrationId,
                event_id: event_id,
                user_id: userId,
                created_by: userId,
                attendee_name: participant.name,
                attendee_email: participant.email,
                attendee_phone: participant.phone || null,
                ic_reg: participant.ic || null,
                ticket_type: participant.ticket_type_name || 'Standard',
                payment_status: 'paid',
                payment_amount: participant.price || 0,
                payment_date: new Date().toISOString(),
                transaction_id: transaction.id,
                status: 'confirmed',
                payment_callback_data: returnData,
                custom_field_responses: participant.custom_field_responses || {},
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              };
            });

            // Insert the registrations
            const { data: insertedRegistrations, error: insertError } = await supabaseAdmin
              .from("registrations")
              .insert(registrations)
              .select("id");

            if (insertError) {
              console.error("Error creating registrations:", insertError);
            } else {
              registrationIds = insertedRegistrations.map(reg => reg.id);
              console.log("Created registrations:", registrationIds);

              // Update transaction with first registration_id
              if (registrationIds.length > 0) {
                await supabaseAdmin
                  .from("transactions")
                  .update({ registration_id: registrationIds[0] })
                  .eq("id", transaction.id);
              }
            }
          }
        }
      }

      // If payment is successful, update existing registrations (if any were not just created)
      if (status_id === '1' && transaction.registration_id && registrationIds.length === 0) {
        const { error: regUpdateError } = await supabaseAdmin
          .from("registrations")
          .update({
            payment_status: 'paid',
            payment_date: new Date().toISOString(),
            status: 'confirmed',
            payment_callback_data: returnData
          })
          .eq("transaction_id", transaction.id);

        if (regUpdateError) {
          console.error("Error updating registration:", regUpdateError);
        } else {
          console.log(`Registration updated for transaction ${transaction.id}`);
        }
      } else if (transaction.registration_id && registrationIds.length === 0) {
        // For non-successful payments, still save the callback data for debugging
        const { error: regUpdateError } = await supabaseAdmin
          .from("registrations")
          .update({
            payment_callback_data: returnData
          })
          .eq("transaction_id", transaction.id);

        if (regUpdateError) {
          console.error("Error updating registration callback data:", regUpdateError);
        }
      }

      // Generate receipt and ticket tokens for public access (if payment is successful and registration exists)
      let receiptToken = null;
      let ticketToken = null;
      const finalRegistrationId = registrationIds.length > 0 ? registrationIds[0] : transaction.registration_id;

      if (status_id === '1' && finalRegistrationId) {
        receiptToken = generateReceiptToken(finalRegistrationId, transaction.id);
        ticketToken = generateTicketToken(finalRegistrationId, transaction.id);
      }

      return NextResponse.json({
        success: true,
        message: "Payment return data saved successfully",
        transaction_id: transaction.id,
        registration_id: finalRegistrationId,
        registration_ids: registrationIds.length > 0 ? registrationIds : [transaction.registration_id].filter(Boolean),
        user_id: userId,
        receipt_token: receiptToken,
        ticket_token: ticketToken,
        status: newStatus,
        user_created: registrationIds.length > 0 && userId && !transaction.user_id,
        registrations_created: registrationIds.length
      });
    } else {
      // Transaction not found, log for monitoring

      // You could create a separate table for orphaned return data or just log it
      // For now, we'll return success but log the issue
      return NextResponse.json({
        success: true,
        message: "Payment return data logged (transaction not found)",
        warning: "Transaction not found for billcode"
      });
    }

  } catch (error) {
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
