-- Add organization_id column to events table
-- This allows events to be directly associated with organizations

DO $$ 
BEGIN
  -- Add organization_id column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'organization_id') THEN
    ALTER TABLE events ADD COLUMN organization_id UUID REFERENCES organizations(id) ON DELETE SET NULL;
  END IF;
END $$;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_events_organization_id ON events(organization_id);

-- Update existing events to set organization_id based on the creator's organization
UPDATE events 
SET organization_id = users.organization_id
FROM users 
WHERE events.created_by = users.id 
AND users.organization_id IS NOT NULL
AND events.organization_id IS NULL;
