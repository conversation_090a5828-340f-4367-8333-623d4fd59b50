"use client"

import { useState } from "react"
import { CreditCard, CheckCircle2, AlertCircle } from "lucide-react"

import { <PERSON>ton } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { useRegistrations } from "@/contexts/registration-context"
import type { EventType } from "@/contexts/event-context"

interface PaymentProcessorProps {
  event: EventType
  registrationId: string
  participantCount: number
  onSuccess?: () => void
  onCancel?: () => void
}

export function PaymentProcessor({
  event,
  registrationId,
  participantCount,
  onSuccess,
  onCancel,
}: PaymentProcessorProps) {
  const { processPayment } = useRegistrations()
  const [paymentMethod, setPaymentMethod] = useState<string>("billplz")
  const [paymentStatus, setPaymentStatus] = useState<"idle" | "processing" | "success" | "error">("idle")
  const { toast } = useToast()

  // Calculate total amount
  const totalAmount = event.price ? event.price * participantCount : 0

  // Handle payment submission
  const handlePayment = async () => {
    if (totalAmount <= 0) {
      toast({
        title: "Error",
        description: "Invalid payment amount",
        variant: "destructive",
      })
      return
    }

    setPaymentStatus("processing")
    try {
      const result = await processPayment(registrationId, totalAmount, paymentMethod)

      if (result) {
        // In a real app, this would redirect to the payment gateway
        // For demo purposes, we'll simulate a successful payment after a delay
        setTimeout(() => {
          setPaymentStatus("success")
          toast({
            title: "Success",
            description: "Payment processed successfully",
          })
          if (onSuccess) {
            onSuccess()
          }
        }, 3000)
      } else {
        throw new Error("Payment processing failed")
      }
    } catch (error) {
      console.error("Error processing payment:", error)
      setPaymentStatus("error")
      toast({
        title: "Error",
        description: "Failed to process payment",
        variant: "destructive",
      })
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Payment</CardTitle>
        <CardDescription>Complete your payment to confirm registration</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {paymentStatus === "idle" && (
          <>
            <div className="space-y-4">
              <div className="flex justify-between text-sm">
                <span>Event:</span>
                <span className="font-medium">{event.title}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Participants:</span>
                <span>{participantCount}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Price per participant:</span>
                <span>RM {event.price?.toFixed(2)}</span>
              </div>
              <div className="flex justify-between font-medium">
                <span>Total Amount:</span>
                <span>RM {totalAmount.toFixed(2)}</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Select Payment Method</Label>
              <RadioGroup
                defaultValue={paymentMethod}
                onValueChange={setPaymentMethod}
                className="grid grid-cols-1 gap-4"
              >
                <div className="flex items-center space-x-2 rounded-md border p-3">
                  <RadioGroupItem value="billplz" id="billplz" />
                  <Label htmlFor="billplz" className="flex-1 cursor-pointer">
                    <div className="flex items-center justify-between">
                      <span>Billplz</span>
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </Label>
                </div>
                <div className="flex items-center space-x-2 rounded-md border p-3">
                  <RadioGroupItem value="toyyibpay" id="toyyibpay" />
                  <Label htmlFor="toyyibpay" className="flex-1 cursor-pointer">
                    <div className="flex items-center justify-between">
                      <span>ToyyibPay</span>
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </Label>
                </div>
              </RadioGroup>
            </div>
          </>
        )}

        {paymentStatus === "processing" && (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            <p className="mt-4 text-center">
              Processing your payment...
              <br />
              Please do not close this window.
            </p>
          </div>
        )}

        {paymentStatus === "success" && (
          <div className="flex flex-col items-center justify-center py-8">
            <CheckCircle2 className="h-16 w-16 text-green-500" />
            <h3 className="mt-4 text-xl font-semibold">Payment Successful!</h3>
            <p className="mt-2 text-center text-muted-foreground">
              Your registration is now confirmed.
              <br />
              You will receive a confirmation email shortly.
            </p>
          </div>
        )}

        {paymentStatus === "error" && (
          <div className="flex flex-col items-center justify-center py-8">
            <AlertCircle className="h-16 w-16 text-destructive" />
            <h3 className="mt-4 text-xl font-semibold">Payment Failed</h3>
            <p className="mt-2 text-center text-muted-foreground">
              There was an issue processing your payment.
              <br />
              Please try again or contact support.
            </p>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        {paymentStatus === "idle" && (
          <>
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button onClick={handlePayment}>Pay RM {totalAmount.toFixed(2)}</Button>
          </>
        )}

        {paymentStatus === "error" && (
          <>
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button onClick={() => setPaymentStatus("idle")}>Try Again</Button>
          </>
        )}

        {paymentStatus === "success" && (
          <Button className="w-full" onClick={onSuccess}>
            Continue
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
