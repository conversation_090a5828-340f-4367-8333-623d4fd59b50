import { createClient, SupabaseClient } from '@supabase/supabase-js'
import type { DatabaseConfig } from './types'

/**
 * Database client management
 */

let supabaseAdmin: SupabaseClient | null = null
let supabaseClient: SupabaseClient | null = null

/**
 * Get Supabase admin client (server-side only)
 */
export function getSupabaseAdmin(): SupabaseClient {
  if (!supabaseAdmin) {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing Supabase environment variables')
    }



    supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })


  }

  return supabaseAdmin
}

/**
 * Get Supabase client (client-side)
 */
export function getSupabaseClient(): SupabaseClient {
  if (!supabaseClient) {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error('Missing Supabase environment variables')
    }

    supabaseClient = createClient(supabaseUrl, supabaseAnonKey)
  }

  return supabaseClient
}

/**
 * Create a new Supabase client with custom configuration
 */
export function createSupabaseClient(config: DatabaseConfig): SupabaseClient {
  return createClient(config.url, config.serviceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  })
}

/**
 * Test database connection
 */
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    const supabase = getSupabaseAdmin()
    const { data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1)

    return !error
  } catch (error) {
    console.error('Database connection test failed:', error)
    return false
  }
}

/**
 * Get database health status
 */
export async function getDatabaseHealth(): Promise<{
  connected: boolean
  latency: number
  error?: string
}> {
  const startTime = Date.now()

  try {
    const supabase = getSupabaseAdmin()
    const { error } = await supabase
      .from('users')
      .select('id')
      .limit(1)

    const latency = Date.now() - startTime

    return {
      connected: !error,
      latency,
      error: error?.message
    }
  } catch (error: any) {
    return {
      connected: false,
      latency: Date.now() - startTime,
      error: error.message
    }
  }
}

/**
 * Execute raw SQL query (admin only)
 */
export async function executeRawSQL(sql: string): Promise<{
  data: any[] | null
  error: string | null
}> {
  try {
    const supabase = getSupabaseAdmin()
    const { data, error } = await supabase.rpc('execute_sql', { sql_query: sql })

    return {
      data,
      error: error?.message || null
    }
  } catch (error: any) {
    return {
      data: null,
      error: error.message
    }
  }
}

/**
 * Get table information
 */
export async function getTableInfo(tableName: string): Promise<{
  columns: Array<{
    name: string
    type: string
    nullable: boolean
    default: any
  }>
  error?: string
}> {
  try {
    const supabase = getSupabaseAdmin()

    // This would need a custom RPC function in Supabase
    // For now, return a placeholder
    return {
      columns: [],
      error: 'Table info not implemented'
    }
  } catch (error: any) {
    return {
      columns: [],
      error: error.message
    }
  }
}

/**
 * Reset database connection (useful for testing)
 */
export function resetDatabaseConnection(): void {
  supabaseAdmin = null
  supabaseClient = null
}
