"use client"

import { use<PERSON>ffe<PERSON>, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation"
import { ArrowLeft, Users, CheckCircle2, Clock, RefreshCw, QrCode, Shield, MapPin } from "lucide-react"
import Link from "next/link"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { QrScanner } from "@/components/qr-scanner"
import { parseSecureTeamToken, verifySecureTeamToken } from "@/lib/security-token"

interface TeamInfo {
  id: string
  team_name: string
  location?: string
  permissions: {
    can_scan_qr: boolean
    can_view_attendance: boolean
  }
  event: {
    id: string
    title: string
    slug: string
  }
}

interface EventData {
  id: string
  title: string
  slug: string
  start_date: string
  end_date: string
  location: string
}

interface RecentCheckIn {
  id: string
  attendee_name: string
  attendee_email: string
  attendee_phone?: string
  ticket_type?: string
  checked_in_at: string
  payment_status: string
}

export default function TeamQRScannerPage() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()

  const [accessToken, setAccessToken] = useState("")
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isAuthenticating, setIsAuthenticating] = useState(false)
  const [teamInfo, setTeamInfo] = useState<TeamInfo | null>(null)
  const [event, setEvent] = useState<EventData | null>(null)
  const [attendanceCount, setAttendanceCount] = useState(0)
  const [totalAttendees, setTotalAttendees] = useState(0)
  const [recentCheckIns, setRecentCheckIns] = useState<RecentCheckIn[]>([])
  const [refreshing, setRefreshing] = useState(false)
  const [error, setError] = useState("")

  const eventSlug = params.slug as string

  // Check if there's a token in URL params or stored token for this event
  useEffect(() => {
    const urlToken = searchParams.get('token')
    const secureToken = searchParams.get('secure_token')
    const storedToken = localStorage.getItem(`team_token_${eventSlug}`)

    // Handle secure token from URL
    if (secureToken) {
      try {
        const currentUrl = window.location.href
        const parsedToken = parseSecureTeamToken(currentUrl)

        if (parsedToken) {
          const verification = verifySecureTeamToken(parsedToken)

          if (verification.valid && verification.accessToken) {
            setAccessToken(verification.accessToken)
            handleAuthenticate(verification.accessToken)
            return
          } else {
            setError(verification.reason || "Invalid secure token")
            return
          }
        } else {
          setError("Failed to parse secure token")
          return
        }
      } catch (error) {
        console.error("Error processing secure token:", error)
        setError("Invalid secure token format")
        return
      }
    }

    // Handle plain token from URL (for manual entry)
    if (urlToken) {
      setAccessToken(urlToken)
      handleAuthenticate(urlToken)
    } else if (storedToken) {
      setAccessToken(storedToken)
      handleAuthenticate(storedToken)
    }
  }, [eventSlug, searchParams])

  // Fetch event data
  useEffect(() => {
    if (isAuthenticated && teamInfo) {
      fetchEventData()
      fetchAttendanceCount()
      fetchRecentCheckIns()
    }
  }, [isAuthenticated, teamInfo])

  const handleAuthenticate = async (token?: string) => {
    const tokenToUse = token || accessToken
    if (!tokenToUse.trim()) {
      setError("Please enter an access token")
      return
    }

    setIsAuthenticating(true)
    setError("")

    try {
      const response = await fetch("/api/teams/auth", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          access_token: tokenToUse,
          event_slug: eventSlug,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Authentication failed")
      }

      setTeamInfo(data.team)
      setIsAuthenticated(true)

      // Store token for future use
      localStorage.setItem(`team_token_${eventSlug}`, tokenToUse)

      toast({
        title: "Authentication successful",
        description: `Welcome, ${data.team.team_name}!`,
      })

    } catch (error: any) {
      console.error("Authentication error:", error)
      setError(error.message || "Authentication failed")

      // Clear stored token if authentication fails
      localStorage.removeItem(`team_token_${eventSlug}`)

      toast({
        title: "Authentication failed",
        description: error.message || "Please check your access token",
        variant: "destructive",
      })
    } finally {
      setIsAuthenticating(false)
    }
  }

  const fetchEventData = async () => {
    try {
      const response = await fetch(`/api/events/${eventSlug}`)
      const data = await response.json()

      if (response.ok) {
        setEvent(data)
      }
    } catch (error) {
      console.error("Error fetching event data:", error)
    }
  }

  const fetchAttendanceCount = async () => {
    if (!teamInfo?.event.id) return

    try {
      const response = await fetch(`/api/events/${eventSlug}/attendance/count`)
      const data = await response.json()

      if (response.ok) {
        setAttendanceCount(data.count || 0)
        setTotalAttendees(data.total || 0)
      }
    } catch (error) {
      console.error("Error fetching attendance count:", error)
    }
  }

  const fetchRecentCheckIns = async () => {
    if (!teamInfo?.event.id) return

    try {
      const response = await fetch(`/api/events/${eventSlug}/attendance/recent?limit=10`)
      const data = await response.json()

      if (response.ok) {
        setRecentCheckIns(data.recent_checkins || [])
      }
    } catch (error) {
      console.error("Error fetching recent check-ins:", error)
    }
  }

  const handleAttendanceMarked = (attendeeData: any) => {
    // Refresh attendance count and recent check-ins
    fetchAttendanceCount()
    fetchRecentCheckIns()

    toast({
      title: "Attendance marked",
      description: `${attendeeData.guest_name || attendeeData.attendee} checked in successfully`,
    })
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await Promise.all([
      fetchAttendanceCount(),
      fetchRecentCheckIns()
    ])
    setTimeout(() => setRefreshing(false), 1000)
  }

  const handleLogout = () => {
    localStorage.removeItem(`team_token_${eventSlug}`)
    setIsAuthenticated(false)
    setTeamInfo(null)
    setAccessToken("")
    setError("")
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  // Authentication form
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900">
        <div className="container mx-auto py-8 md:py-12 px-3 md:px-4">
          <div className="max-w-md mx-auto">
            {/* Header */}
            <div className="text-center mb-6 md:mb-8">
              <div className="inline-flex items-center justify-center w-12 h-12 md:w-16 md:h-16 bg-white/10 rounded-full mb-3 md:mb-4">
                <Shield className="h-6 w-6 md:h-8 md:w-8 text-white" />
              </div>
              <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">Team Access</h1>
              <p className="text-sm md:text-base text-purple-200 px-2">
                Enter your team access token to access attendance system
              </p>
            </div>

            {/* Authentication Card */}
            <Card className="border-white/20 bg-white/10 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white">Team Authentication</CardTitle>
                <CardDescription className="text-purple-200">
                  Access the attendance system for {eventSlug}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="token" className="text-white">Access Token</Label>
                  <Input
                    id="token"
                    type="password"
                    placeholder="Enter your team access token"
                    value={accessToken}
                    onChange={(e) => setAccessToken(e.target.value)}
                    className="bg-white/10 border-white/20 text-white placeholder:text-purple-300"
                    onKeyPress={(e) => e.key === "Enter" && handleAuthenticate()}
                  />
                </div>

                {error && (
                  <Alert className="border-red-500/50 bg-red-500/10">
                    <AlertDescription className="text-red-200">
                      {error}
                    </AlertDescription>
                  </Alert>
                )}

                <Button
                  onClick={() => handleAuthenticate()}
                  disabled={isAuthenticating || !accessToken.trim()}
                  className="w-full bg-white text-purple-900 hover:bg-purple-50"
                >
                  {isAuthenticating ? "Authenticating..." : "Access System"}
                </Button>

                <div className="text-center">
                  <Link href={`/events/${eventSlug}`}>
                    <Button variant="ghost" size="sm" className="!text-purple-200 hover:!text-white hover:!bg-white/10">
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Back to Event
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  // Main scanner interface
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900">
      <div className="container mx-auto py-4 md:py-6 px-3 md:px-4">
        {/* Header */}
        <div className="mb-4 md:mb-6">
          {/* Mobile Header */}
          <div className="flex flex-col gap-3 md:hidden">
            <div className="flex items-center justify-between">
              <Link href={`/events/${eventSlug}`}>
                <Button variant="outline" size="sm" className="!border-white/20 !text-white !bg-transparent hover:!bg-white/10 hover:!text-white">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Button>
              </Link>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}
                  className="!border-white/20 !text-white !bg-transparent hover:!bg-white/10 hover:!text-white">
                  <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                </Button>
                <Button variant="outline" size="sm" onClick={handleLogout}
                  className="!border-white/20 !text-white !bg-transparent hover:!bg-white/10 hover:!text-white">
                  Logout
                </Button>
              </div>
            </div>
            <div className="flex flex-wrap items-center gap-2">
              <Badge variant="secondary" className="bg-white/10 text-white border-white/20">
                Team: {teamInfo?.team_name}
              </Badge>
              {teamInfo?.location && (
                <Badge variant="outline" className="bg-white/5 text-purple-200 border-white/20">
                  <MapPin className="h-3 w-3 mr-1" />
                  {teamInfo.location}
                </Badge>
              )}
            </div>
          </div>

          {/* Desktop Header */}
          <div className="hidden md:flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Link href={`/events/${eventSlug}`}>
                <Button variant="outline" size="sm" className="!border-white/20 !text-white !bg-transparent hover:!bg-white/10 hover:!text-white">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Event
                </Button>
              </Link>
              <Badge variant="secondary" className="bg-white/10 text-white border-white/20">
                Team: {teamInfo?.team_name}
              </Badge>
              {teamInfo?.location && (
                <Badge variant="outline" className="bg-white/5 text-purple-200 border-white/20">
                  <MapPin className="h-3 w-3 mr-1" />
                  {teamInfo.location}
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}
                className="!border-white/20 !text-white !bg-transparent hover:!bg-white/10 hover:!text-white">
                <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
              <Button variant="outline" size="sm" onClick={handleLogout}
                className="!border-white/20 !text-white !bg-transparent hover:!bg-white/10 hover:!text-white">
                Logout
              </Button>
            </div>
          </div>
        </div>

        {/* Page Title */}
        <div className="mb-4 md:mb-6 text-center">
          <h1 className="text-2xl md:text-3xl font-bold text-white mb-2">
            {teamInfo?.permissions.can_scan_qr ? "QR Scanner" : "Attendance Monitor"}
          </h1>
          <p className="text-sm md:text-base text-purple-200 px-2">
            {teamInfo?.permissions.can_scan_qr
              ? `Scan attendee QR codes for ${event?.title || eventSlug}`
              : `Monitor attendance for ${event?.title || eventSlug}`
            }
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4 mb-4 md:mb-6">
          <Card className="border-white/20 bg-white/10 backdrop-blur-sm">
            <CardContent className="p-3 md:p-4">
              <div className="flex items-center gap-2 md:gap-3">
                <div className="p-1.5 md:p-2 rounded-lg bg-green-500/20">
                  <CheckCircle2 className="h-4 w-4 md:h-5 md:w-5 text-green-400" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs md:text-sm text-purple-200">Checked In</p>
                  <p className="text-lg md:text-2xl font-bold text-white">{attendanceCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-white/20 bg-white/10 backdrop-blur-sm">
            <CardContent className="p-3 md:p-4">
              <div className="flex items-center gap-2 md:gap-3">
                <div className="p-1.5 md:p-2 rounded-lg bg-blue-500/20">
                  <Users className="h-4 w-4 md:h-5 md:w-5 text-blue-400" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs md:text-sm text-purple-200">Total</p>
                  <p className="text-lg md:text-2xl font-bold text-white">{totalAttendees}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-white/20 bg-white/10 backdrop-blur-sm">
            <CardContent className="p-3 md:p-4">
              <div className="flex items-center gap-2 md:gap-3">
                <div className="p-1.5 md:p-2 rounded-lg bg-orange-500/20">
                  <Clock className="h-4 w-4 md:h-5 md:w-5 text-orange-400" />
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs md:text-sm text-purple-200">Pending</p>
                  <p className="text-lg md:text-2xl font-bold text-white">{totalAttendees - attendanceCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-white/20 bg-white/10 backdrop-blur-sm">
            <CardContent className="p-3 md:p-4">
              <div className="flex items-center gap-2 md:gap-3">
                <div className="p-1.5 md:p-2 rounded-lg bg-purple-500/20">
                  {teamInfo?.location ? <MapPin className="h-4 w-4 md:h-5 md:w-5 text-purple-400" /> : <QrCode className="h-4 w-4 md:h-5 md:w-5 text-purple-400" />}
                </div>
                <div className="min-w-0 flex-1">
                  <p className="text-xs md:text-sm text-purple-200">
                    {teamInfo?.location ? "Location" : "Team"}
                  </p>
                  <p className="text-sm md:text-lg font-semibold text-white truncate">
                    {teamInfo?.location || teamInfo?.team_name}
                  </p>
                  {teamInfo?.location && (
                    <p className="text-xs text-purple-300 truncate">{teamInfo.team_name}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className={`grid gap-4 md:gap-6 ${teamInfo?.permissions.can_scan_qr ? 'grid-cols-1 xl:grid-cols-2' : 'grid-cols-1'}`}>
          {/* QR Scanner Section - Only show if team has scan permissions */}
          {teamInfo?.permissions.can_scan_qr && (
            <div className="space-y-4 order-1">
              <Card className="border-white/20 bg-white/10 backdrop-blur-sm">
                <CardHeader className="pb-3 md:pb-6">
                  <CardTitle className="flex items-center gap-2 text-white text-lg md:text-xl">
                    <div className="p-1.5 md:p-2 rounded-lg bg-primary/20">
                      <QrCode className="h-4 w-4 md:h-5 md:w-5 text-primary" />
                    </div>
                    QR Scanner
                  </CardTitle>
                  <CardDescription className="text-purple-200 text-sm">
                    Position QR code within camera frame
                    {teamInfo?.location && (
                      <span className="block mt-1 text-xs">
                        <MapPin className="h-3 w-3 inline mr-1" />
                        Scanning at: {teamInfo.location}
                      </span>
                    )}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  {event?.id && (
                    <QrScanner
                      eventId={event.id}
                      onAttendanceMarked={handleAttendanceMarked}
                    />
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {/* Recent Check-ins Section */}
          <div className={`space-y-4 ${teamInfo?.permissions.can_scan_qr ? 'order-2' : 'order-1'}`}>
            <Card className="border-white/20 bg-white/10 backdrop-blur-sm">
              <CardHeader className="pb-3 md:pb-6">
                <CardTitle className="flex items-center gap-2 text-white text-lg md:text-xl">
                  <div className="p-1.5 md:p-2 rounded-lg bg-green-500/20">
                    <CheckCircle2 className="h-4 w-4 md:h-5 md:w-5 text-green-400" />
                  </div>
                  Recent Check-ins
                </CardTitle>
                <CardDescription className="text-purple-200 text-sm">
                  Latest attendees checked in
                  {teamInfo?.location && (
                    <span className="block mt-1 text-xs">
                      <MapPin className="h-3 w-3 inline mr-1" />
                      Monitoring: {teamInfo.location}
                    </span>
                  )}
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                {recentCheckIns.length === 0 ? (
                  <div className="text-center py-6 md:py-8 text-purple-200">
                    <CheckCircle2 className="h-8 w-8 md:h-12 md:w-12 mx-auto mb-3 md:mb-4 opacity-50" />
                    <p className="text-sm md:text-base">No check-ins yet</p>
                    <p className="text-xs md:text-sm mt-1">
                      {teamInfo?.permissions.can_scan_qr
                        ? "Scan QR codes to mark attendance"
                        : "Check-ins will appear here when attendees are scanned"
                      }
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2 md:space-y-3 max-h-80 md:max-h-96 overflow-y-auto">
                    {recentCheckIns.map((checkIn) => (
                      <div key={checkIn.id} className="flex items-start gap-3 p-3 border border-white/10 rounded-lg bg-white/5">
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-white text-sm md:text-base truncate">{checkIn.attendee_name}</div>
                          <div className="text-xs md:text-sm text-purple-200 truncate">{checkIn.attendee_email}</div>
                          <div className="flex items-center gap-2 mt-1">
                            {checkIn.ticket_type && (
                              <Badge variant="outline" className="text-xs border-white/20 text-purple-200">
                                {checkIn.ticket_type}
                              </Badge>
                            )}
                            <div className="text-xs text-purple-300">
                              {formatDateTime(checkIn.checked_in_at)}
                            </div>
                          </div>
                        </div>
                        <div className="flex-shrink-0">
                          <Badge className="bg-green-500/20 text-green-400 border-green-500/30 text-xs">
                            <CheckCircle2 className="h-3 w-3 mr-1" />
                            <span className="hidden sm:inline">Checked In</span>
                            <span className="sm:hidden">OK</span>
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
