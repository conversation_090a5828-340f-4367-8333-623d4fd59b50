/**
 * Performance monitoring utilities for tracking and optimizing application performance
 */

export interface PerformanceMetric {
  name: string
  duration: number
  timestamp: number
  metadata?: Record<string, any>
}

export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: PerformanceMetric[] = []
  private timers: Map<string, number> = new Map()

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  /**
   * Start timing an operation
   */
  startTimer(name: string): void {
    this.timers.set(name, performance.now())
  }

  /**
   * End timing an operation and record the metric
   */
  endTimer(name: string, metadata?: Record<string, any>): number {
    const startTime = this.timers.get(name)
    if (!startTime) {
      return 0
    }

    const duration = performance.now() - startTime
    this.timers.delete(name)

    const metric: PerformanceMetric = {
      name,
      duration,
      timestamp: Date.now(),
      metadata
    }

    this.metrics.push(metric)

    // Monitor slow operations (> 1000ms) - integrate with monitoring system
    if (duration > 1000) {
      // Log to monitoring system instead of console
    }

    return duration
  }

  /**
   * Measure a function execution time
   */
  async measure<T>(name: string, fn: () => Promise<T>, metadata?: Record<string, any>): Promise<T> {
    this.startTimer(name)
    try {
      const result = await fn()
      this.endTimer(name, metadata)
      return result
    } catch (error) {
      this.endTimer(name, { ...metadata, error: true })
      throw error
    }
  }

  /**
   * Get performance metrics
   */
  getMetrics(): PerformanceMetric[] {
    return [...this.metrics]
  }

  /**
   * Get metrics for a specific operation
   */
  getMetricsForOperation(name: string): PerformanceMetric[] {
    return this.metrics.filter(metric => metric.name === name)
  }

  /**
   * Get average duration for an operation
   */
  getAverageDuration(name: string): number {
    const operationMetrics = this.getMetricsForOperation(name)
    if (operationMetrics.length === 0) return 0

    const totalDuration = operationMetrics.reduce((sum, metric) => sum + metric.duration, 0)
    return totalDuration / operationMetrics.length
  }

  /**
   * Clear old metrics (keep only last 1000)
   */
  cleanup(): void {
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000)
    }
  }

  /**
   * Get performance summary
   */
  getSummary(): Record<string, { count: number; avgDuration: number; maxDuration: number }> {
    const summary: Record<string, { count: number; avgDuration: number; maxDuration: number }> = {}

    this.metrics.forEach(metric => {
      if (!summary[metric.name]) {
        summary[metric.name] = {
          count: 0,
          avgDuration: 0,
          maxDuration: 0
        }
      }

      const current = summary[metric.name]
      current.count++
      current.maxDuration = Math.max(current.maxDuration, metric.duration)
      current.avgDuration = (current.avgDuration * (current.count - 1) + metric.duration) / current.count
    })

    return summary
  }
}

/**
 * Decorator for measuring method execution time
 */
export function measurePerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const operationName = name || `${target.constructor.name}.${propertyKey}`

    descriptor.value = async function (...args: any[]) {
      const monitor = PerformanceMonitor.getInstance()
      return monitor.measure(operationName, () => originalMethod.apply(this, args))
    }

    return descriptor
  }
}

/**
 * Simple performance measurement utility
 */
export const perf = {
  start: (name: string) => PerformanceMonitor.getInstance().startTimer(name),
  end: (name: string, metadata?: Record<string, any>) => PerformanceMonitor.getInstance().endTimer(name, metadata),
  measure: <T>(name: string, fn: () => Promise<T>, metadata?: Record<string, any>) =>
    PerformanceMonitor.getInstance().measure(name, fn, metadata),
  getSummary: () => PerformanceMonitor.getInstance().getSummary()
}

/**
 * Database query performance wrapper
 */
export async function measureDatabaseQuery<T>(
  queryName: string,
  queryFn: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  return perf.measure(`db:${queryName}`, queryFn, metadata)
}

/**
 * API endpoint performance wrapper
 */
export async function measureApiEndpoint<T>(
  endpoint: string,
  handler: () => Promise<T>,
  metadata?: Record<string, any>
): Promise<T> {
  return perf.measure(`api:${endpoint}`, handler, metadata)
}
