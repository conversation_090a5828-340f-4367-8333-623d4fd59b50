"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import SubscriptionPlanForm from "@/components/subscription-plan-form"

type SubscriptionPlan = {
  id: string
  name: string
  price: number
  description: string
  features: string[]
  is_popular?: boolean
  max_events?: number | null
  max_attendees_per_event?: number | null
  // Feature toggles
  certificates_enabled?: boolean
  attendance_enabled?: boolean
  webhooks_enabled?: boolean
  analytics_enabled?: boolean
  reports_enabled?: boolean
}

export default function EditSubscriptionPlanPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [plan, setPlan] = useState<SubscriptionPlan | null>(null)
  const [loading, setLoading] = useState(true)
  const planId = params.id as string

  // Helper function to get cookie value
  const getCookie = (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  useEffect(() => {
    const fetchPlan = async () => {
      setLoading(true)
      try {
        const token = getCookie('auth_token')
        if (!token) {
          throw new Error('No authentication token found')
        }

        const response = await fetch(`/api/admin/subscription-plans/${planId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        })

        if (!response.ok) {
          if (response.status === 404) {
            toast({
              title: "Plan not found",
              description: "The subscription plan you're trying to edit doesn't exist or has been removed.",
              variant: "destructive",
            })
            router.push("/dashboard/settings/subscriptions")
            return
          }
          throw new Error('Failed to fetch subscription plan')
        }

        const data = await response.json()
        setPlan(data.plan)
      } catch (error: any) {
        console.error("Error fetching subscription plan:", error)
        toast({
          title: "Error",
          description: error.message || "Failed to fetch subscription plan",
          variant: "destructive",
        })
        router.push("/dashboard/settings/subscriptions")
      } finally {
        setLoading(false)
      }
    }

    if (planId) {
      fetchPlan()
    }
  }, [planId, router, toast])

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center gap-4">
          <div className="h-8 w-16 bg-muted rounded animate-pulse"></div>
          <div className="h-8 w-48 bg-muted rounded animate-pulse"></div>
        </div>
        <div className="space-y-4">
          <div className="h-64 bg-muted rounded animate-pulse"></div>
          <div className="h-32 bg-muted rounded animate-pulse"></div>
        </div>
      </div>
    )
  }

  if (!plan) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Plan not found</h1>
          <p className="text-muted-foreground">The subscription plan you're looking for doesn't exist.</p>
        </div>
      </div>
    )
  }

  return <SubscriptionPlanForm plan={plan} isEdit={true} />
}
