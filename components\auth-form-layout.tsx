"use client"

import React from "react"
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import MainNav from "@/components/main-nav"

interface AuthFormLayoutProps {
  title: string
  description: string
  children: React.ReactNode
  footer?: React.ReactNode
  className?: string
}

export function AuthFormLayout({ 
  title, 
  description, 
  children, 
  footer, 
  className = "" 
}: AuthFormLayoutProps) {
  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1 flex items-center justify-center p-4 md:p-8">
        <Card className={`w-full max-w-md ${className}`}>
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold">{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </CardHeader>
          {children}
          {footer && <CardFooter>{footer}</CardFooter>}
        </Card>
      </main>
    </div>
  )
}
