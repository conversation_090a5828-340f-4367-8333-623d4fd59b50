"use client"

import { lazy, Suspense } from "react"
import { Skeleton } from "@/components/ui/skeleton"

// Lazy load heavy components
export const LazyCertificateGenerator = lazy(() => 
  import("@/components/certificate-generator").then(module => ({
    default: module.CertificateGenerator
  }))
)

export const LazyQRCodeGenerator = lazy(() => 
  import("@/components/qr-code-generator").then(module => ({
    default: module.QRCodeGenerator
  }))
)

export const LazyChartComponents = lazy(() => 
  import("react-chartjs-2").then(module => ({
    default: module
  }))
)

export const LazyMonacoEditor = lazy(() => 
  import("@monaco-editor/react").then(module => ({
    default: module.default
  }))
)

// Loading components
export const CertificateGeneratorSkeleton = () => (
  <div className="space-y-4">
    <Skeleton className="h-8 w-48" />
    <Skeleton className="h-64 w-full" />
    <div className="flex gap-2">
      <Skeleton className="h-10 w-24" />
      <Skeleton className="h-10 w-24" />
    </div>
  </div>
)

export const QRCodeGeneratorSkeleton = () => (
  <div className="flex flex-col items-center space-y-2">
    <Skeleton className="h-32 w-32" />
    <Skeleton className="h-4 w-24" />
  </div>
)

export const ChartSkeleton = () => (
  <div className="space-y-2">
    <Skeleton className="h-6 w-32" />
    <Skeleton className="h-64 w-full" />
  </div>
)

export const EditorSkeleton = () => (
  <div className="space-y-2">
    <Skeleton className="h-6 w-24" />
    <Skeleton className="h-96 w-full" />
  </div>
)

// Wrapper components with Suspense
export const CertificateGeneratorWithSuspense = (props: any) => (
  <Suspense fallback={<CertificateGeneratorSkeleton />}>
    <LazyCertificateGenerator {...props} />
  </Suspense>
)

export const QRCodeGeneratorWithSuspense = (props: any) => (
  <Suspense fallback={<QRCodeGeneratorSkeleton />}>
    <LazyQRCodeGenerator {...props} />
  </Suspense>
)

export const ChartWithSuspense = (props: any) => (
  <Suspense fallback={<ChartSkeleton />}>
    <LazyChartComponents {...props} />
  </Suspense>
)

export const EditorWithSuspense = (props: any) => (
  <Suspense fallback={<EditorSkeleton />}>
    <LazyMonacoEditor {...props} />
  </Suspense>
)
