-- Add feature toggle columns to subscription_plans table
DO $$
BEGIN
  -- Add certificates_enabled column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'certificates_enabled') THEN
    ALTER TABLE subscription_plans ADD COLUMN certificates_enabled BOOLEAN DEFAULT false;
  END IF;

  -- Add attendance_enabled column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'attendance_enabled') THEN
    ALTER TABLE subscription_plans ADD COLUMN attendance_enabled BOOLEAN DEFAULT false;
  END IF;

  -- Add webhooks_enabled column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'webhooks_enabled') THEN
    ALTER TABLE subscription_plans ADD COLUMN webhooks_enabled BOOLEAN DEFAULT false;
  END IF;

  -- Add analytics_enabled column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'analytics_enabled') THEN
    ALTER TABLE subscription_plans ADD COLUMN analytics_enabled BOOLEAN DEFAULT false;
  END IF;

  -- Add reports_enabled column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'subscription_plans' AND column_name = 'reports_enabled') THEN
    ALTER TABLE subscription_plans ADD COLUMN reports_enabled BOOLEAN DEFAULT false;
  END IF;
END $$;

-- Update existing plans with appropriate feature toggles based on their tier
UPDATE subscription_plans
SET
  certificates_enabled = CASE
    WHEN name IN ('Basic', 'Pro', 'Enterprise') THEN true
    ELSE false
  END,
  attendance_enabled = CASE
    WHEN name IN ('Basic', 'Pro', 'Enterprise') THEN true
    ELSE false
  END,
  webhooks_enabled = CASE
    WHEN name IN ('Pro', 'Enterprise') THEN true
    ELSE false
  END,
  analytics_enabled = CASE
    WHEN name IN ('Pro', 'Enterprise') THEN true
    ELSE false
  END,
  reports_enabled = CASE
    WHEN name = 'Enterprise' THEN true
    ELSE false
  END
WHERE id IS NOT NULL;

-- Create analytics tracking tables
CREATE TABLE IF NOT EXISTS subscription_analytics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  date DATE NOT NULL,
  total_revenue DECIMAL(10, 2) DEFAULT 0,
  active_subscriptions INTEGER DEFAULT 0,
  new_subscriptions INTEGER DEFAULT 0,
  cancelled_subscriptions INTEGER DEFAULT 0,
  plan_distribution JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create unique index on date for analytics
CREATE UNIQUE INDEX IF NOT EXISTS idx_subscription_analytics_date ON subscription_analytics(date);

-- Create certificate templates table
CREATE TABLE IF NOT EXISTS certificate_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  template_data JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create attendance settings table
CREATE TABLE IF NOT EXISTS attendance_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  qr_enabled BOOLEAN DEFAULT true,
  manual_checkin_enabled BOOLEAN DEFAULT true,
  auto_certificate_generation BOOLEAN DEFAULT false,
  attendance_notifications BOOLEAN DEFAULT true,
  real_time_updates BOOLEAN DEFAULT true,
  bulk_checkin_enabled BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default attendance settings
INSERT INTO attendance_settings (qr_enabled, manual_checkin_enabled, auto_certificate_generation, attendance_notifications, real_time_updates, bulk_checkin_enabled)
VALUES (true, true, false, true, true, true)
ON CONFLICT DO NOTHING;

-- Create reports table
CREATE TABLE IF NOT EXISTS reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  type TEXT NOT NULL,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'generating', 'completed', 'failed')),
  parameters JSONB DEFAULT '{}',
  file_url TEXT,
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for reports
CREATE INDEX IF NOT EXISTS idx_reports_status ON reports(status);
CREATE INDEX IF NOT EXISTS idx_reports_type ON reports(type);
CREATE INDEX IF NOT EXISTS idx_reports_created_by ON reports(created_by);
CREATE INDEX IF NOT EXISTS idx_reports_created_at ON reports(created_at);

-- Create updated_at triggers for new tables
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for updated_at
DROP TRIGGER IF EXISTS update_subscription_analytics_updated_at ON subscription_analytics;
CREATE TRIGGER update_subscription_analytics_updated_at
  BEFORE UPDATE ON subscription_analytics
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_certificate_templates_updated_at ON certificate_templates;
CREATE TRIGGER update_certificate_templates_updated_at
  BEFORE UPDATE ON certificate_templates
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_attendance_settings_updated_at ON attendance_settings;
CREATE TRIGGER update_attendance_settings_updated_at
  BEFORE UPDATE ON attendance_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_reports_updated_at ON reports;
CREATE TRIGGER update_reports_updated_at
  BEFORE UPDATE ON reports
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Insert default certificate templates
INSERT INTO certificate_templates (name, description, template_data, is_active, is_default)
VALUES
  (
    'Completion Certificate',
    'Standard completion certificate for events',
    '{"background_color": "#ffffff", "text_color": "#000000", "font_family": "Arial", "layout": "standard"}',
    true,
    true
  ),
  (
    'Attendance Certificate',
    'Certificate for event attendance',
    '{"background_color": "#f8f9fa", "text_color": "#333333", "font_family": "Times New Roman", "layout": "modern"}',
    true,
    true
  ),
  (
    'Achievement Certificate',
    'Certificate for special achievements',
    '{"background_color": "#fff3cd", "text_color": "#856404", "font_family": "Georgia", "layout": "elegant"}',
    true,
    true
  ),
  (
    'Professional Certificate',
    'Professional-looking certificate for corporate events',
    '{"background_color": "#f8f9fa", "text_color": "#2c3e50", "font_family": "Georgia", "layout": "professional"}',
    true,
    true
  ),
  (
    'Modern Certificate',
    'Modern design certificate with clean layout',
    '{"background_color": "#ffffff", "text_color": "#1a202c", "font_family": "Inter", "layout": "modern"}',
    true,
    true
  )
ON CONFLICT DO NOTHING;
