"use client"

import { useState, useEffect, useCallback } from "react"
import { QRCodeSVG } from "qrcode.react"
import { Shield, Clock, RefreshCw } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"

interface DynamicQRCodeProps {
  ticketData: {
    id: string
    event_id: string
    guest_name: string
    event?: {
      id: string
      title: string
    }
  }
  size?: number
  className?: string
  showTimer?: boolean
  showSecurityBadge?: boolean
  isCheckedIn?: boolean
}

export function DynamicQRCode({
  ticketData,
  size = 200,
  className = "",
  showTimer = true,
  showSecurityBadge = true,
  isCheckedIn = false,
}: DynamicQRCodeProps) {
  const [qrData, setQrData] = useState<string>("")
  const [timeRemaining, setTimeRemaining] = useState<number>(30)
  const [isGenerating, setIsGenerating] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [cycleCount, setCycleCount] = useState<number>(0)
  const [needsRefresh, setNeedsRefresh] = useState<boolean>(false)
  const [isActive, setIsActive] = useState<boolean>(!isCheckedIn)

  // Generate new QR code with secure token
  const generateQRCode = useCallback(async () => {
    setIsGenerating(true)
    setError(null)

    try {
      const response = await fetch('/api/tickets/secure-qr', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ticketData,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to generate secure QR code')
      }

      const result = await response.json()
      setQrData(result.qrData)
    } catch (err) {
      console.error('Error generating QR code:', err)
      setError('Failed to generate QR code')
    } finally {
      setIsGenerating(false)
    }
  }, [ticketData])

  // Calculate time remaining in current window
  const updateTimeRemaining = useCallback(() => {
    const now = Math.floor(Date.now() / 1000)
    const windowStart = Math.floor(now / 30) * 30
    const windowEnd = windowStart + 30
    const remaining = windowEnd - now
    setTimeRemaining(remaining)
  }, [])

  // Handle manual refresh
  const handleRefresh = useCallback(() => {
    setCycleCount(0)
    setNeedsRefresh(false)
    setIsActive(true)
    generateQRCode()
  }, [])

  // Stop QR updates when checked in
  useEffect(() => {
    if (isCheckedIn) {
      setIsActive(false)
      setNeedsRefresh(false)
    }
  }, [isCheckedIn])

  // Initialize QR code and timer
  useEffect(() => {
    if (isActive && !isCheckedIn) {
      generateQRCode()
      updateTimeRemaining()
    }
  }, [generateQRCode, updateTimeRemaining, isActive, isCheckedIn])

  // Set up timer to refresh QR code every 30 seconds for 6 cycles
  useEffect(() => {
    if (!isActive || isCheckedIn) return

    const interval = setInterval(() => {
      updateTimeRemaining()

      // Generate new QR code when time window expires
      if (timeRemaining <= 1) {
        if (cycleCount < 5) { // 0-5 = 6 cycles
          setCycleCount(prev => prev + 1)
          generateQRCode()
        } else {
          // After 6 cycles, stop and require manual refresh
          setIsActive(false)
          setNeedsRefresh(true)
        }
      }
    }, 1000)

    return () => clearInterval(interval)
  }, [timeRemaining, generateQRCode, updateTimeRemaining, cycleCount, isActive, isCheckedIn])

  // Calculate progress percentage
  const progressPercentage = (timeRemaining / 30) * 100

  if (error) {
    return (
      <Card className={`p-4 ${className}`}>
        <CardContent className="flex flex-col items-center justify-center space-y-2">
          <div className="text-red-500 text-sm text-center">
            {error}
          </div>
          <button
            onClick={generateQRCode}
            className="text-blue-500 hover:text-blue-700 text-sm underline"
          >
            Try again
          </button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={`relative ${className}`}>
      <CardContent className="p-4">
        <div className="flex flex-col items-center space-y-4">
          {/* Security Badge */}
          {showSecurityBadge && (
            <div className="flex items-center space-x-2 flex-wrap justify-center">
              {isCheckedIn ? (
                <Badge className="bg-green-100 text-green-800">
                  <Shield className="w-3 h-3 mr-1" />
                  Checked In
                </Badge>
              ) : needsRefresh ? (
                <Badge variant="destructive">
                  <RefreshCw className="w-3 h-3 mr-1" />
                  Refresh Required
                </Badge>
              ) : (
                <>
                  <Badge variant="secondary" className="bg-green-100 text-green-800">
                    <Shield className="w-3 h-3 mr-1" />
                    Secure QR
                  </Badge>
                  <Badge variant="outline" className="text-xs">
                    Cycle {cycleCount + 1}/6
                  </Badge>
                </>
              )}
            </div>
          )}

          {/* QR Code */}
          <div className="relative">
            <div className={`bg-white p-3 rounded-lg border-2 ${isGenerating || needsRefresh || isCheckedIn ? 'opacity-50' : ''}`}>
              {qrData && !needsRefresh ? (
                <QRCodeSVG
                  value={qrData}
                  size={size}
                  level="M"
                  includeMargin={false}
                />
              ) : (
                <div
                  className="flex items-center justify-center bg-gray-100"
                  style={{ width: size, height: size }}
                >
                  <div className="text-gray-500 text-sm text-center">
                    {isCheckedIn ? "Checked In" : needsRefresh ? "Refresh Required" : "Generating..."}
                  </div>
                </div>
              )}
            </div>

            {/* Loading overlay */}
            {isGenerating && (
              <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 rounded-lg">
                <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
              </div>
            )}

            {/* Refresh button overlay */}
            {needsRefresh && !isCheckedIn && (
              <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 rounded-lg">
                <button
                  onClick={handleRefresh}
                  className="flex flex-col items-center space-y-2 p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                >
                  <RefreshCw className="w-6 h-6" />
                  <span className="text-sm font-medium">Refresh QR</span>
                </button>
              </div>
            )}
          </div>

          {/* Timer and Progress */}
          {showTimer && !isCheckedIn && (
            <div className="w-full max-w-xs space-y-1">
              {needsRefresh ? (
                <div className="text-center">
                  <div className="text-xs text-orange-600 font-medium">
                    6 cycles completed - Click refresh to continue
                  </div>
                </div>
              ) : isActive ? (
                <>
                  <div className="flex items-center justify-between text-xs text-gray-600">
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>Refreshes in</span>
                    </div>
                    <span className="font-mono font-medium">
                      {timeRemaining}s
                    </span>
                  </div>

                  <Progress
                    value={progressPercentage}
                    className="h-1.5"
                  />

                  <div className="text-center text-xs text-gray-500">
                    Cycle {cycleCount + 1} of 6
                  </div>
                </>
              ) : (
                <div className="text-center">
                  <div className="text-xs text-gray-500">
                    QR code inactive
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Instructions */}
          <p className="text-xs text-center text-gray-500 max-w-xs leading-tight">
            {isCheckedIn
              ? "Checked in - QR inactive"
              : needsRefresh
                ? "6 cycles complete - Click refresh"
                : "Updates every 30s for 6 cycles"
            }
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
