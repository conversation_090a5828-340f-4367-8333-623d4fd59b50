// Authentication system types

export interface User {
  id: string
  email: string
  full_name: string
  role_id: string
  created_at: string
  updated_at: string
  email_verified: boolean
  password_hash?: string
  user_roles?: {
    id: string
    role_name: string
    description: string
  }
}

export interface AuthSession {
  user: User
  token: string
  expires: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  full_name: string
  role_id?: string
}

export interface AuthResponse {
  success: boolean
  user?: User
  token?: string
  error?: string
  message?: string
}

export interface TokenPayload {
  userId: string
  email: string
  role: string
  iat: number
  exp: number
}

export interface AuthVerificationResult {
  isAuthenticated: boolean
  isAdmin: boolean
  isManager: boolean
  isElevated: boolean
  user?: User
  error?: string
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordResetData {
  token: string
  password: string
}

// Role-based access control types
export type UserRole = 'admin' | 'user' | 'manager' | 'supermanager' | 'event_admin'

export interface RolePermissions {
  canAccessAdmin: boolean
  canManageUsers: boolean
  canManageEvents: boolean
  canManagePayments: boolean
  canViewAnalytics: boolean
  canManageOrganizations: boolean
}

// NextAuth types
export interface NextAuthUser {
  id: string
  email: string
  name: string
  role: string
}

export interface NextAuthSession {
  user: NextAuthUser
  expires: string
}

export interface NextAuthToken {
  userId: string
  email: string
  name: string
  role: string
  iat: number
  exp: number
}
