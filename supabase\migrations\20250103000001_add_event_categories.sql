-- Create event_categories table if it doesn't exist
CREATE TABLE IF NOT EXISTS event_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  color TEXT DEFAULT '#6366f1',
  icon TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default categories
INSERT INTO event_categories (name, description, color, icon) VALUES
  ('conference', 'Professional conferences and conventions', '#3b82f6', 'users'),
  ('workshop', 'Hands-on learning workshops', '#10b981', 'wrench'),
  ('seminar', 'Educational seminars and lectures', '#8b5cf6', 'book-open'),
  ('webinar', 'Online webinars and virtual events', '#f59e0b', 'monitor'),
  ('training', 'Professional training sessions', '#ef4444', 'graduation-cap'),
  ('other', 'Other types of events', '#6b7280', 'calendar')
ON CONFLICT (name) DO NOTHING;

-- Add category_id column to events table if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'category_id') THEN
    ALTER TABLE events ADD COLUMN category_id UUID REFERENCES event_categories(id);
  END IF;
END $$;

-- Add is_featured column to events table if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'is_featured') THEN
    ALTER TABLE events ADD COLUMN is_featured BOOLEAN DEFAULT FALSE;
  END IF;
END $$;

-- Update existing events to link with categories based on their category text field
UPDATE events 
SET category_id = (
  SELECT id FROM event_categories 
  WHERE event_categories.name = events.category
)
WHERE category IS NOT NULL 
AND category_id IS NULL;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_events_category_id ON events(category_id);
CREATE INDEX IF NOT EXISTS idx_events_is_featured ON events(is_featured);
CREATE INDEX IF NOT EXISTS idx_event_categories_name ON event_categories(name);
CREATE INDEX IF NOT EXISTS idx_event_categories_is_active ON event_categories(is_active);

-- Add RLS policies for event_categories table
ALTER TABLE event_categories ENABLE ROW LEVEL SECURITY;

-- Allow public read access to active categories
CREATE POLICY "Enable public read access for active categories" 
ON event_categories FOR SELECT 
USING (is_active = true);

-- Allow authenticated users to read all categories
CREATE POLICY "Enable read access for authenticated users" 
ON event_categories FOR SELECT 
TO authenticated 
USING (true);

-- Allow admins to manage categories
CREATE POLICY "Enable all for admins" 
ON event_categories FOR ALL 
TO authenticated 
USING (
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'admin'
  )
);
