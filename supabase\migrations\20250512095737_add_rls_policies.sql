-- Enable Row Level Security on all tables
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
ALTER TABLE registrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE certificates ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_settings ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Enable public read access for published events" ON events;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON events;
DROP POLICY IF EXISTS "Enable update for event creators" ON events;
DROP POLICY IF EXISTS "Enable all for admins" ON events;

DROP POLICY IF EXISTS "Enable read access for own registrations" ON registrations;
DROP POLICY IF EXISTS "Enable insert for authenticated users" ON registrations;
DROP POLICY IF EXISTS "Enable read access for event organizers" ON registrations;

DROP POLICY IF EXISTS "Enable read access for own certificates" ON certificates;
DROP POLICY IF EXISTS "Enable public read access for verification" ON certificates;

DROP POLICY IF EXISTS "Enable all for admins only" ON system_settings;

-- Events table policies
CREATE POLICY "Enable public read access for published events" 
ON events FOR SELECT 
USING (status = 'published');

CREATE POLICY "Enable insert for authenticated users" 
ON events FOR INSERT 
TO authenticated 
WITH CHECK (true);

CREATE POLICY "Enable update for event creators"
ON events FOR UPDATE
USING (auth.uid() = created_by)
WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Enable all for admins"
ON events
USING (auth.role() = 'service_role' OR auth.role() = 'anon');

-- Registrations table policies
CREATE POLICY "Enable read access for own registrations" 
ON registrations FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Enable insert for authenticated users"
ON registrations FOR INSERT
TO authenticated
WITH CHECK (true);

CREATE POLICY "Enable read access for event organizers"
ON registrations FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM events 
    WHERE events.id = registrations.event_id 
    AND events.created_by = auth.uid()
  )
);

-- Certificates table policies
CREATE POLICY "Enable read access for own certificates"
ON certificates FOR SELECT
USING (auth.uid() = user_id);

CREATE POLICY "Enable public read access for verification"
ON certificates FOR SELECT
TO anon
USING (true);

-- System settings table policies
CREATE POLICY "Enable all for admins only"
ON system_settings
USING (auth.role() = 'service_role')
WITH CHECK (auth.role() = 'service_role');

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_events_created_by ON events(created_by);
CREATE INDEX IF NOT EXISTS idx_registrations_user_id ON registrations(user_id);
CREATE INDEX IF NOT EXISTS idx_registrations_event_id ON registrations(event_id);
CREATE INDEX IF NOT EXISTS idx_certificates_user_id ON certificates(user_id);
CREATE INDEX IF NOT EXISTS idx_certificates_verification_code ON certificates(verification_code);

-- Add foreign key constraint for registrations.user_id to auth.users.id
-- Note: This requires the uuid-ossp extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create a function to update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_events_updated_at
BEFORE UPDATE ON events
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Note: Add similar triggers for other tables if they have updated_at columns
