# Components Reference

This document provides a comprehensive reference for all components in the mTicket.my application.

## Layout Components

### Main Navigation (`components/main-nav.tsx`)
- **Purpose**: Primary navigation bar for public pages
- **Features**:
  - Responsive design
  - Authentication state awareness
  - Brand logo and menu items
  - Mobile hamburger menu
- **Usage**: Used in public pages layout

### Dashboard Sidebar (`components/dashboard/sidebar.tsx`)
- **Purpose**: Navigation sidebar for dashboard area
- **Features**:
  - Role-based menu filtering
  - Collapsible design
  - Active route highlighting
  - Admin-specific menu items
- **Access Control**: Role-based navigation items
- **Admin Items**: Users, Roles, Payment Gateways, Activity Logs, Webhooks

### Page Layout (`components/page-layout.tsx`)
- **Purpose**: Consistent layout wrapper for public pages
- **Features**:
  - Header and footer integration
  - Responsive container
  - SEO optimization
- **Usage**: Wraps all public pages

### Footer (`components/footer.tsx`)
- **Purpose**: Site footer with links and information
- **Features**:
  - Company information
  - Legal links
  - Social media links
  - Newsletter signup

## Authentication Components

### Auth Form Layout (`components/auth-form-layout.tsx`)
- **Purpose**: Consistent layout for authentication forms
- **Features**:
  - Centered card design
  - Brand integration
  - Responsive layout
  - Error handling display

### Login Form (`app/auth/login/page.tsx`)
- **Purpose**: User login functionality
- **Features**:
  - Email/password validation
  - Show/hide password toggle
  - Remember me option
  - Error handling
  - Redirect after login

### Register Form (`app/auth/register/page.tsx`)
- **Purpose**: User registration with subscription selection
- **Features**:
  - Form validation
  - Subscription plan selection
  - Password strength indicator
  - Terms acceptance
  - Email verification

## Dashboard Components

### Stats Card (`components/dashboard/stats-card.tsx`)
- **Purpose**: Display key metrics on dashboard
- **Features**:
  - Icon integration
  - Loading states
  - Percentage change indicators
  - Responsive design

### Mini Chart (`components/mini-chart.tsx`)
- **Purpose**: Small charts for dashboard analytics
- **Features**:
  - Multiple chart types (line, bar, doughnut)
  - Time range selection
  - Loading states
  - Dynamic data updates
- **Dependencies**: Chart.js integration

### Analytics Chart (`components/analytics-chart.tsx`)
- **Purpose**: Detailed charts for analytics pages
- **Features**:
  - Interactive charts
  - Multiple data series
  - Export functionality
  - Responsive design

## Event Components

### Event Card (`components/event-card.tsx`)
- **Purpose**: Display event information in card format
- **Features**:
  - Event image display
  - Category badges
  - Organizer information
  - Price display
  - Registration button
  - Responsive design

### Event Details (`components/event-details.tsx`)
- **Purpose**: Detailed event information display
- **Features**:
  - Full event description
  - Date and time formatting
  - Location information
  - Registration form integration
  - Social sharing

### QR Code Generator (`components/qr-code-generator.tsx`)
- **Purpose**: Generate QR codes for events
- **Features**:
  - Dynamic QR code generation
  - Customizable size and format
  - Download functionality
  - Error correction levels

### QR Scanner (`components/qr-scanner.tsx`)
- **Purpose**: Scan QR codes for attendance tracking
- **Features**:
  - Camera integration
  - Real-time scanning
  - Attendance marking
  - Error handling
  - **Secure QR Support**: Handles both legacy and secure time-based QR codes
- **Props**: `eventId`, `onAttendanceMarked`

### Dynamic QR Code (`components/dynamic-qr-code.tsx`)
- **Purpose**: Generate secure, time-based QR codes for tickets
- **Features**:
  - **Auto-refresh**: Updates every 30 seconds for 6 cycles automatically
  - **Manual refresh**: Requires user action after 6 cycles
  - **Check-in control**: Stops updating once user is checked in
  - **Security badges**: Visual indicators for secure QR codes and status
  - **Progress timer**: Shows time remaining and cycle count
  - **HMAC signatures**: Cryptographically signed tokens
  - **Error handling**: Graceful fallback for generation failures
- **Props**: `ticketData`, `size`, `showTimer`, `showSecurityBadge`, `isCheckedIn`
- **Security**: Time-based tokens with replay protection and cycle management

## Admin Components

### User Management (`components/admin/user-management.tsx`)
- **Purpose**: Admin interface for user management
- **Features**:
  - User listing with pagination
  - Role assignment
  - Search and filtering
  - Bulk operations
  - User creation/editing
- **Access Control**: Admin-only access (only "admin" role)

### Role Management (`components/admin/role-management.tsx`)
- **Purpose**: Admin interface for role management
- **Features**:
  - Role creation/editing
  - Permission assignment
  - Role deletion
  - Permission categories
- **Access Control**: Admin-only access (only "admin" role)
- **Role System**: Manages 5 database roles: admin, user, manager, supermanager, event_admin

### Settings Panel (`components/admin/settings-panel.tsx`)
- **Purpose**: System settings management
- **Features**:
  - Tabbed interface
  - Form validation
  - Real-time updates
  - Setting categories
- **Tabs**: Fees & Limits, Payment Gateways, Certificate Templates
- **Access Control**: Admin-only access (only "admin" role)

### Activity Log Viewer (`components/admin/activity-log-viewer.tsx`)
- **Purpose**: Display system activity logs
- **Features**:
  - Filterable log entries
  - Date range selection
  - User filtering
  - Export functionality
- **Access Control**: Admin-only access (only "admin" role)

## Certificate Components

### Certificate Template Editor (`components/certificates/template-editor.tsx`)
- **Purpose**: Visual editor for certificate templates
- **Features**:
  - Drag-drop field positioning
  - Template preview
  - Field customization
  - Logo upload
  - Font selection

### Certificate Viewer (`components/certificates/certificate-viewer.tsx`)
- **Purpose**: Display generated certificates
- **Features**:
  - PDF rendering
  - QR code integration
  - Print functionality
  - Download options

### Certificate Verification (`components/certificates/verification.tsx`)
- **Purpose**: Verify certificate authenticity
- **Features**:
  - QR code scanning
  - Database verification
  - Certificate details display
  - Fraud detection

## Form Components

### Event Form (`components/forms/event-form.tsx`)
- **Purpose**: Create/edit event form
- **Features**:
  - Multi-step form
  - Image upload
  - Date/time pickers
  - Category selection
  - Validation
  - Draft saving

### Registration Form (`components/forms/registration-form.tsx`)
- **Purpose**: Event registration form
- **Features**:
  - Attendee information collection
  - Payment integration
  - Ticket type selection
  - Validation
  - Confirmation

### Ticket Selection Page (`app/events/[slug]/tickets/page.tsx`)
- **Purpose**: ✅ **PRODUCTION-READY** Dedicated ticket selection page with multi-step flow
- **Features**:
  - ✅ **Multi-Step Process**: Selection → Review → Registration with progress tracking
  - ✅ **Professional Page Layout**: Full-page experience with better UX than modal
  - ✅ **Desktop Sidebar Cart**: Sticky cart with real-time updates and order summary
  - ✅ **Mobile Fixed Bottom Cart**: Optimized mobile experience with fixed bottom bar
  - ✅ **Multiple Ticket Types**: Early Bird (20% off), Standard (base price), VIP (50% premium)
  - ✅ **Dynamic Quantity Selection**: Large, touch-friendly +/- controls
  - ✅ **Real-time Price Calculation**: Live total updates with detailed breakdown
  - ✅ **Progress Indicator**: Visual progress bar showing current step
  - ✅ **Responsive Design**: Mobile-first approach with adaptive layouts
  - ✅ **Shopping Cart Features**: Add/remove items, quantity adjustment, clear cart
  - ✅ **Loading States**: Professional loading animations and disabled states
  - ✅ **Visual Feedback**: Hover effects, animations, and state indicators
  - ✅ **Popular Ticket Highlighting**: Gradient badges with star icons
  - ✅ **Feature Comparison**: Badge-based feature display for each ticket type
  - ✅ **Session Storage Integration**: Seamless data transfer to registration
  - ✅ **Free Event Support**: Single ticket type for free events
  - ✅ **Error Handling**: Graceful validation and user feedback with toasts
  - ✅ **Back Navigation**: Proper navigation flow with back buttons
  - ✅ **Event Information**: Comprehensive event details display
- **Route**: `/events/[slug]/tickets`
- **Types**: `TicketType`, `SelectedTicket` interfaces for type safety
- **Integration**: Works with registration page via session storage
- **Pricing Logic**: Automatic Early Bird (20% off), Standard (base price), VIP (50% premium)
- **Mobile Features**: Fixed bottom cart, touch-optimized controls, responsive grid
- **Desktop Features**: Sidebar cart, detailed order summary, professional layout
- **Steps**: 1. Selection (choose tickets) → 2. Review (confirm order) → 3. Registration

### Payment Form (`components/forms/payment-form.tsx`)
- **Purpose**: Payment processing form
- **Features**:
  - Multiple payment gateways
  - Secure card input
  - Validation
  - Error handling
  - Receipt generation

## Profile Components

### ProfileTab (`components/profile/profile-tab.tsx`)
- **Purpose**: Personal information management
- **Features**:
  - Profile image upload with compression
  - Personal details editing (name, phone, bio)
  - Account information display (role, subscription)
  - Image preview and validation
- **Props**: `profileData`, `setProfileData`, `isSubmitting`, `setIsSubmitting`

### OrganizationTab (`components/profile/organization-tab.tsx`)
- **Purpose**: Comprehensive organization management and linking
- **Features**:
  - ✅ Real-time search and select existing organizations
  - ✅ Create new organizations with full details (SSM, PIC info, address, website)
  - ✅ Edit existing organization information with permission validation
  - ✅ Link/unlink organizations to user profile safely
  - ✅ Visual status indicators (Linked/Selected with badges)
  - ✅ Permission-based editing (creator, admin, manager roles only)
  - ✅ Duplicate prevention and validation
  - ✅ Activity logging for all operations
- **Props**: `organizationData`, `setOrganizationData`, `isSubmitting`, `setIsSubmitting`, `isLoadingOrganization`, `initialSelectedOrganization`
- **States**:
  - Create new organization mode
  - Edit existing organization mode
  - Link/unlink functionality
  - Real-time organization search with debouncing
  - Loading states for better UX
- **API Integration**: Uses organization CRUD endpoints with proper error handling

### SubscriptionTab (`components/profile/subscription-tab.tsx`)
- **Purpose**: Subscription management interface
- **Features**:
  - Current subscription display
  - Plan upgrade/downgrade
  - Billing information
  - Usage statistics
- **Props**: None (uses auth context)

### NotificationsTab (`components/profile/notifications-tab.tsx`)
- **Purpose**: Notification preferences management
- **Features**:
  - Email notification settings
  - Push notification preferences
  - Event-specific notifications
  - Frequency controls
- **Props**: None

### WebhookTab (`components/profile/webhook-tab.tsx`)
- **Purpose**: API and webhook management
- **Features**:
  - Webhook creation and management
  - API key generation
  - Documentation access
  - Event subscription management
- **Props**: None

## UI Components (Shadcn/ui)

### Core Components
- **Button** (`components/ui/button.tsx`): Customizable button component
- **Input** (`components/ui/input.tsx`): Form input with validation
- **Card** (`components/ui/card.tsx`): Container component
- **Dialog** (`components/ui/dialog.tsx`): Modal dialogs
- **Table** (`components/ui/table.tsx`): Data tables
- **Tabs** (`components/ui/tabs.tsx`): Tabbed interfaces
- **Badge** (`components/ui/badge.tsx`): Status indicators
- **Alert** (`components/ui/alert.tsx`): Notification messages

### Form Components
- **Form** (`components/ui/form.tsx`): Form wrapper with validation
- **Label** (`components/ui/label.tsx`): Form labels
- **Select** (`components/ui/select.tsx`): Dropdown selection
- **Checkbox** (`components/ui/checkbox.tsx`): Checkbox input
- **Radio Group** (`components/ui/radio-group.tsx`): Radio button groups
- **Switch** (`components/ui/switch.tsx`): Toggle switches

### Navigation Components
- **Navigation Menu** (`components/ui/navigation-menu.tsx`): Complex navigation
- **Breadcrumb** (`components/ui/breadcrumb.tsx`): Breadcrumb navigation
- **Pagination** (`components/ui/pagination.tsx`): Page navigation

### Feedback Components
- **Toast** (`components/ui/toast.tsx`): Notification toasts
- **Tooltip** (`components/ui/tooltip.tsx`): Hover tooltips
- **Progress** (`components/ui/progress.tsx`): Progress indicators
- **Skeleton** (`components/ui/skeleton.tsx`): Loading skeletons

## Utility Components

### Theme Provider (`components/theme-provider.tsx`)
- **Purpose**: Dark/light theme management
- **Features**:
  - Theme switching
  - System preference detection
  - Persistent theme storage

### Loading Spinner (`components/loading-spinner.tsx`)
- **Purpose**: Loading state indicator
- **Features**:
  - Customizable size
  - Animation options
  - Accessible design

### Error Boundary (`components/error-boundary.tsx`)
- **Purpose**: Error handling wrapper
- **Features**:
  - Error catching
  - Fallback UI
  - Error reporting
  - Recovery options

### Search Component (`components/search.tsx`)
- **Purpose**: Search functionality
- **Features**:
  - Real-time search
  - Debounced input
  - Search suggestions
  - Filter integration

## Data Display Components

### Data Table (`components/data-table.tsx`)
- **Purpose**: Advanced data table with features
- **Features**:
  - Sorting
  - Filtering
  - Pagination
  - Column customization
  - Export functionality

### Metric Card (`components/metric-card.tsx`)
- **Purpose**: Display key performance metrics
- **Features**:
  - Trend indicators
  - Comparison values
  - Visual charts
  - Color coding

### Status Badge (`components/status-badge.tsx`)
- **Purpose**: Display status information
- **Features**:
  - Color-coded statuses
  - Icon integration
  - Tooltip descriptions
  - Animation effects

## Security Components

### DynamicQRCode (`components/dynamic-qr-code.tsx`)
- **Purpose**: ✅ Advanced secure QR code generation with time-based tokens
- **Features**:
  - ✅ Time-based secure token generation (30-second cycles)
  - ✅ HMAC signature validation for anti-forgery
  - ✅ 6-cycle automatic refresh with manual refresh after
  - ✅ Real-time countdown timer display
  - ✅ Security badge indicators
  - ✅ Check-in status management (stops updating when checked in)
  - ✅ Loading states and error handling
  - ✅ Replay protection with unique nonces
- **Props**: `ticketData`, `size`, `className`, `showTimer`, `showSecurityBadge`, `isCheckedIn`
- **Security**: Uses `/api/tickets/secure-qr` endpoint for token generation
- **States**: QR data, time remaining, cycle count, refresh status, loading states

## Integration Components

### Payment Gateway (`components/payment-gateway.tsx`)
- **Purpose**: Payment processing integration
- **Features**:
  - Multiple gateway support
  - Secure payment forms
  - Transaction handling
  - Receipt generation

### Email Template (`components/email-template.tsx`)
- **Purpose**: Email template rendering
- **Features**:
  - Dynamic content
  - Responsive design
  - Brand consistency
  - Personalization

### Webhook Manager (`components/webhook-manager.tsx`)
- **Purpose**: Webhook configuration interface
- **Features**:
  - Webhook creation/editing
  - Event selection
  - URL validation
  - Testing functionality

## Component Usage Guidelines

### Naming Conventions
- Use PascalCase for component names
- Use kebab-case for file names
- Include component type in name (e.g., `UserCard`, `EventForm`)

### Props Interface
- Define TypeScript interfaces for all props
- Use optional props with default values
- Include JSDoc comments for complex props

### Accessibility
- Include ARIA labels and roles
- Ensure keyboard navigation
- Provide screen reader support
- Use semantic HTML elements

### Performance
- Use React.memo for expensive components
- Implement lazy loading for large components
- Optimize re-renders with useMemo and useCallback
- Use dynamic imports for code splitting

### Testing
- Write unit tests for all components
- Include accessibility tests
- Test user interactions
- Mock external dependencies
