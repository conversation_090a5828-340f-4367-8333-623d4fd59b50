import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"
import { getUserFromToken } from "@/lib/auth/token"
import { getUserSubscriptionPlan } from "@/lib/subscription-utils"

export async function GET(request: NextRequest) {
  try {
    // Get user from token
    const userToken = await getUserFromToken(request)
    if (!userToken) {
      return NextResponse.json(
        {
          success: false,
          error: "Authentication required",
        },
        { status: 401 }
      )
    }

    const supabase = createClient()

    // Get user details with subscription info
    const { data: user, error: userError } = await supabase
      .from('users')
      .select(`
        *,
        user_subscription (
          subscription_type,
          start_date,
          end_date,
          is_active
        )
      `)
      .eq('id', userToken.userId)
      .single()

    if (userError || !user) {
      console.error("Error fetching user:", userError)
      return NextResponse.json(
        {
          success: false,
          error: "User not found",
        },
        { status: 404 }
      )
    }

    // Get subscription plan details using utility function
    const planDetails = await getUserSubscriptionPlan(userToken.userId, user.role_name)

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        role_name: user.role_name,
        subscription_status: user.subscription_status,
        subscription_end_date: user.subscription_end_date,
      },
      subscription: user.user_subscription?.[0] || null,
      plan: planDetails,
    })

  } catch (error) {
    console.error("Error fetching current subscription:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    )
  }
}
