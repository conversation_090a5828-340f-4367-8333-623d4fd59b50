"use client";

import React, { createContext, useContext, useState, useEffect, type ReactNode } from "react";
import { useToast } from "@/hooks/use-toast";
import { logActivity, ActivityCategory } from "@/utils/activity-logger";

// Types
export type UserType = {
  id: string;
  email: string;
  full_name: string;
  role_id: string | null;
  role_name: string | null;
  subscription_status: "none" | "active" | "canceled" | "expired";
  subscription_end_date: string | null;
  created_at: string;
  organization: string | null;
  organization_id: string | null;
  profile_image_url: string | null;
  phone: string | null;
  bio: string | null;
  events_created: number;
  total_earnings: number;
  available_balance: number;
  profile_updated_at?: string; // Timestamp for profile updates to force re-renders
};

type AuthContextType = {
  user: UserType | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<UserType | null>;
  logout: () => Promise<void>;
  resetPassword: (email: string) => Promise<boolean>;
  updateProfile: (data: Partial<UserType>) => Promise<UserType | null>;
  isAdmin: () => boolean;
  isManager: () => boolean;
  refreshUser: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<UserType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Check for existing session on mount
  useEffect(() => {
    checkExistingSession();
  }, []);

  // Helper function to get cookie value
  const getCookie = (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  // Helper function to set cookie
  const setCookie = (name: string, value: string, days: number = 7) => {
    if (typeof document === 'undefined') return;
    const expires = new Date(Date.now() + days * 24 * 60 * 60 * 1000).toUTCString();
    const isProduction = process.env.NODE_ENV === 'production';
    const domain = isProduction ? process.env.COOKIE_DOMAIN || 'mticket.my' : '';
    const secure = isProduction ? '; Secure' : '';
    const domainPart = domain ? `; Domain=${domain}` : '';

    document.cookie = `${name}=${value}; expires=${expires}; path=/; SameSite=Lax${secure}${domainPart}`;
  };

  // Helper function to delete cookie
  const deleteCookie = (name: string) => {
    if (typeof document === 'undefined') return;
    const isProduction = process.env.NODE_ENV === 'production';
    const domain = isProduction ? process.env.COOKIE_DOMAIN || 'mticket.my' : '';
    const domainPart = domain ? `; Domain=${domain}` : '';

    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/${domainPart};`;
  };

  // Check if user is already logged in
  const checkExistingSession = async () => {
    console.log("Auth context: checkExistingSession called");
    try {
      const token = getCookie('auth_token');
      console.log("Auth context: Token found:", !!token);
      if (!token) {
        setLoading(false);
        return;
      }

      console.log("Auth context: Calling verify API");
      // Verify token and get user data
      const response = await fetch('/api/auth/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      console.log("Auth context: Verify API response status:", response.status);
      if (response.ok) {
        try {
          const userData = await response.json();
          console.log("Auth context: User data from verify:", userData.user);
          setUser(userData.user);
        } catch (jsonError) {
          console.error("Auth context: Error parsing JSON response:", jsonError);
          deleteCookie('auth_token');
        }
      } else {
        // Invalid token, remove it
        console.log("Auth context: Invalid token, removing");
        deleteCookie('auth_token');
      }
    } catch (err) {
      console.error('Error checking existing session:', err);
      deleteCookie('auth_token');
    } finally {
      setLoading(false);
    }
  };

  // Refresh user data
  const refreshUser = async (): Promise<void> => {
    try {
      const token = getCookie('auth_token');
      if (!token) {
        setUser(null);
        return;
      }

      const response = await fetch('/api/auth/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      if (response.ok) {
        try {
          const userData = await response.json();
          setUser(userData.user);
        } catch (jsonError) {
          console.error("Error parsing JSON response in refreshUser:", jsonError);
          deleteCookie('auth_token');
          setUser(null);
        }
      } else {
        deleteCookie('auth_token');
        setUser(null);
      }
    } catch (err) {
      console.error('Error refreshing user:', err);
      setUser(null);
    }
  };

  // Login user
  const login = async (email: string, password: string): Promise<UserType | null> => {
    console.log("Auth context: login function called with email:", email);
    setError(null);
    setLoading(true);

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.error || "Login failed");
        toast({
          title: "Login failed",
          description: data.error || "Invalid email or password",
          variant: "destructive",
        });
        return null;
      }

      // Store the token in cookie
      setCookie('auth_token', data.token, 7); // 7 days expiry

      // Set user data
      console.log("Auth context: Setting user from login:", data.user);
      setUser(data.user);

      // Log the login activity
      try {
        await logActivity({
          userId: data.user.id,
          action: "login",
          entityType: "user",
          entityId: data.user.id,
          category: ActivityCategory.AUTH,
          details: {
            email: data.user.email,
            role_name: data.user.role_name,
          },
        });
      } catch (logError) {
        console.error("Error logging login activity:", logError);
      }

      toast({
        title: "Success",
        description: "Logged in successfully",
      });

      return data.user;
    } catch (err: any) {
      console.error("Error logging in:", err);
      setError("An unexpected error occurred during login");
      toast({
        title: "Error",
        description: "Failed to log in",
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  };

  // Logout user
  const logout = async (): Promise<void> => {
    console.log("Logging out user");

    // Log the logout activity if we have a user
    if (user?.id) {
      try {
        await logActivity({
          userId: user.id,
          action: "logout",
          entityType: "user",
          entityId: user.id,
          category: ActivityCategory.AUTH,
          details: {
            email: user.email,
            role_name: user.role_name,
          },
        });
      } catch (logError) {
        // Don't fail logout if logging fails
        console.error("Error logging logout activity:", logError);
      }
    }

    try {
      // Call logout API to invalidate token on server
      const token = getCookie('auth_token');
      if (token) {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
        });
      }

      // Clear cookie
      deleteCookie('auth_token');

      // Clear user state
      setUser(null);
      setError(null);

      toast({
        title: "Logged out",
        description: "You have been logged out successfully",
      });

      console.log("Logout complete");
    } catch (err) {
      console.error("Error during logout:", err);
      // Still clear local state even if server logout fails
      deleteCookie('auth_token');
      setUser(null);
      setError(null);

      toast({
        title: "Logged out",
        description: "You have been logged out successfully",
      });
    }
  };

  // Reset password
  const resetPassword = async (email: string): Promise<boolean> => {
    setError(null);

    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.error || "Reset password failed");
        toast({
          title: "Reset failed",
          description: data.error || "Failed to send reset email",
          variant: "destructive",
        });
        return false;
      }

      toast({
        title: "Reset email sent",
        description: "Check your email for password reset instructions",
      });

      return true;
    } catch (err: any) {
      console.error("Error resetting password:", err);
      setError("An unexpected error occurred");
      toast({
        title: "Error",
        description: "Failed to send reset email",
        variant: "destructive",
      });
      return false;
    }
  };

  // Update user profile
  const updateProfile = async (data: Partial<UserType>): Promise<UserType | null> => {
    if (!user?.id) return null;

    try {
      const token = getCookie('auth_token');
      if (!token) {
        setError("Not authenticated");
        return null;
      }

      const response = await fetch('/api/auth/update-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      });

      const responseData = await response.json();

      if (!response.ok) {
        setError(responseData.error || "Update failed");
        toast({
          title: "Error",
          description: responseData.error || "Failed to update profile",
          variant: "destructive",
        });
        return null;
      }

      const updatedUser = {
        ...user,
        ...responseData.user,
        profile_updated_at: new Date().toISOString() // Add timestamp to force re-renders
      };
      // Log profile update for debugging (can be removed in production)
      console.log("Profile updated - new image URL:", updatedUser.profile_image_url);
      setUser(updatedUser);

      // Log the profile update activity
      try {
        await logActivity({
          userId: user.id,
          action: "update_profile",
          entityType: "user",
          entityId: user.id,
          category: ActivityCategory.USER,
          details: {
            fields_updated: Object.keys(data),
          },
        });
      } catch (logError) {
        console.error("Error logging profile update activity:", logError);
      }

      toast({
        title: "Profile updated",
        description: "Your profile has been updated successfully",
      });

      return updatedUser;
    } catch (err: any) {
      console.error("Error updating profile:", err);
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive",
      });
      return null;
    }
  };

  // Check if user is admin (only "admin" role has full access)
  const isAdmin = (): boolean => {
    return user?.role_name === "admin";
  };

  // Check if user is manager (includes manager roles, elevated admin roles, and full admin)
  const isManager = (): boolean => {
    const managerRoles = ["manager", "super_admin", "supermanager", "event_admin", "admin"];
    return managerRoles.includes(user?.role_name || "");
  };

  const value = {
    user,
    loading,
    error,
    login,
    logout,
    resetPassword,
    updateProfile,
    isAdmin,
    isManager,
    refreshUser,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
