"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { Co<PERSON>, Plus, Edit, Trash2, Eye, EyeOff, AlertCircle, Webhook, Key, FileText } from "lucide-react"
import { isAdminUser } from "@/lib/role-utils"

type WebhookType = {
  id: string
  name: string
  url: string
  events: string[]
  active: boolean
  created_at: string
  last_triggered_at?: string
  success_count: number
  failure_count: number
}

export function WebhookTab() {
  const [webhooks, setWebhooks] = useState<WebhookType[]>([])
  const [apiKey, setApiKey] = useState("")
  const [loading, setLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedWebhook, setSelectedWebhook] = useState<WebhookType | null>(null)
  const [showApiKey, setShowApiKey] = useState(false)
  const [hasWebhookAccess, setHasWebhookAccess] = useState(false)
  const { user } = useAuth()
  const { toast } = useToast()

  // Check if user has webhook access based on subscription
  useEffect(() => {
    checkWebhookAccess()
  }, [user])

  const checkWebhookAccess = async () => {
    if (!user) {
      setHasWebhookAccess(false)
      return
    }

    // Admin users always have access to all features
    if (isAdminUser(user.role_name)) {
      setHasWebhookAccess(true)
      return
    }

    try {
      // Get user's subscription plan details for non-admin users
      const response = await fetch('/api/subscriptions/current')
      if (response.ok) {
        const data = await response.json()
        setHasWebhookAccess(data.plan?.webhooks_enabled || false)
      } else {
        setHasWebhookAccess(false)
      }
    } catch (error) {
      console.error('Error checking webhook access:', error)
      setHasWebhookAccess(false)
    }
  }

  useEffect(() => {
    if (hasWebhookAccess) {
      fetchWebhooks()
      fetchApiKey()
    }
    setLoading(false)
  }, [hasWebhookAccess])

  const fetchWebhooks = async () => {
    try {
      const response = await fetch('/api/webhooks/manage')
      if (response.ok) {
        const data = await response.json()
        setWebhooks(data.webhooks || [])
      }
    } catch (error) {
      console.error('Error fetching webhooks:', error)
      toast({
        title: "Error",
        description: "Failed to fetch webhooks",
        variant: "destructive",
      })
    }
  }

  const fetchApiKey = async () => {
    try {
      const response = await fetch('/api/webhooks/api-key')
      if (response.ok) {
        const data = await response.json()
        setApiKey(data.apiKey || '')
      }
    } catch (error) {
      console.error('Error fetching API key:', error)
    }
  }

  const handleCopyApiKey = () => {
    navigator.clipboard.writeText(apiKey)
    toast({
      title: "Copied",
      description: "API key copied to clipboard",
    })
  }

  const regenerateApiKey = async () => {
    try {
      const response = await fetch('/api/webhooks/api-key', {
        method: 'POST',
      })
      if (response.ok) {
        const data = await response.json()
        setApiKey(data.apiKey)
        toast({
          title: "Success",
          description: "API key regenerated successfully",
        })
      }
    } catch (error) {
      console.error('Error regenerating API key:', error)
      toast({
        title: "Error",
        description: "Failed to regenerate API key",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex h-40 items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
        </CardContent>
      </Card>
    )
  }

  if (!hasWebhookAccess) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Webhook className="h-5 w-5" />
            API & Webhooks
          </CardTitle>
          <CardDescription>
            Manage API access and webhook integrations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertCircle className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">Webhook Access Required</h3>
            <p className="text-muted-foreground mb-4 max-w-md">
              Webhooks and API access are only available with certain subscription plans.
              Please upgrade your subscription to access these features.
            </p>
            <Button onClick={() => window.location.href = '/pricing'}>
              View Subscription Plans
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Webhook className="h-5 w-5" />
          API & Webhooks
        </CardTitle>
        <CardDescription>
          Manage API access and webhook integrations
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="webhooks">
          <TabsList>
            <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
            <TabsTrigger value="api">API Keys</TabsTrigger>
            <TabsTrigger value="docs">Documentation</TabsTrigger>
          </TabsList>

          <TabsContent value="webhooks" className="mt-6 space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-semibold">Webhooks</h3>
                <p className="text-sm text-muted-foreground">
                  Webhooks allow external services to be notified when certain events happen in your account.
                </p>
              </div>
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Webhook
              </Button>
            </div>

            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>URL</TableHead>
                  <TableHead>Events</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Triggered</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {webhooks.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      No webhooks configured
                    </TableCell>
                  </TableRow>
                ) : (
                  webhooks.map((webhook) => (
                    <TableRow key={webhook.id}>
                      <TableCell className="font-medium">{webhook.name}</TableCell>
                      <TableCell className="max-w-xs truncate">{webhook.url}</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {webhook.events.slice(0, 2).map((event) => (
                            <Badge key={event} variant="secondary" className="text-xs">
                              {event}
                            </Badge>
                          ))}
                          {webhook.events.length > 2 && (
                            <Badge variant="outline" className="text-xs">
                              +{webhook.events.length - 2} more
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={webhook.active ? "default" : "secondary"}>
                          {webhook.active ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {webhook.last_triggered_at
                          ? new Date(webhook.last_triggered_at).toLocaleDateString()
                          : "Never"}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setSelectedWebhook(webhook)
                              setIsEditDialogOpen(true)
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TabsContent>

          <TabsContent value="api" className="mt-6">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold">API Keys</h3>
                <p className="text-sm text-muted-foreground">
                  Your API key provides access to the mTicket.my API. Keep it secure and never share it publicly.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="api-key">API Key</Label>
                <div className="flex">
                  <Input
                    id="api-key"
                    value={apiKey}
                    readOnly
                    type={showApiKey ? "text" : "password"}
                    className="rounded-r-none"
                  />
                  <Button
                    variant="outline"
                    className="rounded-none"
                    onClick={() => setShowApiKey(!showApiKey)}
                  >
                    {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                  <Button variant="outline" className="rounded-l-none" onClick={handleCopyApiKey}>
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <Button variant="destructive" onClick={regenerateApiKey}>
                <Key className="h-4 w-4 mr-2" />
                Regenerate API Key
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="docs" className="mt-6">
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold">API Documentation</h3>
                <p className="text-sm text-muted-foreground">
                  Learn how to integrate with the mTicket.my API and set up webhooks.
                </p>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">API Reference</CardTitle>
                    <CardDescription>
                      Complete API documentation with examples
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button variant="outline" className="w-full">
                      <FileText className="h-4 w-4 mr-2" />
                      View API Docs
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Webhook Guide</CardTitle>
                    <CardDescription>
                      Learn how to set up and use webhooks
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button variant="outline" className="w-full">
                      <Webhook className="h-4 w-4 mr-2" />
                      Webhook Guide
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
