import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"

/**
 * GET /api/organizations
 * Fetches organizations with optional search functionality
 * This is a public endpoint for searching organizations
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const search = searchParams.get("search")
    const ssmNumber = searchParams.get("ssm_number")
    const limit = parseInt(searchParams.get("limit") || "10")

    console.log("Organizations API: Fetching organizations", { search, ssmNumber, limit })

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin()

    let query = supabaseAdmin
      .from("organizations")
      .select("id, name, ssm_number, pic_name, pic_email, website, created_at")
      .order("name", { ascending: true })
      .limit(limit)

    // Add search filters if provided
    if (search && search.trim()) {
      query = query.ilike("name", `%${search.trim()}%`)
    }

    if (ssmNumber && ssmNumber.trim()) {
      query = query.ilike("ssm_number", `%${ssmNumber.trim()}%`)
    }

    const { data, error } = await query

    if (error) {
      console.error("Error fetching organizations:", error)
      return NextResponse.json(
        { error: "Failed to fetch organizations" },
        { status: 500 }
      )
    }

    console.log(`Organizations API: Successfully fetched ${data?.length || 0} organizations`)

    return NextResponse.json({
      organizations: data || [],
      count: data?.length || 0
    })
  } catch (error: any) {
    console.error("Error in organizations API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
