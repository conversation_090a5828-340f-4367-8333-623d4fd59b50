import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { verifyJWTToken } from "@/lib/auth"

/**
 * GET /api/dashboard/stats
 * Fetches dashboard statistics for events owned by the authenticated user
 * Requires authentication
 */
export async function GET(request: Request) {
  try {
    console.log("Dashboard Stats API: Starting request")

    // Get the authorization header
    const authHeader = request.headers.get("Authorization")
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid Authorization header" },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7) // Remove "Bearer " prefix

    // Verify the JWT token and get user data
    const authResult = await verifyJWTToken(token)
    if (!authResult || !authResult.user) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 }
      )
    }

    const userId = authResult.user.id
    console.log("Dashboard Stats API: Fetching stats for user:", userId)

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin()

    // Get total events created by user
    const { count: eventsCount, error: eventsError } = await supabaseAdmin
      .from("events")
      .select("*", { count: "exact", head: true })
      .eq("event_manager_id", userId)

    if (eventsError) {
      console.error("Error fetching events count:", eventsError)
      throw eventsError
    }

    // Get total registrations for user's events
    const { data: registrationsData, error: registrationsError } = await supabaseAdmin
      .from("registrations")
      .select(`
        id,
        checked_in,
        payment_amount,
        payment_status,
        events!inner(event_manager_id)
      `)
      .eq("events.event_manager_id", userId)

    if (registrationsError) {
      console.error("Error fetching registrations:", registrationsError)
      throw registrationsError
    }

    const totalRegistrations = registrationsData ? registrationsData.length : 0
    const checkedInRegistrations = registrationsData 
      ? registrationsData.filter((reg) => reg.checked_in).length 
      : 0
    const attendanceRate = totalRegistrations > 0 
      ? (checkedInRegistrations / totalRegistrations) * 100 
      : 0

    // Calculate total revenue from user's events
    const totalRevenue = registrationsData
      ? registrationsData
          .filter(reg => reg.payment_status === "completed")
          .reduce((sum, reg) => sum + (reg.payment_amount || 0), 0)
      : 0

    // For withdrawals, we'll need to check financial_transactions table
    // For now, we'll use a placeholder or calculate based on available data
    const { data: withdrawalData, error: withdrawalError } = await supabaseAdmin
      .from("financial_transactions")
      .select("amount")
      .eq("transaction_type", "withdrawal")
      .eq("status", "completed")
      .eq("user_id", userId) // Assuming financial_transactions has user_id

    const totalWithdrawals = withdrawalData 
      ? withdrawalData.reduce((sum, item) => sum + (item.amount || 0), 0) 
      : 0

    const stats = {
      totalEvents: eventsCount || 0,
      totalRegistrations,
      totalRevenue,
      totalWithdrawals,
      attendanceRate,
    }

    console.log("Dashboard Stats API: Successfully calculated stats:", stats)

    return NextResponse.json({
      stats,
      success: true
    })
  } catch (error: any) {
    console.error("Error in dashboard stats API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
