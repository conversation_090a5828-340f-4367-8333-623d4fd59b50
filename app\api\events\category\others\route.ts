import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"

/**
 * GET /api/events/category/others
 * Fetches all published events without a category (category_id is null)
 * This is a public endpoint - no authentication required
 */
export async function GET(request: Request) {
  try {
    console.log("Events by Others Category API: Fetching events without category")

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin()

    // Fetch all published events without a category
    const { data, error } = await supabaseAdmin
      .from("events")
      .select(`
        *,
        organizations:organization_id(id, name),
        event_category:category_id(id, name, color, icon)
      `)
      .is("category_id", null)
      .eq("is_published", true)
      .order("start_date", { ascending: true })

    if (error) {
      console.error("Error fetching events without category:", error)
      return NextResponse.json(
        { error: "Failed to fetch events" },
        { status: 500 }
      )
    }

    console.log(`Events by Others Category API: Successfully fetched ${data?.length || 0} events`)

    return NextResponse.json({
      events: data || [],
      count: data?.length || 0,
      category: {
        id: "others",
        name: "Others",
        color: "#6b7280"
      }
    })
  } catch (error) {
    console.error("Unexpected error in events by others category API:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
