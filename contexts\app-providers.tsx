"use client"

import React from "react"
import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/contexts/auth-context"
import { SettingsProvider } from "@/contexts/settings-context"
import { EventProvider } from "@/contexts/event-context"
import { RegistrationProvider } from "@/contexts/registration-context"
import { CertificateProvider } from "@/contexts/certificate-context"
import { FinancialProvider } from "@/contexts/financial-context"
import { Toaster } from "@/components/ui/toaster"

/**
 * Optimized app providers component that combines all context providers
 * This reduces the nesting depth and improves performance
 */
export function AppProviders({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider attribute="class" defaultTheme="light" disableTransitionOnChange>
      <AuthProvider>
        <SettingsProvider>
          <EventProvider>
            <RegistrationProvider>
              <CertificateProvider>
                <FinancialProvider>
                  {children}
                  <Toaster />
                </FinancialProvider>
              </CertificateProvider>
            </RegistrationProvider>
          </EventProvider>
        </SettingsProvider>
      </AuthProvider>
    </ThemeProvider>
  )
}

/**
 * Alternative optimized version using React.memo for better performance
 * This version memoizes each provider to prevent unnecessary re-renders
 */
const MemoizedThemeProvider = React.memo(ThemeProvider)
const MemoizedAuthProvider = React.memo(AuthProvider)
const MemoizedSettingsProvider = React.memo(SettingsProvider)
const MemoizedEventProvider = React.memo(EventProvider)
const MemoizedRegistrationProvider = React.memo(RegistrationProvider)
const MemoizedCertificateProvider = React.memo(CertificateProvider)
const MemoizedFinancialProvider = React.memo(FinancialProvider)

export function OptimizedAppProviders({ children }: { children: React.ReactNode }) {
  return (
    <MemoizedThemeProvider attribute="class" defaultTheme="light" disableTransitionOnChange>
      <MemoizedAuthProvider>
        <MemoizedSettingsProvider>
          <MemoizedEventProvider>
            <MemoizedRegistrationProvider>
              <MemoizedCertificateProvider>
                <MemoizedFinancialProvider>
                  {children}
                  <Toaster />
                </MemoizedFinancialProvider>
              </MemoizedCertificateProvider>
            </MemoizedRegistrationProvider>
          </MemoizedEventProvider>
        </MemoizedSettingsProvider>
      </MemoizedAuthProvider>
    </MemoizedThemeProvider>
  )
}

/**
 * Future optimization: Combined context provider
 * This would be the most optimal solution but requires refactoring all contexts
 */
interface CombinedContextValue {
  auth: any
  settings: any
  events: any
  registrations: any
  certificates: any
  financial: any
}

const CombinedContext = React.createContext<CombinedContextValue | undefined>(undefined)

export function useCombinedContext() {
  const context = React.useContext(CombinedContext)
  if (context === undefined) {
    throw new Error('useCombinedContext must be used within a CombinedProvider')
  }
  return context
}

// This would be implemented in the future to replace all individual contexts
export function CombinedProvider({ children }: { children: React.ReactNode }) {
  // TODO: Implement combined state management
  // This would combine all context states into a single provider
  // reducing the component tree depth and improving performance
  
  const value: CombinedContextValue = {
    auth: {},
    settings: {},
    events: {},
    registrations: {},
    certificates: {},
    financial: {}
  }

  return (
    <CombinedContext.Provider value={value}>
      {children}
    </CombinedContext.Provider>
  )
}
