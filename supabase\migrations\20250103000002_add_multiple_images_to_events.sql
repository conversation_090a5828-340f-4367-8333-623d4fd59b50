-- Add support for multiple images in events table
-- This migration adds columns for storing multiple compressed images and other missing fields

-- Add multiple images support
DO $$ 
BEGIN
  -- Add images array column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'images') THEN
    ALTER TABLE events ADD COLUMN images JSONB DEFAULT '[]';
  END IF;
  
  -- Add short description if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'short_description') THEN
    ALTER TABLE events ADD COLUMN short_description TEXT;
  END IF;
  
  -- Add venue details if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'venue_details') THEN
    ALTER TABLE events ADD COLUMN venue_details JSONB;
  END IF;
  
  -- Add registration deadline if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'registration_deadline') THEN
    ALTER TABLE events ADD COLUMN registration_deadline TIMESTAMP WITH TIME ZONE;
  END IF;
  
  -- Add enable certificates if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'enable_certificates') THEN
    ALTER TABLE events ADD COLUMN enable_certificates BOOLEAN DEFAULT false;
  END IF;
  
  -- Add enable attendance if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'enable_attendance') THEN
    ALTER TABLE events ADD COLUMN enable_attendance BOOLEAN DEFAULT true;
  END IF;
  
  -- Add is_public if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'is_public') THEN
    ALTER TABLE events ADD COLUMN is_public BOOLEAN DEFAULT true;
  END IF;
  
  -- Add max_attendees if it doesn't exist (rename from max_participants)
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'max_attendees') THEN
    ALTER TABLE events ADD COLUMN max_attendees INTEGER;
    -- Copy data from max_participants if it exists
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'max_participants') THEN
      UPDATE events SET max_attendees = max_participants WHERE max_participants IS NOT NULL;
    END IF;
  END IF;
END $$;

-- Create index on images for better performance
CREATE INDEX IF NOT EXISTS idx_events_images ON events USING GIN (images);

-- Add comment to explain the images structure
COMMENT ON COLUMN events.images IS 'Array of image objects with url, alt_text, and order fields. Max 10 images per event.';

-- Example of images structure:
-- [
--   {
--     "url": "https://storage.supabase.co/...",
--     "alt_text": "Event banner",
--     "order": 1,
--     "is_primary": true
--   },
--   {
--     "url": "https://storage.supabase.co/...",
--     "alt_text": "Event venue",
--     "order": 2,
--     "is_primary": false
--   }
-- ]
