-- Create a function to create the system_settings table if it doesn't exist
CREATE OR REPLACE FUNCTION create_system_settings_if_not_exists()
<PERSON><PERSON><PERSON><PERSON> void AS $$
BEGIN
  -- Check if the system_settings table exists
  IF NOT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'system_settings'
  ) THEN
    -- Create the system_settings table
    CREATE TABLE system_settings (
      id SERIAL PRIMARY KEY,
      certificate_templates JSONB DEFAULT '[]'::JSON<PERSON>,
      created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
      updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
    
    -- Insert default record
    INSERT INTO system_settings (id, certificate_templates) 
    VALUES (1, '[]'::JSONB);
  END IF;
END;
$$ LANGUAGE plpgsql;
