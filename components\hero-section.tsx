"use client"

import React from "react"

interface HeroSectionProps {
  title: string
  description: string
  children?: React.ReactNode
  className?: string
}

export function HeroSection({ title, description, children, className = "" }: HeroSectionProps) {
  return (
    <section className={`w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-purple-100 to-indigo-100 dark:from-purple-950 dark:to-indigo-950 ${className}`}>
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center space-y-4 text-center">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
              {title}
            </h1>
            <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl dark:text-gray-400">
              {description}
            </p>
          </div>
          {children && (
            <div className="w-full max-w-sm space-y-2">
              {children}
            </div>
          )}
        </div>
      </div>
    </section>
  )
}
