import { PageLayout } from "@/components/page-layout"
import { HeroSection } from "@/components/hero-section"

export const metadata = {
  title: "Blog | mTicket.my",
  description: "Latest news, tips, and insights about event management and technology",
}

export default function BlogPage() {
  return (
    <PageLayout>
      {/* Corporate Hero Section */}
      <section className="relative w-full py-20 md:py-32 lg:py-40 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}
          ></div>
        </div>

        <div className="container relative px-4 md:px-6">
          <div className="flex flex-col items-center space-y-8 text-center">
            {/* Main Heading */}
            <div className="space-y-4 max-w-4xl">
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl lg:text-7xl">
                mTicket.my
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> Blog</span>
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-gray-300 md:text-2xl leading-relaxed">
                Stay updated with the latest news, tips, and insights about event management and technology.
              </p>
            </div>
          </div>
        </div>
      </section>

      <div className="container px-4 md:px-6 py-12">
        <div className="mx-auto max-w-7xl">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-4">Coming Soon</h2>
            <p className="text-lg text-muted-foreground mb-8">
              We're working on bringing you valuable content about event management,
              industry trends, and platform updates.
            </p>

            <div className="bg-card p-8 rounded-lg border">
              <h3 className="text-xl font-semibold mb-4">What to Expect</h3>
              <div className="grid gap-4 md:grid-cols-3 text-left">
                <div>
                  <h4 className="font-medium mb-2">Event Tips</h4>
                  <p className="text-sm text-muted-foreground">
                    Best practices for organizing successful events
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Platform Updates</h4>
                  <p className="text-sm text-muted-foreground">
                    Latest features and improvements to mTicket.my
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Industry Insights</h4>
                  <p className="text-sm text-muted-foreground">
                    Trends and analysis in the events industry
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-8">
              <p className="text-muted-foreground mb-4">
                Want to be notified when we publish new content?
              </p>
              <a
                href="mailto:<EMAIL>?subject=Blog Updates Subscription"
                className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90"
              >
                Subscribe for Updates
              </a>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  )
}
