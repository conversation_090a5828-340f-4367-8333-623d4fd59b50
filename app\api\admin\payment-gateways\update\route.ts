import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth";
import { logActivity } from "@/lib/activity-logger";

export async function POST(request: Request) {
  try {
    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === "development";
    console.log("API payment-gateways/update - Is development mode:", isDevelopment);

    // Get JWT token from request
    const token = getJWTTokenFromRequest(request);
    console.log("API payment-gateways/update - Token present:", !!token);

    // Initialize session variable
    let session: any = null;

    // In production, verify authentication and admin role
    // In development, allow access without authentication for easier testing
    if (!isDevelopment) {
      if (!token) {
        console.log("API payment-gateways/update - Access denied: No token provided");
        return NextResponse.json(
          { error: "Unauthorized. Admin access required." },
          { status: 403 }
        );
      }

      // Verify the JWT token and get user data
      const authResult = await verifyJWTToken(token);
      if (!authResult?.user) {
        console.log("API payment-gateways/update - Access denied: Invalid token");
        return NextResponse.json(
          { error: "Unauthorized. Invalid token." },
          { status: 403 }
        );
      }

      // Check if user has admin role (only "admin" role has full access)
      const userRole = authResult.user.role_name || authResult.user.role;

      if (userRole !== "admin") {
        console.log("API payment-gateways/update - Access denied: Not admin role. User role:", userRole);
        return NextResponse.json(
          { error: "Unauthorized. Admin access required." },
          { status: 403 }
        );
      }

      // Set session for authenticated user
      session = { user: authResult.user };
      console.log("API payment-gateways/update - Access granted to admin user:", authResult.user.email);
    } else {
      console.log("API payment-gateways/update - Development mode: Allowing access without authentication");
    }

    // Log access in development mode
    if (isDevelopment) {
      console.log("Development mode: Allowing access to update payment gateway without authentication");
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin();
    const body = await request.json();

    // Validate required fields
    if (!body.id || !body.gateway_name) {
      return NextResponse.json(
        { error: "Gateway ID and name are required" },
        { status: 400 }
      );
    }

    // Update payment gateway
    const updates = {
      gateway_name: body.gateway_name,
      is_enabled: body.is_enabled,
      is_test_mode: body.is_test_mode,
      configuration: body.configuration,
      test_configuration: body.test_configuration,
      live_configuration: body.live_configuration,
      display_order: body.display_order,
      updated_by: session?.user?.id || null,
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabaseAdmin
      .from("payment_gateway_settings")
      .update(updates)
      .eq("id", body.id)
      .select();

    if (error) {
      console.error("Error updating payment gateway:", error);
      return NextResponse.json(
        { error: "Failed to update payment gateway" },
        { status: 500 }
      );
    }

    if (!data || data.length === 0) {
      return NextResponse.json(
        { error: "Payment gateway not found" },
        { status: 404 }
      );
    }

    // Log activity
    if (session?.user?.id) {
      await logActivity({
        user_id: session.user.id,
        action: "update",
        entity_type: "payment_gateway",
        entity_id: data[0].id,
        details: { gateway_name: data[0].gateway_name },
        category: "settings",
      });
    }

    return NextResponse.json(data[0]);
  } catch (error: any) {
    console.error("Error in payment gateways update API:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
