# Server-Side Data Fetching Pattern

This document outlines the current implementation pattern for fetching data from the Supabase database in the mTicket.my application using JWT-based authentication with server-side API endpoints.

## Overview

The application uses a hybrid approach where:
- **Server-side API endpoints** handle data fetching using the Supabase admin client to bypass RLS policies
- **JWT token authentication** is used for API authorization instead of NextAuth sessions
- **Client-side components** fetch data from these API endpoints using standard fetch requests
- **Development mode** allows easier testing by bypassing authentication checks

## Current Implementation Pattern

### 1. Server-Side API Endpoint Structure

All admin API endpoints follow this standardized pattern:

```typescript
// app/api/admin/[resource]/list/route.ts
import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth";

export async function GET(request: Request) {
  try {
    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === "development";
    console.log("API [resource]/list - Is development mode:", isDevelopment);

    // Get JWT token from request
    const token = getJWTTokenFromRequest(request);
    console.log("API [resource]/list - Token present:", !!token);

    // In production, verify authentication and admin role
    // In development, allow access without authentication for easier testing
    if (!isDevelopment) {
      if (!token) {
        console.log("API [resource]/list - Access denied: No token provided");
        return NextResponse.json(
          { error: "Unauthorized. Admin access required." },
          { status: 403 }
        );
      }

      // Verify the JWT token and get user data
      const authResult = await verifyJWTToken(token);
      if (!authResult?.user) {
        console.log("API [resource]/list - Access denied: Invalid token");
        return NextResponse.json(
          { error: "Unauthorized. Invalid token." },
          { status: 403 }
        );
      }

      // Check if user has admin role (only "admin" role has full access)
      const userRole = authResult.user.role_name || authResult.user.role;

      if (userRole !== "admin") {
        console.log("API [resource]/list - Access denied: Not admin role. User role:", userRole);
        return NextResponse.json(
          { error: "Unauthorized. Admin access required." },
          { status: 403 }
        );
      }

      console.log("API [resource]/list - Access granted to admin user:", authResult.user.email);
    } else {
      console.log("API [resource]/list - Development mode: Allowing access without authentication");
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin();

    // Fetch data from the database
    const { data, error } = await supabaseAdmin
      .from("your_table")
      .select("*")
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching data:", error);
      return NextResponse.json(
        { error: "Failed to fetch data" },
        { status: 500 }
      );
    }

    return NextResponse.json({ data });
  } catch (error: any) {
    console.error("Error in API:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
```

### 2. Client-Side Data Fetching Pattern

Client-side components fetch data from API endpoints using JWT tokens for authentication:

```typescript
// Client-side component data fetching
const fetchData = async () => {
  setLoading(true);
  try {
    // Get auth token for API request
    const token = getAuthToken();
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Use the server-side API endpoint to fetch data
    const response = await fetch('/api/admin/data/list', {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch data');
    }

    const { data } = await response.json();

    if (data && data.length > 0) {
      console.log("Data fetched successfully:", data.length);
      setData(data);
    } else {
      console.log("No data found");
      setData([]);
    }
  } catch (err) {
    console.error("Error fetching data:", err);
    toast({
      title: "Error",
      description: "Failed to fetch data from database",
      variant: "destructive",
    });
    // Use fallback data if needed
    setData([]);
  } finally {
    setLoading(false);
  }
};
```

### 3. Authentication Token Helper

The `getAuthToken()` function retrieves the JWT token from localStorage:

```typescript
// lib/auth-utils.ts or similar
export function getAuthToken(): string | null {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('auth_token');
}
```

## API Endpoints Reference

For a comprehensive list of all API endpoints, their parameters, and usage examples, see:
- **[API Endpoints Reference](./api-endpoints-reference.md)** - Complete API documentation

## Role-Based Access Control

Different endpoints have different role requirements:

### Admin-Only Access
- `["admin", "super_admin", "supermanager", "event_admin"]`
- Used for: User management, role management, authentication admin functions

### Admin/Manager Access
- `["admin", "super_admin", "supermanager", "event_admin", "manager"]`
- Used for: Payment gateway management

## Benefits

1. **Security**: Sensitive database operations are performed on the server side with proper JWT token validation
2. **Bypass RLS**: Server-side API endpoints use the Supabase admin client to bypass RLS policies
3. **Consistent Authentication**: Standardized JWT token verification across all endpoints
4. **Role-Based Access**: Granular permission control based on user roles
5. **Development Flexibility**: Development mode bypasses authentication for easier testing
6. **Error Handling**: Centralized error handling and logging in API endpoints

## Implementation Examples

### Users Management
- **Page**: `app/dashboard/users/page.tsx`
- **API**: `/api/admin/users/list` and `/api/admin/roles/list`
- **Pattern**: Client-side fetch with JWT token authentication

### Activity Logs
- **Page**: `app/dashboard/activity-logs/page.tsx`
- **Pattern**: Mixed approach - direct Supabase client for some queries, API endpoints for others

### Payment Gateways
- **Page**: `app/dashboard/settings/payment-gateways/page.tsx`
- **API**: `/api/admin/payment-gateways/list`
- **Pattern**: Client-side fetch without explicit token (development mode)

### Events Dashboard
- **Page**: `app/dashboard/events/page.tsx`
- **API**: `/api/dashboard/events`
- **Pattern**: User-specific data fetching with JWT authentication

## Important Notes

1. **JWT Token Storage**: Tokens are stored in localStorage and included in Authorization headers
2. **Development Mode**: API endpoints bypass authentication in development for easier testing
3. **Error Handling**: All endpoints return consistent error responses with appropriate HTTP status codes
4. **Role Compatibility**: Endpoints check both `role` and `role_name` fields for backward compatibility
5. **Logging**: Comprehensive logging for debugging and audit trails
6. **Package Manager**: Always use pnpm as the package manager for this project
