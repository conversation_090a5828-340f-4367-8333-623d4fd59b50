import { NextRequest, NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { generateReceiptToken } from "@/app/api/receipts/public/route";
import { generateTicketToken } from "@/app/api/tickets/public/route";

/**
 * GET /api/events/[slug]/registration-tokens
 * Get receipt and ticket tokens for the most recent successful registration for an event
 * Public access - no authentication required
 * Used when users access registration step 4 directly without going through payment return flow
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    console.log("Registration Tokens API: Starting request for event slug:", slug);

    if (!slug) {
      return NextResponse.json(
        { error: "Event slug is required" },
        { status: 400 }
      );
    }

    const supabaseAdmin = getSupabaseAdmin();

    // First, get the event by slug
    const { data: event, error: eventError } = await supabaseAdmin
      .from("events")
      .select("id")
      .eq("slug", slug)
      .single();

    if (eventError || !event) {
      console.error("Registration Tokens API: Event not found:", eventError);
      return NextResponse.json(
        { error: "Event not found" },
        { status: 404 }
      );
    }

    // Find the most recent registration for this event
    // First try to find paid registrations, then fall back to any registration with a transaction
    let registration = null;
    let regError = null;

    // Try to find paid registrations first
    const { data: paidRegistration, error: paidError } = await supabaseAdmin
      .from("registrations")
      .select(`
        id,
        transaction_id,
        payment_status,
        status,
        created_at,
        transaction:transaction_id (
          id,
          status,
          amount,
          currency,
          gateway_transaction_id,
          processed_at
        )
      `)
      .eq("event_id", event.id)
      .eq("payment_status", "paid")
      .not("transaction_id", "is", null)
      .order("created_at", { ascending: false })
      .limit(1)
      .maybeSingle();

    if (paidRegistration) {
      registration = paidRegistration;
      console.log("Registration Tokens API: Found paid registration:", registration.id);
    } else {
      // If no paid registration found, try to find any registration with a transaction
      const { data: anyRegistration, error: anyError } = await supabaseAdmin
        .from("registrations")
        .select(`
          id,
          transaction_id,
          payment_status,
          status,
          created_at,
          transaction:transaction_id (
            id,
            status,
            amount,
            currency,
            gateway_transaction_id,
            processed_at
          )
        `)
        .eq("event_id", event.id)
        .not("transaction_id", "is", null)
        .order("created_at", { ascending: false })
        .limit(1)
        .maybeSingle();

      registration = anyRegistration;
      regError = anyError;

      if (registration) {
        console.log("Registration Tokens API: Found registration with transaction:", registration.id, "Payment status:", registration.payment_status, "Transaction status:", registration.transaction?.status);
      }
    }

    if (regError || !registration) {
      console.log("Registration Tokens API: No registration found for event:", slug);
      return NextResponse.json(
        {
          success: false,
          message: "No registration found for this event"
        },
        { status: 404 }
      );
    }

    // For non-paid registrations, still generate tokens but with a warning
    if (registration.payment_status !== 'paid' && registration.transaction?.status !== 'paid') {
      console.log("Registration Tokens API: Generating tokens for non-paid registration:", registration.id);
      // Continue to generate tokens anyway for testing/demo purposes
    }

    // Generate tokens for the registration
    const receiptToken = generateReceiptToken(registration.id, registration.transaction_id);
    const ticketToken = generateTicketToken(registration.id, registration.transaction_id);

    console.log("Registration Tokens API: Tokens generated successfully for registration:", registration.id);

    return NextResponse.json({
      success: true,
      receipt_token: receiptToken,
      ticket_token: ticketToken,
      registration_id: registration.id,
      transaction_id: registration.transaction_id
    });

  } catch (error) {
    console.error("Registration Tokens API: Error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
