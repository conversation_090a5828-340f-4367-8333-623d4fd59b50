import { NextResponse } from 'next/server';
import { getSystemSettings } from '@/lib/system-settings';
import { getSupabaseAdmin } from '@/lib/supabase';

/**
 * API endpoint to fetch system settings
 * This provides a server-side way to fetch settings to avoid CORS issues
 */
export async function GET() {
  try {
    // Try to fetch system settings using our utility function
    const settings = await getSystemSettings();
    
    // If that fails, try using the admin client directly
    if (!settings) {
      const supabaseAdmin = getSupabaseAdmin();
      const { data, error } = await supabaseAdmin
        .from('system_settings')
        .select('*')
        .single();
      
      if (error) {
        console.error('Error fetching system settings with admin client:', error);
        return NextResponse.json(
          { error: 'Failed to fetch system settings' },
          { status: 500 }
        );
      }
      
      return NextResponse.json(data);
    }
    
    return NextResponse.json(settings);
  } catch (error: any) {
    console.error('Error in settings API:', error);
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
