"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { getSupabaseClient } from "@/lib/supabase"

export default function CertificateViewPage() {
  const params = useParams()
  const { toast } = useToast()
  const [certificate, setCertificate] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const registrationId = params.id as string

  useEffect(() => {
    const fetchCertificate = async () => {
      setLoading(true)
      try {
        const supabase = getSupabaseClient()
        const { data: certificate, error } = await supabase
          .from('certificates')
          .select(`
            *,
            registrations!inner(
              attendee_name,
              events!inner(
                title,
                start_date
              )
            )
          `)
          .eq('id', params.id)
          .single()

        if (error) throw error

        if (certificate) {
          // Transform data to match expected format
          const transformedCertificate = {
            ...certificate,
            attendee_name: certificate.registrations.attendee_name,
            event_name: certificate.registrations.events.title,
            event_date: new Date(certificate.registrations.events.start_date).toLocaleDateString(),
            issue_date: certificate.issued_at,
            issuer_name: "mTicket.my",
            certificate_number: certificate.verification_code
          }
          setCertificate(transformedCertificate)
        } else {
          toast({
            title: "Error",
            description: "Certificate not found",
            variant: "destructive",
          })
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load certificate",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    if (registrationId) {
      fetchCertificate()
    }
  }, [registrationId, toast])

  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    )
  }

  if (!certificate) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Certificate Not Found</h2>
          <p className="text-muted-foreground">The certificate you're looking for doesn't exist or has been removed.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gray-100 p-4">
      <div className="relative bg-white border shadow-lg w-full max-w-4xl aspect-[1.4/1] p-8 print:shadow-none">
        {/* Certificate Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-800">Certificate of Completion</h1>
          <div className="h-1 w-40 bg-primary mx-auto mt-4"></div>
        </div>

        {/* Certificate Body */}
        <div className="text-center space-y-6">
          <p className="text-lg text-gray-600">This is to certify that</p>
          <h2 className="text-2xl md:text-3xl font-bold text-gray-800 font-serif">{certificate.attendee_name}</h2>
          <p className="text-lg text-gray-600">has successfully participated in</p>
          <h3 className="text-xl md:text-2xl font-bold text-gray-800">{certificate.event_name}</h3>
          <p className="text-lg text-gray-600">held on {certificate.event_date}</p>
        </div>

        {/* Certificate Footer */}
        <div className="absolute bottom-8 w-[calc(100%-4rem)] flex justify-between items-end">
          <div>
            <div className="h-0.5 w-40 bg-gray-400"></div>
            <p className="mt-2 text-gray-600">Date: {new Date(certificate.issue_date).toLocaleDateString()}</p>
          </div>
          <div className="text-center">
            <div className="h-0.5 w-40 bg-gray-400 mx-auto"></div>
            <p className="mt-2 text-gray-600">{certificate.issuer_name}</p>
          </div>
        </div>

        {/* Certificate Number */}
        <div className="absolute bottom-4 left-8 text-xs text-gray-500">
          Certificate No: {certificate.certificate_number}
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-0 left-0 w-full h-full border-[12px] border-double border-gray-200 pointer-events-none"></div>
        <div className="absolute top-4 right-4 text-6xl text-gray-100 font-serif">mTicketz</div>
      </div>

      <div className="mt-6 flex gap-4 print:hidden">
        <button
          onClick={() => window.print()}
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
        >
          Print Certificate
        </button>
        <button
          onClick={() => window.close()}
          className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
        >
          Close
        </button>
      </div>
    </div>
  )
}
