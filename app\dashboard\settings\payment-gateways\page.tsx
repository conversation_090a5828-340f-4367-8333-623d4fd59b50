"use client"

import { useEffect, useState } from "react"
import { Edit, Plus, Trash } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { Badge } from "@/components/ui/badge"

// Define gateway types and their specific fields
type GatewayType = "billplz" | "toyyibpay" | "chip" | "stripe"

interface GatewayField {
  name: string
  label: string
  type: "text" | "password" | "url"
  placeholder: string
  required: boolean
}

type PaymentGateway = {
  id: string
  name: string
  type: GatewayType
  enabled: boolean
  description?: string
  fields: Record<string, string>
  created_at?: string
  updated_at?: string
}

// Define gateway-specific fields
const gatewayFields: Record<GatewayType, GatewayField[]> = {
  billplz: [
    { name: "api_key", label: "API Key", type: "password", placeholder: "Enter API Key", required: true },
    { name: "collection_id", label: "Collection ID", type: "text", placeholder: "Enter Collection ID", required: true },
    {
      name: "x_signature",
      label: "X-Signature Key",
      type: "password",
      placeholder: "Enter X-Signature Key",
      required: true,
    },
    {
      name: "sandbox_mode",
      label: "Sandbox Mode URL",
      type: "url",
      placeholder: "https://www.billplz-sandbox.com/api/v3",
      required: false,
    },
  ],
  toyyibpay: [
    {
      name: "user_secret_key",
      label: "User Secret Key",
      type: "password",
      placeholder: "Enter User Secret Key",
      required: true,
    },
    { name: "category_code", label: "Category Code", type: "text", placeholder: "Enter Category Code", required: true },
    {
      name: "sandbox_mode",
      label: "Sandbox Mode URL",
      type: "url",
      placeholder: "https://dev.toyyibpay.com/",
      required: false,
    },
  ],
  chip: [
    { name: "secret_key", label: "Secret Key", type: "password", placeholder: "Enter Secret Key", required: true },
    { name: "brand_id", label: "Brand ID", type: "text", placeholder: "Enter Brand ID", required: true },
    {
      name: "webhook_endpoint_secret",
      label: "Webhook Endpoint Secret",
      type: "password",
      placeholder: "Enter Webhook Secret",
      required: false,
    },
  ],
  stripe: [
    {
      name: "publishable_key",
      label: "Publishable Key",
      type: "text",
      placeholder: "Enter Publishable Key",
      required: true,
    },
    { name: "secret_key", label: "Secret Key", type: "password", placeholder: "Enter Secret Key", required: true },
    {
      name: "webhook_secret",
      label: "Webhook Secret",
      type: "password",
      placeholder: "Enter Webhook Secret",
      required: false,
    },
  ],
}

// Gateway display names
const gatewayNames: Record<GatewayType, string> = {
  billplz: "Billplz",
  toyyibpay: "ToyyibPay",
  chip: "Chip",
  stripe: "Stripe",
}

export default function PaymentGatewaysPage() {
  const [paymentGateways, setPaymentGateways] = useState<PaymentGateway[]>([])
  const [loading, setLoading] = useState(true)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedGateway, setSelectedGateway] = useState<PaymentGateway | null>(null)
  const [newGatewayType, setNewGatewayType] = useState<GatewayType>("billplz")
  const [newGateway, setNewGateway] = useState<PaymentGateway>({
    id: "",
    name: "",
    type: "billplz",
    enabled: true,
    description: "",
    fields: {},
  })
  const [currentUser, setCurrentUser] = useState<any>(null)
  const { toast } = useToast()

  // Mock data for demonstration
  const mockGateways: PaymentGateway[] = [
    {
      id: "1",
      name: "Billplz Payment",
      type: "billplz",
      enabled: true,
      description: "Malaysian payment gateway",
      fields: {
        api_key: "73eb57d0-7d4e-45c9-958e-cde516ebc53a",
        collection_id: "collection_id_123",
        x_signature: "S-signature-key",
      },
      created_at: "2023-01-15T08:30:00Z",
    },
    {
      id: "2",
      name: "ToyyibPay",
      type: "toyyibpay",
      enabled: false,
      description: "Alternative Malaysian payment solution",
      fields: {
        user_secret_key: "user-secret-key-123",
        category_code: "category-123",
      },
      created_at: "2023-02-20T10:15:00Z",
    },
  ]

  // Function to fetch payment gateways from database
  const fetchPaymentGateways = async () => {
    setLoading(true)
    try {
      // Use the server-side API endpoint to fetch payment gateways
      const response = await fetch('/api/admin/payment-gateways/list')

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch payment gateways')
      }

      const { payment_gateways: gatewaysData } = await response.json()

      if (gatewaysData && gatewaysData.length > 0) {
        console.log("Payment gateways fetched successfully:", gatewaysData.length)

        // Map database fields to UI format
        const mappedGateways = gatewaysData.map((gateway: any) => {
          // Determine gateway type from name
          let type: GatewayType = "billplz"
          if (gateway.gateway_name.toLowerCase().includes("billplz")) {
            type = "billplz"
          } else if (gateway.gateway_name.toLowerCase().includes("toyyibpay")) {
            type = "toyyibpay"
          } else if (gateway.gateway_name.toLowerCase().includes("chip")) {
            type = "chip"
          } else if (gateway.gateway_name.toLowerCase().includes("stripe")) {
            type = "stripe"
          }

          // Get configuration based on test mode
          const config = gateway.is_test_mode ? gateway.test_configuration : gateway.live_configuration

          return {
            id: gateway.id,
            name: gateway.gateway_name,
            type,
            enabled: gateway.is_enabled,
            description: gateway.configuration.description || "",
            fields: config || {},
            created_at: gateway.created_at,
            updated_at: gateway.updated_at,
          }
        })

        setPaymentGateways(mappedGateways)
      } else {
        console.log("No payment gateways found in database, using mock data")
        // Use mock data as fallback
        setPaymentGateways(mockGateways)
      }

      // Get current user info (in a real app, this would come from the session)
      // For now, we'll assume the user is an admin
      setCurrentUser({ role: "admin" })
    } catch (err) {
      console.error("Error fetching payment gateways:", err)
      toast({
        title: "Error",
        description: "Failed to fetch payment gateways",
        variant: "destructive",
      })
      // Use mock data as fallback
      setPaymentGateways(mockGateways)
    } finally {
      setLoading(false)
    }
  }

  // Fetch payment gateways on component mount
  useEffect(() => {
    fetchPaymentGateways()
  }, [toast])

  const handleToggleGateway = async (index: number) => {
    try {
      const gateway = paymentGateways[index]

      // Use the dedicated toggle API endpoint
      const response = await fetch("/api/admin/payment-gateways/toggle", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: gateway.id,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to toggle payment gateway")
      }

      // Refresh the payment gateways list
      await fetchPaymentGateways()

      toast({
        title: "Success",
        description: `${gateway.name} ${!gateway.enabled ? "enabled" : "disabled"} successfully`,
      })
    } catch (err) {
      console.error("Error toggling payment gateway:", err)
      toast({
        title: "Error",
        description: "Failed to update payment gateway",
        variant: "destructive",
      })
    }
  }

  const handleAddGateway = async () => {
    try {
      if (!newGateway.name) {
        toast({
          title: "Error",
          description: "Gateway name is required",
          variant: "destructive",
        })
        return
      }

      // Check required fields
      const requiredFields = gatewayFields[newGateway.type].filter((field) => field.required)
      for (const field of requiredFields) {
        if (!newGateway.fields[field.name]) {
          toast({
            title: "Error",
            description: `${field.label} is required`,
            variant: "destructive",
          })
          return
        }
      }

      // Create gateway in the database via API
      const response = await fetch("/api/admin/payment-gateways/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          gateway_name: newGateway.name,
          is_enabled: newGateway.enabled,
          is_test_mode: true, // Default to test mode for new gateways
          configuration: {
            description: newGateway.description || "",
            display_name: newGateway.name,
            icon: newGateway.type.toLowerCase(),
          },
          test_configuration: newGateway.fields,
          live_configuration: {}, // Empty for now
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to add payment gateway")
      }

      // Refresh the payment gateways list
      await fetchPaymentGateways()

      // Reset form
      setNewGateway({
        id: "",
        name: "",
        type: "billplz",
        enabled: true,
        description: "",
        fields: {},
      })
      setIsAddDialogOpen(false)

      toast({
        title: "Success",
        description: "Payment gateway added successfully",
      })
    } catch (err) {
      console.error("Error adding payment gateway:", err)
      toast({
        title: "Error",
        description: "Failed to add payment gateway",
        variant: "destructive",
      })
    }
  }

  const handleEditGateway = async () => {
    try {
      if (!selectedGateway || !selectedGateway.name) {
        toast({
          title: "Error",
          description: "Gateway name is required",
          variant: "destructive",
        })
        return
      }

      // Check required fields
      const requiredFields = gatewayFields[selectedGateway.type].filter((field) => field.required)
      for (const field of requiredFields) {
        if (!selectedGateway.fields[field.name]) {
          toast({
            title: "Error",
            description: `${field.label} is required`,
            variant: "destructive",
          })
          return
        }
      }

      // Update gateway in the database via API
      const response = await fetch("/api/admin/payment-gateways/update", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: selectedGateway.id,
          gateway_name: selectedGateway.name,
          is_enabled: selectedGateway.enabled,
          is_test_mode: true, // Maintain test mode for now
          configuration: {
            description: selectedGateway.description || "",
            display_name: selectedGateway.name,
            icon: selectedGateway.type.toLowerCase(),
          },
          test_configuration: selectedGateway.fields,
          live_configuration: selectedGateway.fields, // Use same config for live for now
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update payment gateway")
      }

      // Refresh the payment gateways list
      await fetchPaymentGateways()
      setSelectedGateway(null)
      setIsEditDialogOpen(false)

      toast({
        title: "Success",
        description: "Payment gateway updated successfully",
      })
    } catch (err) {
      console.error("Error updating payment gateway:", err)
      toast({
        title: "Error",
        description: "Failed to update payment gateway",
        variant: "destructive",
      })
    }
  }

  const handleDeleteGateway = async () => {
    try {
      if (!selectedGateway) return

      // Delete gateway from the database via API
      const response = await fetch("/api/admin/payment-gateways/delete", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          id: selectedGateway.id,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to delete payment gateway")
      }

      // Refresh the payment gateways list
      await fetchPaymentGateways()
      setSelectedGateway(null)
      setIsDeleteDialogOpen(false)

      toast({
        title: "Success",
        description: "Payment gateway deleted successfully",
      })
    } catch (err) {
      console.error("Error deleting payment gateway:", err)
      toast({
        title: "Error",
        description: "Failed to delete payment gateway",
        variant: "destructive",
      })
    }
  }

  const handleNewGatewayTypeChange = (type: GatewayType) => {
    setNewGatewayType(type)
    setNewGateway({
      ...newGateway,
      type,
      name: gatewayNames[type],
      fields: {},
    })
  }

  const handleNewGatewayFieldChange = (fieldName: string, value: string) => {
    setNewGateway({
      ...newGateway,
      fields: {
        ...newGateway.fields,
        [fieldName]: value,
      },
    })
  }

  const handleEditGatewayFieldChange = (fieldName: string, value: string) => {
    if (!selectedGateway) return
    setSelectedGateway({
      ...selectedGateway,
      fields: {
        ...selectedGateway.fields,
        [fieldName]: value,
      },
    })
  }

  if (currentUser && currentUser.role !== "admin" && currentUser.role !== "manager") {
    return (
      <Card className="m-6">
        <CardHeader>
          <CardTitle>Payment Gateway Settings</CardTitle>
          <CardDescription>You don't have permission to access this page</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <h1 className="text-2xl font-bold">Payment Gateway Settings</h1>
        <Button onClick={() => setIsAddDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Gateway
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Manage Payment Gateways</CardTitle>
          <CardDescription>Configure and manage payment gateways for your events</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex h-40 items-center justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
          ) : paymentGateways.length === 0 ? (
            <div className="flex h-40 flex-col items-center justify-center gap-4 rounded-lg border border-dashed p-8 text-center">
              <div className="text-muted-foreground">No payment gateways configured</div>
              <Button onClick={() => setIsAddDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Gateway
              </Button>
            </div>
          ) : (
            <div className="space-y-6">
              {paymentGateways.map((gateway, index) => (
                <div
                  key={gateway.id}
                  className="flex flex-col sm:flex-row items-start sm:items-center justify-between rounded-lg border p-4 gap-4"
                >
                  <div className="flex items-center gap-4">
                    <div className="flex h-12 w-12 items-center justify-center rounded-md bg-primary/10">
                      <span className="text-lg font-semibold text-primary">{gateway.name.charAt(0)}</span>
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{gateway.name}</span>
                        <Badge variant={gateway.enabled ? "default" : "outline"}>{gateway.type}</Badge>
                      </div>
                      <div className="text-sm text-muted-foreground">{gateway.description || "No description"}</div>
                    </div>
                  </div>
                  <div className="flex items-center gap-4 w-full sm:w-auto justify-between sm:justify-end">
                    <div className="flex items-center gap-2">
                      <Switch
                        checked={gateway.enabled}
                        onCheckedChange={() => handleToggleGateway(index)}
                        id={`gateway-${gateway.id}`}
                      />
                      <Label htmlFor={`gateway-${gateway.id}`} className="hidden sm:inline">
                        {gateway.enabled ? "Enabled" : "Disabled"}
                      </Label>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setSelectedGateway(gateway)
                          setIsEditDialogOpen(true)
                        }}
                      >
                        <Edit className="h-4 w-4" />
                        <span className="sr-only">Edit</span>
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setSelectedGateway(gateway)
                          setIsDeleteDialogOpen(true)
                        }}
                      >
                        <Trash className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Gateway Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add Payment Gateway</DialogTitle>
            <DialogDescription>Add a new payment gateway to your system</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="gateway-type">Gateway Type</Label>
              <Select
                value={newGatewayType}
                onValueChange={(value) => handleNewGatewayTypeChange(value as GatewayType)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select gateway type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="billplz">Billplz</SelectItem>
                  <SelectItem value="toyyibpay">ToyyibPay</SelectItem>
                  <SelectItem value="chip">Chip</SelectItem>
                  <SelectItem value="stripe">Stripe</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="gateway-name">Gateway Name</Label>
              <Input
                id="gateway-name"
                value={newGateway.name}
                onChange={(e) => setNewGateway({ ...newGateway, name: e.target.value })}
                placeholder={`e.g., ${gatewayNames[newGatewayType]}`}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="gateway-description">Description</Label>
              <Input
                id="gateway-description"
                value={newGateway.description || ""}
                onChange={(e) => setNewGateway({ ...newGateway, description: e.target.value })}
                placeholder="Optional description"
              />
            </div>

            <div className="space-y-4">
              <Label>Gateway Configuration</Label>
              {gatewayFields[newGatewayType].map((field) => (
                <div key={field.name} className="space-y-2">
                  <Label htmlFor={`new-${field.name}`}>
                    {field.label}
                    {field.required && <span className="text-red-500 ml-1">*</span>}
                  </Label>
                  <Input
                    id={`new-${field.name}`}
                    type={field.type}
                    value={newGateway.fields[field.name] || ""}
                    onChange={(e) => handleNewGatewayFieldChange(field.name, e.target.value)}
                    placeholder={field.placeholder}
                    required={field.required}
                  />
                </div>
              ))}
            </div>

            <div className="flex items-center gap-2">
              <Switch
                checked={newGateway.enabled}
                onCheckedChange={(checked) => setNewGateway({ ...newGateway, enabled: checked })}
                id="gateway-enabled"
              />
              <Label htmlFor="gateway-enabled">Enable Gateway</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddGateway}>Add Gateway</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Gateway Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Payment Gateway</DialogTitle>
            <DialogDescription>Update payment gateway details</DialogDescription>
          </DialogHeader>
          {selectedGateway && (
            <div className="space-y-4 py-4">
              <div className="flex items-center gap-2">
                <Badge>{selectedGateway.type}</Badge>
                <span className="text-sm text-muted-foreground">Gateway Type (cannot be changed)</span>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-gateway-name">Gateway Name</Label>
                <Input
                  id="edit-gateway-name"
                  value={selectedGateway.name}
                  onChange={(e) => setSelectedGateway({ ...selectedGateway, name: e.target.value })}
                  placeholder={`e.g., ${gatewayNames[selectedGateway.type]}`}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-gateway-description">Description</Label>
                <Input
                  id="edit-gateway-description"
                  value={selectedGateway.description || ""}
                  onChange={(e) => setSelectedGateway({ ...selectedGateway, description: e.target.value })}
                  placeholder="Optional description"
                />
              </div>

              <div className="space-y-4">
                <Label>Gateway Configuration</Label>
                {gatewayFields[selectedGateway.type].map((field) => (
                  <div key={field.name} className="space-y-2">
                    <Label htmlFor={`edit-${field.name}`}>
                      {field.label}
                      {field.required && <span className="text-red-500 ml-1">*</span>}
                    </Label>
                    <Input
                      id={`edit-${field.name}`}
                      type={field.type}
                      value={selectedGateway.fields[field.name] || ""}
                      onChange={(e) => handleEditGatewayFieldChange(field.name, e.target.value)}
                      placeholder={field.placeholder}
                      required={field.required}
                    />
                  </div>
                ))}
              </div>

              <div className="flex items-center gap-2">
                <Switch
                  checked={selectedGateway.enabled}
                  onCheckedChange={(checked) => setSelectedGateway({ ...selectedGateway, enabled: checked })}
                  id="edit-gateway-enabled"
                />
                <Label htmlFor="edit-gateway-enabled">Enable Gateway</Label>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditGateway}>Update Gateway</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Gateway Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Payment Gateway</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {selectedGateway?.name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteGateway}>
              Delete Gateway
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
