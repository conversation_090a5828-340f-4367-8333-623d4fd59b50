"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Calendar,
  Activity,
  Download,
  Filter,
  RefreshCw
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

type AnalyticsData = {
  subscriptionMetrics: {
    totalRevenue: number
    revenueGrowth: number
    activeSubscriptions: number
    subscriptionGrowth: number
    churnRate: number
    averageRevenuePerUser: number
  }
  planMetrics: {
    planDistribution: Array<{
      plan_name: string
      subscriber_count: number
      revenue: number
      percentage: number
    }>
    conversionRates: Array<{
      from_plan: string
      to_plan: string
      conversion_rate: number
    }>
  }
  usageMetrics: {
    eventsCreated: number
    certificatesGenerated: number
    attendanceTracked: number
    webhookCalls: number
    apiCalls: number
  }
  timeSeriesData: Array<{
    date: string
    revenue: number
    subscriptions: number
    events: number
  }>
}

export default function AnalyticsPage() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState("30d")
  const [refreshing, setRefreshing] = useState(false)
  const { user, isAdmin } = useAuth()
  const { toast } = useToast()

  // Helper function to get cookie value
  const getCookie = (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  const fetchAnalytics = async () => {
    if (!isAdmin()) {
      setLoading(false)
      return
    }

    try {
      const token = getCookie('auth_token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/admin/analytics/subscriptions?timeRange=${timeRange}`, {
        headers: { 'Authorization': `Bearer ${token}` },
      })

      if (response.ok) {
        const data = await response.json()
        setAnalytics(data.analytics)
      } else {
        // Mock data for now
        setAnalytics({
          subscriptionMetrics: {
            totalRevenue: 12450,
            revenueGrowth: 15.3,
            activeSubscriptions: 156,
            subscriptionGrowth: 8.7,
            churnRate: 2.1,
            averageRevenuePerUser: 79.81
          },
          planMetrics: {
            planDistribution: [
              { plan_name: "Free", subscriber_count: 89, revenue: 0, percentage: 57.1 },
              { plan_name: "Basic", subscriber_count: 45, revenue: 1305, percentage: 28.8 },
              { plan_name: "Pro", subscriber_count: 18, revenue: 1782, percentage: 11.5 },
              { plan_name: "Enterprise", subscriber_count: 4, revenue: 9363, percentage: 2.6 }
            ],
            conversionRates: [
              { from_plan: "Free", to_plan: "Basic", conversion_rate: 12.4 },
              { from_plan: "Basic", to_plan: "Pro", conversion_rate: 8.9 },
              { from_plan: "Pro", to_plan: "Enterprise", conversion_rate: 5.2 }
            ]
          },
          usageMetrics: {
            eventsCreated: 234,
            certificatesGenerated: 89,
            attendanceTracked: 1456,
            webhookCalls: 2341,
            apiCalls: 15678
          },
          timeSeriesData: []
        })
      }
    } catch (error) {
      console.error("Error fetching analytics:", error)
      toast({
        title: "Error",
        description: "Failed to fetch analytics data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  useEffect(() => {
    fetchAnalytics()
  }, [timeRange, isAdmin])

  const handleRefresh = () => {
    setRefreshing(true)
    fetchAnalytics()
  }

  const handleExport = () => {
    toast({
      title: "Export Started",
      description: "Your analytics report is being generated and will be downloaded shortly.",
    })
  }

  if (!isAdmin()) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-muted-foreground">Access Denied</h1>
          <p className="text-muted-foreground mt-2">You need admin privileges to access analytics.</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <h1 className="text-2xl font-bold">Subscription Analytics</h1>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-6 w-24 bg-muted rounded"></div>
                <div className="h-8 w-32 bg-muted rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-4 w-full bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Subscription Analytics</h1>
          <p className="text-muted-foreground">Comprehensive insights into subscription performance</p>
        </div>
        <div className="flex gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleRefresh} disabled={refreshing}>
            <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      {analytics && (
        <>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">RM{analytics.subscriptionMetrics.totalRevenue.toFixed(2)}</div>
                <div className="flex items-center text-xs">
                  {analytics.subscriptionMetrics.revenueGrowth > 0 ? (
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  <span className={analytics.subscriptionMetrics.revenueGrowth > 0 ? "text-green-500" : "text-red-500"}>
                    {Math.abs(analytics.subscriptionMetrics.revenueGrowth)}%
                  </span>
                  <span className="text-muted-foreground ml-1">from last period</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Subscriptions</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.subscriptionMetrics.activeSubscriptions}</div>
                <div className="flex items-center text-xs">
                  {analytics.subscriptionMetrics.subscriptionGrowth > 0 ? (
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  <span className={analytics.subscriptionMetrics.subscriptionGrowth > 0 ? "text-green-500" : "text-red-500"}>
                    {Math.abs(analytics.subscriptionMetrics.subscriptionGrowth)}%
                  </span>
                  <span className="text-muted-foreground ml-1">growth</span>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Churn Rate</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.subscriptionMetrics.churnRate}%</div>
                <p className="text-xs text-muted-foreground">
                  Monthly churn rate
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">ARPU</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">RM{analytics.subscriptionMetrics.averageRevenuePerUser}</div>
                <p className="text-xs text-muted-foreground">
                  Average revenue per user
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Plan Distribution */}
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Plan Distribution</CardTitle>
                <CardDescription>Subscriber distribution across plans</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analytics.planMetrics.planDistribution.map((plan) => (
                    <div key={plan.plan_name} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-3 h-3 rounded-full bg-primary"></div>
                        <span className="font-medium">{plan.plan_name}</span>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">{plan.subscriber_count}</div>
                        <div className="text-sm text-muted-foreground">{plan.percentage}%</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Usage Metrics</CardTitle>
                <CardDescription>Platform usage statistics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Events Created</span>
                    <span className="font-bold">{analytics.usageMetrics.eventsCreated}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Certificates Generated</span>
                    <span className="font-bold">{analytics.usageMetrics.certificatesGenerated}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Attendance Tracked</span>
                    <span className="font-bold">{analytics.usageMetrics.attendanceTracked}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Webhook Calls</span>
                    <span className="font-bold">{analytics.usageMetrics.webhookCalls}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">API Calls</span>
                    <span className="font-bold">{analytics.usageMetrics.apiCalls}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Conversion Rates */}
          <Card>
            <CardHeader>
              <CardTitle>Plan Conversion Rates</CardTitle>
              <CardDescription>Conversion rates between subscription plans</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                {analytics.planMetrics.conversionRates.map((conversion) => (
                  <div key={`${conversion.from_plan}-${conversion.to_plan}`} className="text-center p-4 border rounded-lg">
                    <div className="text-sm text-muted-foreground">
                      {conversion.from_plan} → {conversion.to_plan}
                    </div>
                    <div className="text-2xl font-bold text-primary">
                      {conversion.conversion_rate}%
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  )
}
