"use client"

import { Line, Bar, Doughnut } from "react-chartjs-2"
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from "chart.js"

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
)

interface ChartProps {
  type: 'line' | 'bar' | 'doughnut'
  data: any
  options?: any
  className?: string
}

export function Chart({ type, data, options = {}, className = '' }: ChartProps) {
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: true,
    ...options,
  }

  switch (type) {
    case 'line':
      return <Line data={data} options={chartOptions} className={className} />
    case 'bar':
      return <Bar data={data} options={chartOptions} className={className} />
    case 'doughnut':
      return <Doughnut data={data} options={chartOptions} className={className} />
    default:
      return null
  }
}
