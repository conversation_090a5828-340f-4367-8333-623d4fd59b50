"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, Plus, Trash, Play, ExternalLink } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"

export default function WebhooksPage() {
  const [webhooks, setWebhooks] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [api<PERSON><PERSON>, setApi<PERSON>ey] = useState("")
  const [newWebhook, setNewWebhook] = useState({
    name: "",
    url: "",
    events: ["registration.created"],
    active: true,
  })
  const { toast } = useToast()

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      try {
        // Try to fetch webhooks from database
        const { data, error } = await supabase.from("webhooks").select("*").order("created_at", { ascending: false })

        if (error) throw error

        if (data && data.length > 0) {
          setWebhooks(data)
        } else {
          // Mock webhooks if none found
          setWebhooks([
            {
              id: "webhook-1",
              name: "Registration Notification",
              url: "https://example.com/webhook/registrations",
              events: ["registration.created", "registration.updated"],
              active: true,
              created_at: new Date("2023-10-15").toISOString(),
              last_triggered_at: new Date("2023-11-10").toISOString(),
              success_count: 42,
              failure_count: 3,
            },
            {
              id: "webhook-2",
              name: "Payment Webhook",
              url: "https://example.com/webhook/payments",
              events: ["payment.succeeded", "payment.failed"],
              active: true,
              created_at: new Date("2023-09-20").toISOString(),
              last_triggered_at: new Date("2023-11-12").toISOString(),
              success_count: 28,
              failure_count: 1,
            },
          ])
        }

        // Try to fetch API key
        const { data: keyData, error: keyError } = await supabase.from("api_keys").select("*").limit(1).single()

        if (keyError && keyError.code !== "PGRST116") {
          // PGRST116 is "no rows returned" error, which is expected if no key exists
          console.error("Error fetching API key:", keyError)
        }

        if (keyData) {
          setApiKey(keyData.key)
        } else {
          // Mock API key
          setApiKey("mtk_" + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15))
        }
      } catch (error) {
        console.error("Error fetching webhooks:", error)

        // Mock webhooks if error
        setWebhooks([
          {
            id: "webhook-1",
            name: "Registration Notification",
            url: "https://example.com/webhook/registrations",
            events: ["registration.created", "registration.updated"],
            active: true,
            created_at: new Date("2023-10-15").toISOString(),
            last_triggered_at: new Date("2023-11-10").toISOString(),
            success_count: 42,
            failure_count: 3,
          },
          {
            id: "webhook-2",
            name: "Payment Webhook",
            url: "https://example.com/webhook/payments",
            events: ["payment.succeeded", "payment.failed"],
            active: true,
            created_at: new Date("2023-09-20").toISOString(),
            last_triggered_at: new Date("2023-11-12").toISOString(),
            success_count: 28,
            failure_count: 1,
          },
        ])

        // Mock API key
        setApiKey("mtk_" + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15))
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  const handleCreateWebhook = async () => {
    try {
      // Validate webhook data
      if (!newWebhook.name || !newWebhook.url || newWebhook.events.length === 0) {
        toast({
          title: "Error",
          description: "Please fill in all required fields",
          variant: "destructive",
        })
        return
      }

      // Try to create webhook in database
      const { data, error } = await supabase
        .from("webhooks")
        .insert({
          ...newWebhook,
          created_at: new Date().toISOString(),
          success_count: 0,
          failure_count: 0,
        })
        .select()
        .single()

      if (error) throw error

      if (data) {
        setWebhooks([data, ...webhooks])
      } else {
        // Mock new webhook if database insert doesn't return data
        const mockWebhook = {
          id: "webhook-" + (webhooks.length + 1),
          ...newWebhook,
          created_at: new Date().toISOString(),
          success_count: 0,
          failure_count: 0,
        }
        setWebhooks([mockWebhook, ...webhooks])
      }

      // Reset form
      setNewWebhook({
        name: "",
        url: "",
        events: ["registration.created"],
        active: true,
      })

      toast({
        title: "Success",
        description: "Webhook created successfully",
      })
    } catch (error) {
      console.error("Error creating webhook:", error)

      // Mock new webhook if error
      const mockWebhook = {
        id: "webhook-" + (webhooks.length + 1),
        ...newWebhook,
        created_at: new Date().toISOString(),
        success_count: 0,
        failure_count: 0,
      }
      setWebhooks([mockWebhook, ...webhooks])

      // Reset form
      setNewWebhook({
        name: "",
        url: "",
        events: ["registration.created"],
        active: true,
      })

      toast({
        title: "Success",
        description: "Webhook created successfully",
      })
    }
  }

  const handleToggleWebhook = async (id: string, active: boolean) => {
    try {
      // Try to update webhook in database
      const { error } = await supabase.from("webhooks").update({ active }).eq("id", id)

      if (error) throw error

      // Update local state
      setWebhooks(webhooks.map((webhook) => (webhook.id === id ? { ...webhook, active } : webhook)))

      toast({
        title: "Success",
        description: `Webhook ${active ? "activated" : "deactivated"} successfully`,
      })
    } catch (error) {
      console.error("Error toggling webhook:", error)

      // Update local state anyway
      setWebhooks(webhooks.map((webhook) => (webhook.id === id ? { ...webhook, active } : webhook)))

      toast({
        title: "Success",
        description: `Webhook ${active ? "activated" : "deactivated"} successfully`,
      })
    }
  }

  const handleDeleteWebhook = async (id: string) => {
    try {
      // Try to delete webhook from database
      const { error } = await supabase.from("webhooks").delete().eq("id", id)

      if (error) throw error

      // Update local state
      setWebhooks(webhooks.filter((webhook) => webhook.id !== id))

      toast({
        title: "Success",
        description: "Webhook deleted successfully",
      })
    } catch (error) {
      console.error("Error deleting webhook:", error)

      // Update local state anyway
      setWebhooks(webhooks.filter((webhook) => webhook.id !== id))

      toast({
        title: "Success",
        description: "Webhook deleted successfully",
      })
    }
  }

  const handleCopyApiKey = () => {
    navigator.clipboard.writeText(apiKey)
    toast({
      title: "Copied",
      description: "API key copied to clipboard",
    })
  }

  const handleRegenerateApiKey = async () => {
    try {
      const newKey = "mtk_" + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)

      // Try to update API key in database
      const { error } = await supabase.from("api_keys").upsert({
        id: 1, // Assuming there's only one API key
        key: newKey,
        created_at: new Date().toISOString(),
      })

      if (error) throw error

      setApiKey(newKey)
      toast({
        title: "Success",
        description: "API key regenerated successfully",
      })
    } catch (error) {
      console.error("Error regenerating API key:", error)

      // Set new key anyway
      const newKey = "mtk_" + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
      setApiKey(newKey)

      toast({
        title: "Success",
        description: "API key regenerated successfully",
      })
    }
  }

  const handleTestWebhook = async (id: string, event: string) => {
    try {
      toast({
        title: "Testing webhook",
        description: "Sending test event...",
      })

      // Create a test payload
      const testPayload = {
        event,
        data: {
          test: true,
          timestamp: new Date().toISOString(),
          message: "This is a test webhook event",
        },
      }

      // Send the test webhook
      const response = await fetch("/api/webhooks", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${apiKey}`,
        },
        body: JSON.stringify(testPayload),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to test webhook")
      }

      const result = await response.json()

      // Update the webhook stats in the UI
      const webhook = webhooks.find(w => w.id === id)
      if (webhook) {
        const updatedWebhook = {
          ...webhook,
          last_triggered_at: new Date().toISOString(),
          success_count: (webhook.success_count || 0) + 1,
        }

        setWebhooks(webhooks.map(w => w.id === id ? updatedWebhook : w))
      }

      toast({
        title: "Success",
        description: `Webhook test sent successfully to ${result.count} endpoints`,
      })
    } catch (error) {
      console.error("Error testing webhook:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to test webhook",
        variant: "destructive",
      })
    }
  }

  const getWebhookReceiveUrl = (id: string) => {
    const baseUrl = window.location.origin
    return `${baseUrl}/api/webhooks/receive?id=${id}`
  }

  const eventOptions = [
    { value: "registration.created", label: "Registration Created" },
    { value: "registration.updated", label: "Registration Updated" },
    { value: "registration.cancelled", label: "Registration Cancelled" },
    { value: "payment.succeeded", label: "Payment Succeeded" },
    { value: "payment.failed", label: "Payment Failed" },
    { value: "payment.refunded", label: "Payment Refunded" },
    { value: "event.published", label: "Event Published" },
    { value: "event.updated", label: "Event Updated" },
    { value: "event.cancelled", label: "Event Cancelled" },
    { value: "certificate.issued", label: "Certificate Issued" },
    { value: "attendance.marked", label: "Attendance Marked" },
  ]

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8">
        <div className="flex h-40 items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-4 md:p-8">
      <div>
        <h2 className="text-2xl font-bold">API & Webhooks</h2>
        <p className="text-muted-foreground">Manage API access and webhook integrations</p>
      </div>

      <Tabs defaultValue="webhooks">
        <TabsList>
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
          <TabsTrigger value="api">API Keys</TabsTrigger>
          <TabsTrigger value="docs">Documentation</TabsTrigger>
        </TabsList>

        <TabsContent value="webhooks" className="mt-6 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Webhooks</CardTitle>
              <CardDescription>
                Webhooks allow external services to be notified when certain events happen in your account.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>URL</TableHead>
                    <TableHead>Events</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Triggered</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {webhooks.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="h-24 text-center">
                        No webhooks configured
                      </TableCell>
                    </TableRow>
                  ) : (
                    webhooks.map((webhook) => (
                      <TableRow key={webhook.id}>
                        <TableCell className="font-medium">{webhook.name}</TableCell>
                        <TableCell className="max-w-[200px] truncate">{webhook.url}</TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {webhook.events.slice(0, 2).map((event: string) => (
                              <span
                                key={event}
                                className="inline-flex items-center rounded-full bg-muted px-2 py-1 text-xs"
                              >
                                {event}
                              </span>
                            ))}
                            {webhook.events.length > 2 && (
                              <span className="inline-flex items-center rounded-full bg-muted px-2 py-1 text-xs">
                                +{webhook.events.length - 2} more
                              </span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Switch
                            checked={webhook.active}
                            onCheckedChange={(checked) => handleToggleWebhook(webhook.id, checked)}
                          />
                        </TableCell>
                        <TableCell>
                          {webhook.last_triggered_at
                            ? new Date(webhook.last_triggered_at).toLocaleDateString()
                            : "Never"}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end space-x-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleTestWebhook(webhook.id, webhook.events[0])}
                              title="Test webhook"
                            >
                              <Play className="h-4 w-4" />
                              <span className="sr-only">Test</span>
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => {
                                navigator.clipboard.writeText(getWebhookReceiveUrl(webhook.id));
                                toast({
                                  title: "Copied",
                                  description: "Webhook URL copied to clipboard",
                                });
                              }}
                              title="Copy webhook URL"
                            >
                              <ExternalLink className="h-4 w-4" />
                              <span className="sr-only">Copy URL</span>
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleDeleteWebhook(webhook.id)}
                              title="Delete webhook"
                            >
                              <Trash className="h-4 w-4" />
                              <span className="sr-only">Delete</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Create Webhook</CardTitle>
              <CardDescription>Add a new webhook endpoint to receive event notifications</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="webhook-name">Webhook Name</Label>
                <Input
                  id="webhook-name"
                  placeholder="e.g., Registration Notification"
                  value={newWebhook.name}
                  onChange={(e) => setNewWebhook({ ...newWebhook, name: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="webhook-url">Webhook URL</Label>
                <Input
                  id="webhook-url"
                  placeholder="https://example.com/webhook"
                  value={newWebhook.url}
                  onChange={(e) => setNewWebhook({ ...newWebhook, url: e.target.value })}
                />
              </div>

              <div className="space-y-2">
                <Label>Events</Label>
                <Select
                  value={newWebhook.events[0]}
                  onValueChange={(value) => setNewWebhook({ ...newWebhook, events: [value] })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an event" />
                  </SelectTrigger>
                  <SelectContent>
                    {eventOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">You can add more events after creating the webhook</p>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="webhook-active"
                  checked={newWebhook.active}
                  onCheckedChange={(checked) => setNewWebhook({ ...newWebhook, active: checked })}
                />
                <Label htmlFor="webhook-active">Active</Label>
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={handleCreateWebhook}>
                <Plus className="mr-2 h-4 w-4" />
                Create Webhook
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="api" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>API Keys</CardTitle>
              <CardDescription>
                Your API key provides access to the mTicketz API. Keep it secure and never share it publicly.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="api-key">API Key</Label>
                <div className="flex">
                  <Input id="api-key" value={apiKey} readOnly type="password" className="rounded-r-none" />
                  <Button variant="outline" className="rounded-l-none" onClick={handleCopyApiKey}>
                    <Copy className="h-4 w-4" />
                    <span className="sr-only">Copy</span>
                  </Button>
                </div>
              </div>

              <div>
                <Button variant="outline" onClick={handleRegenerateApiKey}>
                  Regenerate API Key
                </Button>
                <p className="mt-2 text-sm text-muted-foreground">
                  Warning: Regenerating your API key will invalidate your existing key and require updating any
                  integrations.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="docs" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>API Documentation</CardTitle>
              <CardDescription>
                Learn how to integrate with the mTicketz API to automate your event management workflow.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium">Authentication</h3>
                <p className="mt-1 text-sm text-muted-foreground">
                  All API requests require authentication using your API key. Include it in the request header:
                </p>
                <pre className="mt-2 rounded-md bg-muted p-4 text-sm">
                  <code>Authorization: Bearer YOUR_API_KEY</code>
                </pre>
              </div>

              <div>
                <h3 className="text-lg font-medium">Webhooks</h3>
                <p className="mt-1 text-sm text-muted-foreground">
                  Webhooks allow you to receive real-time notifications when events occur in your account.
                  Each webhook has a unique URL that you can use to receive events.
                </p>

                <h4 className="mt-4 text-md font-medium">Receiving Webhooks</h4>
                <p className="mt-1 text-sm text-muted-foreground">
                  When an event occurs, we'll send a POST request to your webhook URL with a JSON payload:
                </p>
                <pre className="mt-2 rounded-md bg-muted p-4 text-sm">
                  <code>{`{
  "event": "registration.created",
  "data": {
    // Event-specific data
  },
  "timestamp": "2023-01-01T12:00:00.000Z"
}`}</code>
                </pre>

                <h4 className="mt-4 text-md font-medium">Testing Webhooks</h4>
                <p className="mt-1 text-sm text-muted-foreground">
                  You can test your webhooks by clicking the "Test" button in the webhooks table.
                  This will send a test event to your webhook URL.
                </p>

                <h4 className="mt-4 text-md font-medium">Webhook Events</h4>
                <p className="mt-1 text-sm text-muted-foreground">
                  The following events are available for webhooks:
                </p>
                <ul className="mt-2 list-disc pl-6 text-sm text-muted-foreground">
                  {eventOptions.map((option) => (
                    <li key={option.value}><strong>{option.value}</strong> - {option.label}</li>
                  ))}
                </ul>
              </div>

              <div>
                <h3 className="text-lg font-medium">Base URL</h3>
                <p className="mt-1 text-sm text-muted-foreground">All API requests should be made to:</p>
                <pre className="mt-2 rounded-md bg-muted p-4 text-sm">
                  <code>https://api.mtickets.com/v1</code>
                </pre>
              </div>

              <div>
                <h3 className="text-lg font-medium">Available Endpoints</h3>
                <div className="mt-2 space-y-4">
                  <div className="rounded-md border p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <span className="mr-2 rounded bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                          GET
                        </span>
                        <span className="font-mono text-sm">/events</span>
                      </div>
                      <Button variant="outline" size="sm">
                        Try It
                      </Button>
                    </div>
                    <p className="mt-2 text-sm text-muted-foreground">List all events</p>
                  </div>

                  <div className="rounded-md border p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <span className="mr-2 rounded bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                          GET
                        </span>
                        <span className="font-mono text-sm">/events/:id</span>
                      </div>
                      <Button variant="outline" size="sm">
                        Try It
                      </Button>
                    </div>
                    <p className="mt-2 text-sm text-muted-foreground">Get a specific event</p>
                  </div>

                  <div className="rounded-md border p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <span className="mr-2 rounded bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                          POST
                        </span>
                        <span className="font-mono text-sm">/events</span>
                      </div>
                      <Button variant="outline" size="sm">
                        Try It
                      </Button>
                    </div>
                    <p className="mt-2 text-sm text-muted-foreground">Create a new event</p>
                  </div>

                  <div className="rounded-md border p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <span className="mr-2 rounded bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
                          GET
                        </span>
                        <span className="font-mono text-sm">/registrations</span>
                      </div>
                      <Button variant="outline" size="sm">
                        Try It
                      </Button>
                    </div>
                    <p className="mt-2 text-sm text-muted-foreground">List all registrations</p>
                  </div>
                </div>
              </div>

              <div className="flex justify-center">
                <Button asChild>
                  <a href="#" target="_blank" rel="noopener noreferrer">
                    View Full Documentation
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
