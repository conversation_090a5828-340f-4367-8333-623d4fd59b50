"use client"

import type React from "react"
import { useState, use<PERSON><PERSON>back, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Check, ChevronsUpDown, Plus } from "lucide-react"
import { cn } from "@/lib/utils"

interface OrganizationData {
  name: string
  ssmNumber: string
  picName: string
  picPhone: string
  picEmail: string
  address: string
  website: string
}

interface OrganizationTabProps {
  organizationData: OrganizationData
  setOrganizationData: (data: OrganizationData) => void
  isSubmitting: boolean
  setIsSubmitting: (submitting: boolean) => void
  isLoadingOrganization?: boolean
  initialSelectedOrganization?: any
}

export function OrganizationTab({
  organizationData,
  setOrganizationData,
  isSubmitting,
  setIsSubmitting,
  isLoadingOrganization = false,
  initialSelectedOrganization = null
}: OrganizationTabProps) {
  // Organization search state
  const [organizations, setOrganizations] = useState<any[]>([])
  const [selectedOrganization, setSelectedOrganization] = useState<any>(null)
  const [organizationSearchOpen, setOrganizationSearchOpen] = useState(false)
  const [organizationSearchValue, setOrganizationSearchValue] = useState("")
  const [showCreateNew, setShowCreateNew] = useState(false)
  const [isLoadingOrganizations, setIsLoadingOrganizations] = useState(false)
  const [isEditingExisting, setIsEditingExisting] = useState(false)
  const [isUserLinkedToOrg, setIsUserLinkedToOrg] = useState(false)

  const { updateProfile, user } = useAuth()
  const { toast } = useToast()

  // Initialize selected organization from props
  useEffect(() => {
    if (initialSelectedOrganization && !selectedOrganization) {
      setSelectedOrganization(initialSelectedOrganization)
      setOrganizationSearchValue(initialSelectedOrganization.name)
      setShowCreateNew(false)
      // Check if user is linked to this organization
      setIsUserLinkedToOrg(user?.organization_id === initialSelectedOrganization.id)
    }
  }, [initialSelectedOrganization, selectedOrganization, user?.organization_id])

  // Initialize search value when organization data is loaded
  useEffect(() => {
    if (organizationData.name && !organizationSearchValue) {
      setOrganizationSearchValue(organizationData.name)
    }
  }, [organizationData.name, organizationSearchValue])

  // Helper function to get cookie value
  const getCookie = (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  // Function to search organizations
  const searchOrganizations = useCallback(async (searchTerm: string) => {
    if (!searchTerm.trim()) {
      setOrganizations([])
      return
    }

    setIsLoadingOrganizations(true)
    try {
      const response = await fetch(`/api/organizations?search=${encodeURIComponent(searchTerm)}&limit=10`)
      if (response.ok) {
        const data = await response.json()
        setOrganizations(data.organizations || [])
      } else {
        console.error("Failed to search organizations")
        setOrganizations([])
      }
    } catch (error) {
      console.error("Error searching organizations:", error)
      setOrganizations([])
    } finally {
      setIsLoadingOrganizations(false)
    }
  }, [])

  // Handle organization selection
  const handleOrganizationSelect = (organization: any) => {
    setSelectedOrganization(organization)
    setOrganizationData({
      name: organization.name,
      ssmNumber: organization.ssm_number || "",
      picName: organization.pic_name || "",
      picPhone: organization.pic_phone || "",
      picEmail: organization.pic_email || "",
      address: organization.address || "",
      website: organization.website || "",
    })
    setOrganizationSearchValue(organization.name)
    setOrganizationSearchOpen(false)
    setShowCreateNew(false)
    setIsEditingExisting(false)
    // Check if user is linked to this organization
    setIsUserLinkedToOrg(user?.organization_id === organization.id)
  }

  // Handle creating new organization
  const handleCreateNewOrganization = () => {
    setSelectedOrganization(null)
    setOrganizationData({
      name: organizationSearchValue,
      ssmNumber: "",
      picName: "",
      picPhone: "",
      picEmail: "",
      address: "",
      website: "",
    })
    setShowCreateNew(true)
    setOrganizationSearchOpen(false)
    setIsUserLinkedToOrg(false)
  }

  // Handle unlinking organization
  const handleUnlinkOrganization = async () => {
    if (!selectedOrganization) return

    setIsSubmitting(true)
    try {
      // Update user profile to remove organization link
      const updatedUser = await updateProfile({
        organization_id: null,
        organization: null,
      })

      if (updatedUser) {
        setIsUserLinkedToOrg(false)
        toast({
          title: "Success",
          description: "Organization unlinked from your profile successfully",
        })
      }
    } catch (error: any) {
      console.error("Error unlinking organization:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to unlink organization",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleOrganizationSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      let organizationId = selectedOrganization?.id
      let isCreatingNew = false
      let isUpdatingExisting = false

      const token = getCookie('auth_token');
      if (!token) {
        throw new Error("Not authenticated");
      }

      // Handle different scenarios
      if (showCreateNew && !selectedOrganization) {
        // Creating a new organization
        const response = await fetch("/api/organizations/create", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            name: organizationData.name,
            ssmNumber: organizationData.ssmNumber,
            picName: organizationData.picName,
            picPhone: organizationData.picPhone,
            picEmail: organizationData.picEmail,
            address: organizationData.address,
            website: organizationData.website,
          }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "Failed to create organization")
        }

        const data = await response.json()
        organizationId = data.organization.id
        setSelectedOrganization(data.organization)
        setShowCreateNew(false)
        isCreatingNew = true
      } else if (isEditingExisting && selectedOrganization) {
        // Updating existing organization
        const response = await fetch("/api/organizations/update", {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            organizationId: selectedOrganization.id,
            name: organizationData.name,
            ssmNumber: organizationData.ssmNumber,
            picName: organizationData.picName,
            picPhone: organizationData.picPhone,
            picEmail: organizationData.picEmail,
            address: organizationData.address,
            website: organizationData.website,
          }),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || "Failed to update organization")
        }

        const data = await response.json()
        setSelectedOrganization(data.organization)
        setIsEditingExisting(false)
        isUpdatingExisting = true
        organizationId = selectedOrganization.id
      } else if (selectedOrganization) {
        // Using existing organization (just linking)
        organizationId = selectedOrganization.id
      } else {
        throw new Error("Please select an organization or create a new one")
      }

      // Update user profile with organization details (only if not just updating org details)
      if (!isUpdatingExisting) {
        const updatedUser = await updateProfile({
          organization_id: organizationId,
          organization: selectedOrganization?.name || organizationData.name,
        })

        if (!updatedUser) {
          throw new Error("Failed to update user profile")
        }

        // Update linked status
        setIsUserLinkedToOrg(true)
      }

      // Show success message
      toast({
        title: "Success",
        description: isCreatingNew
          ? "Organization created and linked successfully"
          : isUpdatingExisting
            ? "Organization details updated successfully"
            : "Organization linked to your profile successfully",
      })
    } catch (error: any) {
      console.error("Error updating organization:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to update organization details",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Organization Details</CardTitle>
        <CardDescription>Search for an existing organization or create a new one</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoadingOrganization ? (
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
              <p className="text-sm text-muted-foreground">Loading organization data...</p>
            </div>
          </div>
        ) : (
        <form onSubmit={handleOrganizationSubmit} className="space-y-6">
          <div className="grid gap-4">
            <div className="grid gap-2">
              <Label htmlFor="org-search">Organization Name</Label>
              <Popover open={organizationSearchOpen} onOpenChange={setOrganizationSearchOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={organizationSearchOpen}
                    className="w-full justify-between"
                  >
                    {organizationSearchValue || "Search for organization..."}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandInput
                      placeholder="Search organizations..."
                      value={organizationSearchValue}
                      onValueChange={(value) => {
                        setOrganizationSearchValue(value)
                        searchOrganizations(value)
                      }}
                    />
                    <CommandList>
                      <CommandEmpty>
                        {isLoadingOrganizations ? (
                          "Searching..."
                        ) : organizationSearchValue ? (
                          <div className="p-2">
                            <p className="text-sm text-muted-foreground mb-2">No organizations found.</p>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={handleCreateNewOrganization}
                              className="w-full"
                            >
                              <Plus className="mr-2 h-4 w-4" />
                              Create "{organizationSearchValue}"
                            </Button>
                          </div>
                        ) : (
                          "Start typing to search organizations..."
                        )}
                      </CommandEmpty>
                      {organizations.length > 0 && (
                        <CommandGroup>
                          {organizations.map((org) => (
                            <CommandItem
                              key={org.id}
                              value={org.name}
                              onSelect={() => handleOrganizationSelect(org)}
                            >
                              <Check
                                className={cn(
                                  "mr-2 h-4 w-4",
                                  selectedOrganization?.id === org.id ? "opacity-100" : "opacity-0"
                                )}
                              />
                              <div className="flex flex-col">
                                <span className="font-medium">{org.name}</span>
                                {org.ssm_number && (
                                  <span className="text-xs text-muted-foreground">SSM: {org.ssm_number}</span>
                                )}
                              </div>
                            </CommandItem>
                          ))}
                          {organizationSearchValue && (
                            <CommandItem onSelect={handleCreateNewOrganization}>
                              <Plus className="mr-2 h-4 w-4" />
                              Create "{organizationSearchValue}"
                            </CommandItem>
                          )}
                        </CommandGroup>
                      )}
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
              {selectedOrganization && !showCreateNew && !isEditingExisting && (
                <div className="mt-2 p-3 bg-muted rounded-md">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-medium">
                          {isUserLinkedToOrg ? "Linked:" : "Selected:"} {selectedOrganization.name}
                        </p>
                        {isUserLinkedToOrg && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Linked
                          </span>
                        )}
                      </div>
                      {selectedOrganization.ssm_number && (
                        <p className="text-xs text-muted-foreground">SSM: {selectedOrganization.ssm_number}</p>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setIsEditingExisting(true)}
                      >
                        Edit Details
                      </Button>
                      {isUserLinkedToOrg && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handleUnlinkOrganization}
                          disabled={isSubmitting}
                        >
                          Unlink
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {(showCreateNew || !selectedOrganization || isEditingExisting) && (
              <>
                <div className="grid gap-2">
                  <Label htmlFor="org-name-input">Organization Name</Label>
                  <Input
                    id="org-name-input"
                    value={organizationData.name}
                    onChange={(e) => setOrganizationData({ ...organizationData, name: e.target.value })}
                    placeholder="Enter organization name"
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="ssm-number">SSM Number</Label>
                  <Input
                    id="ssm-number"
                    value={organizationData.ssmNumber}
                    onChange={(e) => setOrganizationData({ ...organizationData, ssmNumber: e.target.value })}
                    placeholder="Enter SSM number"
                    required
                  />
                </div>
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="grid gap-2">
                    <Label htmlFor="pic-name">Person In Charge (PIC)</Label>
                    <Input
                      id="pic-name"
                      value={organizationData.picName}
                      onChange={(e) => setOrganizationData({ ...organizationData, picName: e.target.value })}
                      placeholder="Enter PIC name"
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="pic-phone">PIC Phone Number</Label>
                    <Input
                      id="pic-phone"
                      value={organizationData.picPhone}
                      onChange={(e) => setOrganizationData({ ...organizationData, picPhone: e.target.value })}
                      placeholder="Enter PIC phone number"
                      required
                    />
                  </div>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="pic-email">PIC Email</Label>
                  <Input
                    id="pic-email"
                    type="email"
                    value={organizationData.picEmail}
                    onChange={(e) => setOrganizationData({ ...organizationData, picEmail: e.target.value })}
                    placeholder="Enter PIC email address"
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="address">Organization Address</Label>
                  <Textarea
                    id="address"
                    value={organizationData.address}
                    onChange={(e) => setOrganizationData({ ...organizationData, address: e.target.value })}
                    placeholder="Enter organization address"
                    rows={3}
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    type="url"
                    value={organizationData.website}
                    onChange={(e) => setOrganizationData({ ...organizationData, website: e.target.value })}
                    placeholder="https://example.com"
                  />
                </div>
              </>
            )}
          </div>
          <div className="flex justify-end gap-2">
            {isEditingExisting && (
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsEditingExisting(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isSubmitting || (selectedOrganization && isUserLinkedToOrg && !isEditingExisting && !showCreateNew)}
            >
              {isSubmitting
                ? "Saving..."
                : showCreateNew
                  ? "Create & Link Organization"
                  : isEditingExisting
                    ? "Update Organization"
                    : selectedOrganization && !isUserLinkedToOrg
                      ? "Link Organization"
                      : selectedOrganization && isUserLinkedToOrg
                        ? "Already Linked"
                        : "Save Organization"
              }
            </Button>
          </div>
        </form>
        )}
      </CardContent>
    </Card>
  )
}
