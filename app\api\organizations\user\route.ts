import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth"

/**
 * GET /api/organizations/user
 * Fetches the organization details for the current user
 * Requires authentication
 */
export async function GET(request: Request) {
  try {
    // Get JWT token from request
    const token = getJWTTokenFromRequest(request)

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Verify the token
    const authResult = await verifyJWTToken(token)
    if (!authResult || !authResult.user) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 }
      )
    }

    const userId = authResult.user.id
    console.log("User Organization API: Fetching organization for user:", userId)

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin()

    // First, get the user's organization_id
    const { data: userData, error: userError } = await supabaseAdmin
      .from("users")
      .select("organization_id, organization")
      .eq("id", userId)
      .single()

    if (userError) {
      console.error("Error fetching user data:", userError)
      return NextResponse.json(
        { error: "Failed to fetch user data" },
        { status: 500 }
      )
    }

    // If user has no organization_id, return null
    if (!userData.organization_id) {
      console.log("User Organization API: User has no organization_id")
      return NextResponse.json({
        organization: null,
        legacy_organization_name: userData.organization || null
      })
    }

    // Fetch the organization details
    const { data: orgData, error: orgError } = await supabaseAdmin
      .from("organizations")
      .select("*")
      .eq("id", userData.organization_id)
      .single()

    if (orgError) {
      console.error("Error fetching organization:", orgError)
      return NextResponse.json(
        { error: "Failed to fetch organization details" },
        { status: 500 }
      )
    }

    console.log("User Organization API: Successfully fetched organization:", orgData.name)

    return NextResponse.json({
      organization: orgData,
      legacy_organization_name: userData.organization || null
    })
  } catch (error: any) {
    console.error("Error in user organization API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
