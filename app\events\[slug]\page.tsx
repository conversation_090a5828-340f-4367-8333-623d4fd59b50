"use client"

import * as React from "react"
import { EventDetailComponent } from "@/components/event-detail-component"

interface EventDetailPageProps {
  params: Promise<{
    slug: string
  }>
}

export default function EventDetailPage({ params }: EventDetailPageProps) {
  // Get the slug from params using React.use()
  const { slug } = React.use(params)

  return <EventDetailComponent slug={slug} />
}
