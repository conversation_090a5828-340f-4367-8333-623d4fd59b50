"use client"

import React from 'react'
import { QRCodeSVG } from 'qrcode.react'

export interface TicketTemplateProps {
  ticketData: {
    id: string
    event: {
      title: string
      start_date: string
      end_date: string
      location: string
      image_url?: string
    }
    guest_name: string
    guest_email: string
    phone?: string
    payment_amount?: number
    payment_status: string
    registration_code: string
    created_at: string
    qr_code_data: string
  }
  className?: string
}

export function TicketPDFTemplate({ ticketData, className = "" }: TicketTemplateProps) {
  const eventDate = new Date(ticketData.event.start_date).toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
  
  const eventTime = new Date(ticketData.event.start_date).toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit'
  })

  return (
    <div 
      className={`ticket-template ${className}`}
      style={{
        width: '800px',
        height: '400px',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        borderRadius: '20px',
        padding: '30px',
        boxSizing: 'border-box',
        fontFamily: 'Arial, sans-serif',
        color: 'white',
        position: 'relative',
        overflow: 'hidden',
        boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
      }}
    >
      {/* Decorative Elements */}
      <div
        style={{
          position: 'absolute',
          width: '200px',
          height: '200px',
          background: 'rgba(255,255,255,0.1)',
          borderRadius: '50%',
          top: '-100px',
          right: '-100px'
        }}
      />
      <div
        style={{
          position: 'absolute',
          width: '150px',
          height: '150px',
          background: 'rgba(255,255,255,0.05)',
          borderRadius: '50%',
          bottom: '-75px',
          left: '-75px'
        }}
      />

      {/* Perforation Line */}
      <div
        style={{
          position: 'absolute',
          right: '200px',
          top: '0',
          bottom: '0',
          width: '2px',
          background: `repeating-linear-gradient(
            to bottom,
            transparent 0px,
            transparent 8px,
            rgba(255,255,255,0.3) 8px,
            rgba(255,255,255,0.3) 12px
          )`
        }}
      />

      {/* Main Content */}
      <div
        style={{
          display: 'flex',
          height: '100%',
          gap: '30px',
          position: 'relative',
          zIndex: 1
        }}
      >
        {/* Left Section - Event Details */}
        <div
          style={{
            flex: '2',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between'
          }}
        >
          {/* Header */}
          <div>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '20px' }}>
              <div
                style={{
                  background: 'white',
                  padding: '8px 16px',
                  borderRadius: '8px',
                  marginRight: '15px'
                }}
              >
                <span style={{ color: '#667eea', fontWeight: 'bold', fontSize: '16px' }}>
                  mTicket.my
                </span>
              </div>
              <span style={{ fontSize: '14px', opacity: '0.9' }}>Event Ticket</span>
            </div>
          </div>

          {/* Event Details */}
          <div>
            <h1
              style={{
                fontSize: '28px',
                fontWeight: 'bold',
                margin: '0 0 15px 0',
                lineHeight: '1.2'
              }}
            >
              {ticketData.event.title}
            </h1>
            <div style={{ fontSize: '16px', opacity: '0.9', marginBottom: '10px' }}>
              <div style={{ marginBottom: '8px' }}>📅 {eventDate}</div>
              <div style={{ marginBottom: '8px' }}>🕐 {eventTime}</div>
              <div style={{ marginBottom: '8px' }}>📍 {ticketData.event.location}</div>
            </div>
          </div>

          {/* Attendee Info */}
          <div
            style={{
              background: 'rgba(255,255,255,0.1)',
              padding: '15px',
              borderRadius: '10px',
              backdropFilter: 'blur(10px)'
            }}
          >
            <div style={{ fontSize: '14px', opacity: '0.8', marginBottom: '5px' }}>
              Attendee
            </div>
            <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
              {ticketData.guest_name}
            </div>
            <div style={{ fontSize: '12px', opacity: '0.7', marginTop: '5px' }}>
              Ticket #{ticketData.registration_code}
            </div>
          </div>
        </div>

        {/* Right Section - QR Code */}
        <div
          style={{
            flex: '1',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            textAlign: 'center'
          }}
        >
          <div
            style={{
              background: 'white',
              padding: '20px',
              borderRadius: '15px',
              marginBottom: '15px'
            }}
          >
            <QRCodeSVG
              value={`https://mticket.my/verify?data=${btoa(ticketData.qr_code_data)}`}
              size={120}
              level="M"
              includeMargin={false}
            />
          </div>
          <div style={{ fontSize: '12px', opacity: '0.8' }}>Scan at entrance</div>
          <div style={{ fontSize: '10px', opacity: '0.6', marginTop: '5px' }}>
            Security verified
          </div>
        </div>
      </div>

      {/* Security Footer */}
      <div
        style={{
          position: 'absolute',
          bottom: '10px',
          left: '30px',
          right: '30px',
          display: 'flex',
          justifyContent: 'space-between',
          fontSize: '8px',
          opacity: '0.6'
        }}
      >
        <span>AUTHENTIC TICKET - mTicket.my</span>
        <span>Generated: {new Date().toLocaleString()}</span>
      </div>
    </div>
  )
}

export function ReceiptPDFTemplate({ ticketData, className = "" }: TicketTemplateProps) {
  return (
    <div 
      className={`receipt-template ${className}`}
      style={{
        width: '600px',
        minHeight: '800px',
        background: 'white',
        padding: '40px',
        boxSizing: 'border-box',
        fontFamily: 'Arial, sans-serif',
        color: '#333',
        border: '1px solid #ddd'
      }}
    >
      {/* Header */}
      <div style={{ textAlign: 'center', marginBottom: '40px' }}>
        <div
          style={{
            background: '#667eea',
            color: 'white',
            padding: '15px',
            borderRadius: '8px',
            marginBottom: '20px'
          }}
        >
          <h1 style={{ margin: '0', fontSize: '24px' }}>mTicket.my</h1>
          <p style={{ margin: '5px 0 0 0', fontSize: '14px', opacity: '0.9' }}>
            Payment Receipt
          </p>
        </div>
      </div>

      {/* Receipt Details */}
      <div style={{ marginBottom: '30px' }}>
        <h2 style={{ fontSize: '18px', marginBottom: '20px', color: '#667eea' }}>
          Receipt Details
        </h2>
        <div style={{ display: 'grid', gap: '10px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <span style={{ fontWeight: 'bold' }}>Receipt ID:</span>
            <span>{ticketData.registration_code}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <span style={{ fontWeight: 'bold' }}>Date:</span>
            <span>{new Date(ticketData.created_at).toLocaleDateString()}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <span style={{ fontWeight: 'bold' }}>Status:</span>
            <span style={{ 
              color: ticketData.payment_status === 'paid' ? '#10b981' : '#f59e0b',
              fontWeight: 'bold'
            }}>
              {ticketData.payment_status.toUpperCase()}
            </span>
          </div>
        </div>
      </div>

      {/* Event Details */}
      <div style={{ marginBottom: '30px' }}>
        <h2 style={{ fontSize: '18px', marginBottom: '20px', color: '#667eea' }}>
          Event Details
        </h2>
        <div style={{ display: 'grid', gap: '10px' }}>
          <div>
            <span style={{ fontWeight: 'bold' }}>Event:</span>
            <div style={{ marginTop: '5px' }}>{ticketData.event.title}</div>
          </div>
          <div>
            <span style={{ fontWeight: 'bold' }}>Date:</span>
            <div style={{ marginTop: '5px' }}>
              {new Date(ticketData.event.start_date).toLocaleDateString()}
            </div>
          </div>
          <div>
            <span style={{ fontWeight: 'bold' }}>Location:</span>
            <div style={{ marginTop: '5px' }}>{ticketData.event.location}</div>
          </div>
        </div>
      </div>

      {/* Payment Details */}
      <div style={{ marginBottom: '30px' }}>
        <h2 style={{ fontSize: '18px', marginBottom: '20px', color: '#667eea' }}>
          Payment Details
        </h2>
        <div
          style={{
            background: '#f8fafc',
            padding: '20px',
            borderRadius: '8px',
            border: '1px solid #e2e8f0'
          }}
        >
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
            <span>Ticket Price:</span>
            <span>RM {(ticketData.payment_amount || 0).toFixed(2)}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '10px' }}>
            <span>Processing Fee:</span>
            <span>RM 0.00</span>
          </div>
          <hr style={{ margin: '15px 0', border: 'none', borderTop: '1px solid #e2e8f0' }} />
          <div style={{ display: 'flex', justifyContent: 'space-between', fontWeight: 'bold', fontSize: '18px' }}>
            <span>Total Paid:</span>
            <span>RM {(ticketData.payment_amount || 0).toFixed(2)}</span>
          </div>
        </div>
      </div>

      {/* Attendee Details */}
      <div style={{ marginBottom: '40px' }}>
        <h2 style={{ fontSize: '18px', marginBottom: '20px', color: '#667eea' }}>
          Attendee Information
        </h2>
        <div style={{ display: 'grid', gap: '10px' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <span style={{ fontWeight: 'bold' }}>Name:</span>
            <span>{ticketData.guest_name}</span>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <span style={{ fontWeight: 'bold' }}>Email:</span>
            <span>{ticketData.guest_email}</span>
          </div>
          {ticketData.phone && (
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span style={{ fontWeight: 'bold' }}>Phone:</span>
              <span>{ticketData.phone}</span>
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div
        style={{
          textAlign: 'center',
          padding: '20px',
          background: '#f8fafc',
          borderRadius: '8px',
          marginTop: '40px'
        }}
      >
        <p style={{ margin: '0', fontSize: '14px', color: '#64748b' }}>
          Thank you for your payment!
        </p>
        <p style={{ margin: '10px 0 0 0', fontSize: '12px', color: '#94a3b8' }}>
          This is an official receipt from mTicket.my
        </p>
      </div>
    </div>
  )
}
