import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import jwt from "jsonwebtoken";
import { createServerClient } from '@supabase/ssr';
import { getSupabaseAdmin } from '@/lib/supabase';

// Simple JWT token validation for Edge Runtime
function isValidJWTFormat(token: string): boolean {
  try {
    // JWT should have 3 parts separated by dots
    const parts = token.split('.');
    if (parts.length !== 3) return false;

    // Each part should be base64 encoded
    for (const part of parts) {
      if (!part || part.length === 0) return false;
    }

    // Try to decode the payload to check if it's valid JSON
    const payload = JSON.parse(atob(parts[1].replace(/-/g, '+').replace(/_/g, '/')));

    // Check if token has required fields and is not expired
    if (!payload.userId || !payload.exp) return false;

    // Check if token is not expired (exp is in seconds, Date.now() is in milliseconds)
    if (payload.exp * 1000 < Date.now()) return false;

    return true;
  } catch (error) {
    return false;
  }
}

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;
  const isAuthPage = pathname.startsWith("/auth");
  const isDashboardPage = pathname.startsWith("/dashboard");
  const isApiRoute = pathname.startsWith("/api");
  const isMaintenancePage = pathname.startsWith("/maintenance");

  console.log(`Middleware: ${pathname} - Auth: ${isAuthPage}, Dashboard: ${isDashboardPage}, API: ${isApiRoute}`);

  // Skip maintenance check for certain paths
  const skipMaintenancePaths = [
    '/api/maintenance',
    '/maintenance',
    '/api/admin/app-settings',
    '/api/settings',         // Allow settings API during maintenance
    '/auth/login',           // Allow admin login during maintenance
    '/auth/register',        // Allow registration if needed
    '/auth/forgot-password', // Allow password reset
    '/auth',                 // Allow all auth routes
    '/api/auth',             // Allow auth API routes
    '/_next',
    '/favicon.ico',
    '/images',
    '/static'
  ];

  // Check maintenance mode first (unless it's an exempt path)
  if (!skipMaintenancePaths.some(path => pathname.startsWith(path))) {
    try {
      const supabaseAdmin = getSupabaseAdmin();
      const { data: settings, error } = await supabaseAdmin
        .from('app_settings')
        .select('maintenance_mode, maintenance_message')
        .single();

      // If maintenance mode is enabled, check if user is admin
      if (!error && settings?.maintenance_mode) {
        let isAdmin = false;

        // Get the token from cookies
        const token = req.cookies.get("auth_token")?.value;

        if (token && isValidJWTFormat(token)) {
          try {
            const decoded = jwt.decode(token) as any;
            isAdmin = decoded?.role_name === 'admin';
          } catch (error) {
            console.error('Error decoding token for admin check:', error);
          }
        }

        // If user is not admin, redirect to maintenance page
        if (!isAdmin && !isMaintenancePage) {
          const maintenanceUrl = new URL('/maintenance', req.url);
          // Security: Don't expose maintenance message in URL parameters
          // The maintenance page will fetch the message server-side
          return NextResponse.redirect(maintenanceUrl);
        }
      }
    } catch (error) {
      console.error('Error checking maintenance mode:', error);
      // On error, continue normally
    }
  }

  // Skip middleware for API routes (except auth routes which handle their own auth)
  if (isApiRoute) {
    return NextResponse.next();
  }

  // Get the token from cookies
  const token = req.cookies.get("auth_token")?.value;
  console.log(`Middleware: Token present: ${!!token}`);

  // If the user is logged in and tries to access an auth page, redirect to dashboard
  if (token && isAuthPage) {
    if (isValidJWTFormat(token)) {
      console.log(`Middleware: Valid token found, redirecting to dashboard from ${req.nextUrl.pathname}`);

      // Try to decode the token to get user role for proper redirect
      try {
        const decoded = jwt.decode(token) as any;
        if (decoded && decoded.role_name === "user") {
          console.log(`Middleware: User role detected, redirecting to My Tickets`);
          return NextResponse.redirect(new URL("/dashboard/my-tickets", req.url));
        }
      } catch (error) {
        console.log(`Middleware: Could not decode token for role check, using default redirect`);
      }

      return NextResponse.redirect(new URL("/dashboard", req.url));
    } else {
      // Token is invalid, continue to auth page
      console.log(`Middleware: Invalid token found, removing cookie and continuing to auth page`);
      const response = NextResponse.next();
      response.cookies.delete("auth_token");
      return response;
    }
  }

  // If the user is not logged in and tries to access a protected page, redirect to login
  if (!token && isDashboardPage) {
    console.log(`Middleware: No token found, redirecting to login from ${req.nextUrl.pathname}`);
    const loginUrl = new URL("/auth/login", req.url);
    loginUrl.searchParams.set("redirectTo", req.nextUrl.pathname);
    return NextResponse.redirect(loginUrl);
  }

  // If user has a token but tries to access dashboard, verify the token
  if (token && isDashboardPage) {
    console.log(`Middleware: Token preview: ${token.substring(0, 20)}...`);

    if (isValidJWTFormat(token)) {
      console.log(`Middleware: Valid token verified, allowing access to ${req.nextUrl.pathname}`);
      return NextResponse.next();
    } else {
      // Token is invalid, redirect to login
      console.log(`Middleware: Invalid token for dashboard access, redirecting to login`);
      const response = NextResponse.redirect(new URL("/auth/login", req.url));
      response.cookies.delete("auth_token");
      return response;
    }
  }

  // For all other cases, continue normally
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - robots.txt (robots file)
     * - sitemap.xml (sitemap file)
     * - manifest.json (manifest file)
     * - icon.png, apple-icon.png (icon files)
     */
    '/((?!_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|manifest.json|icon.png|apple-icon.png).*)',
  ],
};
