import { NextAuthOptions, getServerSession } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { supabase, getSupabaseAdmin } from "@/lib/supabase";
import { GetServerSidePropsContext, NextApiRequest, NextApiResponse } from "next";
import { logActivity, ActivityCategory } from "@/utils/activity-logger";
import jwt from "jsonwebtoken";

// Get the Supabase admin client for operations that need to bypass RLS
const supabaseAdmin = getSupabaseAdmin();

// Simple password hashing function using Web Crypto API
export async function hashPassword(password: string): Promise<string> {
  // Convert the password string to a Uint8Array
  const encoder = new TextEncoder();
  const data = encoder.encode(password);

  // Generate a hash using SHA-256
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);

  // Convert the hash to a hex string
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

  return hashHex;
}

// Password verification function
export async function verifyPassword(inputPassword: string, storedHash: string): Promise<boolean> {
  console.log("verifyPassword called with password length:", inputPassword?.length || 0);
  console.log("storedHash exists:", !!storedHash);

  if (!storedHash) {
    console.error("No stored hash provided for verification");
    return false;
  }

  const inputHash = await hashPassword(inputPassword);
  console.log("Input hash:", inputHash);
  console.log("Stored hash:", storedHash);
  console.log("Hashes match:", inputHash === storedHash);

  return inputHash === storedHash;
}

// Configure NextAuth options
export const authOptions: NextAuthOptions = {
  // Configure one or more authentication providers
  providers: [
    CredentialsProvider({
      // The name to display on the sign in form (e.g. "Sign in with...")
      name: "Credentials",
      // The credentials is used to generate a suitable form on the sign in page.
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      // This ensures we only use our custom login page
      async authorize(credentials) {
        console.log("NextAuth authorize function called with email:", credentials?.email);

        if (!credentials?.email || !credentials?.password) {
          console.log("Missing email or password in NextAuth authorize");
          return null;
        }

        try {
          console.log("NextAuth: Attempting to find user in database with email:", credentials.email);
          console.log("Using supabaseAdmin client:", !!supabaseAdmin);

          // Get the user from the database
          const { data, error } = await supabaseAdmin
            .from("users")
            .select("*")
            .eq("email", credentials.email)
            .single();

          if (error || !data) {
            console.error("NextAuth: User not found:", error);

            // Let's try to list some users to see if we can access the database at all
            const { data: usersData, error: usersError } = await supabaseAdmin
              .from("users")
              .select("email")
              .limit(5);

            console.log("NextAuth: Sample users in database:", usersData);
            console.log("NextAuth: Error fetching sample users:", usersError);

            return null;
          }

          console.log("NextAuth: User found with ID:", data.id);
          console.log("NextAuth: User role:", data.role);

          // Verify the password - now async
          console.log("NextAuth: Verifying password");
          console.log("NextAuth: Password hash from database:", data.password_hash ? "exists" : "missing");

          const isValid = await verifyPassword(credentials.password, data.password_hash);
          console.log("NextAuth: Password verification result:", isValid);

          if (!isValid) {
            console.error("NextAuth: Invalid password");
            return null;
          }

          console.log("NextAuth: Password verified successfully");

          // Log the login activity using the activity logger
          try {
            await logActivity({
              userId: data.id,
              action: "login",
              entityType: "user",
              entityId: data.id,
              category: ActivityCategory.AUTH,
              details: {
                email: data.email,
                role: data.role,
                login_method: "credentials",
              },
            });
          } catch (logError) {
            // Don't fail login if logging fails
            console.error("Error logging login activity:", logError);
          }

          // Get the role name from user_roles if role_id is set
          let roleName = data.role;

          if (data.role_id) {
            try {
              const { data: roleData, error: roleError } = await supabaseAdmin
                .from("user_roles")
                .select("role_name")
                .eq("id", data.role_id)
                .single();

              if (!roleError && roleData) {
                roleName = roleData.role_name;
                console.log("NextAuth: Found role_name from role_id:", roleName);
              }
            } catch (roleErr) {
              console.error("Error getting role name:", roleErr);
            }
          }

          // Return the user object
          return {
            id: data.id,
            email: data.email,
            name: data.full_name,
            role: roleName, // Use the role name from user_roles if available
            role_id: data.role_id, // Include role_id
            subscription_status: data.subscription_status,
            subscription_end_date: data.subscription_end_date,
            organization: data.organization,
            organization_id: data.organization_id,
            profile_image_url: data.profile_image_url,
            events_created: data.events_created,
            total_earnings: data.total_earnings,
            available_balance: data.available_balance,
          };
        } catch (err) {
          console.error("Error in authorize:", err);
          return null;
        }
      },
    }),
  ],
  callbacks: {
    // JWT callback is called whenever a JWT is created or updated
    async jwt({ token, user }) {
      // If the user object is available (during sign-in), add custom fields to the token
      if (user) {
        token.id = user.id;
        token.role = user.role;
        token.role_id = user.role_id; // Include role_id in the token
        token.subscription_status = user.subscription_status;
        token.subscription_end_date = user.subscription_end_date;
        token.organization = user.organization;
        token.organization_id = user.organization_id;
        token.profile_image_url = user.profile_image_url;
        token.events_created = user.events_created;
        token.total_earnings = user.total_earnings;
        token.available_balance = user.available_balance;

        console.log("JWT callback: Setting token with role:", user.role);
        console.log("JWT callback: Setting token with role_id:", user.role_id);
      }
      return token;
    },
    // Session callback is called whenever a session is accessed
    async session({ session, token }) {
      // Add custom fields from the token to the session
      if (token && session.user) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.user.role_id = token.role_id as string; // Include role_id in the session
        session.user.subscription_status = token.subscription_status as string;
        session.user.subscription_end_date = token.subscription_end_date as string | null;
        session.user.organization = token.organization as string | null;
        session.user.organization_id = token.organization_id as string | null;
        session.user.profile_image_url = token.profile_image_url as string | null;
        session.user.events_created = token.events_created as number;
        session.user.total_earnings = token.total_earnings as number;
        session.user.available_balance = token.available_balance as number;

        console.log("Session callback: Setting session with role:", session.user.role);
        console.log("Session callback: Setting session with role_id:", session.user.role_id);
      }
      return session;
    },
  },
  pages: {
    signIn: "/auth/login",
    signOut: "/auth/logout",
    error: "/auth/error",
  },
  // Prevent automatic redirects to the sign-in page
  theme: {
    colorScheme: "light",
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  secret: process.env.NEXTAUTH_SECRET || process.env.BETTER_AUTH_SECRET || "your-secret-key-here",
  debug: process.env.NODE_ENV === "development",
  // Configure secure cookies for production
  useSecureCookies: process.env.NODE_ENV === "production",
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === "production"
        ? "__Secure-next-auth.session-token"
        : "next-auth.session-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
        domain: process.env.NODE_ENV === "production"
          ? process.env.COOKIE_DOMAIN || undefined
          : undefined,
      },
    },
    callbackUrl: {
      name: process.env.NODE_ENV === "production"
        ? "__Secure-next-auth.callback-url"
        : "next-auth.callback-url",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
        domain: process.env.NODE_ENV === "production"
          ? process.env.COOKIE_DOMAIN || undefined
          : undefined,
      },
    },
    csrfToken: {
      name: process.env.NODE_ENV === "production"
        ? "__Host-next-auth.csrf-token"
        : "next-auth.csrf-token",
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
        domain: process.env.NODE_ENV === "production"
          ? process.env.COOKIE_DOMAIN || undefined
          : undefined,
      },
    },
  },
};

// Helper function to get the session on the server side
export const getAuthSession = () => getServerSession(authOptions);

// Helper function to verify JWT token in API routes
export async function verifyJWTToken(token: string): Promise<{ user: any; error?: string } | null> {
  try {
    if (!token) {
      return null;
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || "your-secret-key") as any;

    if (!decoded.userId) {
      return null;
    }

    // Get fresh user data from database
    const { data: userData, error: userError } = await supabaseAdmin
      .from("users")
      .select(`
        *,
        user_roles!role_id (
          id,
          role_name,
          description
        )
      `)
      .eq("id", decoded.userId)
      .single();

    if (userError || !userData) {
      return null;
    }

    // Extract role name from the user_roles relation
    const roleName = userData.user_roles?.role_name || userData.role || "free";

    // Create user object
    const user = {
      id: userData.id,
      email: userData.email,
      full_name: userData.full_name,
      role_id: userData.role_id,
      role_name: roleName,
      role: roleName, // For backward compatibility
      subscription_status: userData.subscription_status,
      subscription_end_date: userData.subscription_end_date,
      created_at: userData.created_at,
      organization: userData.organization,
      organization_id: userData.organization_id,
      profile_image_url: userData.profile_image_url,
      events_created: userData.events_created,
      total_earnings: userData.total_earnings,
      available_balance: userData.available_balance,
    };

    return { user };
  } catch (error) {
    console.error("Error verifying JWT token:", error);
    return null;
  }
}

// Helper function to get JWT token from request headers or cookies
export function getJWTTokenFromRequest(request: Request): string | null {
  // Try Authorization header first
  const authHeader = request.headers.get("Authorization");
  if (authHeader && authHeader.startsWith("Bearer ")) {
    return authHeader.substring(7);
  }

  // Try cookies
  const cookieHeader = request.headers.get("Cookie");
  if (cookieHeader) {
    const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      acc[key] = value;
      return acc;
    }, {} as Record<string, string>);

    return cookies.auth_token || null;
  }

  return null;
}

// Helper function to create a new user
export async function createUser(email: string, password: string, fullName: string, roleName: string = "user") {
  try {
    // Generate a unique ID using Web Crypto API
    const userId = crypto.randomUUID();

    // Hash the password - now async
    const passwordHash = await hashPassword(password);

    // Get the role_id for the specified role name
    const { data: roleData, error: roleError } = await supabaseAdmin
      .from("user_roles")
      .select("id")
      .eq("role_name", roleName)
      .single();

    if (roleError || !roleData) {
      console.error("Error finding role:", roleError);
      throw new Error(`Role '${roleName}' not found`);
    }

    // Insert the user into the database
    const { data, error } = await supabaseAdmin
      .from("users")
      .insert({
        id: userId,
        email,
        password_hash: passwordHash,
        full_name: fullName || email.split('@')[0],
        role: "free", // Legacy field - use 'free' to satisfy constraint
        role_id: roleData.id, // New role system
        subscription_status: "none", // Use 'none' to satisfy constraint
        subscription_end_date: null,
        created_at: new Date().toISOString(),
        organization: null,
        profile_image_url: null,
        events_created: 0,
        total_earnings: 0,
        available_balance: 0,
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating user:", error);
      throw new Error(error.message);
    }

    // Log the registration activity using the activity logger
    try {
      await logActivity({
        userId: userId,
        action: "register",
        entityType: "user",
        entityId: userId,
        category: ActivityCategory.AUTH,
        details: {
          email,
          role,
          registration_method: "email",
        },
      });
    } catch (logError) {
      // Don't fail registration if logging fails
      console.error("Error logging registration activity:", logError);
    }

    return data;
  } catch (err) {
    console.error("Error in createUser:", err);
    throw err;
  }
}

// Extend the types for NextAuth
declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      role: string;
      role_id: string; // Add role_id to Session
      subscription_status: string;
      subscription_end_date: string | null;
      organization: string | null;
      organization_id: string | null;
      profile_image_url: string | null;
      events_created: number;
      total_earnings: number;
      available_balance: number;
    } & DefaultSession["user"];
  }

  interface User {
    id: string;
    role: string;
    role_id: string; // Add role_id to User
    subscription_status: string;
    subscription_end_date: string | null;
    organization: string | null;
    organization_id: string | null;
    profile_image_url: string | null;
    events_created: number;
    total_earnings: number;
    available_balance: number;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    role: string;
    role_id: string; // Add role_id to JWT
    subscription_status: string;
    subscription_end_date: string | null;
    organization: string | null;
    organization_id: string | null;
    profile_image_url: string | null;
    events_created: number;
    total_earnings: number;
    available_balance: number;
  }
}
