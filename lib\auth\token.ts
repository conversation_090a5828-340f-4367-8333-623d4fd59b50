import { NextRequest } from "next/server"
import { getJWTTokenFromRequest, verifyJ<PERSON><PERSON><PERSON> } from "@/lib/auth"

export interface UserToken {
  userId: string
  email: string
  role_id?: string
  role_name?: string
}

/**
 * Get user information from <PERSON><PERSON><PERSON> token in the request
 * @param request NextRequest object
 * @returns UserToken object or null if authentication fails
 */
export async function getUserFromToken(request: NextRequest): Promise<UserToken | null> {
  try {
    // Get JWT token from request headers or cookies
    const token = getJWTTokenFromRequest(request)
    
    if (!token) {
      return null
    }

    // Verify the JWT token and get user data
    const authResult = await verifyJ<PERSON>TToken(token)
    
    if (!authResult?.user) {
      return null
    }

    // Return simplified user token object
    return {
      userId: authResult.user.id,
      email: authResult.user.email,
      role_id: authResult.user.role_id,
      role_name: authResult.user.role_name,
    }
  } catch (error) {
    console.error("Error getting user from token:", error)
    return null
  }
}
