-- Add location field to event_teams table for existing databases
-- This migration adds the location field that was added to the original create table migration

DO $$
BEGIN
  -- Add location column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'event_teams' AND column_name = 'location') THEN
    ALTER TABLE event_teams ADD COLUMN location VARCHAR(255);
  END IF;
END $$;

-- Add comment to document the location column
COMMENT ON COLUMN event_teams.location IS 'Location/gate where team operates for check-in tracking';
