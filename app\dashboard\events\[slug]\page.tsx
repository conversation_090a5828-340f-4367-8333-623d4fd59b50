"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON>, useR<PERSON>er, useSearchParams } from "next/navigation"
import {
  Calendar,
  Edit,
  MapPin,
  ArrowLeft,
  Archive,
  Eye,
  EyeOff,
  Copy,
  Building2,
  Tag
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"
import { QRCodeGenerator } from "@/components/qr-code-generator"
import { getEventInitials, generateEventGradient } from "@/lib/utils"
import { OverviewTab, AnalyticsTab, AttendeesTab, CertificatesTab } from "@/components/events"

export default function EventDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const [event, setEvent] = useState<any>(null)
  const [registrations, setRegistrations] = useState<any[]>([])
  const [organization, setOrganization] = useState<any>(null)
  const [category, setCategory] = useState<any>(null)
  const [analytics, setAnalytics] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [isArchiveDialogOpen, setIsArchiveDialogOpen] = useState(false)
  const [isPublishDialogOpen, setIsPublishDialogOpen] = useState(false)
  const [activeTab, setActiveTab] = useState("overview")
  const eventSlug = params.slug as string

  // Handle tab from URL parameters
  useEffect(() => {
    const tab = searchParams.get('tab')
    if (tab && ['overview', 'analytics', 'attendees', 'certificates'].includes(tab)) {
      setActiveTab(tab)
    }
  }, [searchParams])

  useEffect(() => {
    const fetchEventData = async () => {
      setLoading(true)
      try {
        // Fetch event with related data including images, organization, and category
        const { data: eventData, error: eventError } = await supabase
          .from("events")
          .select(`
            *,
            organizations:organization_id (
              id,
              name,
              ssm_number,
              pic_name,
              pic_phone,
              pic_email,
              logo_url,
              address,
              city,
              state,
              postal_code,
              country,
              website
            ),
            event_categories:category_id (
              id,
              name,
              color,
              icon
            )
          `)
          .eq("slug", eventSlug)
          .single()

        if (eventError) {
          console.error("Error fetching event:", eventError)

          // If record not found
          if (eventError.code === "PGRST116") {
            toast({
              title: "Event not found",
              description: "The event you're looking for doesn't exist or has been removed.",
              variant: "destructive",
            })
            router.push("/dashboard/events")
            return
          }

          toast({
            title: "Error",
            description: "Failed to fetch event details. Please try again later.",
            variant: "destructive",
          })
          throw eventError
        }

        if (eventData) {
          setEvent(eventData)

          // Set organization and category from the joined data
          if (eventData.organizations) {
            setOrganization(eventData.organizations)
          }

          if (eventData.event_categories) {
            setCategory(eventData.event_categories)
          }

          // Fetch registrations for this event
          const { data: registrationsData, error: registrationsError } = await supabase
            .from("registrations")
            .select(`
              id,
              attendee_name,
              attendee_email,
              attendee_phone,
              ic_reg,
              payment_status,
              payment_amount,
              checked_in,
              checked_in_at,
              created_at
            `)
            .eq("event_id", eventData.id)
            .order("created_at", { ascending: false })

          if (registrationsError) {
            console.error("Error fetching registrations:", registrationsError)
          } else {
            setRegistrations(registrationsData || [])
          }

          // Calculate analytics
          const totalRegistrations = registrationsData?.length || 0
          const paidRegistrations = registrationsData?.filter(r => r.payment_status === "paid").length || 0
          const checkedInCount = registrationsData?.filter(r => r.checked_in).length || 0
          const totalRevenue = registrationsData?.filter(r => r.payment_status === "paid")
            .reduce((sum, r) => sum + (parseFloat(r.payment_amount) || 0), 0) || 0

          setAnalytics({
            totalRegistrations,
            paidRegistrations,
            checkedInCount,
            totalRevenue,
            attendanceRate: totalRegistrations > 0 ? (checkedInCount / totalRegistrations) * 100 : 0,
            conversionRate: totalRegistrations > 0 ? (paidRegistrations / totalRegistrations) * 100 : 0
          })
        }
      } catch (error) {
        console.error("Error fetching event data:", error)
        toast({
          title: "Error",
          description: "Failed to fetch event details. Please try again later.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    if (eventSlug) {
      fetchEventData()
    }
  }, [eventSlug, router, toast])

  const handleArchive = async () => {
    try {
      const { error } = await supabase
        .from("events")
        .update({
          status: "cancelled"
        })
        .eq("id", event.id)

      if (error) throw error

      setEvent({ ...event, status: "cancelled" })

      // Log activity
      await logActivity("archive_event", {
        event_id: event.id,
        event_title: event.title,
        previous_status: event.status
      })

      toast({
        title: "Success",
        description: "Event archived successfully",
      })
    } catch (error) {
      console.error("Error archiving event:", error)
      toast({
        title: "Error",
        description: "Failed to archive event",
        variant: "destructive",
      })
    } finally {
      setIsArchiveDialogOpen(false)
    }
  }

  const handlePublishToggle = async () => {
    try {
      const newPublishStatus = !event.is_published
      const newStatus = newPublishStatus ? "published" : "draft"

      const { error } = await supabase
        .from("events")
        .update({
          is_published: newPublishStatus
        })
        .eq("id", event.id)

      if (error) throw error

      setEvent({ ...event, is_published: newPublishStatus, status: newStatus })

      // Log activity
      await logActivity(newPublishStatus ? "publish_event" : "unpublish_event", {
        event_id: event.id,
        event_title: event.title,
        new_status: newStatus
      })

      toast({
        title: "Success",
        description: `Event ${newPublishStatus ? "published" : "unpublished"} successfully`,
      })
    } catch (error) {
      console.error("Error updating event status:", error)
      toast({
        title: "Error",
        description: "Failed to update event status",
        variant: "destructive",
      })
    } finally {
      setIsPublishDialogOpen(false)
    }
  }

  const logActivity = async (action: string, details: any) => {
    try {
      await supabase.from("activity_logs").insert([
        {
          action,
          details,
          created_at: new Date().toISOString(),
        },
      ])
    } catch (error) {
      console.error("Error logging activity:", error)
    }
  }

  const handleDuplicateEvent = async () => {
    try {
      const duplicateData = {
        ...event,
        id: undefined,
        slug: `${event.slug}-copy-${Date.now()}`,
        title: `${event.title} (Copy)`,
        status: "draft",
        is_published: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }

      const { data, error } = await supabase
        .from("events")
        .insert([duplicateData])
        .select()
        .single()

      if (error) throw error

      await logActivity("duplicate_event", {
        original_event_id: event.id,
        new_event_id: data.id,
        event_title: event.title
      })

      toast({
        title: "Success",
        description: "Event duplicated successfully",
      })

      router.push(`/dashboard/events/${data.slug}/edit`)
    } catch (error) {
      console.error("Error duplicating event:", error)
      toast({
        title: "Error",
        description: "Failed to duplicate event",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6 px-4">
        <div className="mb-6">
          <Skeleton className="h-8 w-40" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="space-y-6">
            <Skeleton className="h-40 w-full" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!event) {
    return (
      <div className="container mx-auto py-6 px-4">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-2">Event Not Found</h2>
          <p className="text-muted-foreground mb-6">The event you're looking for doesn't exist or has been removed.</p>
          <Link href="/dashboard/events">
            <Button>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Events
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="mb-6 flex items-center justify-between">
        <Link href="/dashboard/events">
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Events
          </Button>
        </Link>
        <div className="flex gap-2">
          <QRCodeGenerator
            eventSlug={eventSlug}
            eventTitle={event?.title || "Event"}
          />
          <Button variant="outline" size="sm" onClick={handleDuplicateEvent}>
            <Copy className="mr-2 h-4 w-4" />
            Duplicate
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsPublishDialogOpen(true)}
          >
            {event?.is_published ? (
              <>
                <EyeOff className="mr-2 h-4 w-4" />
                Unpublish
              </>
            ) : (
              <>
                <Eye className="mr-2 h-4 w-4" />
                Publish
              </>
            )}
          </Button>
          <Link href={`/dashboard/events/${eventSlug}/edit`}>
            <Button variant="outline" size="sm">
              <Edit className="mr-2 h-4 w-4" />
              Edit Event
            </Button>
          </Link>
          {event?.status !== "cancelled" && (
            <Button variant="outline" size="sm" onClick={() => setIsArchiveDialogOpen(true)}>
              <Archive className="mr-2 h-4 w-4" />
              Archive Event
            </Button>
          )}
        </div>
      </div>

      {/* Event Header */}
      <div className="mb-6">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h1 className="text-3xl font-bold mb-2">{event?.title}</h1>
            <div className="flex items-center gap-4 text-muted-foreground">
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                {formatDate(event?.start_date)}
              </div>
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-1" />
                {event?.location || "No location specified"}
              </div>
              {organization && (
                <div className="flex items-center">
                  <Building2 className="h-4 w-4 mr-1" />
                  {organization.name}
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status={event?.status} />
            {category && (
              <Badge
                variant="outline"
                style={{
                  backgroundColor: `${category.color}20`,
                  borderColor: category.color,
                  color: category.color
                }}
              >
                <Tag className="h-3 w-3 mr-1" />
                {category.name}
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Tabs Navigation */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="attendees">Attendees</TabsTrigger>
          <TabsTrigger value="certificates">Certificates</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview">
          <OverviewTab
            event={event}
            organization={organization}
            category={category}
            analytics={analytics}
            eventSlug={eventSlug}
          />
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics">
          <AnalyticsTab analytics={analytics} />
        </TabsContent>

        {/* Attendees Tab */}
        <TabsContent value="attendees">
          <AttendeesTab eventSlug={eventSlug} />
        </TabsContent>

        {/* Certificates Tab */}
        <TabsContent value="certificates">
          <CertificatesTab eventSlug={eventSlug} />
        </TabsContent>
      </Tabs>

      {/* Archive Confirmation Dialog */}
      <Dialog open={isArchiveDialogOpen} onOpenChange={setIsArchiveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Archive Event</DialogTitle>
            <DialogDescription>
              Are you sure you want to archive this event? It will no longer be visible to the public and will be marked as cancelled.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsArchiveDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleArchive}>Archive</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Publish/Unpublish Confirmation Dialog */}
      <Dialog open={isPublishDialogOpen} onOpenChange={setIsPublishDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {event?.is_published ? "Unpublish Event" : "Publish Event"}
            </DialogTitle>
            <DialogDescription>
              {event?.is_published
                ? "Are you sure you want to unpublish this event? It will no longer be visible to the public."
                : "Are you sure you want to publish this event? It will become visible to the public and users can register."
              }
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPublishDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handlePublishToggle}>
              {event?.is_published ? "Unpublish" : "Publish"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

function StatusBadge({ status }: { status: string }) {
  switch (status) {
    case "published":
      return <Badge className="bg-green-100 text-green-800 border-green-200">Published</Badge>
    case "draft":
      return <Badge variant="secondary">Draft</Badge>
    case "cancelled":
      return <Badge className="bg-red-100 text-red-800 border-red-200">Cancelled</Badge>
    case "completed":
      return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Completed</Badge>
    case "ongoing":
      return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Ongoing</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}
