"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Minus, Plus, ShoppingCart, Star, Check, ArrowR<PERSON>, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { type EventType } from "@/contexts/event-context"
import { type TicketType, type SelectedTicket } from "@/types/ticket-types"

interface InlineTicketSelectionProps {
  event: EventType
  className?: string
}

export function InlineTicketSelection({ event, className }: InlineTicketSelectionProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [selectedTickets, setSelectedTickets] = useState<SelectedTicket[]>([])
  const [isProcessing, setIsProcessing] = useState(false)

  // Check if event has ended
  const eventEnded = new Date(event.end_date) < new Date()

  // Get ticket types from event data or generate fallback
  const getTicketTypes = (): TicketType[] => {
    if (!event) return []

    // If event has tickets defined in the database, use those
    if (event.tickets && event.tickets.length > 0) {
      return event.tickets.map(eventTicket => eventTicket.ticketType)
    }

    // Fallback: Generate ticket types based on legacy price field
    const basePrice = event.price || 0

    if (basePrice === 0) {
      // Free event - only one ticket type
      return [
        {
          id: "free",
          name: "Free Ticket",
          description: "Complimentary access to the event",
          price: 0,
          maxQuantity: 10,
          availableQuantity: 100,
          features: ["Event access", "Digital materials"]
        }
      ]
    }

    // Paid event - multiple ticket types
    return [
      {
        id: "early-bird",
        name: "Early Bird",
        description: "Limited time offer with special pricing",
        price: Math.round(basePrice * 0.8), // 20% discount
        maxQuantity: 5,
        availableQuantity: 25,
        features: ["Event access", "Digital materials", "Early bird discount"],
        isPopular: true
      },
      {
        id: "standard",
        name: "Standard",
        description: "Regular admission ticket",
        price: basePrice,
        maxQuantity: 10,
        availableQuantity: 150,
        features: ["Event access", "Digital materials", "Networking session"]
      },
      {
        id: "vip",
        name: "VIP",
        description: "Premium experience with exclusive benefits",
        price: Math.round(basePrice * 1.5), // 50% premium
        maxQuantity: 3,
        availableQuantity: 50,
        features: ["Event access", "Digital materials", "VIP seating", "Welcome kit", "Priority support"]
      }
    ]
  }

  const ticketTypes = getTicketTypes()

  // Check if a ticket type is available for sale
  const isTicketAvailable = (ticketType: TicketType): boolean => {
    const now = new Date()

    // Check if ticket sale has started
    if (ticketType.saleStartDate) {
      const saleStart = new Date(ticketType.saleStartDate)
      if (now < saleStart) return false
    }

    // Check if ticket sale has ended
    if (ticketType.saleEndDate) {
      const saleEnd = new Date(ticketType.saleEndDate)
      if (now > saleEnd) return false
    }

    // Check event registration deadline
    if (event.registration_deadline) {
      const registrationDeadline = new Date(event.registration_deadline)
      if (now > registrationDeadline) return false
    }

    // Check if tickets are still available
    if (ticketType.availableQuantity <= 0) return false

    return true
  }

  // Get the reason why a ticket is not available
  const getUnavailableReason = (ticketType: TicketType): string => {
    const now = new Date()

    if (ticketType.saleStartDate) {
      const saleStart = new Date(ticketType.saleStartDate)
      if (now < saleStart) {
        return `Sale starts ${saleStart.toLocaleDateString()}`
      }
    }

    if (ticketType.saleEndDate) {
      const saleEnd = new Date(ticketType.saleEndDate)
      if (now > saleEnd) {
        return `Sale ended ${saleEnd.toLocaleDateString()}`
      }
    }

    if (event.registration_deadline) {
      const registrationDeadline = new Date(event.registration_deadline)
      if (now > registrationDeadline) {
        return `Registration closed ${registrationDeadline.toLocaleDateString()}`
      }
    }

    if (ticketType.availableQuantity <= 0) {
      return "Sold out"
    }

    return "Not available"
  }

  const getTicketQuantity = (ticketId: string): number => {
    const ticket = selectedTickets.find(t => t.ticketType.id === ticketId)
    return ticket ? ticket.quantity : 0
  }

  const updateTicketQuantity = (ticketType: TicketType, newQuantity: number) => {
    // Don't allow updates if ticket is not available
    if (!isTicketAvailable(ticketType)) return

    if (newQuantity < 0 || newQuantity > ticketType.maxQuantity) return

    setSelectedTickets(prev => {
      const existing = prev.find(t => t.ticketType.id === ticketType.id)

      if (newQuantity === 0) {
        // Remove ticket if quantity is 0
        return prev.filter(t => t.ticketType.id !== ticketType.id)
      }

      if (existing) {
        // Update existing ticket quantity
        return prev.map(t =>
          t.ticketType.id === ticketType.id
            ? { ...t, quantity: newQuantity }
            : t
        )
      } else {
        // Add new ticket
        return [...prev, { ticketType, quantity: newQuantity }]
      }
    })
  }

  const getTotalAmount = (): number => {
    return selectedTickets.reduce((total, ticket) => {
      return total + (ticket.ticketType.price * ticket.quantity)
    }, 0)
  }

  const getTotalTickets = (): number => {
    return selectedTickets.reduce((total, ticket) => total + ticket.quantity, 0)
  }

  const handleProceedToCheckout = async () => {
    if (selectedTickets.length === 0) {
      toast({
        title: "No tickets selected",
        description: "Please select at least one ticket to continue.",
        variant: "destructive"
      })
      return
    }

    setIsProcessing(true)
    try {
      // Store selected tickets in session storage
      sessionStorage.setItem('selectedTickets', JSON.stringify(selectedTickets))

      // Small delay to ensure session storage is set
      await new Promise(resolve => setTimeout(resolve, 100))

      // Navigate to registration page
      router.push(`/events/${event.slug}/register`)
    } catch (error) {
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsProcessing(false)
    }
  }

  if (ticketTypes.length === 0) {
    return null
  }

  // If event has ended, show ended message
  if (eventEnded) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Event Registration
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <div className="space-y-4">
            <div className="text-muted-foreground">
              <Clock className="h-12 w-12 mx-auto mb-2 text-gray-400" />
              <h3 className="text-lg font-semibold text-gray-600">Event Has Ended</h3>
              <p className="text-sm">
                This event ended on {new Date(event.end_date).toLocaleDateString()}
              </p>
            </div>
            <Button variant="secondary" disabled className="w-full">
              Registration Closed
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingCart className="h-5 w-5" />
          Select Tickets
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Ticket Types */}
        <div className="space-y-3">
          {ticketTypes.map((ticketType) => {
            const quantity = getTicketQuantity(ticketType.id)
            const isAvailable = isTicketAvailable(ticketType)
            const unavailableReason = !isAvailable ? getUnavailableReason(ticketType) : null

            return (
              <Card key={ticketType.id} className={`relative transition-all duration-200 ${
                !isAvailable
                  ? 'opacity-60 bg-gray-50 border-gray-200'
                  : ticketType.isPopular
                    ? 'ring-2 ring-primary border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10'
                    : 'hover:border-primary/30'
              } ${quantity > 0 && isAvailable ? 'bg-primary/5 ring-1 ring-primary/30' : ''}`}>
                {ticketType.isPopular && (
                  <div className="absolute -top-2 left-3 z-10">
                    <Badge className="bg-gradient-to-r from-primary to-primary/80 text-white px-2 py-1 text-xs">
                      <Star className="w-3 h-3 mr-1" />
                      Popular
                    </Badge>
                  </div>
                )}

                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-semibold">{ticketType.name}</h4>
                        <span className="text-lg font-bold text-primary">
                          {ticketType.price === 0 ? 'Free' : `RM ${ticketType.price.toFixed(2)}`}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">{ticketType.description}</p>

                      {/* Features */}
                      <div className="flex flex-wrap gap-1 mb-3">
                        {ticketType.features.slice(0, 3).map((feature, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            <Check className="w-3 h-3 mr-1" />
                            {feature}
                          </Badge>
                        ))}
                        {ticketType.features.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{ticketType.features.length - 3} more
                          </Badge>
                        )}
                      </div>

                      {/* Availability and deadline info */}
                      <div className="space-y-1">
                        {!isAvailable && unavailableReason && (
                          <p className="text-xs text-destructive font-medium">
                            {unavailableReason}
                          </p>
                        )}
                        {isAvailable && (
                          <p className="text-xs text-muted-foreground">
                            {ticketType.availableQuantity} available • Max {ticketType.maxQuantity} per person
                          </p>
                        )}
                        {/* Show ticket deadline if it exists and ticket is available */}
                        {isAvailable && ticketType.saleEndDate && (
                          <p className="text-xs text-orange-600">
                            Sale ends: {new Date(ticketType.saleEndDate).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Quantity Controls */}
                    <div className="flex items-center gap-2 ml-4">
                      {isAvailable ? (
                        <>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateTicketQuantity(ticketType, quantity - 1)}
                            disabled={quantity === 0}
                            className="h-8 w-8 p-0"
                          >
                            <Minus className="h-4 w-4" />
                          </Button>

                          <span className="w-8 text-center font-medium">{quantity}</span>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateTicketQuantity(ticketType, quantity + 1)}
                            disabled={quantity >= ticketType.maxQuantity}
                            className="h-8 w-8 p-0"
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </>
                      ) : (
                        <div className="text-center">
                          <Badge variant="secondary" className="text-xs">
                            Not Available
                          </Badge>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Cart Summary */}
        {selectedTickets.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="font-semibold">Order Summary</h4>
              <div className="space-y-2">
                {selectedTickets.map((ticket) => (
                  <div key={ticket.ticketType.id} className="flex justify-between text-sm">
                    <span>{ticket.ticketType.name} × {ticket.quantity}</span>
                    <span className="font-medium">
                      RM {(ticket.ticketType.price * ticket.quantity).toFixed(2)}
                    </span>
                  </div>
                ))}
              </div>
              <Separator />
              <div className="flex justify-between font-semibold">
                <span>Total ({getTotalTickets()} tickets)</span>
                <span className="text-primary">RM {getTotalAmount().toFixed(2)}</span>
              </div>
            </div>
          </>
        )}

        {/* Checkout Button */}
        <Button
          className="w-full"
          size="lg"
          onClick={handleProceedToCheckout}
          disabled={selectedTickets.length === 0 || isProcessing}
        >
          {isProcessing ? (
            "Processing..."
          ) : selectedTickets.length === 0 ? (
            "Select Tickets"
          ) : (
            <>
              Proceed to Checkout
              <ArrowRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  )
}
