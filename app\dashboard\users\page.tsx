"use client"

import { useEffect, useState } from "react"
import { Edit, KeyRound, Plus, Shield, Trash, User<PERSON>heck, UserX } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"
import { Label } from "@/components/ui/label"
import { useIsMobile } from "@/hooks/use-mobile"
import { useAuth } from "@/contexts/auth-context"

type UserRole = "free" | "paid" | "manager" | "admin"

export default function UsersPage() {
  const [users, setUsers] = useState<any[]>([])
  const [roles, setRoles] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [roleFilter, setRoleFilter] = useState<string>("all")
  const [selectedUser, setSelectedUser] = useState<any>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false)
  const [isViewDetailsDialogOpen, setIsViewDetailsDialogOpen] = useState(false)
  const [isResetPasswordDialogOpen, setIsResetPasswordDialogOpen] = useState(false)
  const [passwordResetMethod, setPasswordResetMethod] = useState<"email" | "direct">("email")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [selectedRoleId, setSelectedRoleId] = useState<string>("")
  const [currentUser, setCurrentUser] = useState<any>(null)
  const [newUser, setNewUser] = useState({
    email: "",
    full_name: "",
    role_id: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isMobile = useIsMobile()
  const { toast } = useToast()
  const { user, loading: authLoading, isAdmin } = useAuth()

  // Helper function to get auth token from cookies
  const getAuthToken = () => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; auth_token=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  // Function to fetch roles from database
  const fetchRoles = async () => {
    try {
      // Get auth token for API request
      const token = getAuthToken();
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Use the server-side API endpoint to fetch roles
      const response = await fetch('/api/admin/roles/list', {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch roles')
      }

      const { roles: rolesData } = await response.json()

      if (rolesData && rolesData.length > 0) {
        console.log("Roles fetched successfully:", rolesData.length)
        setRoles(rolesData)
      } else {
        console.log("No roles found in database")
        setRoles([])
      }
    } catch (err) {
      console.error("Error fetching roles:", err)
      toast({
        title: "Error",
        description: "Failed to fetch roles from database",
        variant: "destructive",
      })
      setRoles([])
    }
  }

  // Function to fetch users from database
  const fetchUsers = async () => {
    setLoading(true)
    try {
      // Get auth token for API request
      const token = getAuthToken();
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Use the server-side API endpoint to fetch users
      const response = await fetch('/api/admin/users/list', {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch users')
      }

      const { users: userData } = await response.json()

      if (userData && userData.length > 0) {
        console.log("Users fetched successfully:", userData.length)
        setUsers(userData)
      } else {
        console.log("No users found in database, using mock data")
        // Mock user data as fallback
        setUsers([
          {
            id: "user-001",
            full_name: "John Doe",
            email: "<EMAIL>",
            role: "admin",
            subscription_status: "active",
            events_created: 12,
            total_earnings: 2500.0,
            created_at: new Date("2023-01-15").toISOString(),
          },
          {
            id: "user-002",
            full_name: "Sarah Johnson",
            email: "<EMAIL>",
            role: "manager",
            subscription_status: "active",
            events_created: 8,
            total_earnings: 1800.0,
            created_at: new Date("2023-02-20").toISOString(),
          },
          {
            id: "user-003",
            full_name: "Michael Brown",
            email: "<EMAIL>",
            role: "paid",
            subscription_status: "active",
            events_created: 5,
            total_earnings: 950.0,
            created_at: new Date("2023-03-10").toISOString(),
          },
          {
            id: "user-004",
            full_name: "Emily Davis",
            email: "<EMAIL>",
            role: "free",
            subscription_status: "none",
            events_created: 2,
            total_earnings: 0.0,
            created_at: new Date("2023-04-05").toISOString(),
          },
          {
            id: "user-005",
            full_name: "David Wilson",
            email: "<EMAIL>",
            role: "paid",
            subscription_status: "canceled",
            events_created: 3,
            total_earnings: 450.0,
            created_at: new Date("2023-05-15").toISOString(),
          },
        ])
      }
    } catch (err) {
      console.error("Error fetching users:", err)
      toast({
        title: "Error",
        description: "Failed to fetch users from database",
        variant: "destructive",
      })
      // Use mock data as fallback
      setUsers([
        {
          id: "user-001",
          full_name: "John Doe",
          email: "<EMAIL>",
          role: "admin",
          subscription_status: "active",
          events_created: 12,
          total_earnings: 2500.0,
          created_at: new Date("2023-01-15").toISOString(),
        },
        {
          id: "user-002",
          full_name: "Sarah Johnson",
          email: "<EMAIL>",
          role: "manager",
          subscription_status: "active",
          events_created: 8,
          total_earnings: 1800.0,
          created_at: new Date("2023-02-20").toISOString(),
        },
        {
          id: "user-003",
          full_name: "Michael Brown",
          email: "<EMAIL>",
          role: "paid",
          subscription_status: "active",
          events_created: 5,
          total_earnings: 950.0,
          created_at: new Date("2023-03-10").toISOString(),
        },
        {
          id: "user-004",
          full_name: "Emily Davis",
          email: "<EMAIL>",
          role: "free",
          subscription_status: "none",
          events_created: 2,
          total_earnings: 0.0,
          created_at: new Date("2023-04-05").toISOString(),
        },
        {
          id: "user-005",
          full_name: "David Wilson",
          email: "<EMAIL>",
          role: "paid",
          subscription_status: "canceled",
          events_created: 3,
          total_earnings: 450.0,
          created_at: new Date("2023-05-15").toISOString(),
        },
      ])
    } finally {
      setLoading(false)
    }
  }

  // Fetch current user and check permissions
  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        // Check if we're in development mode
        const isDevelopment = process.env.NODE_ENV === "development";
        console.log("Users page - Is development mode:", isDevelopment);

        // Use the custom auth context
        if (user?.id) {
          console.log("Using custom auth context user:", user);
          setCurrentUser(user);
          console.log("Users page - Current user set from auth context with role:", user.role_name);

          // Only fetch users and roles if current user is admin
          if (isAdmin()) {
            console.log("Users page - User is admin, fetching roles and users");
            await fetchRoles();
            await fetchUsers();
          } else {
            console.log("Users page - User is not admin, skipping data fetch");
          }
          return;
        }

        // In production, we should not use mock data
        if (!isDevelopment) {
          console.log("Users page - No valid user in production, not using mock data");
          setCurrentUser(null);
          setLoading(false);
          return;
        }

        // Fallback for development when user data is not available
        console.log("No valid user data found, using mock admin user")

        // Set a mock admin user for development purposes
        const mockAdminUser = {
          id: "mock-admin-id",
          full_name: "Mock Admin",
          email: "<EMAIL>",
          role: "admin",
          role_name: "admin",
          subscription_status: "active",
          created_at: new Date().toISOString()
        }

        setCurrentUser(mockAdminUser)
        console.log("Users page - Current user set to mock admin");

        // Fetch roles and users since we're using a mock admin
        await fetchRoles()
        await fetchUsers()
      } catch (err) {
        console.error("Error in authentication flow:", err)
        toast({
          title: "Error",
          description: "Failed to verify user permissions",
          variant: "destructive",
        })

        // Only use mock data in development
        if (process.env.NODE_ENV === "development") {
          console.log("Users page - Using mock admin in development after error");
          // Set a mock admin user as fallback
          setCurrentUser({
            id: "mock-admin-id",
            full_name: "Mock Admin",
            email: "<EMAIL>",
            role: "admin",
            role_name: "admin",
            subscription_status: "active",
            created_at: new Date().toISOString()
          })

          // Fetch roles and users with the mock admin
          await fetchRoles()
          await fetchUsers()
        } else {
          setCurrentUser(null);
        }
      } finally {
        setLoading(false)
      }
    }

    // Only run if auth is not loading
    if (!authLoading) {
      fetchCurrentUser()
    }
  }, [toast, user, authLoading])

  // Handle user role update
  const updateUserRole = async () => {
    if (!selectedUser || !selectedRoleId) return

    setIsSubmitting(true)
    try {
      // Get auth token for API request
      const token = getAuthToken();
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Use the dedicated API endpoint to update the user role
      // This bypasses RLS policies by using the admin client
      const response = await fetch("/api/admin/users/update-role", {
        method: "POST",
        headers,
        body: JSON.stringify({
          userId: selectedUser.id,
          roleId: selectedRoleId,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to update user role");
      }

      // Get the selected role info
      const selectedRole = roles.find(role => role.id === selectedRoleId);

      // Update local state immediately for better UX
      setUsers((prevUsers) =>
        prevUsers.map((user) => (
          user.id === selectedUser.id
            ? {
                ...user,
                role_id: selectedRoleId,
                // Use the mapped role from the response if available, otherwise use the role name
                role: result.mappedRole || selectedRole?.role_name || user.role,
                role_info: selectedRole
              }
            : user
        )),
      )

      toast({
        title: "Success",
        description: "User role updated successfully",
      })

      // Refresh the user list to ensure we have the latest data
      await fetchUsers()

      setIsEditDialogOpen(false)
    } catch (err) {
      console.error("Error updating user role:", err)
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to update user role",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle user deletion
  const handleDeleteUser = async () => {
    if (!selectedUser) return

    setIsSubmitting(true)
    try {
      // Delete user from users table
      const { error: deleteError } = await supabase.from("users").delete().eq("id", selectedUser.id)

      if (deleteError) throw deleteError

      try {
        // Delete user from auth - this might fail if the user doesn't exist in auth
        // but we still want to proceed with the deletion from the users table
        await supabase.auth.admin.deleteUser(selectedUser.id)
      } catch (authError) {
        console.warn("Could not delete user from auth:", authError)
        // Continue with the process even if auth deletion fails
      }

      // Update local state immediately for better UX
      setUsers((prevUsers) => prevUsers.filter((user) => user.id !== selectedUser.id))

      toast({
        title: "Success",
        description: "User deleted successfully",
      })

      // Refresh the user list to ensure we have the latest data
      await fetchUsers()

      setIsDeleteDialogOpen(false)
    } catch (err) {
      console.error("Error deleting user:", err)
      toast({
        title: "Error",
        description: "Failed to delete user",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle adding a new user
  const handleAddUser = async () => {
    setIsSubmitting(true)

    try {
      if (!newUser.email || !newUser.full_name) {
        toast({
          title: "Error",
          description: "Email and full name are required",
          variant: "destructive",
        })
        return
      }

      // First, ensure the database function exists
      try {
        // Apply the migration to create the insert_user_admin function if needed
        await fetch("/api/admin/db/apply-migration", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            migrationName: "20250601000003_add_insert_user_admin_function.sql",
          }),
        });
        // We don't need to check the response - if it fails, we'll still try to create the user
        // which might work if the function already exists
      } catch (migrationError) {
        console.warn("Failed to apply migration, continuing anyway:", migrationError);
      }

      // Get auth token for API request
      const token = getAuthToken();
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Call the admin API to create a new user with authentication
      let response = await fetch("/api/admin/users/create", {
        method: "POST",
        headers,
        body: JSON.stringify({
          email: newUser.email,
          fullName: newUser.full_name,
          role_id: newUser.role_id,
          sendPasswordEmail: false, // Set to true to send password reset email in production
        }),
      })

      let result = await response.json()

      // If the first method fails, try the direct SQL method as a fallback
      if (!response.ok) {
        console.log("First method failed, trying direct SQL insertion...");

        // Try the direct SQL insertion method
        response = await fetch("/api/admin/users/create-direct", {
          method: "POST",
          headers,
          body: JSON.stringify({
            email: newUser.email,
            fullName: newUser.full_name,
            role_id: newUser.role_id,
            sendPasswordEmail: false,
          }),
        })

        result = await response.json()

        if (!response.ok) {
          throw new Error(result.error || "Failed to create user using all available methods")
        }
      }

      // Show success message with temporary password if provided
      if (result.tempPassword) {
        toast({
          title: "User Created Successfully",
          description: `Temporary password: ${result.tempPassword}`,
          duration: 10000, // Show for longer so user can copy the password
        })
      } else {
        toast({
          title: "Success",
          description: "User added successfully. Password reset email will be sent.",
        })
      }

      // Reset form and close dialog
      setNewUser({
        email: "",
        full_name: "",
        role_id: "",
      })
      setIsAddUserDialogOpen(false)

      // Refresh the user list to ensure we have the latest data
      await fetchUsers()
    } catch (err) {
      console.error("Error adding user:", err)

      // Provide more specific error messages
      let errorMessage = "Failed to add user to database";

      if (err instanceof Error) {
        errorMessage = err.message;

        // Check for common errors
        if (errorMessage.includes("violates RLS policy")) {
          errorMessage = "Permission denied: Cannot add user due to database security policies. Please contact the administrator.";
        } else if (errorMessage.includes("duplicate key")) {
          errorMessage = "A user with this email already exists.";
        } else if (errorMessage.includes("network")) {
          errorMessage = "Network error. Please check your connection and try again.";
        }
      }

      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Open edit dialog
  const openEditDialog = (user: any) => {
    setSelectedUser(user)
    setSelectedRoleId(user.role_id || "")
    setIsEditDialogOpen(true)
  }

  // Open delete dialog
  const openDeleteDialog = (user: any) => {
    setSelectedUser(user)
    setIsDeleteDialogOpen(true)
  }

  // Open view details dialog
  const openViewDetailsDialog = (user: any) => {
    setSelectedUser(user)
    setIsViewDetailsDialogOpen(true)
  }

  // Open reset password dialog
  const openResetPasswordDialog = (user: any) => {
    setSelectedUser(user)
    // Reset form state
    setPasswordResetMethod("email")
    setNewPassword("")
    setConfirmPassword("")
    setIsResetPasswordDialogOpen(true)
  }

  // Handle password reset
  const handleResetPassword = async () => {
    if (!selectedUser) return

    setIsSubmitting(true)
    try {
      if (passwordResetMethod === "email") {
        // Send password reset email using Supabase Auth API
        const { error } = await supabase.auth.resetPasswordForEmail(selectedUser.email, {
          redirectTo: `${window.location.origin}/auth/reset-password`,
        })

        if (error) throw error

        toast({
          title: "Success",
          description: `Password reset email sent to ${selectedUser.email}`,
        })
      } else {
        // Direct password change by admin
        if (newPassword !== confirmPassword) {
          toast({
            title: "Error",
            description: "Passwords do not match",
            variant: "destructive",
          })
          setIsSubmitting(false)
          return
        }

        if (newPassword.length < 8) {
          toast({
            title: "Error",
            description: "Password must be at least 8 characters long",
            variant: "destructive",
          })
          setIsSubmitting(false)
          return
        }

        // Get auth token for API request
        const token = getAuthToken();
        const headers: HeadersInit = {
          'Content-Type': 'application/json',
        };

        if (token) {
          headers['Authorization'] = `Bearer ${token}`;
        }

        // Call the API to update the password using the RPC endpoint
        console.log("Updating password for user:", selectedUser.id)

        const response = await fetch("/api/auth/admin/update-password-rpc", {
          method: "POST",
          headers,
          body: JSON.stringify({
            userId: selectedUser.id,
            password: newPassword,
          }),
        })

        const data = await response.json()

        if (!response.ok) {
          console.error("Password update failed:", data)
          throw new Error(data.error || "Failed to update password")
        }

        if (!data.success) {
          console.error("Password update unsuccessful:", data)
          throw new Error("Failed to update password: Operation did not complete successfully")
        }

        console.log("Password update response:", data)

        toast({
          title: "Success",
          description: `Password has been updated for ${selectedUser.email}`,
        })
      }

      // Reset form and close dialog
      setNewPassword("")
      setConfirmPassword("")
      setIsResetPasswordDialogOpen(false)
    } catch (err: any) {
      console.error("Error handling password:", err)
      toast({
        title: "Error",
        description: passwordResetMethod === "email"
          ? "Failed to send password reset email"
          : `Failed to update password: ${err.message || "Unknown error"}`,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Get role badge color
  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-red-100 text-red-800"
      case "manager":
        return "bg-blue-100 text-blue-800"
      case "paid":
        return "bg-green-100 text-green-800"
      case "super_admin":
        return "bg-purple-100 text-purple-800"
      case "event_admin":
        return "bg-amber-100 text-amber-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // Get role name from user
  const getRoleName = (user: any) => {
    // First try to get from role_info
    if (user.role_info?.role_name) {
      return user.role_info.role_name;
    }
    // Fall back to legacy role field
    return user.role || "free";
  }

  // Get subscription status badge color
  const getSubscriptionBadgeColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800"
      case "canceled":
        return "bg-amber-100 text-amber-800"
      case "expired":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // Filter users based on search query and role
  const filteredUsers = users.filter(
    (user) => {
      // Filter by search query (name or email)
      const matchesSearch =
        (user.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchQuery.toLowerCase()));

      // Filter by role if a specific role is selected
      const userRoleName = getRoleName(user);
      const matchesRole = roleFilter === "all" || userRoleName === roleFilter;

      return matchesSearch && matchesRole;
    }
  )

  // Show loading state while authentication is being checked
  if (authLoading) {
    return (
      <div className="flex h-40 items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
      </div>
    )
  }

  // Check if user is admin using the auth context
  if (!isAdmin()) {
    return (
      <Card className="m-6">
        <CardHeader>
          <CardTitle>User Management</CardTitle>
          <CardDescription>You don't have permission to access this page</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <TooltipProvider>
      <div className="space-y-6 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h1 className="text-xl sm:text-2xl font-bold">User Management</h1>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button onClick={() => setIsAddUserDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                <span className="whitespace-nowrap">Add User</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Create a new user account</p>
            </TooltipContent>
          </Tooltip>
        </div>

      <Card>
        <CardHeader>
          <CardTitle>Manage Users</CardTitle>
          <CardDescription>Manage user accounts and permissions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-6 flex flex-col sm:flex-row items-start sm:items-center gap-4 flex-wrap">
            <Input
              placeholder="Search users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full sm:max-w-sm"
            />

            <div className="flex items-center gap-2 w-full sm:w-auto">
              <span className="text-sm text-muted-foreground whitespace-nowrap">Filter by role:</span>
              <Select value={roleFilter} onValueChange={(value) => setRoleFilter(value)}>
                <SelectTrigger className="w-full sm:w-[150px]">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.role_name}>
                      {role.role_name.charAt(0).toUpperCase() + role.role_name.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="w-full sm:ml-auto sm:w-auto flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Total Users: {users.length} | Filtered: {filteredUsers.length}
              </span>
            </div>
          </div>

          {loading ? (
            <div className="flex h-40 items-center justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
          ) : (
            <div className="rounded-md border overflow-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead className="hidden md:table-cell">Subscription</TableHead>
                    <TableHead className="hidden sm:table-cell">Events</TableHead>
                    <TableHead className="hidden sm:table-cell">Earnings</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="h-24 text-center">
                        No users found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                              <span className="text-sm font-medium">
                                {user.full_name
                                  ? user.full_name
                                      .split(" ")
                                      .map((n: string) => n[0])
                                      .join("")
                                  : "U"}
                              </span>
                            </div>
                            <div>
                              <div className="font-medium">{user.full_name}</div>
                              <div className="text-sm text-muted-foreground">{user.email}</div>
                              {/* Mobile-only role and subscription display */}
                              <div className="flex flex-wrap gap-2 mt-1 sm:hidden">
                                <Badge className={getRoleBadgeColor(getRoleName(user))} variant="outline">
                                  {getRoleName(user).charAt(0).toUpperCase() + getRoleName(user).slice(1)}
                                </Badge>
                                {user.subscription_status !== "none" && (
                                  <Badge className={getSubscriptionBadgeColor(user.subscription_status)} variant="outline">
                                    {user.subscription_status.charAt(0).toUpperCase() + user.subscription_status.slice(1)}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          <Badge className={getRoleBadgeColor(getRoleName(user))} variant="outline">
                            {getRoleName(user).charAt(0).toUpperCase() + getRoleName(user).slice(1)}
                          </Badge>
                          {user.role_info?.description && (
                            <div className="text-xs text-muted-foreground mt-1">{user.role_info.description}</div>
                          )}
                        </TableCell>
                        <TableCell className="hidden md:table-cell">
                          {user.subscription_status !== "none" ? (
                            <Badge className={getSubscriptionBadgeColor(user.subscription_status)} variant="outline">
                              {user.subscription_status.charAt(0).toUpperCase() + user.subscription_status.slice(1)}
                            </Badge>
                          ) : (
                            <span className="text-sm text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">{user.events_created || 0}</TableCell>
                        <TableCell className="hidden sm:table-cell">RM {(user.total_earnings || 0).toFixed(2)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-1 md:gap-2">
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button variant="ghost" size="icon" onClick={() => openEditDialog(user)}>
                                  <Edit className="h-4 w-4" />
                                  <span className="sr-only">Edit Role</span>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Edit Role</p>
                              </TooltipContent>
                            </Tooltip>

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button variant="ghost" size="icon" onClick={() => openViewDetailsDialog(user)}>
                                  <UserCheck className="h-4 w-4" />
                                  <span className="sr-only">View Details</span>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>View Details</p>
                              </TooltipContent>
                            </Tooltip>

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button variant="ghost" size="icon" onClick={() => openResetPasswordDialog(user)}>
                                  <KeyRound className="h-4 w-4" />
                                  <span className="sr-only">Reset Password</span>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Reset Password</p>
                              </TooltipContent>
                            </Tooltip>

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => openDeleteDialog(user)}
                                  className="text-destructive hover:text-destructive"
                                >
                                  <Trash className="h-4 w-4" />
                                  <span className="sr-only">Delete User</span>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Delete User</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit User Role Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className={`${isMobile ? 'w-[95vw] max-w-full' : ''}`}>
          <DialogHeader>
            <DialogTitle>Edit User Role</DialogTitle>
            <DialogDescription>
              Change the role for {selectedUser?.full_name}
              <br />
              <span className="text-sm text-muted-foreground break-all">{selectedUser?.email}</span>
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Role</label>
              <Select
                value={selectedRoleId}
                onValueChange={(value) => setSelectedRoleId(value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.role_name.charAt(0).toUpperCase() + role.role_name.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="mt-1 text-xs text-muted-foreground">
                {roles.find(role => role.id === selectedRoleId)?.description || "Select a role to see description"}
              </div>
            </div>
          </div>
          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)} disabled={isSubmitting} className="w-full sm:w-auto">
              Cancel
            </Button>
            <Button onClick={updateUserRole} disabled={isSubmitting} className="w-full sm:w-auto">
              {isSubmitting ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                  Updating...
                </>
              ) : (
                <>
                  <Shield className="mr-2 h-4 w-4" />
                  Update Role
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete User Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className={`${isMobile ? 'w-[95vw] max-w-full' : ''}`}>
          <DialogHeader>
            <DialogTitle>Delete User</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {selectedUser?.full_name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)} disabled={isSubmitting} className="w-full sm:w-auto">
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteUser} disabled={isSubmitting} className="w-full sm:w-auto">
              {isSubmitting ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                  Deleting...
                </>
              ) : (
                "Delete User"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add User Dialog */}
      <Dialog open={isAddUserDialogOpen} onOpenChange={setIsAddUserDialogOpen}>
        <DialogContent className={`${isMobile ? 'w-[95vw] max-w-full' : ''}`}>
          <DialogHeader>
            <DialogTitle>Add New User</DialogTitle>
            <DialogDescription>
              Create a new user account. A temporary password will be generated for the user to log in.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="full-name">Full Name</Label>
              <Input
                id="full-name"
                value={newUser.full_name}
                onChange={(e) => setNewUser({ ...newUser, full_name: e.target.value })}
                placeholder="Enter full name"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={newUser.email}
                onChange={(e) => setNewUser({ ...newUser, email: e.target.value })}
                placeholder="Enter email address"
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="role">Role</Label>
              <Select
                value={newUser.role_id}
                onValueChange={(value) => setNewUser({ ...newUser, role_id: value })}
              >
                <SelectTrigger id="role">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role.id} value={role.id}>
                      {role.role_name.charAt(0).toUpperCase() + role.role_name.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <div className="mt-1 text-xs text-muted-foreground">
                {roles.find(role => role.id === newUser.role_id)?.description || "Select a role to see description"}
              </div>
            </div>
          </div>
          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setIsAddUserDialogOpen(false)} disabled={isSubmitting} className="w-full sm:w-auto">
              Cancel
            </Button>
            <Button onClick={handleAddUser} disabled={isSubmitting} className="w-full sm:w-auto">
              {isSubmitting ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                  Adding...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Add User
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reset Password Dialog */}
      <Dialog open={isResetPasswordDialogOpen} onOpenChange={setIsResetPasswordDialogOpen}>
        <DialogContent className={`${isMobile ? 'w-[95vw] max-w-full' : 'max-w-md'}`}>
          <DialogHeader>
            <DialogTitle>Reset User Password</DialogTitle>
            <DialogDescription>
              Choose how you want to reset the password for {selectedUser?.email}.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-muted">
                <span className="text-sm font-medium">
                  {selectedUser?.full_name
                    ? selectedUser.full_name
                        .split(" ")
                        .map((n: string) => n[0])
                        .join("")
                    : "U"}
                </span>
              </div>
              <div className="overflow-hidden">
                <div className="font-medium">{selectedUser?.full_name}</div>
                <div className="text-sm text-muted-foreground truncate">{selectedUser?.email}</div>
              </div>
            </div>

            <div className="space-y-4 mt-4">
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="email-reset"
                  name="reset-method"
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  checked={passwordResetMethod === "email"}
                  onChange={() => setPasswordResetMethod("email")}
                />
                <Label htmlFor="email-reset" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Send password reset email
                </Label>
              </div>

              {passwordResetMethod === "email" && (
                <div className="pl-6 text-sm text-muted-foreground">
                  <p>The user will receive an email with instructions to reset their password.</p>
                  <p className="mt-2">Note: The current password will be invalidated immediately.</p>
                </div>
              )}

              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="direct-reset"
                  name="reset-method"
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  checked={passwordResetMethod === "direct"}
                  onChange={() => setPasswordResetMethod("direct")}
                />
                <Label htmlFor="direct-reset" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Set new password directly
                </Label>
              </div>

              {passwordResetMethod === "direct" && (
                <div className="pl-6 space-y-3">
                  <div className="space-y-1">
                    <Label htmlFor="new-password" className="text-sm">New Password</Label>
                    <Input
                      id="new-password"
                      type="password"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      placeholder="Enter new password"
                      required
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="confirm-password" className="text-sm">Confirm Password</Label>
                    <Input
                      id="confirm-password"
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      placeholder="Confirm new password"
                      required
                    />
                  </div>
                  <div className="text-xs text-muted-foreground">
                    <p>Password must be at least 8 characters long.</p>
                    <p className="mt-1">Note: The user will not be notified of this change.</p>
                  </div>
                </div>
              )}
            </div>
          </div>
          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setIsResetPasswordDialogOpen(false)} disabled={isSubmitting} className="w-full sm:w-auto">
              Cancel
            </Button>
            <Button onClick={handleResetPassword} disabled={isSubmitting} className="w-full sm:w-auto">
              {isSubmitting ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                  {passwordResetMethod === "email" ? "Sending..." : "Updating..."}
                </>
              ) : (
                <>
                  <KeyRound className="mr-2 h-4 w-4" />
                  {passwordResetMethod === "email" ? "Send Reset Email" : "Update Password"}
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View User Details Dialog */}
      <Dialog open={isViewDetailsDialogOpen} onOpenChange={setIsViewDetailsDialogOpen}>
        <DialogContent className={`${isMobile ? 'w-[95vw] max-w-full' : 'max-w-3xl'}`}>
          <DialogHeader>
            <DialogTitle>User Details</DialogTitle>
            <DialogDescription>
              Detailed information for {selectedUser?.full_name}
            </DialogDescription>
          </DialogHeader>

          {selectedUser && (
            <div className="space-y-6 py-4 overflow-y-auto max-h-[70vh]">
              {/* User Profile Section */}
              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex-shrink-0">
                  <div className="flex h-20 w-20 md:h-24 md:w-24 items-center justify-center rounded-full bg-muted">
                    <span className="text-xl md:text-2xl font-medium">
                      {selectedUser.full_name
                        ? selectedUser.full_name
                            .split(" ")
                            .map((n: string) => n[0])
                            .join("")
                        : "U"}
                    </span>
                  </div>
                </div>

                <div className="flex-1 space-y-2">
                  <h3 className="text-lg md:text-xl font-semibold">{selectedUser.full_name}</h3>
                  <p className="text-muted-foreground text-sm break-all">{selectedUser.email}</p>

                  <div className="flex flex-wrap gap-2 mt-2">
                    <Badge className={getRoleBadgeColor(getRoleName(selectedUser))} variant="outline">
                      {getRoleName(selectedUser).charAt(0).toUpperCase() + getRoleName(selectedUser).slice(1)}
                    </Badge>

                    {selectedUser.subscription_status && selectedUser.subscription_status !== "none" && (
                      <Badge className={getSubscriptionBadgeColor(selectedUser.subscription_status)} variant="outline">
                        {selectedUser.subscription_status.charAt(0).toUpperCase() + selectedUser.subscription_status.slice(1)}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Account Information */}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-muted-foreground">Account Information</h4>

                  <div className="space-y-2">
                    <div className="flex flex-col sm:flex-row sm:justify-between gap-1">
                      <span className="text-sm">User ID</span>
                      <span className="text-sm font-medium break-all">{selectedUser.id}</span>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:justify-between gap-1">
                      <span className="text-sm">Created</span>
                      <span className="text-sm font-medium">
                        {new Date(selectedUser.created_at).toLocaleDateString()}
                      </span>
                    </div>

                    {selectedUser.subscription_end_date && (
                      <div className="flex flex-col sm:flex-row sm:justify-between gap-1">
                        <span className="text-sm">Subscription Ends</span>
                        <span className="text-sm font-medium">
                          {new Date(selectedUser.subscription_end_date).toLocaleDateString()}
                        </span>
                      </div>
                    )}

                    {selectedUser.organization && (
                      <div className="flex flex-col sm:flex-row sm:justify-between gap-1">
                        <span className="text-sm">Organization</span>
                        <span className="text-sm font-medium">{selectedUser.organization}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Activity & Statistics */}
                <div className="space-y-4">
                  <h4 className="text-sm font-medium text-muted-foreground">Activity & Statistics</h4>

                  <div className="space-y-2">
                    <div className="flex flex-col sm:flex-row sm:justify-between gap-1">
                      <span className="text-sm">Events Created</span>
                      <span className="text-sm font-medium">{selectedUser.events_created || 0}</span>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:justify-between gap-1">
                      <span className="text-sm">Total Earnings</span>
                      <span className="text-sm font-medium">RM {(selectedUser.total_earnings || 0).toFixed(2)}</span>
                    </div>

                    <div className="flex flex-col sm:flex-row sm:justify-between gap-1">
                      <span className="text-sm">Available Balance</span>
                      <span className="text-sm font-medium">RM {(selectedUser.available_balance || 0).toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="space-y-4">
                <h4 className="text-sm font-medium text-muted-foreground">Additional Information</h4>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(selectedUser).map(([key, value]) => {
                    // Skip already displayed fields and null/undefined values
                    if (['id', 'full_name', 'email', 'role', 'subscription_status', 'created_at',
                         'events_created', 'total_earnings', 'available_balance', 'organization',
                         'subscription_end_date'].includes(key) || value === null || value === undefined) {
                      return null;
                    }

                    // Format the value based on its type
                    let displayValue = value;
                    if (typeof value === 'boolean') {
                      displayValue = value ? 'Yes' : 'No';
                    } else if (typeof value === 'object') {
                      try {
                        displayValue = JSON.stringify(value);
                      } catch (e) {
                        displayValue = 'Complex Object';
                      }
                    }

                    // Format the key for display
                    const displayKey = key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

                    return (
                      <div key={key} className="flex flex-col sm:flex-row sm:justify-between gap-1">
                        <span className="text-sm">{displayKey}</span>
                        <span className="text-sm font-medium break-all sm:truncate sm:max-w-[200px]" title={String(displayValue)}>
                          {String(displayValue)}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setIsViewDetailsDialogOpen(false)} className="w-full sm:w-auto">
              Close
            </Button>
            <Button onClick={() => {
              setIsViewDetailsDialogOpen(false);
              openEditDialog(selectedUser);
            }} className="w-full sm:w-auto">
              <Edit className="mr-2 h-4 w-4" />
              Edit Role
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
    </TooltipProvider>
  )
}
