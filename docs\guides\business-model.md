# 🎫 mTicket.my Business Model (Mobile Ticketing for Events)

mTicket.my is a comprehensive event management platform that empowers users to **create, manage, and monetize events** such as workshops, fun runs, and seminars, with integrated tools for **ticketing, QR attendance, and certificate issuance**.

## 🚀 Implementation Status

**Current Version**: Production-ready with comprehensive feature set
**Technology Stack**: Next.js 15, Supabase PostgreSQL, TypeScript, Tailwind CSS
**Deployment**: Vercel with automatic deployments

---

## 💼 Core Offerings

### 🧩 Key Features & Implementation Status

| Feature                            | Starter                    | Basic (Lite)            | Plus (Pro) | Elite (Enterprise) | Status |
| ---------------------------------- | -------------------------- | ----------------------- | ---------- | ------------------ | ------ |
| Monthly Price                      | Free                       | RM9.90                  | RM29.90    | RM49.90            | ✅ Implemented |
| Annual Price                       | N/A                        | RM108.90 (1 month free) | RM328.90   | RM548.90           | ✅ Implemented |
| Events per Month                   | 1                          | 5                       | 100        | Unlimited          | ✅ Implemented |
| Max Total Participants             | 30                         | 500                     | 1,000      | Unlimited          | ✅ Implemented |
| Participant Allocation Logic       | Shared limit across events |                         |            |                    | ✅ Implemented |
| QR Attendance                      | ❌                          | ❌                       | ✅          | ✅                  | ✅ Implemented |
| Custom Certificate Template        | ✅                          | ✅                       | ✅          | ✅                  | ✅ Implemented |
| Email Certificates to Participants | ❌                          | ❌                       | ✅          | ✅                  | 🔄 In Progress |
| Invoice & Receipt Generation       | ✅                          | ✅                       | ✅          | ✅                  | 🔄 In Progress |
| Live Dashboard & Analytics         | ✅                          | ✅                       | ✅          | ✅                  | ✅ Implemented |

## ✅ Completed Features

### 🔐 Authentication & User Management
- **JWT-based Authentication**: Secure login/logout with token management
- **Role-Based Access Control**: Granular permissions (Admin, Manager, User roles)
- **User Registration**: Email verification and password reset functionality
- **Profile Management**: User profile editing and password changes
- **Organization Management**: Multi-tenant organization support

### 🎫 Event Management System
- **Event Creation**: Rich event creation with image uploads and categorization
- **Event Publishing**: Public event listings with search and filtering
- **Category Management**: Event categorization with color-coded badges
- **Event Registration**: User registration system with attendee tracking
- **QR Code Generation**: Event sharing and attendance tracking via QR codes
- **Event Analytics**: Registration statistics and performance metrics

### 💳 Subscription & Payment System
- **Database-Driven Plans**: Subscription plans stored in database with editable features
- **Payment Gateway Integration**: Support for Billplz, ToyyibPay, Chip, and Stripe
- **Subscription Management**: Plan upgrades, downgrades, and cancellations
- **Usage Tracking**: Feature usage monitoring and limitations enforcement
- **Transaction Processing**: Secure payment processing with activity logging

### 📜 Certificate System
- **Template Management**: Drag-and-drop certificate template editor
- **Certificate Generation**: Automated certificate creation for event attendees
- **QR Verification**: Certificate authenticity verification via QR codes
- **Custom Fields**: Customizable certificate fields and branding
- **Bulk Generation**: Mass certificate generation for events

### 📊 Analytics & Reporting
- **Dashboard Analytics**: Real-time event and user statistics
- **Revenue Tracking**: Financial reporting and revenue analytics
- **Subscription Analytics**: Plan usage and subscription metrics
- **Event Performance**: Attendance rates and engagement metrics
- **User Behavior**: Activity tracking and user engagement analysis

### 🔗 Integration & API System
- **Webhook System**: Real-time event notifications and third-party integrations
- **API Key Management**: Secure API access with user-specific tokens
- **Activity Logging**: Comprehensive audit trail for all user actions
- **RESTful APIs**: Complete API coverage for all platform features
- **Rate Limiting**: API security with request rate limiting

### 🛡️ Security & Compliance
- **Row Level Security**: Database-level security policies
- **Input Validation**: Comprehensive data validation and sanitization
- **CORS Configuration**: Secure cross-origin resource sharing
- **Password Security**: Bcrypt hashing and secure password policies
- **Audit Trails**: Complete activity logging for compliance

## 🔄 Features In Development

### 📧 Email System Enhancement
- **Email Certificate Delivery**: Automated email delivery of certificates to participants
- **Email Templates**: Customizable email templates for various notifications
- **Bulk Email**: Mass email functionality for event updates and announcements

### 🧾 Invoice & Receipt System
- **Automated Invoicing**: Generate invoices for paid events and subscriptions
- **Receipt Generation**: Digital receipts for all transactions
- **Tax Compliance**: Support for various tax requirements and reporting

### 📱 Mobile Optimization
- **Progressive Web App**: Enhanced mobile experience with PWA features
- **Mobile QR Scanner**: Native mobile QR code scanning for attendance
- **Offline Capability**: Basic offline functionality for event management

## 🚀 Future Roadmap

### 📈 Advanced Analytics
- **Predictive Analytics**: AI-powered event success predictions
- **Custom Reports**: User-defined reporting and data visualization
- **Export Functionality**: Data export in various formats (PDF, Excel, CSV)

### 🌐 Multi-language Support
- **Internationalization**: Support for multiple languages
- **Localized Content**: Region-specific content and currency support
- **RTL Support**: Right-to-left language support

### 🤖 AI Integration
- **Smart Recommendations**: AI-powered event recommendations
- **Automated Categorization**: Intelligent event categorization
- **Chatbot Support**: AI-powered customer support

---

## 💳 Pricing & Monetization

### 1. **Subscription Plans**

* Users subscribe monthly or annually (with 1-month discount for annual).
* All plans include support for free and paid events.

### 2. **Transaction Fees**

* For paid events:

  * **5% per participant fee**, with **RM2 minimum and RM10 maximum per participant**.

### 3. **Withdrawal Fees**

* RM1.50 flat fee per withdrawal (payout to system).

---

## 📦 Operational Costs & Infrastructure

### 🏗️ Current Infrastructure
* **Frontend Hosting**: Vercel Pro Plan (~$20/month) with automatic deployments
* **Database & Auth**: Supabase Pro Plan (~$25/month) with PostgreSQL and authentication
* **Email Service**: Resend/Mailersend (~$10-20/month) for transactional emails
* **Payment Gateways**:
  - Billplz (2.9% + RM0.30 per transaction)
  - ToyyibPay (2.9% + RM0.30 per transaction)
  - Chip (2.9% + RM0.30 per transaction)
  - Stripe (3.4% + RM0.50 per transaction)
* **Domain & SSL**: Included with Vercel
* **CDN & Storage**: Supabase Storage included in plan

### 💰 Monthly Operating Costs
* **Fixed Costs**: ~RM200-250/month (hosting, database, email)
* **Variable Costs**: Payment gateway fees (2.9-3.4% per transaction)
* **Scaling Costs**: Additional database/bandwidth as user base grows

---

## 📊 Scalability Consideration

* Participant cap per plan is enforced **across all events** to manage server load.
* Example: Basic Plan (500 max) — if one event has 400 participants, only 100 participants left across other 4 events.

---

## 🔍 Competitive Analysis

Compared with local competitors like **Ticket2U**, **RaceRoaster**, and **Eventbrite**:

* mTicket.my is **cheaper and more flexible** for small to medium organizers.
* Offers **custom certificate templates, invoices**, and **live analytics** even in free/basic tiers.

---

## 📈 Simulated Financial Feasibility (Year 1)

Assuming:

* Month 1: 100 Starter, 50 Basic, 25 Plus, 5 Elite users.
* 10% growth per month.
* Modest RM5.80K monthly transaction fee.
* Monthly cost \~RM151.80 (server + email).

**Summary (Year 1)**:

* **Total Revenue**: \~RM97,748.90
* **Net Profit**: \~RM95,927.30
* Sustainable with small scale and high margin, scalable as user base grows.

---

## ✅ Summary of Advantages

### 💼 Business Advantages
* 🎯 **Low barrier to entry**: Free plan with minimal fees for small organizers
* 📈 **Scalable model**: Built-in monetization with subscription and transaction fees
* 🛠️ **Feature-rich platform**: Comprehensive features even at low tiers
* 💸 **Transparent pricing**: Clear pricing structure for users and event organizers
* 📧 **Unique features**: Built-in certificate generation and QR verification

### 🔧 Technical Advantages
* ⚡ **Modern Tech Stack**: Next.js 15, TypeScript, Supabase for optimal performance
* 🔒 **Enterprise Security**: Row Level Security, JWT authentication, comprehensive audit trails
* 📱 **Responsive Design**: Mobile-first approach with progressive web app capabilities
* 🚀 **High Performance**: Vercel deployment with global CDN and optimized loading
* 🔗 **API-First**: Complete REST API with webhook integrations for extensibility
* 📊 **Real-time Analytics**: Live dashboard with comprehensive reporting capabilities

### 🏆 Competitive Advantages
* 💰 **Cost-effective**: Significantly cheaper than competitors like Eventbrite
* 🎨 **Customizable**: Drag-and-drop certificate templates and branding options
* 🔍 **Comprehensive**: All-in-one solution from event creation to certificate delivery
* 🌏 **Local Focus**: Optimized for Malaysian market with local payment gateways
* 🤝 **Developer-Friendly**: Open API and webhook system for integrations