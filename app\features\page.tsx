import { PageLayout } from "@/components/page-layout"
import { HeroSection } from "@/components/hero-section"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Calendar,
  Users,
  Shield,
  BarChart3,
  Award,
  Zap,
  CreditCard,
  Mail,
  Smartphone,
  Globe,
  Lock,
  CheckCircle,
  Clock,
  FileText,
  QrCode,
  Settings,
  Webhook,
  Database
} from "lucide-react"

export const metadata = {
  title: "Features | mTicket.my - Event Management Platform",
  description: "Discover all the powerful features that make mTicket.my the complete event management solution for organizers of all sizes.",
}

export default function FeaturesPage() {
  const coreFeatures = [
    {
      icon: Calendar,
      title: "Event Creation & Management",
      description: "Create stunning event pages with advanced features and custom fields",
      features: [
        "Multi-image carousels with primary image selection",
        "WYSIWYG rich text descriptions with Quill editor",
        "Custom registration fields with JSONB storage",
        "Multiple ticket types with deadline management",
        "Event scheduling with smart sorting (active first)",
        "Enhanced venue management and location details",
        "Event categories with advanced filtering",
        "Published/draft status with visibility controls"
      ]
    },
    {
      icon: Users,
      title: "Attendee Management",
      description: "Advanced registration management with custom fields and security",
      features: [
        "Real-time registration tracking with custom fields",
        "Automated confirmation emails with participant details",
        "Secure attendee check-in with dynamic QR codes",
        "Group registration with main contact designation",
        "Custom participant data collection per ticket type",
        "Enhanced profile management with organization linking",
        "Waitlist management with automated notifications",
        "Comprehensive activity logging for audit trails"
      ]
    },
    {
      icon: CreditCard,
      title: "Secure Payment Processing",
      description: "Accept payments securely with multiple payment gateways",
      features: [
        "Multiple payment gateway support",
        "Instant payment confirmation",
        "Automated refund processing",
        "Financial reporting and analytics",
        "Tax calculation and invoicing",
        "Commission tracking for managers"
      ]
    },
    {
      icon: QrCode,
      title: "Dynamic QR Code Security",
      description: "Enterprise-grade secure digital tickets with time-based tokens",
      features: [
        "Time-based tokens refreshing every 30 seconds",
        "HMAC SHA-256 cryptographic signatures",
        "Unique nonces preventing replay attacks",
        "6-cycle automatic refresh with manual override",
        "Mobile-optimized ticket display with countdown",
        "Secure ticket verification with grace periods",
        "PDF ticket downloads with dynamic codes",
        "Anti-fraud protection with check-in control"
      ]
    },
    {
      icon: Award,
      title: "Professional Certificate Management",
      description: "Advanced certificate editor with drag-drop and HTML synchronization",
      features: [
        "Drag-and-drop field positioning with @dnd-kit",
        "Dual editor mode: Visual and HTML synchronization",
        "5 default templates plus custom private templates",
        "A4 printing dimensions with landscape/portrait",
        "Background color and image upload functionality",
        "Comprehensive audit trail with timeline display",
        "Certificate verification with QR codes",
        "Bulk certificate generation with background processing"
      ]
    },
    {
      icon: BarChart3,
      title: "Analytics & Reporting",
      description: "Track performance with detailed insights and reports",
      features: [
        "Real-time event analytics",
        "Registration and sales reports",
        "Attendee demographics analysis",
        "Revenue tracking and forecasting",
        "Custom report generation",
        "Data export capabilities"
      ]
    }
  ]

  const advancedFeatures = [
    {
      icon: Users,
      title: "Organization Management",
      description: "Multi-tenant support with searchable organization database",
      badge: "Multi-Tenant"
    },
    {
      icon: Webhook,
      title: "API & Integrations",
      description: "Connect with your existing tools through our robust API",
      badge: "Developer Friendly"
    },
    {
      icon: Shield,
      title: "Enterprise Security",
      description: "HMAC signatures, time-based tokens, and comprehensive access controls",
      badge: "Enterprise"
    },
    {
      icon: FileText,
      title: "Activity Logging",
      description: "Foreign key relationships with complete audit trail tracking",
      badge: "Audit Ready"
    },
    {
      icon: Globe,
      title: "Multi-language Support",
      description: "Reach global audiences with localized event pages",
      badge: "Global"
    },
    {
      icon: Smartphone,
      title: "Mobile Optimization",
      description: "Fully responsive design for all devices",
      badge: "Mobile First"
    },
    {
      icon: Database,
      title: "Data Management",
      description: "Comprehensive data storage with backup and recovery",
      badge: "Reliable"
    },
    {
      icon: Settings,
      title: "White-label Solution",
      description: "Customize the platform with your own branding",
      badge: "Customizable"
    }
  ]

  return (
    <PageLayout>
      {/* Corporate Hero Section */}
      <section className="relative w-full py-20 md:py-32 lg:py-40 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}
          ></div>
        </div>

        <div className="container relative px-4 md:px-6">
          <div className="flex flex-col items-center space-y-8 text-center">
            {/* Main Heading */}
            <div className="space-y-4 max-w-4xl">
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl lg:text-7xl">
                Powerful Features for
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> Every Event</span>
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-gray-300 md:text-2xl leading-relaxed">
                Discover the comprehensive suite of enterprise-grade tools featuring dynamic QR codes, custom fields,
                professional certificate management, and advanced security that make mTicket.my the complete solution for organizers of all sizes.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Core Features Section */}
      <section className="w-full py-16 md:py-20 bg-white">
        <div className="container px-4 md:px-6">
          <div className="mx-auto max-w-7xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Core Features
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Everything you need to create, manage, and scale successful events
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {coreFeatures.map((feature, index) => (
                <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <CardHeader className="pb-4">
                    <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <feature.icon className="h-8 w-8 text-purple-600" />
                    </div>
                    <CardTitle className="text-xl font-semibold text-center">{feature.title}</CardTitle>
                    <CardDescription className="text-center">{feature.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {feature.features.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-start gap-2 text-sm text-gray-600">
                          <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0 mt-0.5" />
                          {item}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Advanced Features Section */}
      <section className="w-full py-16 md:py-20 bg-gray-50">
        <div className="container px-4 md:px-6">
          <div className="mx-auto max-w-7xl">
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Advanced Capabilities
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Enterprise-grade features for professional event management
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {advancedFeatures.map((feature, index) => (
                <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <CardHeader className="text-center pb-4">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <feature.icon className="h-8 w-8 text-blue-600" />
                    </div>
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <CardTitle className="text-xl font-semibold">{feature.title}</CardTitle>
                      <Badge variant="secondary" className="text-xs">
                        {feature.badge}
                      </Badge>
                    </div>
                    <CardDescription>{feature.description}</CardDescription>
                  </CardHeader>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>
    </PageLayout>
  )
}
