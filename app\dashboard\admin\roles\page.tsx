"use client"

import { useEffect, useState } from "react"
import { Edit, Plus, Save, Trash } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"
import { Label } from "@/components/ui/label"
import { useIsMobile } from "@/hooks/use-mobile"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Role } from "@/lib/db/supabase-schema"
import { createRole, updateRole, deleteRole } from "@/app/actions/roles"
import { useAuth } from "@/contexts/auth-context"

export default function RolesPage() {
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isAddRoleDialogOpen, setIsAddRoleDialogOpen] = useState(false)
  const [currentUser, setCurrentUser] = useState<any>(null)
  const [newRole, setNewRole] = useState<Partial<Role>>({
    role_name: "",
    description: "",
    permissions: {},
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const isMobile = useIsMobile()
  const { toast } = useToast()
  const { user, loading: authLoading, isAdmin } = useAuth()

  // Helper function to get auth token from cookies
  const getAuthToken = () => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; auth_token=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  // Permission categories
  const permissionCategories = [
    {
      id: "events",
      name: "Events",
      permissions: ["view", "create", "edit", "delete", "manage"],
    },
    {
      id: "users",
      name: "Users",
      permissions: ["view", "create", "edit", "delete", "manage"],
    },
    {
      id: "reports",
      name: "Reports",
      permissions: ["view", "export", "manage"],
    },
    {
      id: "settings",
      name: "Settings",
      permissions: ["view", "edit"],
    },
    {
      id: "roles",
      name: "Roles",
      permissions: ["view", "create", "edit", "delete"],
    },
  ]

  // Function to fetch roles from database
  const loadRoles = async () => {
    setLoading(true)
    console.log("Starting to load roles...")
    try {
      // Get auth token for API request
      const token = getAuthToken();
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };

      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Use the server-side API endpoint to fetch roles
      const response = await fetch('/api/admin/roles/list', {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch roles')
      }

      const { roles: rolesData } = await response.json()

      if (rolesData && rolesData.length > 0) {
        console.log("Roles fetched successfully:", rolesData.length, "roles found");
        console.log("First role sample:", JSON.stringify(rolesData[0]));
        setRoles(rolesData)
      } else {
        console.log("No roles found in database or empty data array returned")
        setRoles([])
      }
    } catch (err) {
      console.error("Exception in loadRoles function:", err)
      toast({
        title: "Error",
        description: "Failed to fetch roles from database",
        variant: "destructive",
      })
      setRoles([])
    } finally {
      setLoading(false)
      console.log("Finished loading roles, loading state set to false")
    }
  }

  // Fetch current user and check permissions
  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        // Check if we're in development mode
        const isDevelopment = process.env.NODE_ENV === "development";
        console.log("Roles page - Is development mode:", isDevelopment);

        // Use the custom auth context
        if (user?.id) {
          console.log("Using custom auth context user:", user);
          setCurrentUser(user);
          console.log("Roles page - Current user set from auth context with role:", user.role_name || user.role);

          // Only fetch roles if current user is admin
          if (isAdmin()) {
            console.log("Roles page - User is admin, fetching roles");
            await loadRoles();
          } else {
            console.log("Roles page - User is not admin, skipping data fetch");
          }
          return;
        }

        // In production, we should not use mock data
        if (!isDevelopment) {
          console.log("Roles page - No valid user in production, not using mock data");
          setCurrentUser(null);
          setLoading(false);
          return;
        }

        // Fallback for development when user data is not available
        console.log("No valid user data found, using mock admin user")

        // Set a mock admin user for development purposes
        const mockAdminUser = {
          id: "mock-admin-id",
          full_name: "Mock Admin",
          email: "<EMAIL>",
          role: "admin",
          role_name: "admin",
          subscription_status: "active",
          created_at: new Date().toISOString()
        }

        console.log("Setting mock admin user:", mockAdminUser);
        setCurrentUser(mockAdminUser)

        // Fetch roles since we're using a mock admin
        await loadRoles()
      } catch (err) {
        console.error("Error in authentication flow:", err)
        toast({
          title: "Error",
          description: "Failed to verify user permissions",
          variant: "destructive",
        })

        // Only use mock data in development
        if (process.env.NODE_ENV === "development") {
          console.log("Roles page - Using mock admin in development after error");
          // Set a mock admin user as fallback
          setCurrentUser({
            id: "mock-admin-id",
            full_name: "Mock Admin (Error Fallback)",
            email: "<EMAIL>",
            role: "admin",
            role_name: "admin",
            subscription_status: "active",
            created_at: new Date().toISOString()
          })

          // Fetch roles with the mock admin
          await loadRoles()
        } else {
          setCurrentUser(null);
          setRoles([])
        }
      } finally {
        setLoading(false)
      }
    }

    // Only run if auth is not loading
    if (!authLoading) {
      fetchCurrentUser()
    }
  }, [toast, user, authLoading])

  // Handle role update
  const handleUpdateRole = async () => {
    if (!selectedRole) return

    setIsSubmitting(true)
    try {
      // Use the server action to update the role
      const { success, error } = await updateRole(selectedRole)

      if (!success) throw error

      toast({
        title: "Success",
        description: "Role updated successfully",
      })

      // Refresh the roles list
      await loadRoles()

      setIsEditDialogOpen(false)
    } catch (err) {
      console.error("Error updating role:", err)
      toast({
        title: "Error",
        description: "Failed to update role",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle role deletion
  const handleDeleteRole = async () => {
    if (!selectedRole) return

    setIsSubmitting(true)
    try {
      // Use the server action to delete the role
      const { success, error } = await deleteRole(selectedRole.id)

      if (!success) {
        // Check if the error is about users assigned to the role
        if (error?.message?.includes('users are currently assigned to this role')) {
          toast({
            title: "Error",
            description: error.message,
            variant: "destructive",
          })
          setIsSubmitting(false)
          return
        }

        throw error
      }

      toast({
        title: "Success",
        description: "Role deleted successfully",
      })

      // Refresh the roles list
      await loadRoles()

      setIsDeleteDialogOpen(false)
    } catch (err) {
      console.error("Error deleting role:", err)
      toast({
        title: "Error",
        description: "Failed to delete role",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle adding a new role
  const handleAddRole = async () => {
    setIsSubmitting(true)

    try {
      if (!newRole.role_name) {
        toast({
          title: "Error",
          description: "Role name is required",
          variant: "destructive",
        })
        return
      }

      // Use the server action to create the role
      const { success, error } = await createRole(newRole)

      if (!success) {
        // Check if the error is about duplicate role name
        if (error?.message?.includes('role with this name already exists')) {
          toast({
            title: "Error",
            description: error.message,
            variant: "destructive",
          })
          setIsSubmitting(false)
          return
        }

        throw error
      }

      toast({
        title: "Success",
        description: "Role added successfully",
      })

      // Reset form and close dialog
      setNewRole({
        role_name: "",
        description: "",
        permissions: {},
      })
      setIsAddRoleDialogOpen(false)

      // Refresh the roles list
      await loadRoles()
    } catch (err) {
      console.error("Error adding role:", err)
      toast({
        title: "Error",
        description: "Failed to add role",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Open edit dialog
  const openEditDialog = (role: Role) => {
    setSelectedRole(role)
    setIsEditDialogOpen(true)
  }

  // Open delete dialog
  const openDeleteDialog = (role: Role) => {
    setSelectedRole(role)
    setIsDeleteDialogOpen(true)
  }

  // Filter roles based on search query
  const filteredRoles = roles.filter(
    (role) => {
      return role.role_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
             role.description?.toLowerCase().includes(searchQuery.toLowerCase());
    }
  )

  // Check if a permission is granted
  const hasPermission = (role: Role | Partial<Role>, category: string, permission: string) => {
    if (!role.permissions) return false;
    return role.permissions[category]?.[permission] === true;
  }

  // Toggle a permission
  const togglePermission = (role: Role | Partial<Role>, category: string, permission: string) => {
    const updatedPermissions = { ...role.permissions } || {};

    if (!updatedPermissions[category]) {
      updatedPermissions[category] = {};
    }

    updatedPermissions[category][permission] = !updatedPermissions[category][permission];

    if (role === selectedRole) {
      setSelectedRole({ ...selectedRole, permissions: updatedPermissions });
    } else {
      setNewRole({ ...newRole, permissions: updatedPermissions });
    }
  }

  // Check if user is admin using the auth context
  if (!isAdmin()) {
    return (
      <Card className="m-6">
        <CardHeader>
          <CardTitle>Role Management</CardTitle>
          <CardDescription>You don't have permission to access this page</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <TooltipProvider>
      <div className="space-y-6 p-4 sm:p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h1 className="text-xl sm:text-2xl font-bold">Role Management</h1>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button onClick={() => setIsAddRoleDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                <span className="whitespace-nowrap">Add Role</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Create a new role with custom permissions</p>
            </TooltipContent>
          </Tooltip>
        </div>

      <Card>
        <CardHeader>
          <CardTitle>Manage Roles</CardTitle>
          <CardDescription>Define roles and permissions for users in the system</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-6 flex flex-col sm:flex-row items-start sm:items-center gap-4 flex-wrap">
            <Input
              placeholder="Search roles..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full sm:max-w-sm"
            />

            <div className="w-full sm:ml-auto sm:w-auto flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Total Roles: {roles.length} | Filtered: {filteredRoles.length}
              </span>
            </div>
          </div>

          {loading ? (
            <div className="flex h-40 items-center justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
          ) : (
            <div className="rounded-md border overflow-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Role Name</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Permissions</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRoles.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="h-24 text-center">
                        No roles found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredRoles.map((role) => (
                      <TableRow key={role.id}>
                        <TableCell>
                          <div className="font-medium">{role.role_name}</div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm text-muted-foreground">{role.description || "-"}</div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {role.permissions && Object.keys(role.permissions).map((category) => (
                              <Badge key={category} variant="outline" className="bg-blue-50">
                                {category}
                              </Badge>
                            ))}
                            {(!role.permissions || Object.keys(role.permissions).length === 0) && (
                              <span className="text-sm text-muted-foreground">No permissions</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-1 md:gap-2">
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button variant="ghost" size="icon" onClick={() => openEditDialog(role)}>
                                  <Edit className="h-4 w-4" />
                                  <span className="sr-only">Edit Role</span>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Edit Role</p>
                              </TooltipContent>
                            </Tooltip>

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => openDeleteDialog(role)}
                                  className="text-destructive hover:text-destructive"
                                >
                                  <Trash className="h-4 w-4" />
                                  <span className="sr-only">Delete Role</span>
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Delete Role</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Role Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className={`${isMobile ? 'w-[95vw] max-w-full' : 'max-w-4xl'}`}>
          <DialogHeader>
            <DialogTitle>Edit Role</DialogTitle>
            <DialogDescription>
              Update role details and permissions for {selectedRole?.role_name}
            </DialogDescription>
          </DialogHeader>
          {selectedRole && (
            <div className="space-y-4 py-4 max-h-[70vh] overflow-y-auto">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="role-name">Role Name</Label>
                  <Input
                    id="role-name"
                    value={selectedRole.role_name}
                    onChange={(e) => setSelectedRole({ ...selectedRole, role_name: e.target.value })}
                    placeholder="Enter role name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="role-description">Description</Label>
                  <Textarea
                    id="role-description"
                    value={selectedRole.description || ''}
                    onChange={(e) => setSelectedRole({ ...selectedRole, description: e.target.value })}
                    placeholder="Enter role description"
                    rows={3}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <Label>Permissions</Label>
                <Tabs defaultValue="events" className="w-full">
                  <TabsList className="w-full flex flex-wrap h-auto">
                    {permissionCategories.map((category) => (
                      <TabsTrigger key={category.id} value={category.id} className="flex-1">
                        {category.name}
                      </TabsTrigger>
                    ))}
                  </TabsList>
                  {permissionCategories.map((category) => (
                    <TabsContent key={category.id} value={category.id} className="border rounded-md p-4">
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                        {category.permissions.map((permission) => (
                          <div key={permission} className="flex items-center space-x-2">
                            <Checkbox
                              id={`${category.id}-${permission}`}
                              checked={hasPermission(selectedRole, category.id, permission)}
                              onCheckedChange={() => togglePermission(selectedRole, category.id, permission)}
                            />
                            <Label
                              htmlFor={`${category.id}-${permission}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {permission.charAt(0).toUpperCase() + permission.slice(1)}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </TabsContent>
                  ))}
                </Tabs>
              </div>
            </div>
          )}
          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)} disabled={isSubmitting} className="w-full sm:w-auto">
              Cancel
            </Button>
            <Button onClick={handleUpdateRole} disabled={isSubmitting} className="w-full sm:w-auto">
              {isSubmitting ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                  Updating...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Role Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className={`${isMobile ? 'w-[95vw] max-w-full' : ''}`}>
          <DialogHeader>
            <DialogTitle>Delete Role</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the role "{selectedRole?.role_name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)} disabled={isSubmitting} className="w-full sm:w-auto">
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteRole} disabled={isSubmitting} className="w-full sm:w-auto">
              {isSubmitting ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                  Deleting...
                </>
              ) : (
                "Delete Role"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Role Dialog */}
      <Dialog open={isAddRoleDialogOpen} onOpenChange={setIsAddRoleDialogOpen}>
        <DialogContent className={`${isMobile ? 'w-[95vw] max-w-full' : 'max-w-4xl'}`}>
          <DialogHeader>
            <DialogTitle>Add New Role</DialogTitle>
            <DialogDescription>
              Create a new role with custom permissions
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4 max-h-[70vh] overflow-y-auto">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="new-role-name">Role Name</Label>
                <Input
                  id="new-role-name"
                  value={newRole.role_name}
                  onChange={(e) => setNewRole({ ...newRole, role_name: e.target.value })}
                  placeholder="Enter role name"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-role-description">Description</Label>
                <Textarea
                  id="new-role-description"
                  value={newRole.description || ''}
                  onChange={(e) => setNewRole({ ...newRole, description: e.target.value })}
                  placeholder="Enter role description"
                  rows={3}
                />
              </div>
            </div>

            <div className="space-y-4">
              <Label>Permissions</Label>
              <Tabs defaultValue="events" className="w-full">
                <TabsList className="w-full flex flex-wrap h-auto">
                  {permissionCategories.map((category) => (
                    <TabsTrigger key={category.id} value={category.id} className="flex-1">
                      {category.name}
                    </TabsTrigger>
                  ))}
                </TabsList>
                {permissionCategories.map((category) => (
                  <TabsContent key={category.id} value={category.id} className="border rounded-md p-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                      {category.permissions.map((permission) => (
                        <div key={permission} className="flex items-center space-x-2">
                          <Checkbox
                            id={`new-${category.id}-${permission}`}
                            checked={hasPermission(newRole, category.id, permission)}
                            onCheckedChange={() => togglePermission(newRole, category.id, permission)}
                          />
                          <Label
                            htmlFor={`new-${category.id}-${permission}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {permission.charAt(0).toUpperCase() + permission.slice(1)}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            </div>
          </div>
          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button variant="outline" onClick={() => setIsAddRoleDialogOpen(false)} disabled={isSubmitting} className="w-full sm:w-auto">
              Cancel
            </Button>
            <Button onClick={handleAddRole} disabled={isSubmitting} className="w-full sm:w-auto">
              {isSubmitting ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                  Adding...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Role
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
    </TooltipProvider>
  )
}