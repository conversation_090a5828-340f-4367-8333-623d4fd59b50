"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Award, 
  Plus, 
  Edit, 
  Trash, 
  Download,
  Eye,
  Settings,
  Users,
  Calendar
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"

type CertificateTemplate = {
  id: string
  name: string
  description: string
  template_data: any
  is_active: boolean
  created_at: string
  updated_at: string
}

type CertificateStats = {
  totalTemplates: number
  activeTemplates: number
  certificatesIssued: number
  thisMonthIssued: number
}

export default function CertificatesPage() {
  const [templates, setTemplates] = useState<CertificateTemplate[]>([])
  const [stats, setStats] = useState<CertificateStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<CertificateTemplate | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { user, isAdmin } = useAuth()
  const { toast } = useToast()

  // Helper function to get cookie value
  const getCookie = (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!isAdmin()) {
        setLoading(false)
        return
      }

      try {
        const token = getCookie('auth_token')
        if (!token) {
          throw new Error('No authentication token found')
        }

        // Fetch templates and stats in parallel
        const [templatesResponse, statsResponse] = await Promise.all([
          fetch('/api/admin/certificates/templates', {
            headers: { 'Authorization': `Bearer ${token}` },
          }),
          fetch('/api/admin/certificates/stats', {
            headers: { 'Authorization': `Bearer ${token}` },
          })
        ])

        if (templatesResponse.ok) {
          const templatesData = await templatesResponse.json()
          setTemplates(templatesData.templates || [])
        }

        if (statsResponse.ok) {
          const statsData = await statsResponse.json()
          setStats(statsData.stats)
        } else {
          // Mock data for now
          setStats({
            totalTemplates: 3,
            activeTemplates: 2,
            certificatesIssued: 89,
            thisMonthIssued: 23
          })
        }
      } catch (error) {
        console.error("Error fetching certificate data:", error)
        toast({
          title: "Error",
          description: "Failed to fetch certificate data",
          variant: "destructive",
        })
        // Set mock data
        setStats({
          totalTemplates: 3,
          activeTemplates: 2,
          certificatesIssued: 89,
          thisMonthIssued: 23
        })
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [isAdmin])

  const handleAddTemplate = () => {
    setSelectedTemplate({
      id: "",
      name: "",
      description: "",
      template_data: {},
      is_active: true,
      created_at: "",
      updated_at: ""
    })
    setIsEditDialogOpen(true)
  }

  const handleEditTemplate = (template: CertificateTemplate) => {
    setSelectedTemplate(template)
    setIsEditDialogOpen(true)
  }

  const handleDeleteTemplate = (template: CertificateTemplate) => {
    setSelectedTemplate(template)
    setIsDeleteDialogOpen(true)
  }

  const handleSaveTemplate = async () => {
    if (!selectedTemplate) return

    setIsSubmitting(true)
    try {
      const isUpdate = selectedTemplate.id && selectedTemplate.id !== ""
      const url = isUpdate
        ? `/api/admin/certificates/templates/${selectedTemplate.id}`
        : '/api/admin/certificates/templates'

      const method = isUpdate ? 'PUT' : 'POST'

      const token = getCookie('auth_token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(selectedTemplate),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save certificate template')
      }

      const data = await response.json()

      if (isUpdate) {
        setTemplates(templates.map((template) => 
          template.id === selectedTemplate.id ? data.template : template
        ))
      } else {
        setTemplates([...templates, data.template])
      }

      setIsEditDialogOpen(false)
      toast({
        title: "Success",
        description: `Certificate template ${isUpdate ? "updated" : "created"} successfully`,
      })
    } catch (error: any) {
      console.error("Error saving certificate template:", error)
      toast({
        title: "Error",
        description: error.message || `Failed to ${selectedTemplate.id ? "update" : "create"} certificate template`,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isAdmin()) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-muted-foreground">Access Denied</h1>
          <p className="text-muted-foreground mt-2">You need admin privileges to access certificate management.</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <h1 className="text-2xl font-bold">Certificate Management</h1>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-6 w-24 bg-muted rounded"></div>
                <div className="h-8 w-32 bg-muted rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-4 w-full bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Certificate Management</h1>
          <p className="text-muted-foreground">Manage certificate templates and generation settings</p>
        </div>
        <Button onClick={handleAddTemplate}>
          <Plus className="mr-2 h-4 w-4" />
          Add Template
        </Button>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Templates</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalTemplates}</div>
              <p className="text-xs text-muted-foreground">
                {stats.activeTemplates} active
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Certificates Issued</CardTitle>
              <Download className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.certificatesIssued}</div>
              <p className="text-xs text-muted-foreground">
                Total issued
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Month</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.thisMonthIssued}</div>
              <p className="text-xs text-muted-foreground">
                Issued this month
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">98.5%</div>
              <p className="text-xs text-muted-foreground">
                Generation success
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Templates Grid */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Certificate Templates</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {templates.map((template) => (
            <Card key={template.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">{template.name}</CardTitle>
                  <div className="flex items-center gap-1">
                    <Button variant="ghost" size="icon" onClick={() => handleEditTemplate(template)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => handleDeleteTemplate(template)}>
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <CardDescription>{template.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <Badge variant={template.is_active ? "default" : "secondary"}>
                    {template.is_active ? "Active" : "Inactive"}
                  </Badge>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-1" />
                      Preview
                    </Button>
                    <Button variant="outline" size="sm">
                      <Settings className="h-4 w-4 mr-1" />
                      Configure
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Edit Template Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{selectedTemplate?.id ? "Edit" : "Add"} Certificate Template</DialogTitle>
            <DialogDescription>
              {selectedTemplate?.id ? "Update" : "Create"} a certificate template for your events.
            </DialogDescription>
          </DialogHeader>
          {selectedTemplate && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="template-name">Template Name</Label>
                <Input
                  id="template-name"
                  value={selectedTemplate.name}
                  onChange={(e) => setSelectedTemplate({ ...selectedTemplate, name: e.target.value })}
                  placeholder="e.g., Completion Certificate, Attendance Certificate"
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="template-description">Description</Label>
                <Textarea
                  id="template-description"
                  value={selectedTemplate.description}
                  onChange={(e) => setSelectedTemplate({ ...selectedTemplate, description: e.target.value })}
                  placeholder="Brief description of the certificate template"
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="is-active"
                  checked={selectedTemplate.is_active}
                  onCheckedChange={(checked) => setSelectedTemplate({ ...selectedTemplate, is_active: checked })}
                />
                <Label htmlFor="is-active">Active template</Label>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveTemplate} disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Template"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
