"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  FileText, 
  Download, 
  Calendar,
  Filter,
  Plus,
  Eye,
  Trash,
  Clock,
  CheckCircle,
  AlertCircle
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

type Report = {
  id: string
  name: string
  description: string
  type: string
  status: 'pending' | 'generating' | 'completed' | 'failed'
  created_at: string
  completed_at?: string
  file_url?: string
  parameters: any
}

type ReportTemplate = {
  id: string
  name: string
  description: string
  type: string
  parameters: string[]
}

export default function ReportsPage() {
  const [reports, setReports] = useState<Report[]>([])
  const [templates, setTemplates] = useState<ReportTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null)
  const [reportName, setReportName] = useState("")
  const [reportDescription, setReportDescription] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { user, isAdmin } = useAuth()
  const { toast } = useToast()

  // Helper function to get cookie value
  const getCookie = (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!isAdmin()) {
        setLoading(false)
        return
      }

      try {
        const token = getCookie('auth_token')
        if (!token) {
          throw new Error('No authentication token found')
        }

        // Fetch reports and templates in parallel
        const [reportsResponse, templatesResponse] = await Promise.all([
          fetch('/api/admin/reports/list', {
            headers: { 'Authorization': `Bearer ${token}` },
          }),
          fetch('/api/admin/reports/templates', {
            headers: { 'Authorization': `Bearer ${token}` },
          })
        ])

        if (reportsResponse.ok) {
          const reportsData = await reportsResponse.json()
          setReports(reportsData.reports || [])
        } else {
          // Mock data for now
          setReports([
            {
              id: "1",
              name: "Monthly Subscription Report",
              description: "Comprehensive monthly subscription analytics",
              type: "subscription_monthly",
              status: "completed",
              created_at: "2024-01-15T10:30:00Z",
              completed_at: "2024-01-15T10:35:00Z",
              file_url: "/reports/monthly-subscription-jan-2024.pdf",
              parameters: { month: "2024-01", include_charts: true }
            },
            {
              id: "2",
              name: "Revenue Analysis Q4",
              description: "Quarterly revenue breakdown and analysis",
              type: "revenue_quarterly",
              status: "generating",
              created_at: "2024-01-16T09:15:00Z",
              parameters: { quarter: "Q4-2023", detailed: true }
            },
            {
              id: "3",
              name: "User Activity Report",
              description: "Weekly user activity and engagement metrics",
              type: "user_activity",
              status: "failed",
              created_at: "2024-01-16T14:20:00Z",
              parameters: { week: "2024-W03", include_events: true }
            }
          ])
        }

        if (templatesResponse.ok) {
          const templatesData = await templatesResponse.json()
          setTemplates(templatesData.templates || [])
        } else {
          // Mock data for now
          setTemplates([
            {
              id: "subscription_monthly",
              name: "Monthly Subscription Report",
              description: "Detailed monthly subscription metrics and analytics",
              type: "subscription",
              parameters: ["month", "include_charts", "include_comparisons"]
            },
            {
              id: "revenue_quarterly",
              name: "Quarterly Revenue Report",
              description: "Comprehensive quarterly revenue analysis",
              type: "revenue",
              parameters: ["quarter", "detailed", "include_forecasts"]
            },
            {
              id: "user_activity",
              name: "User Activity Report",
              description: "User engagement and activity metrics",
              type: "activity",
              parameters: ["period", "include_events", "include_certificates"]
            },
            {
              id: "certificate_usage",
              name: "Certificate Usage Report",
              description: "Certificate generation and usage statistics",
              type: "certificates",
              parameters: ["period", "template_breakdown", "success_rates"]
            }
          ])
        }
      } catch (error) {
        console.error("Error fetching reports data:", error)
        toast({
          title: "Error",
          description: "Failed to fetch reports data",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [isAdmin])

  const handleCreateReport = () => {
    setReportName("")
    setReportDescription("")
    setSelectedTemplate(null)
    setIsCreateDialogOpen(true)
  }

  const handleGenerateReport = async () => {
    if (!selectedTemplate || !reportName) return

    setIsSubmitting(true)
    try {
      const token = getCookie('auth_token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch('/api/admin/reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          template_id: selectedTemplate.id,
          name: reportName,
          description: reportDescription,
          parameters: {}
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate report')
      }

      const data = await response.json()
      setReports([data.report, ...reports])
      setIsCreateDialogOpen(false)
      
      toast({
        title: "Success",
        description: "Report generation started successfully",
      })
    } catch (error: any) {
      console.error("Error generating report:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to generate report",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDownloadReport = (report: Report) => {
    if (report.file_url) {
      window.open(report.file_url, '_blank')
    } else {
      toast({
        title: "Error",
        description: "Report file not available",
        variant: "destructive",
      })
    }
  }

  const getStatusIcon = (status: Report['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'generating':
        return <Clock className="h-4 w-4 text-blue-500 animate-spin" />
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusBadge = (status: Report['status']) => {
    const variants = {
      completed: "default",
      generating: "secondary",
      failed: "destructive",
      pending: "outline"
    } as const

    return (
      <Badge variant={variants[status]}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  if (!isAdmin()) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-muted-foreground">Access Denied</h1>
          <p className="text-muted-foreground mt-2">You need admin privileges to access reports.</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <h1 className="text-2xl font-bold">Reports Management</h1>
        <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-6 w-48 bg-muted rounded"></div>
                <div className="h-4 w-full bg-muted rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-4 w-32 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Reports Management</h1>
          <p className="text-muted-foreground">Generate and manage subscription and analytics reports</p>
        </div>
        <Button onClick={handleCreateReport}>
          <Plus className="mr-2 h-4 w-4" />
          Generate Report
        </Button>
      </div>

      {/* Report Templates */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Available Report Templates</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {templates.map((template) => (
            <Card key={template.id} className="hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => {
                    setSelectedTemplate(template)
                    setReportName(template.name)
                    setReportDescription(template.description)
                    setIsCreateDialogOpen(true)
                  }}>
              <CardHeader>
                <CardTitle className="text-base">{template.name}</CardTitle>
                <CardDescription className="text-sm">
                  {template.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Badge variant="outline">{template.type}</Badge>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Generated Reports */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Generated Reports</h2>
        <div className="space-y-4">
          {reports.map((report) => (
            <Card key={report.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(report.status)}
                    <div>
                      <CardTitle className="text-base">{report.name}</CardTitle>
                      <CardDescription>{report.description}</CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(report.status)}
                    {report.status === 'completed' && (
                      <Button variant="outline" size="sm" onClick={() => handleDownloadReport(report)}>
                        <Download className="h-4 w-4 mr-1" />
                        Download
                      </Button>
                    )}
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <span>Created: {new Date(report.created_at).toLocaleDateString()}</span>
                  {report.completed_at && (
                    <span>Completed: {new Date(report.completed_at).toLocaleDateString()}</span>
                  )}
                  <span>Type: {report.type}</span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Generate Report Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Generate New Report</DialogTitle>
            <DialogDescription>
              Configure and generate a new report based on the selected template.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="template">Report Template</Label>
              <Select value={selectedTemplate?.id || ""} onValueChange={(value) => {
                const template = templates.find(t => t.id === value)
                setSelectedTemplate(template || null)
                if (template) {
                  setReportName(template.name)
                  setReportDescription(template.description)
                }
              }}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a report template" />
                </SelectTrigger>
                <SelectContent>
                  {templates.map((template) => (
                    <SelectItem key={template.id} value={template.id}>
                      {template.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="report-name">Report Name</Label>
              <Input
                id="report-name"
                value={reportName}
                onChange={(e) => setReportName(e.target.value)}
                placeholder="Enter report name"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="report-description">Description</Label>
              <Textarea
                id="report-description"
                value={reportDescription}
                onChange={(e) => setReportDescription(e.target.value)}
                placeholder="Enter report description"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleGenerateReport} disabled={isSubmitting || !selectedTemplate || !reportName}>
              {isSubmitting ? "Generating..." : "Generate Report"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
