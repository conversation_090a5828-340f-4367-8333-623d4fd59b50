// Using built-in fetch (Node.js 18+)

// Test registration flow for both free and paid events
async function testRegistrationFlow() {
  console.log("🧪 Testing Registration Flow for Free and Paid Events\n");

  // Test data for registration
  const testParticipants = [
    {
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+60123456789",
      ic: "123456789012"
    }
  ];

  const testMainContact = {
    name: "<PERSON>",
    email: "<EMAIL>",
    phone: "+60123456789"
  };

  const testTickets = [
    {
      ticketType: {
        id: "general",
        name: "General Admission",
        price: 0,
        description: "Standard ticket"
      },
      quantity: 1
    }
  ];

  // Test 1: Free Event Registration (AI Technology Webinar 2025)
  console.log("📝 Test 1: Free Event Registration (slug: 6qh3)");
  try {
    const freeEventResponse = await fetch('http://localhost:3000/api/payments/create-public', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        event_id: "62150f10-ca75-47f1-b7a4-cf4e9e4bee50", // AI Technology Webinar 2025
        participants: testParticipants,
        main_contact: testMainContact,
        main_contact_index: 0,
        selected_tickets: testTickets,
        amount: 0,
        currency: 'MYR',
        description: 'Registration for AI Technology Webinar 2025',
        gateway_id: null, // No gateway needed for free events
      }),
    });

    const freeEventResult = await freeEventResponse.json();
    console.log("Free Event Registration Result:", freeEventResult);

    if (freeEventResult.success) {
      console.log("✅ Free event registration successful");

      // For free events, simulate immediate success
      if (freeEventResult.is_free_event) {
        console.log("🎉 Free event - registration should be auto-confirmed");
      }
    } else {
      console.log("❌ Free event registration failed:", freeEventResult.error);
    }
  } catch (error) {
    console.log("❌ Free event registration error:", error.message);
  }

  console.log("\n" + "=".repeat(50) + "\n");

  // Test 2: Paid Event Registration (Ipoh Fun Run Day 2025)
  console.log("📝 Test 2: Paid Event Registration (slug: 5nEh)");

  const paidTestTickets = [
    {
      ticketType: {
        id: "general",
        name: "General Admission",
        price: 30,
        description: "Standard ticket"
      },
      quantity: 1
    }
  ];

  try {
    const paidEventResponse = await fetch('http://localhost:3000/api/payments/create-public', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        event_id: "60fdcaec-49c7-4b3f-9498-6887b3d80276", // Ipoh Fun Run Day 2025
        participants: testParticipants.map(p => ({...p, email: "<EMAIL>"})),
        main_contact: {...testMainContact, email: "<EMAIL>"},
        main_contact_index: 0,
        selected_tickets: paidTestTickets,
        amount: 30,
        currency: 'MYR',
        description: 'Registration for Ipoh Fun Run Day 2025',
        gateway_id: 'toyyibpay', // Use ToyyibPay for paid events
      }),
    });

    const paidEventResult = await paidEventResponse.json();
    console.log("Paid Event Registration Result:", paidEventResult);

    if (paidEventResult.success) {
      console.log("✅ Paid event registration created successfully");
      console.log("💳 Payment URL:", paidEventResult.payment_url);
      console.log("📋 Transaction ID:", paidEventResult.transaction_id);

      // Simulate payment callback for testing
      console.log("\n🔄 Simulating payment callback...");
      await simulatePaymentCallback(paidEventResult.transaction_id, paidEventResult.order_id);
    } else {
      console.log("❌ Paid event registration failed:", paidEventResult.error);
    }
  } catch (error) {
    console.log("❌ Paid event registration error:", error.message);
  }

  console.log("\n" + "=".repeat(50) + "\n");

  // Test 3: Check database for registrations
  console.log("📊 Test 3: Checking database for registrations");
  await checkDatabaseRegistrations();
}

// Simulate payment callback for testing
async function simulatePaymentCallback(transactionId, orderId) {
  try {
    const callbackData = {
      status_id: '1', // Success
      billcode: 'test_bill_' + Date.now(),
      order_id: orderId,
      msg: 'ok',
      transaction_id: 'TP' + Date.now(),
      timestamp: new Date().toISOString(),
      url_params: {
        status_id: '1',
        billcode: 'test_bill_' + Date.now(),
        order_id: orderId,
        msg: 'ok',
        transaction_id: 'TP' + Date.now()
      }
    };

    const response = await fetch('http://localhost:3000/api/payments/save-return-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(callbackData),
    });

    const result = await response.json();
    console.log("Payment Callback Result:", result);

    if (result.success) {
      console.log("✅ Payment callback processed successfully");
      console.log("🎫 Receipt Token:", result.receipt_token ? "Generated" : "Not generated");
      console.log("🎟️ Ticket Token:", result.ticket_token ? "Generated" : "Not generated");
    } else {
      console.log("❌ Payment callback failed:", result.error);
    }
  } catch (error) {
    console.log("❌ Payment callback error:", error.message);
  }
}

// Check database for registrations
async function checkDatabaseRegistrations() {
  try {
    // This would require a database query endpoint
    console.log("📋 Database check would require admin access");
    console.log("✅ Registration flow tests completed");
    console.log("\n💡 To verify registrations were added to the database:");
    console.log("   1. Check the browser network tab during registration");
    console.log("   2. Look at server logs for registration creation");
    console.log("   3. Use admin dashboard to view registrations");
  } catch (error) {
    console.log("❌ Database check error:", error.message);
  }
}

// Run the tests
testRegistrationFlow().catch(console.error);
