import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth"

/**
 * PUT /api/organizations/update
 * Updates an existing organization
 * Requires authentication and user must be the creator or have admin/manager role
 */
export async function PUT(request: Request) {
  try {
    // Get JWT token from request
    const token = getJWTTokenFromRequest(request)

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Verify the token
    const authResult = await verifyJWTToken(token)
    if (!authResult || !authResult.user) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 }
      )
    }

    const userId = authResult.user.id
    const userRole = authResult.user.role_name
    const body = await request.json()

    // Validate required fields
    const { organizationId, name, ssmNumber, picName, picPhone, picEmail, address, website } = body

    if (!organizationId || !name || !ssmNumber || !picName || !picPhone || !picEmail) {
      return NextResponse.json(
        { error: "Missing required fields: organizationId, name, ssmNumber, picName, picPhone, picEmail" },
        { status: 400 }
      )
    }

    console.log("Organizations Update API: Updating organization", { organizationId, name, userId })

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin()

    // Check if organization exists and user has permission to update it
    const { data: existingOrg, error: checkError } = await supabaseAdmin
      .from("organizations")
      .select("id, name, created_by")
      .eq("id", organizationId)
      .single()

    if (checkError || !existingOrg) {
      console.error("Organization not found:", checkError)
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      )
    }

    // Check permissions: user must be the creator, admin, or manager
    const isCreator = existingOrg.created_by === userId
    const isAdmin = userRole === "admin"
    const isManager = ["manager", "supermanager", "event_admin"].includes(userRole || "")

    if (!isCreator && !isAdmin && !isManager) {
      return NextResponse.json(
        { error: "You don't have permission to update this organization" },
        { status: 403 }
      )
    }

    // Check if another organization with the same name exists (excluding current one)
    const { data: duplicateOrg, error: duplicateError } = await supabaseAdmin
      .from("organizations")
      .select("id, name")
      .eq("name", name)
      .neq("id", organizationId)
      .single()

    if (duplicateError && duplicateError.code !== "PGRST116") { // PGRST116 = no rows returned
      console.error("Error checking duplicate organization:", duplicateError)
      return NextResponse.json(
        { error: "Failed to check duplicate organization" },
        { status: 500 }
      )
    }

    if (duplicateOrg) {
      return NextResponse.json(
        { error: "Another organization with this name already exists" },
        { status: 409 }
      )
    }

    // Update the organization
    const { data: orgData, error: updateError } = await supabaseAdmin
      .from("organizations")
      .update({
        name,
        ssm_number: ssmNumber,
        pic_name: picName,
        pic_phone: picPhone,
        pic_email: picEmail,
        address: address || null,
        website: website || null,
        updated_at: new Date().toISOString(),
      })
      .eq("id", organizationId)
      .select()
      .single()

    if (updateError) {
      console.error("Error updating organization:", updateError)
      return NextResponse.json(
        { error: "Failed to update organization" },
        { status: 500 }
      )
    }

    // Log activity
    await supabaseAdmin.from("activity_logs").insert([
      {
        user_id: userId,
        action: "update_organization",
        entity_type: "organization",
        entity_id: organizationId,
        details: { organization_name: name, updated_fields: Object.keys(body) },
      },
    ])

    console.log("Organizations Update API: Successfully updated organization", organizationId)

    return NextResponse.json({
      success: true,
      organization: orgData
    })
  } catch (error: any) {
    console.error("Error in organizations update API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
