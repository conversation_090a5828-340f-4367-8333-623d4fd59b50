import { createClient as createSupabaseClient } from "@supabase/supabase-js"

// Create a singleton instance for the Supabase client
let supabaseInstance: ReturnType<typeof createSupabaseClient> | null = null

export function getSupabaseClient() {
  if (supabaseInstance) return supabaseInstance

  // Check if environment variables are available
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseAnonKey) {
    // Return a dummy client that will handle errors gracefully
    return createSupabaseClient("https://placeholder-url.supabase.co", "placeholder-key", {
      auth: {
        persistSession: false,
      },
    })
  }

  supabaseInstance = createSupabaseClient(supabaseUrl, supabaseAnonKey)
  return supabaseInstance
}

// Get a Supabase client with the service role key to bypass RLS policies
// This should ONLY be used in server-side code, never in client-side code
export function getSupabaseAdmin() {
  // We create a fresh instance each time to ensure we have the latest env vars

  // Check if environment variables are available
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error("Missing Supabase environment variables")
  }

  // Create a fresh instance each time to ensure we have the latest env vars
  const adminClient = createSupabaseClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  })

  return adminClient
}

// Export the supabase client for convenience
export const supabase = getSupabaseClient()

// Export createClient function for compatibility with API routes
export const createClient = getSupabaseAdmin

// Helper function to get the current session
export async function getSession() {
  try {
    const { data, error } = await supabase.auth.getSession()
    if (error) {
      return null
    }
    return data.session
  } catch (error) {
    return null
  }
}

// Helper function to get the current user
export async function getCurrentUser() {
  try {
    const session = await getSession()
    if (!session) return null

    const { data, error } = await supabase.from("users").select("*").eq("id", session.user.id).single()

    if (error) {
      console.error("Error getting user:", error)
      return null
    }

    return data
  } catch (error) {
    console.error("Error getting current user:", error)
    return null
  }
}
