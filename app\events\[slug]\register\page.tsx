"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import * as React from "react"
import { useRouter } from "next/navigation"
import { FileText, ShoppingCart, CreditCard as CreditCardIcon, Check } from "lucide-react"
import { type StepperStep } from "@/components/ui/stepper"
import { type ParticipantType } from "@/components/registration-form"
import { RegistrationLayout } from "@/components/registration/registration-layout"
import { RegistrationStep } from "@/components/registration/registration-step"
import { ReviewStep } from "@/components/registration/review-step"
import { PaymentStep } from "@/components/registration/payment-step"
import { SuccessStep } from "@/components/registration/success-step"
import { useToast } from "@/hooks/use-toast"
import { type SelectedTicket } from "@/types/ticket-types"
import { useEvents, type EventType } from "@/contexts/event-context"
import { useAuth } from "@/contexts/auth-context"

interface RegisterPageProps {
  params: Promise<{
    slug: string
  }>
  searchParams: Promise<{
    step?: string
  }>
}



// Payment gateway type
interface PaymentGateway {
  id: string
  name: string
  type: string
  description: string
  logo_url?: string | null
  enabled: boolean
}

type RegistrationStep = "form" | "summary" | "payment" | "success"

export default function RegisterPage({ params, searchParams }: RegisterPageProps) {
  const router = useRouter()
  const { toast } = useToast()
  const { getEventBySlug } = useEvents()
  const { user } = useAuth()
  const [event, setEvent] = useState<EventType | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [registrationId, setRegistrationId] = useState<string | null>(null)
  const [participants, setParticipants] = useState<ParticipantType[]>([])
  const [mainContact, setMainContact] = useState<any>(null)
  const [currentStep, setCurrentStep] = useState<RegistrationStep>("form")
  const [selectedGateway, setSelectedGateway] = useState<string | null>(null)
  const [formData, setFormData] = useState<any>(null)
  const [paymentStatus, setPaymentStatus] = useState<"idle" | "processing" | "success" | "failed">("idle")
  const [paymentDetails, setPaymentDetails] = useState<any>(null)
  const [selectedTickets, setSelectedTickets] = useState<SelectedTicket[]>([])
  const [ticketsLoaded, setTicketsLoaded] = useState(false)
  const hasLoadedTickets = useRef(false)
  const [paymentGateways, setPaymentGateways] = useState<PaymentGateway[]>([])
  const [paymentGatewaysLoading, setPaymentGatewaysLoading] = useState(false)
  const [receiptToken, setReceiptToken] = useState<string | null>(null)
  const [ticketToken, setTicketToken] = useState<string | null>(null)

  // Get the slug from params and step from searchParams using React.use()
  const { slug } = React.use(params)
  const { step } = React.use(searchParams)

  // Stepper configuration
  const getStepperSteps = (): StepperStep[] => {
    const isFreeEvent = calculateTotal() <= 0
    const steps: StepperStep[] = [
      {
        id: "form",
        title: "Registration",
        description: "Enter participant details",
        icon: <FileText className="h-4 w-4" />,
        status: currentStep === "form" ? "current" :
                (currentStep === "summary" || currentStep === "payment" || currentStep === "success") ? "completed" : "upcoming",
        clickable: true
      },
      {
        id: "summary",
        title: "Review",
        description: "Confirm your details",
        icon: <ShoppingCart className="h-4 w-4" />,
        status: currentStep === "summary" ? "current" :
                (currentStep === "payment" || currentStep === "success") ? "completed" : "upcoming",
        clickable: true
      }
    ]

    // Only add payment step for paid events
    if (!isFreeEvent) {
      steps.push({
        id: "payment",
        title: "Payment",
        description: "Complete your payment",
        icon: <CreditCardIcon className="h-4 w-4" />,
        status: currentStep === "payment" ? "current" :
                currentStep === "success" ? "completed" : "upcoming",
        clickable: true
      })
    }

    steps.push({
      id: "success",
      title: "Complete",
      description: "Registration confirmed",
      icon: <Check className="h-4 w-4" />,
      status: currentStep === "success" ? "current" : "upcoming",
      clickable: false
    })

    return steps
  }

  // Get current step index for stepper
  const getCurrentStepIndex = () => {
    const isFreeEvent = calculateTotal() <= 0
    switch (currentStep) {
      case "form": return 0
      case "summary": return 1
      case "payment": return isFreeEvent ? -1 : 2 // Skip payment for free events
      case "success": return isFreeEvent ? 2 : 3
      default: return 0
    }
  }

  // Handle stepper navigation
  const handleStepperClick = (stepIndex: number) => {
    const isFreeEvent = calculateTotal() <= 0
    const stepperSteps = getStepperSteps()
    const targetStepId = stepperSteps[stepIndex]?.id

    // Only allow navigation to previous steps or current step
    if (stepIndex <= getCurrentStepIndex()) {
      switch (targetStepId) {
        case "form":
          handleBackToForm()
          break
        case "summary":
          // Only allow if we have registration data
          const savedData = sessionStorage.getItem(`registrationData_${slug}`)
          if (savedData) {
            setCurrentStep("summary")
            router.push(`/events/${slug}/register?step=2`)
          } else {
            toast({
              title: "Registration Required",
              description: "Please complete the registration form first",
              variant: "destructive",
            })
          }
          break
        case "payment":
          // Only allow if we have registration data and total > 0
          const savedPaymentData = sessionStorage.getItem(`registrationData_${slug}`)
          if (savedPaymentData && !isFreeEvent) {
            setCurrentStep("payment")
            router.push(`/events/${slug}/register?step=3`)
          } else if (isFreeEvent) {
            toast({
              title: "No Payment Required",
              description: "This event is free, no payment step needed",
              variant: "default",
            })
          } else {
            toast({
              title: "Registration Required",
              description: "Please complete the registration form first",
              variant: "destructive",
            })
          }
          break
        case "success":
          // Don't allow manual navigation to success step
          toast({
            title: "Complete Registration",
            description: "Please complete the registration process to reach this step",
            variant: "default",
          })
          break
      }
    }
  }

  // Load form data from sessionStorage
  const loadFormData = useCallback(() => {
    try {
      const saved = sessionStorage.getItem(`registrationFormData_${slug}`)
      return saved ? JSON.parse(saved) : null
    } catch (error) {
      console.error("Error loading form data:", error)
      return null
    }
  }, [slug])

  // Set current step based on URL parameter and restore data
  useEffect(() => {
    if (step) {
      const stepNumber = parseInt(step)
      switch (stepNumber) {
        case 2:
          setCurrentStep("summary")
          // Restore registration data if available
          const savedRegistrationData = sessionStorage.getItem(`registrationData_${slug}`)
          if (savedRegistrationData) {
            const data = JSON.parse(savedRegistrationData)
            setRegistrationId(data.registrationId)
            setParticipants(data.participants)
            setMainContact(data.mainContact)
          }
          break
        case 3:
          setCurrentStep("payment")
          // Restore registration data if available
          const savedPaymentData = sessionStorage.getItem(`registrationData_${slug}`)
          console.log("Step 3 - Saved payment data:", savedPaymentData)
          if (savedPaymentData) {
            const data = JSON.parse(savedPaymentData)
            console.log("Step 3 - Parsed data:", data)
            setParticipants(data.participants)
            setMainContact(data.mainContact)
          } else {
            console.log("Step 3 - No saved registration data found")
            // Redirect to step 1 if no registration data
            toast({
              title: "Registration Required",
              description: "Please complete the registration form first",
              variant: "destructive",
            })
            router.push(`/events/${slug}/register`)
          }
          break
        case 4:
          setCurrentStep("success")
          // Check for tokens in URL parameters (from payment return)
          const urlParams = new URLSearchParams(window.location.search)
          const receiptTokenParam = urlParams.get('receipt_token')
          const ticketTokenParam = urlParams.get('ticket_token')

          if (receiptTokenParam) {
            setReceiptToken(receiptTokenParam)
          }
          if (ticketTokenParam) {
            setTicketToken(ticketTokenParam)
          }

          // Also check session storage for tokens and registration ID (from payment return)
          try {
            const storedReceiptToken = sessionStorage.getItem('receiptToken')
            const storedTicketToken = sessionStorage.getItem('ticketToken')
            const storedRegistrationId = sessionStorage.getItem('registrationId')

            if (storedReceiptToken && !receiptTokenParam) {
              setReceiptToken(storedReceiptToken)
            }
            if (storedTicketToken && !ticketTokenParam) {
              setTicketToken(storedTicketToken)
            }
            if (storedRegistrationId) {
              setRegistrationId(storedRegistrationId)
              console.log("Registration ID loaded from session storage:", storedRegistrationId)
              // Also fetch participant data for this registration
              fetchParticipantData(storedRegistrationId)
            }

            // If no tokens found, try to fetch them for existing registrations
            if (!receiptTokenParam && !ticketTokenParam && !storedReceiptToken && !storedTicketToken) {
              fetchTokensForEvent(slug)
            }
          } catch (error) {
            console.error("Error loading tokens from session storage:", error)
            // Try to fetch tokens as fallback
            fetchTokensForEvent(slug)
          }
          break
        default:
          setCurrentStep("form")
      }
    } else {
      // No step parameter means first step (form)
      setCurrentStep("form")
      // Load saved form data when returning to step 1
      const savedFormData = loadFormData()
      if (savedFormData) {
        console.log("🔄 Loading saved form data for step 1:", savedFormData)
        setFormData(savedFormData)
      }
    }
  }, [step, slug])

  // Fetch event data
  useEffect(() => {
    if (!slug) {
      setError("No event specified")
      setLoading(false)
      return
    }

    const fetchEvent = async () => {
      setLoading(true)
      try {
        console.log("Fetching event with slug:", slug)
        const eventData = await getEventBySlug(slug)

        if (!eventData) {
          console.error("Event not found for slug:", slug)
          setError("Event not found")
          return
        }

        console.log("Event data loaded successfully:", eventData)
        setEvent(eventData)

        // Store event slug for payment return navigation
        sessionStorage.setItem('currentEventSlug', slug)
      } catch (err: any) {
        console.error("Error fetching event:", err)
        setError(err.message || "Failed to load event details")
      } finally {
        setLoading(false)
      }
    }

    fetchEvent()
  }, [slug, getEventBySlug])

  // Load selected tickets from session storage
  useEffect(() => {
    // Prevent double execution in React Strict Mode
    if (hasLoadedTickets.current) {
      console.log("🔍 Already loaded tickets, skipping...")
      return
    }

    // Don't redirect if we're on step 4 (success page) - allow direct access
    const currentStepNumber = step ? parseInt(step) : 1
    if (currentStepNumber === 4) {
      console.log("🔍 On step 4 (success page), skipping ticket validation...")
      setTicketsLoaded(true)
      hasLoadedTickets.current = true
      return
    }

    console.log("🔍 Registration page: Loading tickets from session storage...")

    const storedTickets = sessionStorage.getItem('selectedTickets')
    console.log("🔍 Stored tickets raw:", storedTickets)

    if (storedTickets) {
      try {
        const tickets = JSON.parse(storedTickets) as SelectedTicket[]
        console.log("🔍 Parsed tickets:", tickets)

        if (tickets.length > 0) {
          console.log("✅ Valid tickets found, setting state...")
          setSelectedTickets(tickets)
          setTicketsLoaded(true)
          hasLoadedTickets.current = true
          // Don't remove from session storage immediately - wait until form is submitted
          console.log("✅ Tickets loaded successfully!")
        } else {
          console.log("❌ Empty tickets array, redirecting back...")
          // Empty tickets array, redirect back
          router.push(`/events/${slug}`)
        }
      } catch (error) {
        console.error('❌ Error parsing selected tickets:', error)
        // Redirect back to event page if no valid tickets
        router.push(`/events/${slug}`)
      }
    } else {
      console.log("❌ No tickets in session storage, redirecting back...")
      // No tickets selected, redirect back to event page
      router.push(`/events/${slug}`)
    }
  }, [slug, router, step])

  // Fetch payment gateways from API
  const fetchPaymentGateways = async () => {
    setPaymentGatewaysLoading(true)
    try {
      console.log("Fetching payment gateways...")
      const response = await fetch('/api/payment-gateways/public')

      if (!response.ok) {
        throw new Error('Failed to fetch payment gateways')
      }

      const data = await response.json()

      if (data.success && data.gateways) {
        console.log(`Found ${data.gateways.length} enabled payment gateways`)
        setPaymentGateways(data.gateways)

        // Auto-select the first gateway if available
        if (data.gateways.length > 0 && !selectedGateway) {
          setSelectedGateway(data.gateways[0].id)
        }
      } else {
        console.warn("No payment gateways found")
        setPaymentGateways([])
      }
    } catch (error) {
      console.error("Error fetching payment gateways:", error)
      toast({
        title: "Error",
        description: "Failed to load payment methods. Please try again.",
        variant: "destructive",
      })
      setPaymentGateways([])
    } finally {
      setPaymentGatewaysLoading(false)
    }
  }

  // Fetch payment gateways when reaching payment step
  useEffect(() => {
    if (currentStep === "payment") {
      fetchPaymentGateways()
    }
  }, [currentStep])

  // Save form data to sessionStorage
  const saveFormData = (data: any) => {
    try {
      if (data === null) {
        // Clear form data
        sessionStorage.removeItem(`registrationFormData_${slug}`)
      } else {
        sessionStorage.setItem(`registrationFormData_${slug}`, JSON.stringify(data))
      }
    } catch (error) {
      console.error("Error saving form data:", error)
    }
  }

  // Clear form data from sessionStorage
  const clearFormData = () => {
    try {
      sessionStorage.removeItem(`registrationFormData_${slug}`)
    } catch (error) {
      console.error("Error clearing form data:", error)
    }
  }

  const handleRegistrationSuccess = (regParticipants: ParticipantType[], regMainContact: any, mainContactIndex: number) => {
    // Don't create registration yet, just store the data for payment
    setParticipants(regParticipants)
    setMainContact(regMainContact)
    setCurrentStep("summary")

    // Save registration data for potential back navigation
    const registrationData = {
      participants: regParticipants,
      mainContact: regMainContact,
      mainContactIndex: mainContactIndex
    }
    sessionStorage.setItem(`registrationData_${slug}`, JSON.stringify(registrationData))

    // Navigate to step 2 (summary)
    router.push(`/events/${slug}/register?step=2`)
  }

  const handleProceedToPayment = () => {
    const total = calculateTotal()
    if (total <= 0) {
      // If event is free, auto-generate payment details and skip to success
      setPaymentDetails({
        transaction_id: "FREE-" + Math.random().toString(36).substring(2, 10).toUpperCase(),
        amount: 0,
        currency: "MYR",
        payment_date: new Date().toISOString(),
        gateway: "Free Registration",
      })
      setPaymentStatus("success")
      setCurrentStep("success")
      router.push(`/events/${slug}/register?step=4`)
      return
    }
    setCurrentStep("payment")
    // Navigate to step 3 (payment)
    router.push(`/events/${slug}/register?step=3`)
  }

  const handleBackToForm = () => {
    setCurrentStep("form")
    router.push(`/events/${slug}/register`)
  }

  const handleBackToSummary = () => {
    setCurrentStep("summary")
    router.push(`/events/${slug}/register?step=2`)
  }

  const handleProcessPayment = async () => {
    if (!selectedGateway) {
      toast({
        title: "Error",
        description: "Please select a payment method",
        variant: "destructive",
      })
      return
    }

    if (!participants || participants.length === 0 || !mainContact) {
      toast({
        title: "Error",
        description: "No registration data found. Please complete the registration form first.",
        variant: "destructive",
      })
      router.push(`/events/${slug}/register`)
      return
    }

    if (!event) {
      toast({
        title: "Error",
        description: "Event data not loaded. Please refresh the page.",
        variant: "destructive",
      })
      return
    }

    // No authentication required for public registration

    setPaymentStatus("processing")

    try {
      // Calculate total amount
      const totalAmount = calculateTotal()

      // No authentication required for public registration

      // Get saved registration data
      const savedData = sessionStorage.getItem(`registrationData_${slug}`)
      const registrationData = savedData ? JSON.parse(savedData) : null

      if (!registrationData) {
        throw new Error('Registration data not found')
      }

      // Call the new public payment API that doesn't require authentication
      const response = await fetch('/api/payments/create-public', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          event_id: event.id,
          participants: participants,
          main_contact: mainContact,
          main_contact_index: registrationData.mainContactIndex,
          selected_tickets: selectedTickets,
          amount: totalAmount,
          currency: 'MYR',
          description: `Registration for ${event.title}`,
          gateway_id: selectedGateway,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create payment')
      }

      const result = await response.json()

      if (result.success && result.payment_url) {
        // Redirect to payment gateway
        console.log("Redirecting to payment URL:", result.payment_url)
        window.location.href = result.payment_url
      } else {
        throw new Error(result.error || 'Failed to create payment')
      }

    } catch (error) {
      console.error("Payment error:", error)
      setPaymentStatus("failed")
      toast({
        title: "Payment Failed",
        description: error instanceof Error ? error.message : "There was an error processing your payment",
        variant: "destructive",
      })
    }
  }

  const handleCancel = () => {
    router.push(`/events/${slug}`)
  }

  const handleViewPublicReceipt = async () => {
    if (!receiptToken) {
      toast({
        title: "Receipt not available",
        description: "Receipt token not found. Please try again later.",
        variant: "destructive",
      })
      return
    }

    try {
      // Open public receipt in new tab for printing as PDF (no authentication required)
      const receiptUrl = `/api/receipts/public?token=${receiptToken}`
      window.open(receiptUrl, '_blank')

      toast({
        title: "Receipt opened",
        description: "Your receipt has been opened in a new tab. Use Ctrl+P to print as PDF.",
      })
    } catch (error) {
      console.error("Error opening receipt:", error)
      toast({
        title: "Failed to open receipt",
        description: "Failed to open receipt. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleViewPublicTickets = async () => {
    if (!ticketToken) {
      toast({
        title: "Tickets not available",
        description: "Ticket token not found. Please try again later.",
        variant: "destructive",
      })
      return
    }

    try {
      // Open public ticket view in new tab (no authentication required)
      const ticketUrl = `/api/tickets/public?token=${ticketToken}&type=pdf`
      window.open(ticketUrl, '_blank')

      toast({
        title: "Ticket opened",
        description: "Your ticket has been opened in a new tab. Use Ctrl+P to print as PDF.",
      })
    } catch (error) {
      console.error("Error opening ticket:", error)
      toast({
        title: "Failed to open ticket",
        description: "Failed to open ticket. Please try again.",
        variant: "destructive",
      })
    }
  }

  // Fetch participant data for a registration
  const fetchParticipantData = async (registrationId: string) => {
    try {
      console.log("Fetching participant data for registration:", registrationId)

      const response = await fetch(`/api/registrations/${registrationId}/participants`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        console.log("No participant data available for this registration")
        return
      }

      const result = await response.json()

      if (result.success && result.participants) {
        console.log("Participant data received:", result.participants)
        console.log("Main contact received:", result.main_contact)
        setParticipants(result.participants)
        if (result.main_contact) {
          setMainContact(result.main_contact)
        }
        console.log("Participant data fetched successfully - participants count:", result.participants.length)
      } else {
        console.log("No participant data in response:", result)
      }
    } catch (error) {
      console.error("Error fetching participant data:", error)
      // Don't show error toast as this is a fallback mechanism
    }
  }

  // Fetch tokens for existing registrations when accessing step 4 directly
  const fetchTokensForEvent = async (eventSlug: string) => {
    try {
      console.log("Fetching tokens for event:", eventSlug)

      // Call API to get tokens for the most recent successful registration for this event
      const response = await fetch(`/api/events/${eventSlug}/registration-tokens`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        console.log("No tokens available for this event")
        return
      }

      const result = await response.json()

      if (result.success) {
        if (result.receipt_token) {
          setReceiptToken(result.receipt_token)
          sessionStorage.setItem('receiptToken', result.receipt_token)
        }
        if (result.ticket_token) {
          setTicketToken(result.ticket_token)
          sessionStorage.setItem('ticketToken', result.ticket_token)
        }
        if (result.registration_id) {
          setRegistrationId(result.registration_id)
          console.log("Registration ID set:", result.registration_id)

          // Also fetch participant data for this registration
          fetchParticipantData(result.registration_id)
        }
        console.log("Tokens and registration data fetched successfully")
      }
    } catch (error) {
      console.error("Error fetching tokens:", error)
      // Don't show error toast as this is a fallback mechanism
    }
  }

  // Calculate total amount based on selected tickets
  const calculateTotal = () => {
    return selectedTickets.reduce((total, ticket) => {
      return total + (ticket.ticketType.price * ticket.quantity)
    }, 0)
  }

  // Get total number of tickets
  const getTotalTickets = () => {
    // If we have selectedTickets (normal flow), use them
    if (selectedTickets.length > 0) {
      return selectedTickets.reduce((total, ticket) => total + ticket.quantity, 0)
    }
    // If we don't have selectedTickets but have participants (payment return flow), use participant count
    if (participants.length > 0) {
      return participants.length
    }
    // Default to 0
    return 0
  }

  // Registration is public - no authentication required

  // Show loading state
  if (loading || !ticketsLoaded || !event) {
    return (
      <RegistrationLayout
        event={{ title: "Loading...", start_date: new Date().toISOString(), end_date: new Date().toISOString(), location: "Loading..." } as EventType}
        steps={[]}
        currentStepIndex={0}
        showEventDetails={false}
      >
        <div className="flex flex-col items-center justify-center min-h-[50vh]">
          <div className="w-16 h-16 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-lg text-gray-600">Loading event details...</p>
        </div>
      </RegistrationLayout>
    )
  }

  // Show error state
  if (error || !event) {
    return (
      <RegistrationLayout
        event={{ title: "Error", start_date: new Date().toISOString(), end_date: new Date().toISOString(), location: "Error" } as EventType}
        steps={[]}
        currentStepIndex={0}
        showEventDetails={false}
      >
        <div className="flex flex-col items-center justify-center min-h-[50vh] text-center">
          <div className="text-red-500 text-5xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold mb-4 text-gray-900">{error || "Event not found"}</h1>
          <p className="text-gray-600 mb-6 max-w-md">
            The event you're looking for doesn't exist or has been removed.
          </p>
          <button
            onClick={() => router.push("/")}
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Return to Home
          </button>
        </div>
      </RegistrationLayout>
    )
  }

  return (
    <RegistrationLayout
      event={event}
      steps={getStepperSteps()}
      currentStepIndex={getCurrentStepIndex()}
      onStepClick={handleStepperClick}
      totalAmount={calculateTotal()}
      totalTickets={getTotalTickets()}
    >
      {currentStep === "form" && (
        <RegistrationStep
          event={event}
          userId={user?.id || null}
          selectedTickets={selectedTickets}
          onSuccess={handleRegistrationSuccess}
          savedFormData={formData}
          onFormDataChange={saveFormData}
        />
      )}

      {currentStep === "summary" && (
        <ReviewStep
          event={event}
          participants={participants}
          mainContact={mainContact}
          selectedTickets={selectedTickets}
          totalAmount={calculateTotal()}
          onBackToForm={handleBackToForm}
          onProceedToPayment={handleProceedToPayment}
        />
      )}

      {currentStep === "payment" && (
        <PaymentStep
          totalAmount={calculateTotal()}
          paymentGateways={paymentGateways}
          selectedGateway={selectedGateway}
          onGatewaySelect={setSelectedGateway}
          paymentStatus={paymentStatus}
          paymentGatewaysLoading={paymentGatewaysLoading}
          onProcessPayment={handleProcessPayment}
          onBackToSummary={handleBackToSummary}
        />
      )}

      {currentStep === "success" && (
        <SuccessStep
          event={event}
          registrationId={registrationId}
          participants={participants}
          mainContact={mainContact}
          paymentDetails={paymentDetails}
          totalTickets={getTotalTickets()}
          receiptToken={receiptToken}
          ticketToken={ticketToken}
          onViewReceipt={handleViewPublicReceipt}
          onViewTickets={handleViewPublicTickets}
          user={user}
        />
      )}
    </RegistrationLayout>
  )
}
