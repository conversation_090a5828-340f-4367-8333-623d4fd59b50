# Team-Based QR Attendance Scanner

## Overview

The team-based QR attendance scanner allows event managers to create teams with dedicated access to a public QR scanner page. This enables multiple people to scan attendee QR codes without requiring dashboard access.

## Features

### 1. Team Management
- **Create Teams**: Event managers can create teams with custom names
- **Access Tokens**: Each team gets a unique access token for authentication
- **Permissions**: Granular permissions for QR scanning and attendance viewing
- **Expiration**: Optional expiration dates for temporary team access
- **Activity Tracking**: Monitor when teams last used the scanner

### 2. Public QR Scanner
- **Dedicated URL**: `/[event-slug]/qrscan` - accessible without dashboard login
- **Team Authentication**: Teams authenticate using their access token
- **Real-time Stats**: Live attendance count and event information
- **Same QR Technology**: Uses the same secure QR verification as dashboard scanner

### 3. Security Features
- **Token-based Access**: Unique access tokens for each team
- **Event-specific**: Teams only have access to their assigned event
- **Expiration Support**: Tokens can be set to expire automatically
- **Activity Logging**: All team activities are logged for audit purposes

## Database Schema

### Event Teams Table (`event_teams`)
```sql
CREATE TABLE event_teams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id UUID REFERENCES events(id) ON DELETE CASCADE NOT NULL,
  team_name VARCHAR(255) NOT NULL,
  access_token VARCHAR(255) UNIQUE NOT NULL,
  permissions JSONB DEFAULT '{"can_scan_qr": true, "can_view_attendance": true}',
  created_by UUID REFERENCES users(id) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  expires_at TIMESTAMP WITH TIME ZONE,
  last_used_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## API Endpoints

### Team Management
- `GET /api/events/[slug]/teams` - List teams for an event
- `POST /api/events/[slug]/teams` - Create a new team
- `PUT /api/events/[slug]/teams/[teamId]` - Update team settings
- `DELETE /api/events/[slug]/teams/[teamId]` - Delete a team

### Team Authentication
- `POST /api/teams/auth` - Authenticate team with access token
- `GET /api/teams/auth?token=xxx&slug=xxx` - Validate token

### Public Data
- `GET /api/events/[slug]/attendance/count` - Get attendance count (public)

## User Flow

### For Event Managers
1. Navigate to `/dashboard/events/[slug]/attendance`
2. Use the "Team Access Management" section
3. Click "Add Team" to create a new team
4. Configure team name, permissions, and optional expiration
5. Share the access token and QR scanner URL with team members

### For Team Members
1. Navigate to `/[event-slug]/qrscan`
2. Enter the access token provided by the event manager
3. Start scanning attendee QR codes
4. View real-time attendance statistics

## Access Control

### Dashboard Access (Event Managers)
- Event managers can manage teams for their events
- Admin and event_admin roles can manage all teams
- RLS policies ensure proper access control

### Public Scanner Access (Teams)
- Teams authenticate using access tokens
- Access is limited to the specific event
- Tokens can be disabled or expired by event managers

## Implementation Files

### Frontend Components
- `app/[slug]/qrscan/page.tsx` - Public QR scanner page
- `components/team-management.tsx` - Team management interface
- `app/dashboard/events/[slug]/attendance/page.tsx` - Updated attendance page

### Backend APIs
- `app/api/events/[slug]/teams/route.ts` - Team CRUD operations
- `app/api/events/[slug]/teams/[teamId]/route.ts` - Individual team management
- `app/api/teams/auth/route.ts` - Team authentication
- `app/api/events/[slug]/attendance/count/route.ts` - Public attendance count

### Database
- `supabase/migrations/20241220000000_create_event_teams.sql` - Database migration
- `docs/database-schema.md` - Updated schema documentation

## Security Considerations

1. **Access Tokens**: Unique, unguessable tokens for each team
2. **Event Isolation**: Teams can only access their assigned event
3. **Permission Control**: Granular permissions for different team capabilities
4. **Expiration**: Automatic token expiration for temporary access
5. **Activity Logging**: All team activities are logged for audit purposes
6. **RLS Policies**: Database-level security ensures proper access control

## Usage Examples

### Creating a Team
```javascript
const response = await fetch(`/api/events/${eventSlug}/teams`, {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    team_name: "Registration Volunteers",
    permissions: {
      can_scan_qr: true,
      can_view_attendance: true
    },
    expires_at: "2024-12-31T23:59:59Z" // Optional
  })
})
```

### Team Authentication
```javascript
const response = await fetch("/api/teams/auth", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    access_token: "team_abc123_1703001234567",
    event_slug: "my-event-2024"
  })
})
```

## Benefits

1. **Scalability**: Multiple people can scan QR codes simultaneously
2. **Security**: No need to share dashboard credentials
3. **Simplicity**: Easy-to-use public interface for team members
4. **Control**: Event managers maintain full control over team access
5. **Audit Trail**: Complete logging of all team activities
6. **Flexibility**: Configurable permissions and expiration dates
