import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"

export async function GET(request: NextRequest) {
  try {
    const supabase = createClient()

    // Fetch subscription plans from database
    const { data: plans, error } = await supabase
      .from('subscription_plans')
      .select('*')
      .eq('is_active', true)
      .order('price', { ascending: true })

    if (error) {
      console.error("Error fetching subscription plans:", error)
      return NextResponse.json(
        {
          success: false,
          error: "Failed to fetch subscription plans",
        },
        { status: 500 }
      )
    }

    // Transform the data to match the expected format
    const transformedPlans = plans.map(plan => ({
      id: plan.id,
      name: plan.name,
      price: parseFloat(plan.price.toString()),
      description: plan.description,
      features: Array.isArray(plan.features) ? plan.features : [],
      max_events: plan.max_events,
      max_attendees_per_event: plan.max_attendees_per_event,
      is_popular: plan.is_popular,
      certificates_enabled: plan.certificates_enabled,
      attendance_enabled: plan.attendance_enabled,
      webhooks_enabled: plan.webhooks_enabled,
      analytics_enabled: plan.analytics_enabled,
      reports_enabled: plan.reports_enabled,
    }))

    return NextResponse.json({
      success: true,
      plans: transformedPlans,
    })
  } catch (error: any) {
    console.error("Error fetching subscription plans:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch subscription plans",
      },
      { status: 500 }
    )
  }
}
