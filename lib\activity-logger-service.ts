import { supabase } from "@/lib/supabase"
import { ActivityCategory, ActivityLogParams } from "@/utils/activity-logger"

/**
 * Service for logging user activities across the application
 */
export class ActivityLoggerService {
  /**
   * Log an authentication activity
   *
   * @param userId User ID
   * @param action Action performed (login, logout, register, etc.)
   * @param details Additional details about the action
   * @param ipAddress IP address of the user
   * @param userAgent User agent of the browser
   */
  static async logAuthActivity(
    userId: string | undefined,
    action: string,
    details: Record<string, any> = {},
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    await this.logActivity({
      userId,
      action,
      entityType: 'auth',
      category: ActivityCategory.AUTH,
      details,
      ipAddress,
      userAgent,
    })
  }

  /**
   * Log a user-related activity
   *
   * @param userId User ID
   * @param action Action performed (update_profile, change_password, etc.)
   * @param targetUserId ID of the user being acted upon (if different from userId)
   * @param details Additional details about the action
   */
  static async logUserActivity(
    userId: string,
    action: string,
    targetUserId?: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logActivity({
      userId,
      action,
      entityType: 'user',
      entityId: targetUserId || userId,
      category: ActivityCategory.USER,
      details,
    })
  }

  /**
   * Log an event-related activity
   *
   * @param userId User ID
   * @param action Action performed (create_event, update_event, etc.)
   * @param eventId ID of the event
   * @param details Additional details about the action
   */
  static async logEventActivity(
    userId: string,
    action: string,
    eventId: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logActivity({
      userId,
      action,
      entityType: 'event',
      entityId: eventId, // Keep for backward compatibility
      eventId, // New foreign key field
      category: ActivityCategory.EVENT,
      details,
    })
  }

  /**
   * Log a registration-related activity
   *
   * @param userId User ID
   * @param action Action performed (register_event, cancel_registration, etc.)
   * @param registrationId ID of the registration
   * @param eventId ID of the event
   * @param details Additional details about the action
   */
  static async logRegistrationActivity(
    userId: string,
    action: string,
    registrationId: string,
    eventId: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logActivity({
      userId,
      action,
      entityType: 'registration',
      entityId: registrationId, // Keep for backward compatibility
      registrationId, // New foreign key field
      eventId, // Related event foreign key
      category: ActivityCategory.REGISTRATION,
      details: {
        ...details,
        event_id: eventId, // Keep in details for backward compatibility
      },
    })
  }

  /**
   * Log a payment-related activity
   *
   * @param userId User ID
   * @param action Action performed (process_payment, refund, etc.)
   * @param paymentId ID of the payment
   * @param eventId ID of the event (if applicable)
   * @param registrationId ID of the registration (if applicable)
   * @param details Additional details about the action
   */
  static async logPaymentActivity(
    userId: string,
    action: string,
    paymentId: string,
    eventId?: string,
    registrationId?: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logActivity({
      userId,
      action,
      entityType: 'payment',
      entityId: paymentId, // Keep for backward compatibility
      paymentId, // New foreign key field
      eventId, // Related event foreign key
      registrationId, // Related registration foreign key
      category: ActivityCategory.PAYMENT,
      details: {
        ...details,
        event_id: eventId, // Keep in details for backward compatibility
        registration_id: registrationId,
      },
    })
  }

  /**
   * Log a settings-related activity
   *
   * @param userId User ID
   * @param action Action performed (update_settings, toggle_feature, etc.)
   * @param settingType Type of setting (payment_gateway, subscription, etc.)
   * @param details Additional details about the action
   */
  static async logSettingsActivity(
    userId: string,
    action: string,
    settingType: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logActivity({
      userId,
      action,
      entityType: 'setting',
      category: ActivityCategory.SETTINGS,
      details: {
        ...details,
        setting_type: settingType,
      },
    })
  }

  /**
   * Log an export-related activity
   *
   * @param userId User ID
   * @param action Action performed (export_registrations, export_events, etc.)
   * @param exportType Type of export (csv, pdf, etc.)
   * @param details Additional details about the action
   */
  static async logExportActivity(
    userId: string,
    action: string,
    exportType: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logActivity({
      userId,
      action,
      entityType: 'export',
      category: ActivityCategory.EXPORT,
      details: {
        ...details,
        export_type: exportType,
      },
    })
  }

  /**
   * Log an attendance-related activity
   *
   * @param userId User ID
   * @param action Action performed (mark_attendance, etc.)
   * @param registrationId ID of the registration
   * @param eventId ID of the event
   * @param details Additional details about the action
   */
  static async logAttendanceActivity(
    userId: string,
    action: string,
    registrationId: string,
    eventId: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logActivity({
      userId,
      action,
      entityType: 'attendance',
      entityId: registrationId, // Keep for backward compatibility
      registrationId, // New foreign key field
      eventId, // Related event foreign key
      category: ActivityCategory.ATTENDANCE,
      details: {
        ...details,
        event_id: eventId, // Keep in details for backward compatibility
      },
    })
  }

  /**
   * Log a certificate-related activity
   *
   * @param userId User ID
   * @param action Action performed (generate_certificate, download_certificate, etc.)
   * @param certificateId ID of the certificate
   * @param registrationId ID of the registration
   * @param eventId ID of the event
   * @param details Additional details about the action
   */
  static async logCertificateActivity(
    userId: string,
    action: string,
    certificateId: string,
    registrationId: string,
    eventId: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logActivity({
      userId,
      action,
      entityType: 'certificate',
      entityId: certificateId, // Keep for backward compatibility
      certificateId, // New foreign key field
      registrationId, // Related registration foreign key
      eventId, // Related event foreign key
      category: ActivityCategory.CERTIFICATE,
      details: {
        ...details,
        registration_id: registrationId,
        event_id: eventId,
      },
    })
  }

  /**
   * Log an organization-related activity
   *
   * @param userId User ID
   * @param action Action performed (create_organization, update_organization, etc.)
   * @param organizationId ID of the organization
   * @param details Additional details about the action
   */
  static async logOrganizationActivity(
    userId: string,
    action: string,
    organizationId: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logActivity({
      userId,
      action,
      entityType: 'organization',
      entityId: organizationId, // Keep for backward compatibility
      organizationId, // New foreign key field
      category: ActivityCategory.ORGANIZATION,
      details,
    })
  }

  /**
   * Log a subscription-related activity
   *
   * @param userId User ID
   * @param action Action performed (subscribe, cancel_subscription, etc.)
   * @param subscriptionId ID of the subscription
   * @param details Additional details about the action
   */
  static async logSubscriptionActivity(
    userId: string,
    action: string,
    subscriptionId: string,
    details: Record<string, any> = {}
  ): Promise<void> {
    await this.logActivity({
      userId,
      action,
      entityType: 'subscription',
      entityId: subscriptionId, // Keep for backward compatibility
      subscriptionId, // New foreign key field
      category: ActivityCategory.SUBSCRIPTION,
      details,
    })
  }

  /**
   * Log any activity in the system
   *
   * @param params Activity log parameters
   */
  static async logActivity(params: ActivityLogParams): Promise<void> {
    try {
      const {
        userId,
        action,
        entityType,
        entityId,
        category,
        details = {},
        ipAddress,
        userAgent
      } = params

      await supabase.from("activity_logs").insert([
        {
          user_id: userId,
          action,
          entity_type: entityType,
          entity_id: entityId,
          category,
          details,
          ip_address: ipAddress,
          user_agent: userAgent,
          created_at: new Date().toISOString(),
        },
      ])
    } catch (error) {
      console.error("Error logging activity:", error)
    }
  }
}
