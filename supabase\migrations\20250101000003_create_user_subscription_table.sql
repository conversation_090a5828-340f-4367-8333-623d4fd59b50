-- Create user_subscription table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_subscription (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  subscription_type TEXT NOT NULL DEFAULT 'free',
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  end_date TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true,
  payment_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_user_subscription_user_id ON user_subscription(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscription_active ON user_subscription(is_active);
CREATE INDEX IF NOT EXISTS idx_user_subscription_payment_id ON user_subscription(payment_id);

-- Add subscription_plan column to users table if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'subscription_plan') THEN
    ALTER TABLE users ADD COLUMN subscription_plan TEXT DEFAULT 'free';
  END IF;
END $$;

-- Create subscription_plans table for storing plan definitions
CREATE TABLE IF NOT EXISTS subscription_plans (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  price DECIMAL(10, 2) NOT NULL DEFAULT 0,
  description TEXT,
  features JSONB DEFAULT '[]',
  max_events INTEGER,
  max_attendees_per_event INTEGER,
  is_popular BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default subscription plans
INSERT INTO subscription_plans (id, name, price, description, features, max_events, max_attendees_per_event, is_popular) 
VALUES 
  (
    'plan-free',
    'Free',
    0,
    'Basic features for individuals and small events',
    '["1 event at a time", "Up to 30 attendees per event", "Basic event page", "Email notifications", "Manual attendance tracking"]',
    1,
    30,
    false
  ),
  (
    'plan-basic',
    29,
    'Basic',
    'Essential features for growing event organizers',
    '["5 events at a time", "Up to 100 attendees per event", "Custom event pages", "Email notifications", "QR code check-in", "Basic analytics", "Certificate generation"]',
    5,
    100,
    true
  ),
  (
    'plan-pro',
    79,
    'Professional',
    'Advanced features for professional event managers',
    '["Unlimited events", "Up to 500 attendees per event", "Custom event pages with branding", "Email and SMS notifications", "QR code check-in", "Advanced analytics", "Certificate generation", "Custom certificates", "Priority support"]',
    NULL,
    500,
    false
  ),
  (
    'plan-enterprise',
    199,
    'Enterprise',
    'Complete solution for large organizations',
    '["Unlimited events", "Unlimited attendees", "Custom event pages with branding", "Email and SMS notifications", "QR code check-in", "Advanced analytics and reporting", "Certificate generation", "Custom certificates", "Dedicated support", "API access", "White-label solution"]',
    NULL,
    NULL,
    false
  )
ON CONFLICT (id) DO NOTHING;

-- Create payment_gateway_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS payment_gateway_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  gateway_name TEXT NOT NULL,
  gateway_type TEXT NOT NULL,
  is_enabled BOOLEAN DEFAULT false,
  configuration JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(gateway_name)
);

-- Insert default payment gateway settings
INSERT INTO payment_gateway_settings (gateway_name, gateway_type, is_enabled, configuration)
VALUES 
  ('Billplz', 'billplz', true, '{"api_key": "", "collection_id": "", "x_signature_key": ""}'),
  ('ToyyibPay', 'toyyibpay', true, '{"user_secret_key": "", "category_code": ""}'),
  ('Chip', 'chip', true, '{"brand_id": "", "api_key": ""}'),
  ('Stripe', 'stripe', false, '{"publishable_key": "", "secret_key": "", "webhook_secret": ""}')
ON CONFLICT (gateway_name) DO NOTHING;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS update_user_subscription_updated_at ON user_subscription;
CREATE TRIGGER update_user_subscription_updated_at
  BEFORE UPDATE ON user_subscription
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_subscription_plans_updated_at ON subscription_plans;
CREATE TRIGGER update_subscription_plans_updated_at
  BEFORE UPDATE ON subscription_plans
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_payment_gateway_settings_updated_at ON payment_gateway_settings;
CREATE TRIGGER update_payment_gateway_settings_updated_at
  BEFORE UPDATE ON payment_gateway_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
