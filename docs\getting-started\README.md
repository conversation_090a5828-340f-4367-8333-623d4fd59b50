# Getting Started with mTicket.my

Welcome to mTicket.my! This guide will help you get up and running quickly with our event management platform.

## What is mTicket.my?

mTicket.my is a comprehensive event management platform that enables you to:
- Create and manage events
- Handle registrations and payments
- Generate digital certificates
- Track attendance with QR codes
- Manage teams and permissions

## Quick Start Options

### 🚀 For Event Organizers
1. [Sign up for an account](../guides/user-guide.md#registration)
2. [Create your first event](../features/event-management.md#creating-events)
3. [Set up payment processing](../architecture/payment-system.md)
4. [Configure certificates](../features/certificate-system.md)

### 👥 For Attendees
1. Browse events on the homepage
2. Select tickets and register
3. Make payment
4. Receive digital tickets via email
5. Use QR codes for check-in

### 🔧 For Developers
1. [Set up development environment](./installation.md)
2. [Review architecture](../architecture/)
3. [Explore API documentation](../api/)
4. [Check developer guide](../guides/developer-guide.md)

### 🛡️ For Administrators
1. [Review admin guide](../guides/admin-guide.md)
2. [Configure security settings](../security/)
3. [Set up deployment](../deployment/)

## Next Steps

- **New Users**: Start with the [Quick Start Guide](./quick-start.md)
- **Developers**: Check out the [Installation Guide](./installation.md)
- **Need Help?**: Browse our [User Guide](../guides/user-guide.md)

## Support

If you need assistance:
- Check our comprehensive [documentation](../README.md)
- Review [frequently asked questions](../guides/user-guide.md#faq)
- Contact our support team

---

**Ready to get started?** Choose your path above and dive in!
