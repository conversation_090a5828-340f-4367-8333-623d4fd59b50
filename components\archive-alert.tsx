"use client"

import { useState } from "react"
import { AlertTriangle } from "lucide-react"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { useRouter } from "next/navigation"
import { logActivity } from "@/utils/activity-logger"

interface ArchiveAlertProps {
  eventId: string
  eventName: string
}

export function ArchiveAlert({ eventId, eventName }: ArchiveAlertProps) {
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const supabase = createClientComponentClient()

  const handleArchive = async () => {
    try {
      setIsLoading(true)

      // Update the event status to 'archived'
      const { error } = await supabase.from("events").update({ status: "archived" }).eq("id", eventId)

      if (error) throw error

      // Log the activity
      await logActivity({
        action: "archive_event",
        entity_type: "event",
        entity_id: eventId,
        description: `Archived event: ${eventName}`,
      })

      router.refresh()
    } catch (error) {
      console.error("Error archiving event:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AlertDialog>
      <AlertDialogTrigger asChild>
        <Button variant="outline" size="sm">
          <AlertTriangle className="mr-2 h-4 w-4" />
          Archive
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Archive Event</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to archive &quot;{eventName}&quot;? Archived events will no longer be visible to the
            public but will still be accessible in your dashboard.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={handleArchive} disabled={isLoading} className="bg-amber-600 hover:bg-amber-700">
            {isLoading ? "Archiving..." : "Archive Event"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
