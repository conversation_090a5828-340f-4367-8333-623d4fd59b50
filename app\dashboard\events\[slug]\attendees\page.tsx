"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { ArrowLeft, Download, Mail, Search, UserPlus } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"

export default function EventAttendeesPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [event, setEvent] = useState<any>(null)
  const [attendees, setAttendees] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const eventSlug = params.slug as string

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      try {
        // Fetch event details by slug
        const { data: eventData, error: eventError } = await supabase
          .from("events")
          .select("*")
          .eq("slug", eventSlug)
          .single()

        if (eventError) {
          if (eventError.code === "PGRST116") {
            toast({
              title: "Event not found",
              description: "The event you're looking for doesn't exist or has been removed.",
              variant: "destructive",
            })
            router.push("/dashboard/events")
            return
          }
          throw eventError
        }

        setEvent(eventData)

        // Fetch attendees using the event ID
        const { data: attendeesData, error: attendeesError } = await supabase
          .from("registrations")
          .select("*")
          .eq("event_id", eventData.id)
          .order("created_at", { ascending: false })

        if (attendeesError) throw attendeesError

        if (attendeesData && attendeesData.length > 0) {
          setAttendees(attendeesData)
        } else {
          // Mock data for development
          setAttendees([
            {
              id: "1",
              event_id: eventData.id,
              user_id: "user1",
              full_name: "John Doe",
              email: "<EMAIL>",
              phone: "+1234567890",
              ticket_type: "Regular",
              payment_status: "paid",
              attendance_status: "confirmed",
              created_at: new Date("2023-11-05").toISOString(),
            },
            {
              id: "2",
              event_id: eventData.id,
              user_id: "user2",
              full_name: "Jane Smith",
              email: "<EMAIL>",
              phone: "+1987654321",
              ticket_type: "VIP",
              payment_status: "paid",
              attendance_status: "attended",
              created_at: new Date("2023-11-06").toISOString(),
            },
            {
              id: "3",
              event_id: eventData.id,
              user_id: "user3",
              full_name: "Bob Johnson",
              email: "<EMAIL>",
              phone: "+1122334455",
              ticket_type: "Regular",
              payment_status: "pending",
              attendance_status: "confirmed",
              created_at: new Date("2023-11-07").toISOString(),
            },
            {
              id: "4",
              event_id: eventData.id,
              user_id: "user4",
              full_name: "Alice Brown",
              email: "<EMAIL>",
              phone: "+1555666777",
              ticket_type: "Regular",
              payment_status: "paid",
              attendance_status: "no_show",
              created_at: new Date("2023-11-08").toISOString(),
            },
            {
              id: "5",
              event_id: eventData.id,
              user_id: "user5",
              full_name: "Charlie Wilson",
              email: "<EMAIL>",
              phone: "+1999888777",
              ticket_type: "VIP",
              payment_status: "paid",
              attendance_status: "confirmed",
              created_at: new Date("2023-11-09").toISOString(),
            },
          ])
        }
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: "Failed to fetch attendees data. Please try again later.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    if (eventSlug) {
      fetchData()
    }
  }, [eventSlug, router, toast])

  const handleExportCSV = () => {
    try {
      if (!attendees.length) return

      // Create CSV content
      const headers = [
        "Full Name",
        "Email",
        "Phone",
        "Ticket Type",
        "Payment Status",
        "Attendance Status",
        "Registration Date",
      ]
      const csvRows = [headers]

      attendees.forEach((attendee) => {
        const row = [
          attendee.full_name,
          attendee.email,
          attendee.phone || "N/A",
          attendee.ticket_type,
          attendee.payment_status,
          attendee.attendance_status,
          new Date(attendee.created_at).toLocaleDateString(),
        ]
        csvRows.push(row)
      })

      const csvContent = csvRows.map((row) => row.join(",")).join("\n")

      // Create and download the CSV file
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
      const url = URL.createObjectURL(blob)
      const link = document.createElement("a")
      link.setAttribute("href", url)
      link.setAttribute("download", `${event.title}-attendees.csv`)
      link.style.visibility = "hidden"
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast({
        title: "Success",
        description: "Attendees list exported successfully",
      })
    } catch (error) {
      console.error("Error exporting CSV:", error)
      toast({
        title: "Error",
        description: "Failed to export attendees list",
        variant: "destructive",
      })
    }
  }

  const filteredAttendees = attendees.filter(
    (attendee) =>
      attendee.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      attendee.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      attendee.phone?.includes(searchQuery),
  )

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="mb-6 flex items-center justify-between">
        <Link href={`/dashboard/events/${eventSlug}`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Event
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">{loading ? "Loading..." : `Attendees: ${event?.title}`}</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Manage Attendees</CardTitle>
          <CardDescription>View and manage attendees for this event</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-6 flex flex-col sm:flex-row items-center gap-4">
            <div className="relative flex-1 w-full">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search attendees..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2 w-full sm:w-auto">
              <Button variant="outline" onClick={handleExportCSV} disabled={!attendees.length}>
                <Download className="mr-2 h-4 w-4" />
                Export CSV
              </Button>
              <Button>
                <UserPlus className="mr-2 h-4 w-4" />
                Add Attendee
              </Button>
            </div>
          </div>

          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center gap-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-40" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                  <div className="ml-auto">
                    <Skeleton className="h-8 w-20" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Attendee</TableHead>
                    <TableHead>Ticket Type</TableHead>
                    <TableHead>Payment Status</TableHead>
                    <TableHead>Attendance</TableHead>
                    <TableHead>Registration Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAttendees.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="h-24 text-center">
                        No attendees found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredAttendees.map((attendee) => (
                      <TableRow key={attendee.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="font-medium">{attendee.full_name}</div>
                          </div>
                          <div className="text-sm text-muted-foreground">{attendee.email}</div>
                        </TableCell>
                        <TableCell>{attendee.ticket_type}</TableCell>
                        <TableCell>
                          <PaymentStatusBadge status={attendee.payment_status} />
                        </TableCell>
                        <TableCell>
                          <AttendanceStatusBadge status={attendee.attendance_status} />
                        </TableCell>
                        <TableCell>{new Date(attendee.created_at).toLocaleDateString()}</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            <Mail className="h-4 w-4" />
                            <span className="sr-only">Email</span>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

function PaymentStatusBadge({ status }: { status: string }) {
  switch (status) {
    case "paid":
      return <Badge variant="success">Paid</Badge>
    case "pending":
      return <Badge variant="warning">Pending</Badge>
    case "failed":
      return <Badge variant="destructive">Failed</Badge>
    case "refunded":
      return <Badge variant="outline">Refunded</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

function AttendanceStatusBadge({ status }: { status: string }) {
  switch (status) {
    case "confirmed":
      return <Badge variant="secondary">Confirmed</Badge>
    case "attended":
      return <Badge variant="success">Attended</Badge>
    case "no_show":
      return <Badge variant="destructive">No Show</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}
