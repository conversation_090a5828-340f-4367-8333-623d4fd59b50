"use client"

import type React from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"

export function NotificationsTab() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Notification Preferences</CardTitle>
        <CardDescription>Manage how you receive notifications</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Email Notifications</p>
              <p className="text-sm text-muted-foreground">Receive email notifications for important updates</p>
            </div>
            <div className="flex items-center space-x-2">
              <Label htmlFor="email-notifications" className="sr-only">
                Email Notifications
              </Label>
              <input
                type="checkbox"
                id="email-notifications"
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                defaultChecked
              />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">SMS Notifications</p>
              <p className="text-sm text-muted-foreground">Receive SMS notifications for important updates</p>
            </div>
            <div className="flex items-center space-x-2">
              <Label htmlFor="sms-notifications" className="sr-only">
                SMS Notifications
              </Label>
              <input
                type="checkbox"
                id="sms-notifications"
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
            </div>
          </div>
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Marketing Emails</p>
              <p className="text-sm text-muted-foreground">
                Receive marketing emails about new features and offers
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Label htmlFor="marketing-emails" className="sr-only">
                Marketing Emails
              </Label>
              <input
                type="checkbox"
                id="marketing-emails"
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                defaultChecked
              />
            </div>
          </div>
        </div>
        <div className="mt-6 flex justify-end">
          <Button>Save Preferences</Button>
        </div>
      </CardContent>
    </Card>
  )
}
