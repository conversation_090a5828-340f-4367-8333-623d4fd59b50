-- Create event_teams table for team-based QR attendance scanning
CREATE TABLE event_teams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id UUID REFERENCES events(id) ON DELETE CASCADE NOT NULL,
  team_name VA<PERSON>HA<PERSON>(255) NOT NULL,
  location VARCHAR(255), -- Location/gate where team operates (e.g., "Gate A", "Gate B")
  access_token VARCHAR(255) UNIQUE NOT NULL,
  permissions JSONB DEFAULT '{"can_scan_qr": true, "can_view_attendance": true}',
  created_by UUID REFERENCES users(id) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  expires_at TIMESTAMP WITH TIME ZONE,
  last_used_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_event_teams_event_id ON event_teams(event_id);
CREATE INDEX idx_event_teams_access_token ON event_teams(access_token);
CREATE INDEX idx_event_teams_created_by ON event_teams(created_by);

-- Enable Row Level Security
ALTER TABLE event_teams ENABLE ROW LEVEL SECURITY;

-- RLS Policies for event_teams
-- 1. Event managers can view teams for their events
CREATE POLICY "Event managers can view teams for their events" ON event_teams
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM events
      WHERE events.id = event_teams.event_id
      AND events.event_manager_id = auth.uid()
    )
    OR
    EXISTS (
      SELECT 1 FROM users
      JOIN user_roles ON users.role_id = user_roles.id
      WHERE users.id = auth.uid()
      AND user_roles.role_name IN ('admin', 'event_admin')
    )
  );

-- 2. Event managers can create teams for their events
CREATE POLICY "Event managers can create teams for their events" ON event_teams
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM events
      WHERE events.id = event_teams.event_id
      AND events.event_manager_id = auth.uid()
    )
    OR
    EXISTS (
      SELECT 1 FROM users
      JOIN user_roles ON users.role_id = user_roles.id
      WHERE users.id = auth.uid()
      AND user_roles.role_name IN ('admin', 'event_admin')
    )
  );

-- 3. Event managers can update/delete teams for their events
CREATE POLICY "Event managers can update teams for their events" ON event_teams
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM events
      WHERE events.id = event_teams.event_id
      AND events.event_manager_id = auth.uid()
    )
    OR
    EXISTS (
      SELECT 1 FROM users
      JOIN user_roles ON users.role_id = user_roles.id
      WHERE users.id = auth.uid()
      AND user_roles.role_name IN ('admin', 'event_admin')
    )
  );

CREATE POLICY "Event managers can delete teams for their events" ON event_teams
  FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM events
      WHERE events.id = event_teams.event_id
      AND events.event_manager_id = auth.uid()
    )
    OR
    EXISTS (
      SELECT 1 FROM users
      JOIN user_roles ON users.role_id = user_roles.id
      WHERE users.id = auth.uid()
      AND user_roles.role_name IN ('admin', 'event_admin')
    )
  );

-- Add comment to table
COMMENT ON TABLE event_teams IS 'Teams with access to event QR attendance scanning';
COMMENT ON COLUMN event_teams.location IS 'Location/gate where team operates for check-in tracking';
COMMENT ON COLUMN event_teams.access_token IS 'Unique token for team access to QR scanner';
COMMENT ON COLUMN event_teams.permissions IS 'JSON object defining team permissions';
COMMENT ON COLUMN event_teams.expires_at IS 'Optional expiration date for temporary team access';
