"use client"

import { useState, useEffect } from "react"
import dynamic from 'next/dynamic'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Skeleton } from "@/components/ui/skeleton"

// Dynamically import the Chart component with no SSR
const Chart = dynamic(() => import('@/components/analytics-chart').then(mod => mod.Chart), {
  ssr: false,
  loading: () => <Skeleton className="w-full h-full" />
})

export default function AnalyticsPage() {
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState("30days")
  const [eventData, setEventData] = useState<any>(null)
  const [registrationData, setRegistrationData] = useState<any>(null)
  const [revenueData, setRevenueData] = useState<any>(null)
  const [attendanceData, setAttendanceData] = useState<any>(null)

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      try {
        // In a real implementation, you would fetch actual data from your database
        // For now, we'll use mock data

        // Mock data for events over time
        const eventLabels = generateDateLabels(timeRange)
        setEventData({
          labels: eventLabels,
          datasets: [
            {
              label: "Events Created",
              data: generateRandomData(eventLabels.length, 0, 5),
              borderColor: "rgb(99, 102, 241)",
              backgroundColor: "rgba(99, 102, 241, 0.5)",
              tension: 0.3,
            },
          ],
        })

        // Mock data for registrations over time
        const registrationLabels = generateDateLabels(timeRange)
        setRegistrationData({
          labels: registrationLabels,
          datasets: [
            {
              label: "Registrations",
              data: generateRandomData(registrationLabels.length, 5, 30),
              backgroundColor: "rgba(16, 185, 129, 0.7)",
              borderColor: "rgb(16, 185, 129)",
              borderWidth: 1,
            },
          ],
        })

        // Mock data for revenue over time
        const revenueLabels = generateDateLabels(timeRange)
        setRevenueData({
          labels: revenueLabels,
          datasets: [
            {
              label: "Revenue (RM)",
              data: generateRandomData(revenueLabels.length, 100, 1000),
              borderColor: "rgb(245, 158, 11)",
              backgroundColor: "rgba(245, 158, 11, 0.5)",
              tension: 0.3,
            },
          ],
        })

        // Mock data for attendance rate
        setAttendanceData({
          labels: ["Attended", "No-show"],
          datasets: [
            {
              data: [78, 22],
              backgroundColor: ["rgba(16, 185, 129, 0.7)", "rgba(239, 68, 68, 0.7)"],
              borderColor: ["rgb(16, 185, 129)", "rgb(239, 68, 68)"],
              borderWidth: 1,
            },
          ],
        })
      } catch (error) {
        console.error("Error fetching analytics data:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [timeRange])

  // Helper function to generate date labels based on time range
  const generateDateLabels = (range: string) => {
    const labels = []
    const today = new Date()
    let days = 30

    switch (range) {
      case "7days":
        days = 7
        break
      case "30days":
        days = 30
        break
      case "90days":
        days = 90
        break
      case "year":
        days = 365
        break
      default:
        days = 30
    }

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date()
      date.setDate(today.getDate() - i)
      labels.push(date.toLocaleDateString("en-US", { month: "short", day: "numeric" }))
    }

    return labels
  }

  // Helper function to generate random data
  const generateRandomData = (length: number, min: number, max: number) => {
    return Array.from({ length }, () => Math.floor(Math.random() * (max - min + 1)) + min)
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Analytics</h1>
          <Skeleton className="h-10 w-32" />
        </div>
        <Tabs defaultValue="overview">
          <TabsList className="mb-4">
            <Skeleton className="h-10 w-[400px]" />
          </TabsList>
          <TabsContent value="overview">
            <div className="grid gap-6 md:grid-cols-2">
              <Skeleton className="h-[300px] w-full" />
              <Skeleton className="h-[300px] w-full" />
              <Skeleton className="h-[300px] w-full" />
              <Skeleton className="h-[300px] w-full" />
            </div>
          </TabsContent>
        </Tabs>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Analytics</h1>
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7days">Last 7 days</SelectItem>
            <SelectItem value="30days">Last 30 days</SelectItem>
            <SelectItem value="90days">Last 90 days</SelectItem>
            <SelectItem value="year">Last year</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs defaultValue="overview">
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
          <TabsTrigger value="registrations">Registrations</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Events Created</CardTitle>
                <CardDescription>Number of events created over time</CardDescription>
              </CardHeader>
              <CardContent>
                {eventData && <Chart type="line" data={eventData} />}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Registrations</CardTitle>
                <CardDescription>Number of registrations over time</CardDescription>
              </CardHeader>
              <CardContent>
                {registrationData && <Chart type="bar" data={registrationData} />}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Revenue</CardTitle>
                <CardDescription>Revenue generated over time</CardDescription>
              </CardHeader>
              <CardContent>
                {revenueData && <Chart type="line" data={revenueData} />}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Attendance Rate</CardTitle>
                <CardDescription>Percentage of registered attendees who attended events</CardDescription>
              </CardHeader>
              <CardContent className="flex justify-center">
                <div className="w-[250px] h-[250px] mx-auto">
                  {attendanceData && (
                    <Chart
                      type="doughnut"
                      data={attendanceData}
                      options={{
                        plugins: {
                          legend: {
                            position: "bottom",
                          },
                        },
                      }}
                    />
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="events">
          <Card>
            <CardHeader>
              <CardTitle>Events Created</CardTitle>
              <CardDescription>Detailed analysis of events created over time</CardDescription>
            </CardHeader>
            <CardContent className="h-[500px]">
              {eventData && <Chart type="line" data={eventData} className="h-full" />}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="registrations">
          <Card>
            <CardHeader>
              <CardTitle>Registrations</CardTitle>
              <CardDescription>Detailed analysis of registrations over time</CardDescription>
            </CardHeader>
            <CardContent className="h-[500px]">
              {registrationData && <Chart type="bar" data={registrationData} className="h-full" />}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="revenue">
          <Card>
            <CardHeader>
              <CardTitle>Revenue</CardTitle>
              <CardDescription>Detailed analysis of revenue over time</CardDescription>
            </CardHeader>
            <CardContent className="h-[500px]">
              {revenueData && <Chart type="line" data={revenueData} className="h-full" />}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
