import crypto from "crypto";

/**
 * Security Token Service for Dynamic QR Code Generation
 * Provides time-based tokens that expire every 30 seconds for secure ticket verification
 */

// Token configuration
const TOKEN_VALIDITY_SECONDS = 30;
const HMAC_SECRET = process.env.QR_SECURITY_SECRET || process.env.JWT_SECRET || "your-secret-key";

export interface SecureTicketToken {
  ticketId: string;
  eventId: string;
  attendeeName: string;
  timestamp: number;
  windowStart: number;
  windowEnd: number;
  nonce: string;
  signature: string;
}

export interface TicketData {
  id: string;
  event_id: string;
  guest_name: string;
  event?: {
    id: string;
    title: string;
  };
}

/**
 * Generate a secure time-based token for a ticket
 * @param ticketData - The ticket/registration data
 * @returns SecureTicketToken with HMAC signature
 */
export function generateSecureTicketToken(ticketData: TicketData): SecureTicketToken {
  const now = Math.floor(Date.now() / 1000);

  // Calculate time window (10-second intervals)
  const windowStart = Math.floor(now / TOKEN_VALIDITY_SECONDS) * TOKEN_VALIDITY_SECONDS;
  const windowEnd = windowStart + TOKEN_VALIDITY_SECONDS;

  // Generate a random nonce to prevent replay attacks
  const nonce = crypto.randomBytes(8).toString('hex');

  // Create the token payload
  const tokenPayload = {
    ticketId: ticketData.id,
    eventId: ticketData.event_id,
    attendeeName: ticketData.guest_name,
    timestamp: now,
    windowStart,
    windowEnd,
    nonce,
  };

  // Generate HMAC signature
  const signature = generateHMACSignature(tokenPayload);

  return {
    ...tokenPayload,
    signature,
  };
}

/**
 * Verify a secure ticket token
 * @param token - The token to verify
 * @param allowedTimeSkew - Additional seconds to allow for clock skew (default: 5)
 * @returns boolean indicating if token is valid
 */
export function verifySecureTicketToken(
  token: SecureTicketToken,
  allowedTimeSkew: number = 5
): { valid: boolean; reason?: string } {
  try {
    const now = Math.floor(Date.now() / 1000);

    // Verify signature first
    const expectedSignature = generateHMACSignature({
      ticketId: token.ticketId,
      eventId: token.eventId,
      attendeeName: token.attendeeName,
      timestamp: token.timestamp,
      windowStart: token.windowStart,
      windowEnd: token.windowEnd,
      nonce: token.nonce,
    });

    if (token.signature !== expectedSignature) {
      return { valid: false, reason: "Invalid signature" };
    }

    // Check if token is within valid time window (with skew allowance)
    const effectiveWindowEnd = token.windowEnd + allowedTimeSkew;
    const effectiveWindowStart = token.windowStart - allowedTimeSkew;

    if (now < effectiveWindowStart) {
      return { valid: false, reason: "Token not yet valid" };
    }

    if (now > effectiveWindowEnd) {
      return { valid: false, reason: "Token expired" };
    }

    // Verify window calculation is correct
    const expectedWindowStart = Math.floor(token.timestamp / TOKEN_VALIDITY_SECONDS) * TOKEN_VALIDITY_SECONDS;
    if (token.windowStart !== expectedWindowStart) {
      return { valid: false, reason: "Invalid time window" };
    }

    return { valid: true };
  } catch (error) {
    console.error("Error verifying secure ticket token:", error);
    return { valid: false, reason: "Verification error" };
  }
}

/**
 * Generate HMAC signature for token payload
 * @param payload - The token payload to sign
 * @returns HMAC signature as hex string
 */
function generateHMACSignature(payload: Omit<SecureTicketToken, 'signature'>): string {
  // Create a deterministic string from the payload
  const dataString = [
    payload.ticketId,
    payload.eventId,
    payload.attendeeName,
    payload.timestamp.toString(),
    payload.windowStart.toString(),
    payload.windowEnd.toString(),
    payload.nonce,
  ].join('|');

  // Generate HMAC-SHA256 signature
  const hmac = crypto.createHmac('sha256', HMAC_SECRET);
  hmac.update(dataString);
  return hmac.digest('hex');
}

/**
 * Convert base64 to URL-safe base64
 * @param base64 - Standard base64 string
 * @returns URL-safe base64 string
 */
function toBase64Url(base64: string): string {
  return base64.replace(/\+/g, '-').replace(/\//g, '_').replace(/=/g, '');
}

/**
 * Convert URL-safe base64 to standard base64
 * @param base64url - URL-safe base64 string
 * @returns Standard base64 string
 */
function fromBase64Url(base64url: string): string {
  // Add padding if needed
  let base64 = base64url.replace(/-/g, '+').replace(/_/g, '/');
  while (base64.length % 4) {
    base64 += '=';
  }
  return base64;
}

/**
 * Generate QR code data URL with secure token
 * @param ticketData - The ticket data
 * @param baseUrl - Base URL for the application (optional)
 * @returns QR code data URL
 */
export function generateSecureQRData(ticketData: TicketData, baseUrl?: string): string {
  const secureToken = generateSecureTicketToken(ticketData);
  const tokenString = JSON.stringify(secureToken);
  const encodedToken = toBase64Url(Buffer.from(tokenString).toString('base64'));

  const domain = baseUrl || 'https://mticket.my';
  return `${domain}/verify/secure?token=${encodedToken}`;
}

/**
 * Parse secure token from QR code data
 * @param qrData - The QR code data URL
 * @returns Parsed secure token or null if invalid
 */
export function parseSecureQRData(qrData: string): SecureTicketToken | null {
  try {
    const url = new URL(qrData);
    const tokenParam = url.searchParams.get('token');

    if (!tokenParam) {
      return null;
    }

    const base64 = fromBase64Url(tokenParam);
    const tokenString = Buffer.from(base64, 'base64').toString('utf-8');
    const token = JSON.parse(tokenString) as SecureTicketToken;

    // Basic validation of token structure
    if (!token.ticketId || !token.eventId || !token.signature || !token.nonce) {
      return null;
    }

    return token;
  } catch (error) {
    console.error("Error parsing secure QR data:", error);
    return null;
  }
}

/**
 * Team Access Token Security
 * Provides secure tokens for team access URLs while keeping plain tokens for manual entry
 */

export interface SecureTeamToken {
  teamId: string;
  eventSlug: string;
  accessToken: string;
  timestamp: number;
  expires: number;
  nonce: string;
  signature: string;
}

/**
 * Generate a secure token for team access URLs
 * @param teamId - The team ID
 * @param eventSlug - The event slug
 * @param accessToken - The plain access token
 * @param validityHours - Token validity in hours (default: 24)
 * @returns Secure team token
 */
export function generateSecureTeamToken(
  teamId: string,
  eventSlug: string,
  accessToken: string,
  validityHours: number = 24
): SecureTeamToken {
  const now = Date.now();
  const expires = now + (validityHours * 60 * 60 * 1000);

  // Generate a random nonce to prevent replay attacks
  const nonce = crypto.randomBytes(8).toString('hex');

  // Create the token payload
  const tokenPayload = {
    teamId,
    eventSlug,
    accessToken,
    timestamp: now,
    expires,
    nonce,
  };

  // Generate HMAC signature
  const signature = generateTeamTokenSignature(tokenPayload);

  return {
    ...tokenPayload,
    signature,
  };
}

/**
 * Verify a secure team token
 * @param token - The token to verify
 * @returns Verification result with validity and reason
 */
export function verifySecureTeamToken(
  token: SecureTeamToken
): { valid: boolean; reason?: string; accessToken?: string } {
  try {
    const now = Date.now();

    // Check if token has expired
    if (now > token.expires) {
      return { valid: false, reason: "Token has expired" };
    }

    // Verify signature
    const expectedSignature = generateTeamTokenSignature({
      teamId: token.teamId,
      eventSlug: token.eventSlug,
      accessToken: token.accessToken,
      timestamp: token.timestamp,
      expires: token.expires,
      nonce: token.nonce,
    });

    if (token.signature !== expectedSignature) {
      return { valid: false, reason: "Invalid signature" };
    }

    return {
      valid: true,
      accessToken: token.accessToken
    };
  } catch (error) {
    console.error("Error verifying secure team token:", error);
    return { valid: false, reason: "Token verification failed" };
  }
}

/**
 * Generate HMAC signature for team token payload
 * @param payload - The token payload to sign
 * @returns HMAC signature as hex string
 */
function generateTeamTokenSignature(payload: Omit<SecureTeamToken, 'signature'>): string {
  // Create a deterministic string from the payload
  const dataString = [
    payload.teamId,
    payload.eventSlug,
    payload.accessToken,
    payload.timestamp.toString(),
    payload.expires.toString(),
    payload.nonce,
  ].join('|');

  // Generate HMAC-SHA256 signature
  const hmac = crypto.createHmac('sha256', HMAC_SECRET);
  hmac.update(dataString);
  return hmac.digest('hex');
}

/**
 * Generate a secure team access URL
 * @param teamId - The team ID
 * @param eventSlug - The event slug
 * @param accessToken - The plain access token
 * @param baseUrl - Base URL for the application (optional)
 * @returns Secure team access URL
 */
export function generateSecureTeamURL(
  teamId: string,
  eventSlug: string,
  accessToken: string,
  baseUrl?: string
): string {
  const secureToken = generateSecureTeamToken(teamId, eventSlug, accessToken);
  const tokenString = JSON.stringify(secureToken);
  const encodedToken = toBase64Url(Buffer.from(tokenString).toString('base64'));

  const domain = baseUrl || 'https://mticket.my';
  return `${domain}/${eventSlug}/qrscan?secure_token=${encodedToken}`;
}

/**
 * Parse secure team token from URL
 * @param url - The URL containing the secure token
 * @returns Parsed secure team token or null if invalid
 */
export function parseSecureTeamToken(url: string): SecureTeamToken | null {
  try {
    const urlObj = new URL(url);
    const tokenParam = urlObj.searchParams.get('secure_token');

    if (!tokenParam) {
      return null;
    }

    const base64 = fromBase64Url(tokenParam);
    const tokenString = Buffer.from(base64, 'base64').toString('utf-8');
    const token = JSON.parse(tokenString) as SecureTeamToken;

    // Basic validation of token structure
    if (!token.teamId || !token.eventSlug || !token.accessToken || !token.signature || !token.nonce) {
      return null;
    }

    return token;
  } catch (error) {
    console.error("Error parsing secure team token:", error);
    return null;
  }
}

/**
 * Get current time window information
 * @returns Object with current window start, end, and remaining seconds
 */
export function getCurrentTimeWindow(): {
  windowStart: number;
  windowEnd: number;
  remainingSeconds: number;
} {
  const now = Math.floor(Date.now() / 1000);
  const windowStart = Math.floor(now / TOKEN_VALIDITY_SECONDS) * TOKEN_VALIDITY_SECONDS;
  const windowEnd = windowStart + TOKEN_VALIDITY_SECONDS;
  const remainingSeconds = windowEnd - now;

  return {
    windowStart,
    windowEnd,
    remainingSeconds,
  };
}
