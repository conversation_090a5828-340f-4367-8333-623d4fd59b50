"use client"

import { Building2, Mail, Phone, Globe, MapPin, User, FileText } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

interface OrganizerData {
  id: string
  name: string
  ssm_number: string
  pic_name: string
  pic_phone: string
  pic_email: string
  logo_url?: string
  address?: string
  city?: string
  state?: string
  postal_code?: string
  country?: string
  website?: string
}

interface EventOrganizerTabProps {
  organizer: OrganizerData | null
}

export function EventOrganizerTab({ organizer }: EventOrganizerTabProps) {
  if (!organizer) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Building2 className="mx-auto h-12 w-12 mb-4 opacity-50" />
            <p>No organizer information available for this event.</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const fullAddress = [
    organizer.address,
    organizer.city,
    organizer.state,
    organizer.postal_code,
    organizer.country
  ].filter(Boolean).join(", ")

  const organizerInitials = organizer.name
    .split(" ")
    .map(word => word.charAt(0))
    .join("")
    .toUpperCase()
    .slice(0, 2)

  return (
    <div className="space-y-6">
      {/* Organization Header */}
      <Card>
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={organizer.logo_url} alt={organizer.name} />
              <AvatarFallback className="text-lg font-semibold bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500 text-white">
                {organizerInitials}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 space-y-2">
              <div className="flex items-center space-x-2">
                <h2 className="text-2xl font-bold">{organizer.name}</h2>
                <Badge variant="secondary" className="text-xs">
                  <FileText className="h-3 w-3 mr-1" />
                  SSM: {organizer.ssm_number}
                </Badge>
              </div>
              {organizer.website && (
                <div className="flex items-center space-x-2">
                  <Globe className="h-4 w-4 text-muted-foreground" />
                  <Button variant="link" className="p-0 h-auto text-sm" asChild>
                    <a
                      href={organizer.website.startsWith('http') ? organizer.website : `https://${organizer.website}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline"
                    >
                      {organizer.website}
                    </a>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Person in Charge */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Person in Charge</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="font-medium text-lg">{organizer.pic_name}</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <Button variant="link" className="p-0 h-auto text-sm" asChild>
                  <a href={`mailto:${organizer.pic_email}`} className="text-primary hover:underline">
                    {organizer.pic_email}
                  </a>
                </Button>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <Button variant="link" className="p-0 h-auto text-sm" asChild>
                  <a href={`tel:${organizer.pic_phone}`} className="text-primary hover:underline">
                    {organizer.pic_phone}
                  </a>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Organization Address */}
        {fullAddress && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MapPin className="h-5 w-5" />
                <span>Organization Address</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm leading-relaxed">{fullAddress}</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
