import Link from "next/link"
import { Shield, Zap, Users, Globe, Target, Award, ArrowRight, Building2, MapPin, Mail, Phone } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { PageLayout } from "@/components/page-layout"

export const metadata = {
  title: "Company Profile | mPass Solutions Sdn Bhd - mTicket.my",
  description: "Learn about mPass Solutions Sdn Bhd, the technology company behind mTicket.my - empowering access and enabling experiences.",
}

export default function CompanyPage() {
  const coreOfferings = [
    {
      icon: Globe,
      title: "Event Management Platforms",
      description: "Comprehensive solutions like mTicket.my for seamless event experiences",
      example: "mTicket.my"
    },
    {
      icon: Award,
      title: "Digital Credential Systems",
      description: "Certificates, e-passes, and verifiable badges for modern organizations",
      example: "Verifiable certificates"
    },
    {
      icon: Shield,
      title: "Access Control Infrastructure",
      description: "QR-based check-ins and real-time attendance tracking systems",
      example: "Smart check-in systems"
    },
    {
      icon: Zap,
      title: "Custom Integrations & APIs",
      description: "RESTful endpoints, webhooks, and seamless third-party integrations",
      example: "Developer-friendly APIs"
    },
    {
      icon: Users,
      title: "SaaS Solutions",
      description: "Multi-tenant platforms with role-based access and scalable architecture",
      example: "Enterprise-ready platforms"
    }
  ]

  const companyValues = [
    {
      icon: Shield,
      title: "Trust First",
      description: "Security and transparency in every interaction"
    },
    {
      icon: Users,
      title: "User-Centered",
      description: "Designed for experience, not just function"
    },
    {
      icon: Target,
      title: "Scalability by Design",
      description: "Solutions built to grow with your needs"
    },
    {
      icon: Zap,
      title: "Innovation-Driven",
      description: "We build for the future, not just today"
    }
  ]

  return (
    <PageLayout>
      {/* Corporate Hero Section */}
      <section className="relative w-full py-20 md:py-32 lg:py-40 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}
          ></div>
        </div>

        <div className="container relative px-4 md:px-6">
          <div className="flex flex-col items-center space-y-8 text-center">
            {/* Main Heading */}
            <div className="space-y-4 max-w-4xl">
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl lg:text-7xl">
                mPass Solutions
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> Sdn Bhd</span>
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-gray-300 md:text-2xl leading-relaxed">
                "Empowering Access. Enabling Experiences."
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="w-full py-16 md:py-20 bg-white">
        <div className="container px-4 md:px-6">
          <div className="mx-auto max-w-7xl">
            {/* Company Overview */}
            <div className="max-w-4xl mx-auto text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Company Overview</h2>
              <p className="text-xl text-gray-600 leading-relaxed">
                mPass Solutions Sdn Bhd is a Malaysian-based technology company focused on delivering smart, secure, and scalable digital access solutions. We specialize in event management systems, digital credentialing, and identity verification tools for modern organizations. Our mission is to bridge the gap between physical and digital experiences through seamless, user-centric platforms.
              </p>
            </div>

            {/* Vision & Mission */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-20">
              <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader className="text-center pb-4">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Target className="h-8 w-8 text-purple-600" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-gray-900">Our Vision</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <p className="text-gray-600 leading-relaxed">
                    To become Southeast Asia's leading provider of smart access infrastructure for events, credentials, and digital identity.
                  </p>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <CardHeader className="text-center pb-4">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Award className="h-8 w-8 text-blue-600" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-gray-900">Our Mission</CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="space-y-2 text-gray-600">
                    <p>• Simplify how people access and manage events, credentials, and participation</p>
                    <p>• Offer secure, scalable, and user-friendly platforms for organizers and attendees</p>
                    <p>• Foster digital trust through verifiable, tamper-resistant credential systems</p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Core Offerings */}
            <div className="mb-20">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Core Offerings</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {coreOfferings.map((offering, index) => (
                  <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
                    <CardHeader className="text-center pb-4">
                      <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <offering.icon className="h-8 w-8 text-indigo-600" />
                      </div>
                      <CardTitle className="text-xl font-semibold">{offering.title}</CardTitle>
                    </CardHeader>
                    <CardContent className="text-center space-y-3">
                      <p className="text-gray-600">{offering.description}</p>
                      <Badge variant="secondary" className="text-xs">
                        {offering.example}
                      </Badge>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Company Values */}
            <div className="mb-20">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Company Values</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {companyValues.map((value, index) => (
                  <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300 text-center">
                    <CardHeader className="pb-4">
                      <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <value.icon className="h-6 w-6 text-green-600" />
                      </div>
                      <CardTitle className="text-lg font-semibold">{value.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-600 text-sm">{value.description}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Legal Status & Contact */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-20">
              <Card className="border-0 shadow-lg bg-gradient-to-r from-gray-50 to-blue-50">
                <CardHeader className="text-center pb-6">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Building2 className="h-8 w-8 text-gray-600" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-gray-900">Legal Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-3">
                    <Badge variant="outline">Private Limited Company (Sdn Bhd)</Badge>
                  </div>
                  <div className="flex items-start gap-3">
                    <MapPin className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-gray-900">Based in:</p>
                      <p className="text-gray-600">Kuala Lumpur, Malaysia</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Building2 className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="font-medium text-gray-900">Company No:</p>
                      <p className="text-gray-600">[Insert company registration number]</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-0 shadow-lg bg-gradient-to-r from-purple-50 to-pink-50">
                <CardHeader className="text-center pb-6">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Globe className="h-8 w-8 text-purple-600" />
                  </div>
                  <CardTitle className="text-2xl font-bold text-gray-900">Flagship Product</CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-4">
                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold text-purple-700">mTicket.my</h3>
                    <p className="text-gray-600">
                      A modern, integrated event management and certificate platform
                    </p>
                  </div>
                  <Link
                    href="/about"
                    className="inline-flex items-center justify-center rounded-md bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 text-sm font-medium transition-colors"
                  >
                    Learn More About mTicket.my
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </CardContent>
              </Card>
            </div>

            {/* Contact Information */}
            <div className="text-center">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 md:p-12 text-white">
                <div className="max-w-3xl mx-auto">
                  <h2 className="text-3xl md:text-4xl font-bold mb-4">Get in Touch</h2>
                  <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                    Interested in partnering with us or learning more about our solutions?
                    We'd love to hear from you.
                  </p>

                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Link
                      href="/contact"
                      className="inline-flex items-center justify-center rounded-xl bg-white text-blue-700 hover:bg-gray-50 px-8 py-4 text-lg font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    >
                      Contact Us
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Link>
                    <Link
                      href="/about"
                      className="inline-flex items-center justify-center rounded-xl border-2 border-white/30 bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 hover:border-white/50 px-8 py-4 text-lg font-semibold transition-all duration-200"
                    >
                      About mTicket.my
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </PageLayout>
  )
}
