"use client"

import { useState, useMemo } from "react"

interface UseSearchFilterOptions<T> {
  data: T[]
  searchFields: (keyof T)[]
  initialQuery?: string
}

export function useSearchFilter<T>({ 
  data, 
  searchFields, 
  initialQuery = "" 
}: UseSearchFilterOptions<T>) {
  const [searchQuery, setSearchQuery] = useState(initialQuery)

  const filteredData = useMemo(() => {
    if (!searchQuery.trim()) return data

    const lowercasedQuery = searchQuery.toLowerCase()
    
    return data.filter(item => 
      searchFields.some(field => {
        const value = item[field]
        if (typeof value === 'string') {
          return value.toLowerCase().includes(lowercasedQuery)
        }
        if (typeof value === 'number') {
          return value.toString().includes(lowercasedQuery)
        }
        return false
      })
    )
  }, [data, searchQuery, searchFields])

  return {
    searchQuery,
    setSearchQuery,
    filteredData,
    hasResults: filteredData.length > 0,
    totalResults: filteredData.length
  }
}
