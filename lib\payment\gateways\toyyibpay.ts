import type { PaymentGateway, PaymentGatewayConfig, PaymentRequest, PaymentResponse } from "../types"

export class ToyyibPayGateway implements PaymentGateway {
  async createPayment(
    config: PaymentGatewayConfig,
    paymentRequest: PaymentRequest,
  ): Promise<PaymentResponse> {
    try {
      // Get ToyyibPay configuration
      const userSecretKey = config.fields.user_secret_key
      const categoryCode = config.fields.category_code
      const sandboxUrl = config.fields.sandbox_url || 'https://dev.toyyibpay.com'
      const baseUrl = sandboxUrl.replace('/api', '') // Remove /api suffix if present

      if (!userSecretKey || !categoryCode) {
        throw new Error("ToyyibPay configuration is incomplete")
      }

      // Prepare ToyyibPay bill data
      const billData = {
        userSecretKey: userSecretKey,
        categoryCode: categoryCode,
        billName: `Registration for Event`,
        billDescription: paymentRequest.description,
        billPriceSetting: '1', // Fixed price
        billPayorInfo: '1', // Collect payer info
        billAmount: (paymentRequest.amount * 100).toString(), // ToyyibPay expects amount in cents (RM50 = 5000)
        billReturnUrl: paymentRequest.success_url,
        billCallbackUrl: `${process.env.NEXT_PUBLIC_APP_URL}/api/payments/toyyibpay/callback`,
        billExternalReferenceNo: paymentRequest.reference_id,
        billTo: paymentRequest.customer_name,
        billEmail: paymentRequest.customer_email,
        billPhone: paymentRequest.customer_phone || '0123456789', // Default phone if not provided
        billSplitPayment: '0',
        billSplitPaymentArgs: '',
        billPaymentChannel: '0', // All channels
        billContentEmail: 'Thank you for your registration!',
        billChargeToCustomer: '1',
        billExpiryDays: '3'
      }

      console.log("Creating ToyyibPay bill with data:", billData)

      // Make API call to ToyyibPay
      const response = await fetch(`${baseUrl}/index.php/api/createBill`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams(billData).toString(),
      })

      if (!response.ok) {
        throw new Error(`ToyyibPay API error: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      console.log("ToyyibPay API response:", result)

      // Check if the response is successful
      if (Array.isArray(result) && result.length > 0 && result[0].BillCode) {
        const billCode = result[0].BillCode
        const paymentUrl = `${baseUrl}/${billCode}`

        return {
          success: true,
          payment_url: paymentUrl,
          transaction_id: billCode,
          payment_gateway_id: paymentRequest.payment_gateway_id,
        }
      } else {
        throw new Error("Invalid response from ToyyibPay API")
      }
    } catch (error: any) {
      console.error("Error creating ToyyibPay payment:", error)
      return {
        success: false,
        error: error.message || "Failed to create payment",
      }
    }
  }

  async verifyPayment(transactionId: string): Promise<boolean> {
    try {
      // In a real implementation, this would make an API call to ToyyibPay
      // For demo purposes, we'll simulate a successful verification

      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 500))

      // Always return true for demo
      return true
    } catch (error) {
      console.error("Error verifying ToyyibPay payment:", error)
      return false
    }
  }
}

// Legacy function for backward compatibility
export async function createToyyibPayment(
  config: PaymentGatewayConfig,
  paymentRequest: PaymentRequest,
): Promise<PaymentResponse> {
  const gateway = new ToyyibPayGateway()
  return gateway.createPayment(config, paymentRequest)
}
