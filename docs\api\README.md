# API Documentation

This directory contains comprehensive API documentation for the mTicket.my platform.

## Contents

### Core APIs
- [Authentication API](./auth.md) - User authentication and authorization endpoints
- [Events API](./events.md) - Event management and retrieval endpoints
- [Registration API](./registrations.md) - Event registration and participant management
- [Payment API](./payments.md) - Payment processing and gateway integration

### Management APIs
- [Admin API](./admin.md) - Administrative functions and system management
- [Organizations API](./organizations.md) - Organization management endpoints
- [Certificates API](./certificates.md) - Certificate generation and verification
- [Tickets API](./tickets.md) - Ticket generation and QR code management

### Integration APIs
- [Webhooks API](./webhooks.md) - Webhook management and event notifications
- [Upload API](./uploads.md) - File upload and media management
- [Settings API](./settings.md) - System and user settings management

## API Reference

For a complete list of all endpoints, see [endpoints-reference.md](./endpoints-reference.md).

## Authentication

All API endpoints require authentication unless explicitly marked as public. See [Authentication Guide](../guides/developer/authentication.md) for details.

## Rate Limiting

API endpoints are rate-limited to ensure system stability. See [Rate Limiting Guide](../guides/developer/rate-limiting.md) for details.

## Error Handling

All APIs follow consistent error response patterns. See [Error Handling Guide](../guides/developer/error-handling.md) for details.
