import { NextRequest, NextResponse } from "next/server"
import { supabase } from "@/lib/supabase"
import jwt from "jsonwebtoken"

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get("authorization")
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const token = authHeader.substring(7)

    // Verify the JWT token
    let decoded: any
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET || "fallback-secret")
    } catch (error) {
      return NextResponse.json({ error: "Invalid token" }, { status: 401 })
    }

    const userId = decoded.userId

    // First get user's registration IDs
    const { data: userRegistrations, error: regError } = await supabase
      .from("registrations")
      .select("id")
      .eq("user_id", userId)

    if (regError) {
      console.error("Error fetching user registrations:", regError)
      return NextResponse.json({ error: "Failed to fetch user registrations" }, { status: 500 })
    }

    const registrationIds = userRegistrations?.map(reg => reg.id) || []

    if (registrationIds.length === 0) {
      return NextResponse.json({
        success: true,
        certificates: [],
      })
    }

    // Fetch user certificates - using actual database columns
    const { data: rawCertificates, error } = await supabase
      .from("certificates")
      .select(`
        id,
        participant_name,
        verification_code,
        certificate_url,
        issued_at,
        template_id,
        event_id,
        registration_id,
        user_id,
        participant_id,
        is_revoked
      `)
      .in("registration_id", registrationIds)
      .order("issued_at", { ascending: false })

    // Map database fields to expected frontend fields
    const certificates = rawCertificates?.map(cert => ({
      id: cert.id,
      recipient_name: cert.participant_name,
      unique_code: cert.verification_code,
      file_path: cert.certificate_url,
      verification_url: cert.certificate_url,
      generated_at: cert.issued_at,
      template_id: cert.template_id,
      event_id: cert.event_id,
      event_title: '', // Will be populated from events if needed
      registration_id: cert.registration_id
    })) || [];

    if (error) {
      console.error("Error fetching certificates:", error)
      return NextResponse.json({ error: "Failed to fetch certificates" }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      certificates: certificates,
    })
  } catch (error: any) {
    console.error("Error in certificates API:", error)
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    )
  }
}
