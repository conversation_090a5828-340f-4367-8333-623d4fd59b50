import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"

interface RouteParams {
  params: { categoryId: string }
}

/**
 * GET /api/events/category/[categoryId]
 * Fetches all published events by a specific category
 * This is a public endpoint - no authentication required
 */
export async function GET(request: Request, { params }: RouteParams) {
  try {
    const { categoryId } = await params

    if (!categoryId) {
      return NextResponse.json(
        { error: "Category ID is required" },
        { status: 400 }
      )
    }

    console.log("Events by Category API: Fetching events for category:", categoryId)

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin()

    // Check if categoryId is a UUID or a name
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(categoryId)

    let data, error

    if (isUUID) {
      // Filter by category ID
      const result = await supabaseAdmin
        .from("events")
        .select(`
          *,
          organizations:organization_id(id, name),
          event_category:category_id(id, name, color, icon)
        `)
        .eq("category_id", categoryId)
        .eq("is_published", true)

      data = result.data
      error = result.error
    } else {
      // Filter by category name - first get all events, then filter by category name
      const result = await supabaseAdmin
        .from("events")
        .select(`
          *,
          organizations:organization_id(id, name),
          event_category:category_id(id, name, color, icon)
        `)
        .eq("is_published", true)

      if (result.error) {
        data = null
        error = result.error
      } else {
        // Filter by category name
        data = result.data?.filter(event =>
          event.event_category?.name?.toLowerCase() === categoryId.toLowerCase()
        ) || []
        error = null
      }
    }

    if (error) {
      console.error("Error fetching events by category:", error)
      return NextResponse.json(
        { error: "Failed to fetch events" },
        { status: 500 }
      )
    }

    // Sort events: available events first (by start_date), then ended events last
    const sortedEvents = (data || []).sort((a, b) => {
      const now = new Date();
      const aEnded = new Date(a.end_date) < now;
      const bEnded = new Date(b.end_date) < now;

      // If one is ended and the other isn't, prioritize the non-ended one
      if (aEnded && !bEnded) return 1;
      if (!aEnded && bEnded) return -1;

      // If both have the same status (both ended or both available), sort by start_date
      return new Date(a.start_date).getTime() - new Date(b.start_date).getTime();
    });

    console.log(`Events by Category API: Successfully fetched ${sortedEvents.length} events`)

    return NextResponse.json({
      events: sortedEvents,
      count: sortedEvents.length,
      category: sortedEvents?.[0]?.event_category || null
    })
  } catch (error: any) {
    console.error("Error in events by category API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
