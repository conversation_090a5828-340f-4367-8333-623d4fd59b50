"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Award, Download, Eye } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { useCertificates } from "@/contexts/certificate-context"
import { useAuth } from "@/contexts/auth-context"
import { useSettings } from "@/contexts/settings-context"

// Form schema
const formSchema = z.object({
  participant_name: z.string().min(3, { message: "Participant name is required" }),
  template_id: z.string().min(1, { message: "Please select a template" }),
  custom_text: z.string().optional(),
  issue_date: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface CertificateGeneratorProps {
  eventId: string
  participantId: string
  participantName?: string
}

export function CertificateGenerator({ eventId, participantId, participantName = 'Participant' }: CertificateGeneratorProps) {
  const { generateCertificate } = useCertificates()
  const { isPaidUser = () => false } = useAuth() || {}
  const { settings } = useSettings() || {}
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const { toast } = useToast()

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      participant_name: participantName || "",
      template_id: "",
      custom_text: "",
      issue_date: new Date().toISOString().split("T")[0],
    },
  })

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    if (!generateCertificate) {
      toast({
        title: "Error",
        description: "Certificate generation service is not available",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)
    try {
      const result = await generateCertificate(
        eventId,
        participantId,
        values.participant_name || participantName,
        values.template_id || availableTemplates[0]?.id || 'default-template',
        {
          custom_text: values.custom_text || "",
          issue_date: values.issue_date || new Date().toISOString().split("T")[0],
        }
      )

      if (result) {
        setPreviewUrl(result.certificate_url)
        toast({
          title: "Success",
          description: "Certificate generated successfully",
        })
      }
    } catch (error) {
      console.error("Error generating certificate:", error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to generate certificate",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Get available templates - fallback to a default template if settings aren't loaded
  const availableTemplates = (settings?.certificate_templates?.length
    ? settings.certificate_templates.filter((template) => !template.is_premium || (isPaidUser && isPaidUser()))
    : [{
        id: "default-template",
        name: "Default Certificate",
        thumbnail_url: "/certificate-template.jpg",
        is_premium: false
      }]) as Array<{
        id: string
        name: string
        thumbnail_url: string
        is_premium: boolean
      }>

  return (
    <div className="space-y-6">
      {previewUrl && (
        <Card>
          <CardHeader>
            <CardTitle>Certificate Preview</CardTitle>
          </CardHeader>
          <CardContent className="flex justify-center">
            <div className="relative aspect-[1.4/1] w-full max-w-2xl overflow-hidden rounded-lg border">
              <img
                src={previewUrl || "/placeholder.svg"}
                alt="Certificate preview"
                className="h-full w-full object-contain"
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-end gap-4">
            <Button variant="outline">
              <Eye className="mr-2 h-4 w-4" />
              View Full Size
            </Button>
            <Button>
              <Download className="mr-2 h-4 w-4" />
              Download Certificate
            </Button>
          </CardFooter>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Generate Certificate</CardTitle>
          <CardDescription>Create a certificate for event participant</CardDescription>
        </CardHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardContent className="space-y-6">
              <FormField
                control={form.control}
                name="participant_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Participant Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter participant's full name" {...field} />
                    </FormControl>
                    <FormDescription>Name as it will appear on the certificate</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="template_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Certificate Template</FormLabel>
                    <FormControl>
                      <RadioGroup
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        className="grid grid-cols-1 gap-4 md:grid-cols-3"
                      >
                        {availableTemplates.map((template) => (
                          <FormItem key={template.id} className="relative space-y-0">
                            <FormControl>
                              <RadioGroupItem
                                value={template.id}
                                id={template.id}
                                className="absolute right-2 top-2 z-10"
                              />
                            </FormControl>
                            <label
                              htmlFor={template.id}
                              className="block cursor-pointer overflow-hidden rounded-lg border bg-card transition-all hover:border-primary"
                            >
                              <div className="aspect-[1.4/1] w-full overflow-hidden">
                                <img
                                  src={template.thumbnail_url || "/placeholder.svg"}
                                  alt={template.name}
                                  className="h-full w-full object-cover"
                                />
                              </div>
                              <div className="flex items-center justify-between p-2">
                                <span className="text-sm font-medium">{template.name}</span>
                                {template.is_premium && (
                                  <span className="rounded-full bg-amber-100 px-2 py-0.5 text-xs font-medium text-amber-800">
                                    Premium
                                  </span>
                                )}
                              </div>
                            </label>
                          </FormItem>
                        ))}
                      </RadioGroup>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="custom_text"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Custom Text (Optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Enter custom text to appear on the certificate"
                        className="min-h-20"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>Additional text to include on the certificate</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="issue_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Issue Date</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter>
              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? "Generating..." : "Generate Certificate"}
                <Award className="ml-2 h-4 w-4" />
              </Button>
            </CardFooter>
          </form>
        </Form>
      </Card>
    </div>
  )
}
