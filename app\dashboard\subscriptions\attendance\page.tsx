"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  QrCode, 
  CheckCircle, 
  XCircle,
  Clock,
  Calendar,
  BarChart3,
  Settings,
  Download,
  Upload
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"

type AttendanceStats = {
  totalEvents: number
  eventsWithTracking: number
  totalAttendees: number
  checkedInAttendees: number
  qrScansToday: number
  averageCheckInTime: string
}

type AttendanceSettings = {
  qr_enabled: boolean
  manual_checkin_enabled: boolean
  auto_certificate_generation: boolean
  attendance_notifications: boolean
  real_time_updates: boolean
  bulk_checkin_enabled: boolean
}

export default function AttendancePage() {
  const [stats, setStats] = useState<AttendanceStats | null>(null)
  const [settings, setSettings] = useState<AttendanceSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const { user, isAdmin } = useAuth()
  const { toast } = useToast()

  // Helper function to get cookie value
  const getCookie = (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!isAdmin()) {
        setLoading(false)
        return
      }

      try {
        const token = getCookie('auth_token')
        if (!token) {
          throw new Error('No authentication token found')
        }

        // Fetch stats and settings in parallel
        const [statsResponse, settingsResponse] = await Promise.all([
          fetch('/api/admin/attendance/stats', {
            headers: { 'Authorization': `Bearer ${token}` },
          }),
          fetch('/api/admin/attendance/settings', {
            headers: { 'Authorization': `Bearer ${token}` },
          })
        ])

        if (statsResponse.ok) {
          const statsData = await statsResponse.json()
          setStats(statsData.stats)
        } else {
          // Mock data for now
          setStats({
            totalEvents: 45,
            eventsWithTracking: 38,
            totalAttendees: 1234,
            checkedInAttendees: 987,
            qrScansToday: 156,
            averageCheckInTime: "2.3 min"
          })
        }

        if (settingsResponse.ok) {
          const settingsData = await settingsResponse.json()
          setSettings(settingsData.settings)
        } else {
          // Mock data for now
          setSettings({
            qr_enabled: true,
            manual_checkin_enabled: true,
            auto_certificate_generation: false,
            attendance_notifications: true,
            real_time_updates: true,
            bulk_checkin_enabled: true
          })
        }
      } catch (error) {
        console.error("Error fetching attendance data:", error)
        toast({
          title: "Error",
          description: "Failed to fetch attendance data",
          variant: "destructive",
        })
        // Set mock data
        setStats({
          totalEvents: 45,
          eventsWithTracking: 38,
          totalAttendees: 1234,
          checkedInAttendees: 987,
          qrScansToday: 156,
          averageCheckInTime: "2.3 min"
        })
        setSettings({
          qr_enabled: true,
          manual_checkin_enabled: true,
          auto_certificate_generation: false,
          attendance_notifications: true,
          real_time_updates: true,
          bulk_checkin_enabled: true
        })
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [isAdmin])

  const handleSettingChange = async (key: keyof AttendanceSettings, value: boolean) => {
    if (!settings) return

    const updatedSettings = { ...settings, [key]: value }
    setSettings(updatedSettings)

    setSaving(true)
    try {
      const token = getCookie('auth_token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch('/api/admin/attendance/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(updatedSettings),
      })

      if (!response.ok) {
        throw new Error('Failed to update attendance settings')
      }

      toast({
        title: "Success",
        description: "Attendance settings updated successfully",
      })
    } catch (error: any) {
      console.error("Error updating attendance settings:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to update attendance settings",
        variant: "destructive",
      })
      // Revert the change
      setSettings(settings)
    } finally {
      setSaving(false)
    }
  }

  if (!isAdmin()) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-muted-foreground">Access Denied</h1>
          <p className="text-muted-foreground mt-2">You need admin privileges to access attendance management.</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <h1 className="text-2xl font-bold">Attendance Management</h1>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-6 w-24 bg-muted rounded"></div>
                <div className="h-8 w-32 bg-muted rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-4 w-full bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  const attendanceRate = stats ? Math.round((stats.checkedInAttendees / stats.totalAttendees) * 100) : 0

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Attendance Management</h1>
          <p className="text-muted-foreground">Configure attendance tracking and QR code settings</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Data
          </Button>
          <Button variant="outline">
            <Upload className="mr-2 h-4 w-4" />
            Import Data
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Events</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalEvents}</div>
              <p className="text-xs text-muted-foreground">
                {stats.eventsWithTracking} with tracking
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Attendance Rate</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{attendanceRate}%</div>
              <p className="text-xs text-muted-foreground">
                {stats.checkedInAttendees} of {stats.totalAttendees}
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">QR Scans Today</CardTitle>
              <QrCode className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.qrScansToday}</div>
              <p className="text-xs text-muted-foreground">
                Scans today
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Check-in Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.averageCheckInTime}</div>
              <p className="text-xs text-muted-foreground">
                Per attendee
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Attendance Settings */}
      {settings && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Attendance Settings
            </CardTitle>
            <CardDescription>
              Configure how attendance tracking works across your platform
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">QR Code Check-in</Label>
                  <div className="text-sm text-muted-foreground">
                    Enable QR code scanning for attendance
                  </div>
                </div>
                <Switch
                  checked={settings.qr_enabled}
                  onCheckedChange={(checked) => handleSettingChange('qr_enabled', checked)}
                  disabled={saving}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">Manual Check-in</Label>
                  <div className="text-sm text-muted-foreground">
                    Allow manual attendance marking
                  </div>
                </div>
                <Switch
                  checked={settings.manual_checkin_enabled}
                  onCheckedChange={(checked) => handleSettingChange('manual_checkin_enabled', checked)}
                  disabled={saving}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">Auto Certificate Generation</Label>
                  <div className="text-sm text-muted-foreground">
                    Generate certificates on check-in
                  </div>
                </div>
                <Switch
                  checked={settings.auto_certificate_generation}
                  onCheckedChange={(checked) => handleSettingChange('auto_certificate_generation', checked)}
                  disabled={saving}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">Attendance Notifications</Label>
                  <div className="text-sm text-muted-foreground">
                    Send notifications on check-in
                  </div>
                </div>
                <Switch
                  checked={settings.attendance_notifications}
                  onCheckedChange={(checked) => handleSettingChange('attendance_notifications', checked)}
                  disabled={saving}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">Real-time Updates</Label>
                  <div className="text-sm text-muted-foreground">
                    Live attendance updates
                  </div>
                </div>
                <Switch
                  checked={settings.real_time_updates}
                  onCheckedChange={(checked) => handleSettingChange('real_time_updates', checked)}
                  disabled={saving}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-base">Bulk Check-in</Label>
                  <div className="text-sm text-muted-foreground">
                    Enable bulk attendance marking
                  </div>
                </div>
                <Switch
                  checked={settings.bulk_checkin_enabled}
                  onCheckedChange={(checked) => handleSettingChange('bulk_checkin_enabled', checked)}
                  disabled={saving}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-blue-500">
                  <BarChart3 className="h-5 w-5 text-white" />
                </div>
                <div>
                  <CardTitle className="text-base">View Analytics</CardTitle>
                  <CardDescription className="text-sm">
                    Detailed attendance analytics
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-green-500">
                  <QrCode className="h-5 w-5 text-white" />
                </div>
                <div>
                  <CardTitle className="text-base">QR Code Generator</CardTitle>
                  <CardDescription className="text-sm">
                    Generate event QR codes
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-purple-500">
                  <Users className="h-5 w-5 text-white" />
                </div>
                <div>
                  <CardTitle className="text-base">Attendee Management</CardTitle>
                  <CardDescription className="text-sm">
                    Manage event attendees
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>
        </div>
      </div>
    </div>
  )
}
