"use client"

import React from "react"
import { ArrowLeft } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Stepper, type StepperStep } from "@/components/ui/stepper"
import MainNav from "@/components/main-nav"
import { useRouter } from "next/navigation"
import { type EventType } from "@/contexts/event-context"

interface RegistrationLayoutProps {
  children: React.ReactNode
  event: EventType
  steps: StepperStep[]
  currentStepIndex: number
  onStepClick?: (stepIndex: number) => void
  showEventDetails?: boolean
  totalAmount?: number
  totalTickets?: number
}

export function RegistrationLayout({
  children,
  event,
  steps,
  currentStepIndex,
  onStepClick,
  showEventDetails = true,
  totalAmount = 0,
  totalTickets = 0
}: RegistrationLayoutProps) {
  const router = useRouter()

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <MainNav />

      {/* Corporate Hero Section */}
      <section className="relative w-full py-12 md:py-16 lg:py-20 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div className="w-full h-full" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>

        <div className="container relative mx-auto px-4 md:px-6">
          <div className="flex flex-col space-y-8">
            {/* Header with Back Button */}
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                className="mr-4 text-white hover:bg-white/10 hover:text-white"
                onClick={() => router.back()}
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Event
              </Button>
            </div>

            {/* Main Heading */}
            <div className="text-center space-y-4">
              <h1 className="text-3xl font-bold tracking-tight text-white sm:text-4xl md:text-5xl lg:text-6xl">
                Event
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> Registration</span>
              </h1>
              <p className="mx-auto max-w-2xl text-lg text-gray-300 md:text-xl leading-relaxed">
                Complete your registration in a few simple steps
              </p>
            </div>

            {/* Registration Progress */}
            <div className="max-w-4xl mx-auto w-full">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                <h3 className="text-lg font-semibold mb-6 text-center text-white">Registration Progress</h3>
                <Stepper
                  steps={steps}
                  currentStep={currentStepIndex}
                  onStepClick={onStepClick}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Event Details Section */}
      {showEventDetails && (
        <div className="bg-gradient-to-r from-slate-50 to-blue-50 border-b border-gray-200 shadow-sm">
          <div className="container mx-auto px-4 py-6">
            <div className="max-w-4xl mx-auto">
              <div className="bg-white rounded-xl border border-gray-200 p-6 shadow-lg">
                <h2 className="text-xl font-bold text-gray-900 mb-4 bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                  {event.title}
                </h2>
                <div className="grid gap-4 md:grid-cols-4 text-sm">
                  <div className="space-y-1">
                    <p className="font-semibold text-gray-700">Event Date</p>
                    <p className="text-gray-600">
                      {new Date(event.start_date).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="font-semibold text-gray-700">Location</p>
                    <p className="text-gray-600">{event.location}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="font-semibold text-gray-700">Total Amount</p>
                    <p className="text-purple-600 font-bold text-lg">RM {totalAmount.toFixed(2)}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="font-semibold text-gray-700">Total Tickets</p>
                    <p className="text-blue-600 font-bold text-lg">{totalTickets} ticket{totalTickets !== 1 ? 's' : ''}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {children}
        </div>
      </main>
    </div>
  )
}
