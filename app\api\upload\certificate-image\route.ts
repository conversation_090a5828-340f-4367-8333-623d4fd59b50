import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import jwt from "jsonwebtoken";

export async function POST(request: Request) {
  try {
    console.log("Certificate image upload started");

    // Get token from Authorization header
    const authHeader = request.headers.get("Authorization");
    console.log("Auth header present:", !!authHeader);

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      console.log("Missing or invalid auth header");
      return NextResponse.json(
        { error: "Authorization token required" },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7); // Remove "Bearer " prefix
    console.log("Token extracted, length:", token.length);

    // Verify JWT token
    let decoded: any;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET || "your-secret-key");
      console.log("Token verified, userId:", decoded.userId);
    } catch (jwtError) {
      console.error("JWT verification failed:", jwtError);
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 401 }
      );
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const imageType = formData.get("type") as string; // 'background' or 'field'

    console.log("File received:", {
      name: file?.name,
      size: file?.size,
      type: file?.type,
      imageType
    });

    if (!file) {
      console.log("No file provided in form data");
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.type.startsWith("image/")) {
      console.log("Invalid file type:", file.type);
      return NextResponse.json(
        { error: "File must be an image" },
        { status: 400 }
      );
    }

    // Validate file size (max 10MB before compression)
    if (file.size > 10 * 1024 * 1024) {
      console.log("File too large:", file.size);
      return NextResponse.json(
        { error: "File size must be less than 10MB" },
        { status: 400 }
      );
    }

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin();
    console.log("Supabase admin client created");

    // Generate unique filename
    const fileExt = file.name.split(".").pop() || "jpg";
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2);
    const fileName = `${decoded.userId}-${timestamp}-${randomString}.${fileExt}`;

    // Determine storage bucket and path based on image type
    const storageBucket = 'certificates';
    const storagePath = imageType === 'background'
      ? `bg/${fileName}`
      : `img/${fileName}`;

    console.log("Generated storage bucket:", storageBucket);
    console.log("Generated storage path:", storagePath);

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = new Uint8Array(arrayBuffer);
    console.log("File converted to buffer, size:", buffer.length);

    // Upload to Supabase storage
    console.log("Starting upload to Supabase storage...");
    const { error: uploadError, data } = await supabaseAdmin.storage
      .from(storageBucket)
      .upload(storagePath, buffer, {
        contentType: file.type,
        upsert: false, // Don't overwrite existing files
      });

    if (uploadError) {
      console.error("Upload error:", uploadError);
      return NextResponse.json(
        { error: `Failed to upload image: ${uploadError.message}` },
        { status: 500 }
      );
    }

    console.log("Upload successful:", data);

    // Get public URL
    const { data: urlData } = supabaseAdmin.storage
      .from(storageBucket)
      .getPublicUrl(storagePath);

    return NextResponse.json({
      success: true,
      url: urlData.publicUrl,
      path: storagePath,
      fileName: fileName,
      originalSize: file.size,
    });

  } catch (error: any) {
    console.error("Upload certificate image error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
