"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  CreditCard,
  Users,
  Award,
  BarChart3,
  FileText,
  Activity,
  DollarSign
} from "lucide-react"

import { useAuth } from "@/contexts/auth-context"
import Link from "next/link"

type SubscriptionStats = {
  totalPlans: number
  activePlans: number
  totalSubscribers: number
  monthlyRevenue: number
  certificatesIssued: number
  attendanceTracked: number
  webhookCalls: number
  analyticsViews: number
}

export default function SubscriptionsOverviewPage() {
  const [stats, setStats] = useState<SubscriptionStats | null>(null)
  const [loading, setLoading] = useState(true)
  const { isAdmin } = useAuth()

  // Helper function to get cookie value
  const getCookie = (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  useEffect(() => {
    const fetchStats = async () => {
      if (!isAdmin()) {
        setLoading(false)
        return
      }

      try {
        const token = getCookie('auth_token')
        if (!token) {
          throw new Error('No authentication token found')
        }

        const response = await fetch('/api/admin/subscription-stats', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        })

        if (!response.ok) {
          throw new Error('Failed to fetch subscription statistics')
        }

        const data = await response.json()
        setStats(data.stats)
      } catch (error) {
        console.error("Error fetching subscription stats:", error)
        // Set mock data for now since API doesn't exist yet
        setStats({
          totalPlans: 4,
          activePlans: 4,
          totalSubscribers: 156,
          monthlyRevenue: 4250,
          certificatesIssued: 89,
          attendanceTracked: 234,
          webhookCalls: 45,
          analyticsViews: 1234
        })
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [isAdmin])

  if (!isAdmin()) {
    return (
      <div className="p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-muted-foreground">Access Denied</h1>
          <p className="text-muted-foreground mt-2">You need admin privileges to access subscription management.</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <h1 className="text-2xl font-bold">Subscription Management</h1>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-6 w-24 bg-muted rounded"></div>
                <div className="h-8 w-32 bg-muted rounded"></div>
              </CardHeader>
              <CardContent>
                <div className="h-4 w-full bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  const quickActions = [
    {
      title: "Manage Plans",
      description: "Create and edit subscription plans",
      href: "/dashboard/settings/subscriptions",
      icon: CreditCard,
      color: "bg-blue-500",
    },
    {
      title: "Certificates",
      description: "Manage certificate templates and generation",
      href: "/dashboard/subscriptions/certificates",
      icon: Award,
      color: "bg-green-500",
    },
    {
      title: "Attendance",
      description: "Configure attendance tracking features",
      href: "/dashboard/subscriptions/attendance",
      icon: Users,
      color: "bg-purple-500",
    },
    {
      title: "Analytics",
      description: "View subscription analytics and insights",
      href: "/dashboard/subscriptions/analytics",
      icon: BarChart3,
      color: "bg-indigo-500",
    },
    {
      title: "Reports",
      description: "Generate and manage subscription reports",
      href: "/dashboard/subscriptions/reports",
      icon: FileText,
      color: "bg-red-500",
    },
  ]

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Subscription Management</h1>
          <p className="text-muted-foreground">Manage subscription plans, features, and analytics</p>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Plans</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalPlans}</div>
              <p className="text-xs text-muted-foreground">
                {stats.activePlans} active
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Subscribers</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalSubscribers}</div>
              <p className="text-xs text-muted-foreground">
                Active subscriptions
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">RM{stats.monthlyRevenue.toFixed(2)}</div>
              <p className="text-xs text-muted-foreground">
                This month
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Activity</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.webhookCalls}</div>
              <p className="text-xs text-muted-foreground">
                Webhook calls today
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Quick Actions */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Quick Actions</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {quickActions.map((action) => (
            <Link key={action.title} href={action.href}>
              <Card className="hover:shadow-md transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${action.color}`}>
                      <action.icon className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-base">{action.title}</CardTitle>
                      <CardDescription className="text-sm">
                        {action.description}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
              </Card>
            </Link>
          ))}
        </div>
      </div>
    </div>
  )
}
