"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { Eye, EyeOff, UserPlus, CreditCard, AlertCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { AuthFormLayout } from "@/components/auth-form-layout"
import { useToast } from "@/hooks/use-toast"

export default function RegisterPage() {
  const [fullName, setFullName] = useState("")
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<any>(null)
  const [isRegistrationEnabled, setIsRegistrationEnabled] = useState<boolean | null>(null)
  const [checkingSettings, setCheckingSettings] = useState(true)
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()

  // Check if registration is enabled
  useEffect(() => {
    const checkRegistrationEnabled = async () => {
      try {
        const response = await fetch('/api/app-settings/check-feature', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ feature: 'register' }),
        })

        if (response.ok) {
          const data = await response.json()
          setIsRegistrationEnabled(data.enabled)
        } else {
          // Default to enabled if check fails
          setIsRegistrationEnabled(true)
        }
      } catch (error) {
        console.error('Error checking registration status:', error)
        // Default to enabled if check fails
        setIsRegistrationEnabled(true)
      } finally {
        setCheckingSettings(false)
      }
    }

    checkRegistrationEnabled()
  }, [])

  // Check for plan selection from URL or sessionStorage
  useEffect(() => {
    // First check URL parameter (for backward compatibility)
    const planParam = searchParams.get('plan')
    if (planParam) {
      try {
        const plan = JSON.parse(decodeURIComponent(planParam))
        setSelectedPlan(plan)
        return
      } catch (error) {
        console.error('Error parsing plan parameter:', error)
      }
    }

    // Then check sessionStorage
    const storedPlan = sessionStorage.getItem('selectedPlan')
    if (storedPlan) {
      try {
        const plan = JSON.parse(storedPlan)
        setSelectedPlan(plan)
      } catch (error) {
        console.error('Error parsing stored plan:', error)
        sessionStorage.removeItem('selectedPlan')
      }
    }
  }, [searchParams])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (password !== confirmPassword) {
      toast({
        title: "Passwords do not match",
        description: "Please make sure your passwords match.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    try {
      // Register the user
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          name: fullName,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to create account")
      }

      toast({
        title: "Registration successful",
        description: "Your account has been created. You can now log in.",
      })

      // Clear the stored plan from sessionStorage after successful registration
      if (selectedPlan) {
        sessionStorage.removeItem('selectedPlan')
        // Store plan for after login
        sessionStorage.setItem('planAfterLogin', JSON.stringify(selectedPlan))
      }

      router.push("/auth/login")
    } catch (error: any) {
      toast({
        title: "Registration failed",
        description: error.message || "There was an error creating your account. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Show loading state while checking settings
  if (checkingSettings) {
    return (
      <AuthFormLayout
        title="Create an account"
        description="Loading..."
        footer={
          <div className="text-center text-sm">
            Already have an account?{" "}
            <Link href="/auth/login" className="font-medium text-primary underline-offset-4 hover:underline">
              Login
            </Link>
          </div>
        }
      >
        <CardContent className="space-y-4">
          <div className="flex items-center justify-center py-8">
            <svg
              className="animate-spin h-8 w-8 text-primary"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </div>
        </CardContent>
      </AuthFormLayout>
    )
  }

  // Show disabled message if registration is disabled
  if (isRegistrationEnabled === false) {
    return (
      <AuthFormLayout
        title="Registration Currently Disabled"
        description="New user registration is currently disabled by the administrator"
        footer={
          <div className="text-center text-sm">
            Already have an account?{" "}
            <Link href="/auth/login" className="font-medium text-primary underline-offset-4 hover:underline">
              Login
            </Link>
          </div>
        }
      >
        <CardContent className="space-y-4">
          <div className="rounded-lg bg-orange-50 p-4 text-orange-800 dark:bg-orange-950 dark:text-orange-300">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              <div>
                <h3 className="font-medium">Registration Disabled</h3>
                <p className="text-sm mt-1">
                  New user registration is currently disabled. Please contact the administrator if you need access.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button asChild className="w-full" variant="outline">
            <Link href="/auth/login">
              Go to Login
            </Link>
          </Button>
        </CardFooter>
      </AuthFormLayout>
    )
  }

  return (
    <AuthFormLayout
      title="Create an account"
      description={
        selectedPlan
          ? `Create your account to subscribe to the ${selectedPlan.planName} plan`
          : "Enter your information to create an account"
      }
      footer={
        <div className="text-center text-sm">
          Already have an account?{" "}
          <Link href="/auth/login" className="font-medium text-primary underline-offset-4 hover:underline">
            Login
          </Link>
        </div>
      }
    >
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {/* Selected Plan Display */}
          {selectedPlan && (
            <Card className="border-primary">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">Selected Plan</CardTitle>
                  {selectedPlan.planName === 'Basic' && (
                    <Badge className="bg-primary text-primary-foreground">Popular</Badge>
                  )}
                </div>
                <div className="flex items-baseline">
                  <span className="text-2xl font-bold">RM{selectedPlan.price}</span>
                  {selectedPlan.price > 0 && <span className="text-muted-foreground ml-1">/month</span>}
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {selectedPlan.planName} plan - You'll be able to subscribe after creating your account.
                </p>
              </CardContent>
            </Card>
          )}

          <div className="space-y-2">
            <Label htmlFor="full-name">Full Name</Label>
            <Input
              id="full-name"
              type="text"
              placeholder="John Doe"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Password</Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-full px-3 py-2 text-muted-foreground"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                <span className="sr-only">{showPassword ? "Hide password" : "Show password"}</span>
              </Button>
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="confirm-password">Confirm Password</Label>
            <Input
              id="confirm-password"
              type={showPassword ? "text" : "password"}
              placeholder="••••••••"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
            />
          </div>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <Button type="submit" className="w-full" disabled={isLoading}>
            {isLoading ? (
              <div className="flex items-center">
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Creating account...
              </div>
            ) : (
              <div className="flex items-center">
                <UserPlus className="mr-2 h-4 w-4" />
                Sign Up
              </div>
            )}
          </Button>
        </CardFooter>
      </form>
    </AuthFormLayout>
  )
}
