import { getSupabaseAdmin } from "../supabase"
import type { PaymentGatewayConfig, PaymentGatewayType } from "./types"

// Default payment gateway configuration for fallback
const defaultPaymentGateways: PaymentGatewayConfig[] = [
  {
    id: "toyyibpay",
    name: "ToyyibPay",
    type: "toyyibpay",
    enabled: true,
    description: "Pay with ToyyibPay - Malaysia's leading payment gateway",
    fields: {}
  }
]

// Get active payment gateways
export async function getActivePaymentGateways(): Promise<PaymentGatewayConfig[]> {
  try {
    const supabaseAdmin = getSupabaseAdmin()

    // Fetch enabled payment gateways from database
    const { data, error } = await supabaseAdmin
      .from("payment_gateway_settings")
      .select("id, gateway_name, is_enabled, is_test_mode, configuration, test_configuration, live_configuration")
      .eq("is_enabled", true)
      .order("display_order", { ascending: true })

    if (error) {
      // Fallback to default gateways if database query fails
      return defaultPaymentGateways.filter((gateway) => gateway.enabled)
    }

    if (!data || data.length === 0) {
      // Fallback to default gateways if no gateways in database
      return defaultPaymentGateways.filter((gateway) => gateway.enabled)
    }

    // Transform database records to PaymentGatewayConfig format
    const gateways: PaymentGatewayConfig[] = data.map((gateway) => {
      // Determine gateway type from name
      let type: PaymentGatewayType = "billplz" // default
      const gatewayName = gateway.gateway_name.toLowerCase()
      if (gatewayName.includes("toyyibpay")) {
        type = "toyyibpay"
      } else if (gatewayName.includes("chip")) {
        type = "chip"
      } else if (gatewayName.includes("stripe")) {
        type = "stripe"
      } else if (gatewayName.includes("billplz")) {
        type = "billplz"
      }

      // Get configuration based on test mode
      const config = gateway.is_test_mode ? gateway.test_configuration : gateway.live_configuration

      return {
        id: gateway.id,
        name: gateway.gateway_name,
        type: type,
        enabled: gateway.is_enabled,
        description: gateway.configuration?.description || `Pay with ${gateway.gateway_name}`,
        fields: config || {}
      }
    })

    return gateways

  } catch (error) {
    // Fallback to default gateways on any error
    return defaultPaymentGateways.filter((gateway) => gateway.enabled)
  }
}

// Get payment gateway by ID
export async function getPaymentGateway(id: string): Promise<PaymentGatewayConfig | null> {
  try {
    const supabaseAdmin = getSupabaseAdmin()

    // Fetch specific payment gateway from database
    const { data, error } = await supabaseAdmin
      .from("payment_gateway_settings")
      .select("id, gateway_name, is_enabled, is_test_mode, configuration, test_configuration, live_configuration")
      .eq("id", id)
      .eq("is_enabled", true)
      .single()

    if (error || !data) {
      // Fallback to default gateways
      const gateway = defaultPaymentGateways.find((g) => g.id === id)
      return gateway || null
    }

    // Determine gateway type from name
    let type: PaymentGatewayType = "billplz" // default
    const gatewayName = data.gateway_name.toLowerCase()
    if (gatewayName.includes("toyyibpay")) {
      type = "toyyibpay"
    } else if (gatewayName.includes("chip")) {
      type = "chip"
    } else if (gatewayName.includes("stripe")) {
      type = "stripe"
    } else if (gatewayName.includes("billplz")) {
      type = "billplz"
    }

    // Get configuration based on test mode
    const config = data.is_test_mode ? data.test_configuration : data.live_configuration

    return {
      id: data.id,
      name: data.gateway_name,
      type: type,
      enabled: data.is_enabled,
      description: data.configuration?.description || `Pay with ${data.gateway_name}`,
      fields: config || {}
    }

  } catch (error) {
    // Fallback to default gateways on any error
    const gateway = defaultPaymentGateways.find((g) => g.id === id)
    return gateway || null
  }
}
