import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"

interface QRRouteParams {
  params: Promise<{
    slug: string
  }>
}

/**
 * GET /api/events/[slug]/qr
 * Returns QR code data for an event
 */
export async function GET(request: Request, { params }: QRRouteParams) {
  try {
    const { slug } = await params

    if (!slug) {
      return NextResponse.json(
        { error: "Event slug is required" },
        { status: 400 }
      )
    }

    // Get the base URL from the request
    const url = new URL(request.url)
    const baseUrl = `${url.protocol}//${url.host}`

    // Validate that the event exists and is published
    const supabaseAdmin = getSupabaseAdmin()
    const { data: event, error } = await supabaseAdmin
      .from("events")
      .select("slug, title, is_published")
      .eq("slug", slug)
      .eq("is_published", true)
      .single()

    if (error || !event) {
      return NextResponse.json(
        { error: "Event not found or not published" },
        { status: 404 }
      )
    }

    // Generate the short URL
    const shortUrl = `${baseUrl}/${slug}`
    const fullUrl = `${baseUrl}/events/${slug}`

    return NextResponse.json({
      success: true,
      event: {
        slug: event.slug,
        title: event.title,
      },
      urls: {
        short: shortUrl,
        full: fullUrl,
      },
      qr: {
        // QR code data that can be used with QR code libraries
        data: shortUrl,
        size: 256,
        format: "PNG",
      },
    })
  } catch (error: any) {
    console.error("Error generating QR code data:", error)
    return NextResponse.json(
      { error: error.message || "Failed to generate QR code data" },
      { status: 500 }
    )
  }
}
