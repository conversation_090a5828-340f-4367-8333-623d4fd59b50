import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check, CreditCard } from "lucide-react"
import SubscriptionPlanCard from "@/components/subscription-plan-card"
import { PageLayout } from "@/components/page-layout"
import { HeroSection } from "@/components/hero-section"
import { createClient } from "@/lib/supabase/server"

export const metadata = {
  title: "Subscription Plans | mTicket.my - Event Management Platform",
  description: "Choose the perfect subscription plan for your event management needs",
}

// Fetch subscription plans from database
async function getSubscriptionPlans() {
  const supabase = createClient()

  const { data: plans, error } = await supabase
    .from('subscription_plans')
    .select('*')
    .eq('is_active', true)
    .order('price', { ascending: true })

  if (error) {
    console.error("Error fetching subscription plans:", error)
    return []
  }

  // Transform the data to match the expected format
  return plans.map(plan => ({
    id: plan.id,
    name: plan.name,
    price: parseFloat(plan.price.toString()),
    description: plan.description,
    features: Array.isArray(plan.features) ? plan.features : [],
    max_events: plan.max_events,
    max_attendees_per_event: plan.max_attendees_per_event,
    is_popular: plan.is_popular,
    certificates_enabled: plan.certificates_enabled,
    attendance_enabled: plan.attendance_enabled,
    webhooks_enabled: plan.webhooks_enabled,
    analytics_enabled: plan.analytics_enabled,
    reports_enabled: plan.reports_enabled,
  }))
}

export default async function SubscriptionsPage() {
  const subscriptionPlans = await getSubscriptionPlans()

  return (
    <PageLayout>
      <HeroSection
        title="Choose Your Plan"
        description="Select the perfect subscription plan for your event management needs. Start with our free plan or upgrade for advanced features."
      />

        {/* Subscription Plans Section */}
        <div className="container mx-auto py-12 px-4">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Subscription Plans</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Whether you're organizing small workshops or large conferences,
                we have a plan that fits your needs and budget.
              </p>
            </div>

            {/* Plans Grid */}
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4 mb-12">
              {subscriptionPlans.map((plan) => (
                <SubscriptionPlanCard key={plan.id} plan={plan} />
              ))}
            </div>

            {/* Features Comparison */}
            <div className="mt-16">
              <h3 className="text-2xl font-bold text-center mb-8">Compare Plans</h3>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-200 dark:border-gray-700">
                  <thead>
                    <tr className="bg-gray-50 dark:bg-gray-800">
                      <th className="border border-gray-200 dark:border-gray-700 p-4 text-left">Features</th>
                      {subscriptionPlans.map((plan) => (
                        <th key={plan.id} className="border border-gray-200 dark:border-gray-700 p-4 text-center">
                          {plan.name}
                          {plan.is_popular && (
                            <Badge className="ml-2 bg-primary text-primary-foreground">Popular</Badge>
                          )}
                        </th>
                      ))}
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="border border-gray-200 dark:border-gray-700 p-4 font-medium">Monthly Price</td>
                      {subscriptionPlans.map((plan) => (
                        <td key={plan.id} className="border border-gray-200 dark:border-gray-700 p-4 text-center">
                          <span className="text-2xl font-bold">RM{plan.price.toFixed(2)}</span>
                          {plan.price > 0 && <span className="text-sm text-muted-foreground">/month</span>}
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="border border-gray-200 dark:border-gray-700 p-4 font-medium">Max Events</td>
                      {subscriptionPlans.map((plan) => (
                        <td key={plan.id} className="border border-gray-200 dark:border-gray-700 p-4 text-center">
                          {plan.max_events ? plan.max_events : "Unlimited"}
                        </td>
                      ))}
                    </tr>
                    <tr>
                      <td className="border border-gray-200 dark:border-gray-700 p-4 font-medium">Max Attendees per Event</td>
                      {subscriptionPlans.map((plan) => (
                        <td key={plan.id} className="border border-gray-200 dark:border-gray-700 p-4 text-center">
                          {plan.max_attendees_per_event ? plan.max_attendees_per_event : "Unlimited"}
                        </td>
                      ))}
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            {/* FAQ Section */}
            <div className="mt-16">
              <h3 className="text-2xl font-bold text-center mb-8">Frequently Asked Questions</h3>
              <div className="max-w-3xl mx-auto space-y-6">
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                  <h4 className="font-semibold mb-2">Can I change my plan anytime?</h4>
                  <p className="text-muted-foreground">
                    Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.
                  </p>
                </div>
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                  <h4 className="font-semibold mb-2">What payment methods do you accept?</h4>
                  <p className="text-muted-foreground">
                    We accept all major credit cards, online banking, and e-wallet payments through our secure payment gateways.
                  </p>
                </div>
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                  <h4 className="font-semibold mb-2">Is there a free trial?</h4>
                  <p className="text-muted-foreground">
                    Our Free plan allows you to explore the platform with basic features. You can upgrade anytime to access advanced features.
                  </p>
                </div>
                <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                  <h4 className="font-semibold mb-2">Do you offer refunds?</h4>
                  <p className="text-muted-foreground">
                    We offer a 30-day money-back guarantee for all paid plans. Contact our support team for assistance.
                  </p>
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="text-center mt-16">
              <h3 className="text-2xl font-bold mb-4">Ready to Get Started?</h3>
              <p className="text-lg text-muted-foreground mb-8">
                Join thousands of event organizers who trust mTicket.my for their event management needs.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild size="lg">
                  <Link href="/auth/register">Start Free Trial</Link>
                </Button>
                <Button asChild variant="outline" size="lg">
                  <Link href="/contact">Contact Sales</Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
    </PageLayout>
  )
}
