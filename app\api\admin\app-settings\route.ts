import { NextRequest, NextResponse } from "next/server"
import { getAppSettings, updateAppSettings } from "@/lib/app-settings"
import { getSupabaseAdmin } from "@/lib/supabase"
import jwt from "jsonwebtoken"

// Helper function to verify admin access
async function verifyAdminAccess(request: NextRequest): Promise<{ isAdmin: boolean; userId?: string; error?: string }> {
  try {
    // Get auth token from cookies
    const authToken = request.cookies.get("auth_token")?.value

    if (!authToken) {
      return { isAdmin: false, error: "No auth token found" }
    }

    // Decode the JWT token
    const decoded = jwt.decode(authToken) as any
    if (!decoded || !decoded.userId) {
      return { isAdmin: false, error: "Invalid auth token" }
    }

    // Get user details with role from database
    const supabaseAdmin = getSupabaseAdmin()
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .select(`
        id,
        role_id,
        user_roles!inner(role_name)
      `)
      .eq('id', decoded.userId)
      .single()

    if (userError || !userData) {
      return { isAdmin: false, error: "User not found" }
    }

    // Check if user has admin role
    // user_roles is joined as an object, not an array due to the inner join
    const isAdmin = (userData.user_roles as any)?.role_name === "admin"

    return {
      isAdmin,
      userId: decoded.userId,
      error: isAdmin ? undefined : "Admin access required"
    }
  } catch (error) {
    console.error("Error verifying admin access:", error)
    return { isAdmin: false, error: "Failed to verify admin access" }
  }
}

/**
 * GET /api/admin/app-settings
 * Fetch app settings (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    // Verify admin access
    const { isAdmin, error } = await verifyAdminAccess(request)

    if (!isAdmin) {
      return NextResponse.json(
        { error: error || "Unauthorized" },
        { status: 401 }
      )
    }

    // Fetch app settings
    const settings = await getAppSettings()

    if (!settings) {
      return NextResponse.json(
        { error: "Failed to fetch app settings" },
        { status: 500 }
      )
    }

    return NextResponse.json({ settings })
  } catch (error: any) {
    console.error("Error in app settings GET API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/admin/app-settings
 * Update app settings (admin only)
 */
export async function PUT(request: NextRequest) {
  try {
    // Verify admin access
    const { isAdmin, userId, error } = await verifyAdminAccess(request)

    if (!isAdmin || !userId) {
      return NextResponse.json(
        { error: error || "Unauthorized" },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const {
      login_enabled,
      register_enabled,
      password_reset_enabled,
      api_enabled,
      maintenance_mode,
      maintenance_message
    } = body

    // Update app settings
    const updatedSettings = await updateAppSettings({
      login_enabled: Boolean(login_enabled),
      register_enabled: Boolean(register_enabled),
      password_reset_enabled: Boolean(password_reset_enabled),
      api_enabled: Boolean(api_enabled),
      maintenance_mode: Boolean(maintenance_mode),
      maintenance_message: maintenance_message || 'We are currently performing maintenance. We will be back soon!'
    }, userId)

    if (!updatedSettings) {
      return NextResponse.json(
        { error: "Failed to update app settings" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      message: "App settings updated successfully",
      settings: updatedSettings
    })
  } catch (error: any) {
    console.error("Error in app settings PUT API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
