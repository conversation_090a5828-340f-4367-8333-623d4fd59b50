// Test script to simulate payment return callback
const testPaymentReturn = async () => {
  const paymentData = {
    status_id: "1", // Success
    billcode: "b2t7zbtp", // Existing transaction with metadata
    order_id: "payment_1748301522186_kmhfhg",
    msg: "ok",
    transaction_id: "TP2505274276991261",
    timestamp: new Date().toISOString(),
    url_params: {
      status_id: "1",
      billcode: "b2t7zbtp",
      order_id: "payment_1748301522186_kmhfhg",
      msg: "ok",
      transaction_id: "TP2505274276991261"
    }
  };

  try {
    console.log("Testing payment return with data:", paymentData);
    
    const response = await fetch('http://localhost:3000/api/payments/save-return-data', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(paymentData)
    });

    const result = await response.json();
    
    console.log("Response status:", response.status);
    console.log("Response data:", JSON.stringify(result, null, 2));
    
    if (response.ok) {
      console.log("✅ SUCCESS: Payment return processed successfully");
      if (result.user_created) {
        console.log("✅ New user created with ID:", result.user_id);
      }
      if (result.registrations_created > 0) {
        console.log("✅ Created", result.registrations_created, "registrations");
      }
      if (result.receipt_token) {
        console.log("✅ Receipt token generated:", result.receipt_token.substring(0, 20) + "...");
      }
    } else {
      console.log("❌ ERROR: Payment return failed");
    }
    
  } catch (error) {
    console.error("❌ ERROR:", error.message);
  }
};

// Run the test
testPaymentReturn();
