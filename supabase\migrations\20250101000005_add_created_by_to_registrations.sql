-- Add created_by column to registrations table for group registration support
-- This allows tracking who created the registration (useful for group registrations)

DO $$ 
BEGIN
  -- Add created_by column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'registrations' AND column_name = 'created_by') THEN
    ALTER TABLE registrations ADD COLUMN created_by UUID REFERENCES users(id) ON DELETE SET NULL;
  END IF;
END $$;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_registrations_created_by ON registrations(created_by);

-- Update existing registrations to set created_by = user_id for backward compatibility
UPDATE registrations 
SET created_by = user_id 
WHERE created_by IS NULL;

-- Update RLS policies to include created_by access
DROP POLICY IF EXISTS "Enable read access for own registrations" ON registrations;

-- Create new policy that allows access to registrations where user is either the attendee or creator
CREATE POLICY "Enable read access for own registrations" 
ON registrations FOR SELECT 
USING (auth.uid() = user_id OR auth.uid() = created_by);

-- Add comment to document the purpose
COMMENT ON COLUMN registrations.created_by IS 'User who created this registration (for group registrations)';
