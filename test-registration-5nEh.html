<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Registration for Event 5nEh</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .event-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .ticket-selection {
            margin-bottom: 20px;
        }
        .ticket-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .ticket-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        input[type="number"] {
            width: 60px;
            padding: 5px;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            text-align: center;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .total {
            font-size: 18px;
            font-weight: bold;
            text-align: right;
            margin: 20px 0;
            padding: 15px;
            background: #e9ecef;
            border-radius: 6px;
        }
        .test-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .test-info h3 {
            margin-top: 0;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test Registration for Event 5nEh</h1>
            <p>Ipoh Fun Run Day 2025</p>
        </div>

        <div class="test-info">
            <h3>🎯 Test Objectives</h3>
            <ul>
                <li><strong>Required Fields Count:</strong> Should show 6 (4 standard + 2 custom)</li>
                <li><strong>Red Asterisks:</strong> All required fields should have red asterisks (*)</li>
                <li><strong>Custom Fields:</strong> Emergency Contact Number & T-Shirt Size</li>
            </ul>
        </div>

        <div class="event-info">
            <h3>📍 Event Details</h3>
            <p><strong>Event:</strong> Ipoh Fun Run Day 2025</p>
            <p><strong>Location:</strong> Ipoh City Center, Perak, Malaysia</p>
            <p><strong>Date:</strong> March 15, 2026</p>
            <p><strong>Custom Fields:</strong> 2 required fields (Emergency Contact Number, T-Shirt Size)</p>
        </div>

        <div class="ticket-selection">
            <h3>🎫 Select Tickets</h3>
            
            <div class="ticket-item">
                <div>
                    <strong>General Admission</strong><br>
                    <small>Standard ticket for the fun run</small><br>
                    <span style="color: #28a745; font-weight: bold;">RM 30.00</span>
                </div>
                <div class="ticket-controls">
                    <label for="general-qty">Quantity:</label>
                    <input type="number" id="general-qty" min="0" max="10" value="1">
                </div>
            </div>
        </div>

        <div class="total" id="total">
            Total: RM 30.00 (1 ticket)
        </div>

        <div style="text-align: center;">
            <button class="btn" onclick="proceedToRegistration()">
                🚀 Proceed to Registration Form
            </button>
        </div>

        <div id="status"></div>
    </div>

    <script>
        const ticketTypes = {
            general: {
                id: 'general',
                name: 'General Admission',
                price: 30,
                description: 'Standard ticket for the fun run'
            }
        };

        function updateTotal() {
            const quantity = parseInt(document.getElementById('general-qty').value) || 0;
            const total = quantity * ticketTypes.general.price;
            
            document.getElementById('total').textContent = 
                `Total: RM ${total.toFixed(2)} (${quantity} ticket${quantity !== 1 ? 's' : ''})`;
        }

        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function proceedToRegistration() {
            const quantity = parseInt(document.getElementById('general-qty').value) || 0;
            
            if (quantity === 0) {
                showStatus('Please select at least one ticket.', 'error');
                return;
            }

            const selectedTickets = [{
                ticketType: ticketTypes.general,
                quantity: quantity
            }];

            // Store selected tickets in session storage
            sessionStorage.setItem('selectedTickets', JSON.stringify(selectedTickets));
            
            showStatus('✅ Tickets selected! Redirecting to registration form...', 'success');
            
            // Redirect to registration form after a short delay
            setTimeout(() => {
                window.location.href = 'http://localhost:3001/events/5nEh/register';
            }, 1500);
        }

        // Update total when quantity changes
        document.getElementById('general-qty').addEventListener('input', updateTotal);

        // Initialize
        updateTotal();
    </script>
</body>
</html>
