import { supabase } from "@/lib/supabase"
import { getSupabaseAdmin } from "@/lib/supabase"
import type { AppSettings } from "@/lib/db/supabase-schema"

/**
 * Fetch app settings from the database
 * @returns Promise with the app settings
 */
export async function getAppSettings(): Promise<AppSettings | null> {
  try {
    // Use admin client for reliable access
    const supabaseAdmin = getSupabaseAdmin()
    const { data, error } = await supabaseAdmin
      .from('app_settings')
      .select('*')
      .single()

    if (error) {
      console.error('Error fetching app settings:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Exception fetching app settings:', error)
    return null
  }
}

/**
 * Update app settings in the database
 * @param settings Partial app settings to update
 * @param userId ID of the user making the update
 * @returns Promise with the updated app settings
 */
export async function updateAppSettings(
  settings: Partial<AppSettings>,
  userId: string
): Promise<AppSettings | null> {
  try {
    const supabaseAdmin = getSupabaseAdmin()

    // Get the current settings ID
    const { data: currentSettings } = await supabaseAdmin
      .from('app_settings')
      .select('id')
      .single()

    if (!currentSettings) {
      console.error('No app settings found to update')
      return null
    }

    const { data, error } = await supabaseAdmin
      .from('app_settings')
      .update({
        ...settings,
        updated_at: new Date().toISOString(),
        updated_by: userId,
      })
      .eq('id', currentSettings.id)
      .select('*')
      .single()

    if (error) {
      console.error('Error updating app settings:', error)
      return null
    }

    return data
  } catch (error) {
    console.error('Exception updating app settings:', error)
    return null
  }
}

/**
 * Sanitize maintenance message for security
 * @param message Raw message from database
 * @returns Sanitized message safe for display
 */
function sanitizeMaintenanceMessage(message: string): string {
  if (!message || typeof message !== 'string') {
    return 'We are currently performing maintenance. We will be back soon!'
  }

  // Security: Remove HTML tags, limit length, and sanitize content
  return message
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .replace(/[<>'"&]/g, '') // Remove potentially dangerous characters
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/data:/gi, '') // Remove data: protocol
    .substring(0, 500) // Limit message length
    .trim()
}

/**
 * Check if maintenance mode is enabled
 * @returns Promise with maintenance mode status and message
 */
export async function getMaintenanceStatus(): Promise<{
  enabled: boolean
  message: string
}> {
  try {
    const settings = await getAppSettings()

    if (!settings) {
      return {
        enabled: false,
        message: 'We are currently performing maintenance. We will be back soon!'
      }
    }

    return {
      enabled: settings.maintenance_mode,
      message: sanitizeMaintenanceMessage(settings.maintenance_message)
    }
  } catch (error) {
    console.error('Exception checking maintenance status:', error)
    return {
      enabled: false,
      message: 'We are currently performing maintenance. We will be back soon!'
    }
  }
}

/**
 * Check if a specific feature is enabled
 * @param feature The feature to check
 * @returns Promise with feature status
 */
export async function isFeatureEnabled(
  feature: 'login' | 'register' | 'password_reset' | 'api'
): Promise<boolean> {
  try {
    const settings = await getAppSettings()

    if (!settings) {
      return true // Default to enabled if no settings found
    }

    switch (feature) {
      case 'login':
        return settings.login_enabled
      case 'register':
        return settings.register_enabled
      case 'password_reset':
        return settings.password_reset_enabled
      case 'api':
        return settings.api_enabled
      default:
        return true
    }
  } catch (error) {
    console.error('Exception checking feature status:', error)
    return true // Default to enabled on error
  }
}
