import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { createPayment, getActivePaymentGateways } from "@/lib/payment-gateway";
import { logActivity } from "@/lib/activity-logger";

/**
 * POST /api/payments/create-public
 * Creates a payment for event registration without requiring authentication
 * This is for public event registration where users don't need to be logged in
 * Only creates registrations after successful payment callback
 */
export async function POST(request: Request) {
  try {
    console.log("Public Payment Create API: Starting request");

    // Parse request body
    const body = await request.json();
    const { 
      event_id, 
      participants, 
      main_contact, 
      selected_tickets,
      amount, 
      currency = 'MYR', 
      description,
      gateway_id 
    } = body;

    // Validate required fields
    if (!event_id || !participants || !Array.isArray(participants) || participants.length === 0) {
      return NextResponse.json(
        { error: "Event ID and participants are required" },
        { status: 400 }
      );
    }

    if (!main_contact || !main_contact.name || !main_contact.email) {
      return NextResponse.json(
        { error: "Main contact information is required" },
        { status: 400 }
      );
    }

    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: "Valid payment amount is required" },
        { status: 400 }
      );
    }

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin();

    // Verify the event exists and is published
    console.log("Public Payment Create API: Fetching event with ID:", event_id);
    const { data: event, error: eventError } = await supabaseAdmin
      .from("events")
      .select("id, title, slug, is_published, max_participants")
      .eq("id", event_id)
      .single();

    if (eventError || !event) {
      console.error("Public Payment Create API: Error fetching event:", eventError);
      return NextResponse.json(
        { error: "Event not found" },
        { status: 404 }
      );
    }

    if (!event.is_published) {
      return NextResponse.json(
        { error: "Event is not published" },
        { status: 400 }
      );
    }

    // Get active payment gateways
    const paymentGateways = await getActivePaymentGateways();
    if (!paymentGateways || paymentGateways.length === 0) {
      return NextResponse.json(
        { error: "No payment gateways available" },
        { status: 500 }
      );
    }

    // Use the specified gateway or the first available one
    let selectedGateway = paymentGateways[0];
    if (gateway_id) {
      const requestedGateway = paymentGateways.find(gw => gw.id === gateway_id);
      if (requestedGateway) {
        selectedGateway = requestedGateway;
      } else {
        return NextResponse.json(
          { error: "Requested payment gateway not available" },
          { status: 400 }
        );
      }
    }

    // Generate a unique reference ID for this payment
    const paymentReference = `payment_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

    // Store registration data temporarily for the payment callback
    const registrationData = {
      event_id,
      participants,
      main_contact,
      selected_tickets,
      payment_reference: paymentReference,
      is_public_registration: true // Flag to indicate this is a public registration
    };

    // Create payment request
    const paymentRequest = {
      amount: parseFloat(amount.toString()),
      currency: currency,
      description: description || `Registration for ${event.title}`,
      customer_email: main_contact.email,
      customer_name: main_contact.name,
      reference_id: paymentReference,
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/return`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/return`,
    };

    console.log("Public Payment Create API: Creating payment with gateway:", selectedGateway.id);

    // Create payment with selected gateway
    const paymentResponse = await createPayment(selectedGateway.id, paymentRequest);

    if (!paymentResponse.success) {
      console.error("Payment creation failed:", paymentResponse.error);
      return NextResponse.json(
        {
          success: false,
          error: paymentResponse.error || "Failed to create payment",
        },
        { status: 500 }
      );
    }

    // Create transaction record with registration data stored in metadata
    const transactionData = {
      id: crypto.randomUUID(),
      user_id: null, // No user ID for public registrations initially
      registration_id: null, // Will be set after successful payment
      gateway_id: selectedGateway.id,
      transaction_type: 'public_registration_payment',
      amount: parseFloat(amount.toString()),
      currency: currency,
      status: 'processing',
      gateway_transaction_id: paymentResponse.transaction_id,
      gateway_response: paymentResponse,
      metadata: registrationData, // Store registration data here
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: transactionRecord, error: transactionError } = await supabaseAdmin
      .from("transactions")
      .insert([transactionData])
      .select()
      .single();

    if (transactionError) {
      console.error("Failed to create transaction record:", transactionError);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to create transaction record",
        },
        { status: 500 }
      );
    }

    // Log activity (without user_id since this is public)
    await logActivity({
      action: "public_payment_initiated",
      user_id: null,
      details: {
        event_id: event_id,
        amount: amount,
        currency: currency,
        payment_gateway: selectedGateway.id,
        transaction_id: paymentResponse.transaction_id,
        participants_count: participants.length,
        main_contact_email: main_contact.email
      },
    });

    console.log("Public Payment Create API: Payment created successfully");

    return NextResponse.json({
      success: true,
      payment_url: paymentResponse.payment_url,
      transaction_id: paymentResponse.transaction_id,
      gateway: selectedGateway.name,
      message: "Payment initiated successfully"
    });

  } catch (error) {
    console.error("Public Payment Create API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
