import { createClient } from "@/lib/supabase/server"
import { ADMIN_ROLES, isAdminUser } from "@/lib/role-utils"

/**
 * Check if a user has access to a specific subscription feature
 */
export async function hasSubscriptionFeature(
  userId: string,
  feature: 'webhooks_enabled' | 'certificates_enabled' | 'attendance_enabled' | 'analytics_enabled' | 'reports_enabled',
  userRoleName?: string
): Promise<boolean> {
  // Admin users always have access to all features
  if (isAdminUser(userRoleName)) {
    return true
  }

  try {
    const supabase = createClient()

    // Get user's subscription details
    const { data: user, error: userError } = await supabase
      .from('users')
      .select(`
        *,
        user_subscription (
          subscription_type
        )
      `)
      .eq('id', userId)
      .single()

    if (userError || !user) {
      return false
    }

    // Check subscription plan for feature access
    if (user.user_subscription && user.user_subscription.length > 0) {
      const subscription = user.user_subscription[0]

      const { data: plan, error: planError } = await supabase
        .from('subscription_plans')
        .select(feature)
        .eq('name', subscription.subscription_type)
        .eq('is_active', true)
        .single()

      if (!planError && plan) {
        return plan[feature] || false
      }
    }

    return false
  } catch (error) {
    console.error(`Error checking subscription feature ${feature}:`, error)
    return false
  }
}

/**
 * Get user's current subscription plan with all features
 */
export async function getUserSubscriptionPlan(userId: string, userRoleName?: string) {
  try {
    const supabase = createClient()

    // Get user's subscription details
    const { data: user, error: userError } = await supabase
      .from('users')
      .select(`
        *,
        user_subscription (
          subscription_type,
          start_date,
          end_date,
          is_active
        )
      `)
      .eq('id', userId)
      .single()

    if (userError || !user) {
      return null
    }

    // For admin users, return a virtual "admin" plan with all features enabled
    if (isAdminUser(userRoleName)) {
      return {
        id: 'admin-plan',
        name: 'Admin',
        price: 0,
        description: 'Administrative access with all features',
        features: ['All features enabled'],
        max_events: null,
        max_attendees_per_event: null,
        is_popular: false,
        is_active: true,
        certificates_enabled: true,
        attendance_enabled: true,
        webhooks_enabled: true,
        analytics_enabled: true,
        reports_enabled: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    }

    // Get subscription plan details if user has an active subscription
    let planDetails = null
    if (user.user_subscription && user.user_subscription.length > 0) {
      const subscription = user.user_subscription[0]

      const { data: plan, error: planError } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('name', subscription.subscription_type)
        .eq('is_active', true)
        .single()

      if (!planError && plan) {
        planDetails = plan
      }
    }

    // If no plan found, check if user has a role-based plan
    if (!planDetails && user.role_name) {
      const { data: plan, error: planError } = await supabase
        .from('subscription_plans')
        .select('*')
        .ilike('name', user.role_name)
        .eq('is_active', true)
        .single()

      if (!planError && plan) {
        planDetails = plan
      }
    }

    // Default to free plan if no plan found for regular users
    if (!planDetails) {
      const { data: freePlan, error: freePlanError } = await supabase
        .from('subscription_plans')
        .select('*')
        .eq('name', 'Free')
        .eq('is_active', true)
        .single()

      if (!freePlanError && freePlan) {
        planDetails = freePlan
      }
    }

    return planDetails
  } catch (error) {
    console.error('Error getting user subscription plan:', error)
    return null
  }
}

/**
 * Check multiple subscription features at once
 */
export async function checkSubscriptionFeatures(
  userId: string,
  features: Array<'webhooks_enabled' | 'certificates_enabled' | 'attendance_enabled' | 'analytics_enabled' | 'reports_enabled'>,
  userRoleName?: string
): Promise<Record<string, boolean>> {
  // Admin users always have access to all features
  if (isAdminUser(userRoleName)) {
    return features.reduce((acc, feature) => {
      acc[feature] = true
      return acc
    }, {} as Record<string, boolean>)
  }

  const plan = await getUserSubscriptionPlan(userId, userRoleName)

  return features.reduce((acc, feature) => {
    acc[feature] = plan?.[feature] || false
    return acc
  }, {} as Record<string, boolean>)
}

/**
 * Middleware function to check subscription feature access in API routes
 */
export async function requireSubscriptionFeature(
  userId: string,
  feature: 'webhooks_enabled' | 'certificates_enabled' | 'attendance_enabled' | 'analytics_enabled' | 'reports_enabled',
  userRoleName?: string
): Promise<{ hasAccess: boolean; error?: string }> {
  const hasAccess = await hasSubscriptionFeature(userId, feature, userRoleName)

  if (!hasAccess) {
    return {
      hasAccess: false,
      error: `${feature.replace('_enabled', '').replace('_', ' ')} access not available in your subscription plan`
    }
  }

  return { hasAccess: true }
}
