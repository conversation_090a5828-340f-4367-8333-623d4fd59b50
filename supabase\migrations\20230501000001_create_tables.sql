-- Create event_categories table if it doesn't exist
CREATE TABLE IF NOT EXISTS event_categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  color TEXT DEFAULT '#6366f1',
  icon TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default categories
INSERT INTO event_categories (name, description, color, icon) VALUES
  ('conference', 'Professional conferences and conventions', '#3b82f6', 'users'),
  ('workshop', 'Hands-on learning workshops', '#10b981', 'wrench'),
  ('seminar', 'Educational seminars and lectures', '#8b5cf6', 'book-open'),
  ('webinar', 'Online webinars and virtual events', '#f59e0b', 'monitor'),
  ('training', 'Professional training sessions', '#ef4444', 'graduation-cap'),
  ('other', 'Other types of events', '#6b7280', 'calendar')
ON CONFLICT (name) DO NOTHING;

-- Create events table if it doesn't exist
CREATE TABLE IF NOT EXISTS events (
  id UUID PRIMARY KEY,
  title TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  description TEXT,
  location TEXT,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  price DECIMAL(10, 2),
  max_participants INTEGER,
  current_participants INTEGER DEFAULT 0,
  status TEXT DEFAULT 'draft',
  created_by UUID NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  payment_gateway_id TEXT,
  category_id UUID REFERENCES event_categories(id),
  category TEXT, -- Keep for backward compatibility
  is_featured BOOLEAN DEFAULT FALSE,
  image_url TEXT
);

-- Create registrations table if it doesn't exist
CREATE TABLE IF NOT EXISTS registrations (
  id UUID PRIMARY KEY,
  event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
  user_id UUID NOT NULL,
  attendee_name TEXT NOT NULL,
  attendee_email TEXT NOT NULL,
  attendee_phone TEXT,
  registration_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  payment_status TEXT DEFAULT 'pending',
  payment_amount DECIMAL(10, 2),
  payment_date TIMESTAMP WITH TIME ZONE,
  payment_method TEXT,
  ticket_issued BOOLEAN DEFAULT FALSE,
  ticket_code TEXT,
  check_in_status TEXT DEFAULT 'not_checked_in',
  check_in_time TIMESTAMP WITH TIME ZONE
);

-- Create certificates table if it doesn't exist
CREATE TABLE IF NOT EXISTS certificates (
  id UUID PRIMARY KEY,
  event_id UUID NOT NULL REFERENCES events(id) ON DELETE CASCADE,
  registration_id UUID REFERENCES registrations(id) ON DELETE SET NULL,
  user_id UUID NOT NULL,
  participant_id UUID NOT NULL,
  participant_name TEXT NOT NULL,
  template_id TEXT NOT NULL,
  issue_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  certificate_url TEXT,
  verification_code TEXT UNIQUE,
  is_revoked BOOLEAN DEFAULT FALSE
);
