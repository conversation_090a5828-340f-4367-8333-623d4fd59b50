-- Add WYSIWYG support for event descriptions
-- This migration adds description_html column to support rich text content

-- Add description_html column for rich text content
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'description_html') THEN
    ALTER TABLE events ADD COLUMN description_html TEXT;
  END IF;
END $$;

-- Create index for better performance on HTML content searches
CREATE INDEX IF NOT EXISTS idx_events_description_html ON events USING GIN (to_tsvector('english', description_html));

-- Add comment to document the purpose
COMMENT ON COLUMN events.description_html IS 'Rich text HTML content for event description. Falls back to description field if null.';

-- Update existing events to copy plain text description to HTML field if HTML is empty
-- This ensures backward compatibility
UPDATE events 
SET description_html = CASE 
  WHEN description IS NOT NULL AND description != '' THEN 
    '<p>' || REPLACE(REPLACE(description, E'\n\n', '</p><p>'), E'\n', '<br>') || '</p>'
  ELSE NULL 
END
WHERE description_html IS NULL AND description IS NOT NULL;
