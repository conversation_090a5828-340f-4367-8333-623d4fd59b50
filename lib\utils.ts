import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(dateInput: string | Date, includeTime = false): string {
  if (!dateInput) return "N/A"

  const date = dateInput instanceof Date ? dateInput : new Date(dateInput)

  if (isNaN(date.getTime())) {
    return "Invalid date"
  }

  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
  }

  if (includeTime) {
    options.hour = "2-digit"
    options.minute = "2-digit"
  }

  return new Intl.DateTimeFormat("en-US", options).format(date)
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat("en-MY", {
    style: "currency",
    currency: "MYR",
    minimumFractionDigits: 2,
  }).format(amount)
}

export function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, "")
    .replace(/\s+/g, "-")
    .replace(/-+/g, "-")
    .trim()
}

/**
 * Random slug generation for events
 * Uses characters [0-9, a-z, A-Z] for random slug generation
 */
const BASE62_CHARS = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"

/**
 * Generates a random slug using Base62 characters
 * - Starts with 4-character slugs
 * - Moves to 5-character slugs when 4-character space is exhausted
 * - Characters are randomly selected from [0-9, a-z, A-Z]
 */
export function generateRandomSlug(existingSlugs: Set<string> = new Set()): string {
  const maxFourCharCombinations = Math.pow(62, 4) // 14,776,336 possible 4-char combinations

  // Determine if we should use 4 or 5 characters
  // If we have too many 4-character slugs, switch to 5 characters
  const fourCharSlugsCount = Array.from(existingSlugs).filter(slug => slug.length === 4).length
  const shouldUseFiveChars = fourCharSlugsCount >= maxFourCharCombinations * 0.9 // Switch at 90% capacity

  const length = shouldUseFiveChars ? 5 : 4
  let slug: string
  let attempts = 0
  const maxAttempts = 1000 // Prevent infinite loops

  do {
    slug = ""
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * BASE62_CHARS.length)
      slug += BASE62_CHARS[randomIndex]
    }
    attempts++

    if (attempts >= maxAttempts) {
      // If we can't find a unique 4-char slug, try 5-char
      if (length === 4) {
        return generateRandomSlug(existingSlugs) // This will use 5 chars due to the condition above
      }
      throw new Error("Unable to generate unique slug after maximum attempts")
    }
  } while (existingSlugs.has(slug))

  return slug
}

/**
 * Extract initials from event title (first letter of each word)
 */
export function getEventInitials(title: string): string {
  if (!title) return "EV"

  const words = title.trim().split(/\s+/)
  if (words.length === 1) {
    // Single word - take first two characters
    return words[0].substring(0, 2).toUpperCase()
  }

  // Multiple words - take first letter of each word, max 3 letters
  return words
    .slice(0, 3)
    .map(word => word.charAt(0))
    .join("")
    .toUpperCase()
}

/**
 * Generate consistent gradient colors based on title hash
 * Returns both Tailwind classes and inline styles for better compatibility
 */
export function generateEventGradient(title: string): {
  className: string;
  style: React.CSSProperties
} {
  if (!title) {
    return {
      className: "bg-gradient-to-br from-purple-500 to-blue-600",
      style: { background: "linear-gradient(to bottom right, #8b5cf6, #2563eb)" }
    }
  }

  // Simple hash function to generate consistent colors
  let hash = 0
  for (let i = 0; i < title.length; i++) {
    const char = title.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }

  // Predefined gradient combinations with both Tailwind and CSS
  const gradients = [
    {
      className: "bg-gradient-to-br from-purple-500 to-blue-600",
      style: { background: "linear-gradient(to bottom right, #8b5cf6, #2563eb)" }
    },
    {
      className: "bg-gradient-to-br from-blue-500 to-teal-600",
      style: { background: "linear-gradient(to bottom right, #3b82f6, #0d9488)" }
    },
    {
      className: "bg-gradient-to-br from-teal-500 to-green-600",
      style: { background: "linear-gradient(to bottom right, #14b8a6, #16a34a)" }
    },
    {
      className: "bg-gradient-to-br from-green-500 to-yellow-600",
      style: { background: "linear-gradient(to bottom right, #22c55e, #ca8a04)" }
    },
    {
      className: "bg-gradient-to-br from-yellow-500 to-orange-600",
      style: { background: "linear-gradient(to bottom right, #eab308, #ea580c)" }
    },
    {
      className: "bg-gradient-to-br from-orange-500 to-red-600",
      style: { background: "linear-gradient(to bottom right, #f97316, #dc2626)" }
    },
    {
      className: "bg-gradient-to-br from-red-500 to-pink-600",
      style: { background: "linear-gradient(to bottom right, #ef4444, #db2777)" }
    },
    {
      className: "bg-gradient-to-br from-pink-500 to-purple-600",
      style: { background: "linear-gradient(to bottom right, #ec4899, #9333ea)" }
    },
    {
      className: "bg-gradient-to-br from-indigo-500 to-purple-600",
      style: { background: "linear-gradient(to bottom right, #6366f1, #9333ea)" }
    },
    {
      className: "bg-gradient-to-br from-cyan-500 to-blue-600",
      style: { background: "linear-gradient(to bottom right, #06b6d4, #2563eb)" }
    },
    {
      className: "bg-gradient-to-br from-emerald-500 to-teal-600",
      style: { background: "linear-gradient(to bottom right, #10b981, #0d9488)" }
    },
    {
      className: "bg-gradient-to-br from-amber-500 to-orange-600",
      style: { background: "linear-gradient(to bottom right, #f59e0b, #ea580c)" }
    }
  ]

  // Use hash to select gradient consistently
  const index = Math.abs(hash) % gradients.length
  return gradients[index]
}

/**
 * Validates if a string is a valid slug format
 */
export function isValidSlug(slug: string): boolean {
  if (!slug || (slug.length !== 4 && slug.length !== 5)) {
    return false
  }

  return slug.split('').every(char => BASE62_CHARS.includes(char))
}

export function truncateText(text: string, maxLength: number): string {
  if (!text) return ""
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + "..."
}

export function formatTime(dateInput: string | Date): string {
  if (!dateInput) return "N/A"

  const date = dateInput instanceof Date ? dateInput : new Date(dateInput)

  if (isNaN(date.getTime())) {
    return "Invalid time"
  }

  const options: Intl.DateTimeFormatOptions = {
    hour: "2-digit",
    minute: "2-digit",
  }

  return new Intl.DateTimeFormat("en-US", options).format(date)
}

/**
 * Format date for event card badge
 * Returns object with day number, month name, and day of week
 */
export function formatDateBadge(dateInput: string | Date): {
  day: string;
  month: string;
  dayOfWeek: string;
} {
  if (!dateInput) {
    return { day: "N/A", month: "N/A", dayOfWeek: "N/A" }
  }

  const date = dateInput instanceof Date ? dateInput : new Date(dateInput)

  if (isNaN(date.getTime())) {
    return { day: "N/A", month: "N/A", dayOfWeek: "N/A" }
  }

  const day = date.getDate().toString()
  const month = date.toLocaleDateString("en-US", { month: "short" })
  const dayOfWeek = date.toLocaleDateString("en-US", { weekday: "short" })

  return { day, month, dayOfWeek }
}

/**
 * Format event date and time information
 * Returns formatted date/time info based on event duration
 */
export function formatEventDateTime(startDate: string | Date, endDate: string | Date): {
  isSingleDay: boolean;
  totalDays: number;
  startDateFormatted: string;
  endDateFormatted: string;
  startTimeFormatted: string;
  endTimeFormatted: string;
  displayText: string;
} {
  if (!startDate || !endDate) {
    return {
      isSingleDay: true,
      totalDays: 1,
      startDateFormatted: "N/A",
      endDateFormatted: "N/A",
      startTimeFormatted: "N/A",
      endTimeFormatted: "N/A",
      displayText: "Date and time not available"
    }
  }

  const start = startDate instanceof Date ? startDate : new Date(startDate)
  const end = endDate instanceof Date ? endDate : new Date(endDate)

  if (isNaN(start.getTime()) || isNaN(end.getTime())) {
    return {
      isSingleDay: true,
      totalDays: 1,
      startDateFormatted: "Invalid date",
      endDateFormatted: "Invalid date",
      startTimeFormatted: "Invalid time",
      endTimeFormatted: "Invalid time",
      displayText: "Invalid date/time"
    }
  }

  // Check if it's the same day
  const startDateOnly = new Date(start.getFullYear(), start.getMonth(), start.getDate())
  const endDateOnly = new Date(end.getFullYear(), end.getMonth(), end.getDate())
  const isSingleDay = startDateOnly.getTime() === endDateOnly.getTime()

  // Calculate total days
  const timeDiff = endDateOnly.getTime() - startDateOnly.getTime()
  const totalDays = Math.ceil(timeDiff / (1000 * 60 * 60 * 24)) + 1

  // Format dates and times
  const startDateFormatted = start.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric"
  })
  const endDateFormatted = end.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric"
  })
  const startTimeFormatted = start.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit"
  })
  const endTimeFormatted = end.toLocaleTimeString("en-US", {
    hour: "2-digit",
    minute: "2-digit"
  })

  // Create display text
  let displayText: string
  if (isSingleDay) {
    displayText = `${startDateFormatted}, ${startTimeFormatted} - ${endTimeFormatted}`
  } else {
    displayText = `${startDateFormatted} ${startTimeFormatted} - ${endDateFormatted} ${endTimeFormatted} (${totalDays} days)`
  }

  return {
    isSingleDay,
    totalDays,
    startDateFormatted,
    endDateFormatted,
    startTimeFormatted,
    endTimeFormatted,
    displayText
  }
}

/**
 * Helper function to get the best available image for an event
 * Prioritizes images array over image_url
 */
export function getEventDisplayImage(event: any): { url: string; alt: string } | null {
  // Check if event has images array
  if (event.images && Array.isArray(event.images) && event.images.length > 0) {
    // Find primary image or use first image
    const primaryImage = event.images.find((img: any) => img.is_primary) || event.images[0]
    return {
      url: primaryImage.url,
      alt: primaryImage.alt_text || event.title || "Event image"
    }
  }

  // Fallback to image_url
  if (event.image_url) {
    return {
      url: event.image_url,
      alt: event.title || "Event image"
    }
  }

  // No image available
  return null
}

/**
 * Strip HTML tags from a string and return plain text
 * Useful for displaying WYSIWYG content in plain text contexts like search or card previews
 */
export function stripHtmlTags(html: string): string {
  if (!html) return ''

  // For server-side rendering, use a simple regex approach
  if (typeof document === 'undefined') {
    return html
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ') // Replace &nbsp; with space
      .replace(/&amp;/g, '&') // Replace &amp; with &
      .replace(/&lt;/g, '<') // Replace &lt; with <
      .replace(/&gt;/g, '>') // Replace &gt; with >
      .replace(/&quot;/g, '"') // Replace &quot; with "
      .replace(/&#39;/g, "'") // Replace &#39; with '
      .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
      .trim()
  }

  // For client-side, use DOM parsing for better accuracy
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = html
  return (tempDiv.textContent || tempDiv.innerText || '').trim()
}
