import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { hashPassword } from "@/lib/auth";
import { isFeatureEnabled } from "@/lib/app-settings";
import { logActivity, ActivityCategory } from "@/utils/activity-logger";
import crypto from "crypto";

export async function POST(request: Request) {
  try {
    // Check if password reset is enabled
    const passwordResetEnabled = await isFeatureEnabled('password_reset');
    if (!passwordResetEnabled) {
      return NextResponse.json(
        { error: "Password reset is currently disabled" },
        { status: 403 }
      );
    }

    const { email, token, newPassword } = await request.json();

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin();

    if (email && !token && !newPassword) {
      // Step 1: Send reset email (generate reset token)

      // Check if user exists
      const { data: userData, error: userError } = await supabaseAdmin
        .from("users")
        .select("id, email, full_name")
        .eq("email", email.toLowerCase())
        .single();

      if (userError || !userData) {
        // Don't reveal if user exists or not for security
        return NextResponse.json({
          success: true,
          message: "If an account with that email exists, a reset link has been sent."
        });
      }

      // Generate reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour from now

      // Store reset token in database (you might want to create a password_resets table)
      // For now, we'll store it in the users table
      const { error: updateError } = await supabaseAdmin
        .from("users")
        .update({
          reset_token: resetToken,
          reset_token_expiry: resetTokenExpiry.toISOString()
        })
        .eq("id", userData.id);

      if (updateError) {
        console.error("Error storing reset token:", updateError);
        return NextResponse.json(
          { error: "Failed to generate reset token" },
          { status: 500 }
        );
      }

      // TODO: Send email with reset link
      // For now, we'll just return success
      // In a real app, you would send an email with a link like:
      // https://yourapp.com/auth/reset-password?token=${resetToken}

      // In production, send email with reset link instead of logging
      // Reset link: ${process.env.NEXT_PUBLIC_APP_URL}/auth/reset-password?token=${resetToken}

      // Log the password reset request activity
      try {
        await logActivity({
          userId: userData.id,
          action: "password_reset_request",
          entityType: "user",
          entityId: userData.id,
          category: ActivityCategory.AUTH,
          details: {
            email: userData.email,
            reset_method: "email",
          },
        });
      } catch (logError) {
        // Don't fail password reset if logging fails
      }

      return NextResponse.json({
        success: true,
        message: "If an account with that email exists, a reset link has been sent.",
        // Remove this in production - only for development
        ...(process.env.NODE_ENV === "development" && {
          resetToken,
          resetLink: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/auth/reset-password?token=${resetToken}`
        })
      });

    } else if (token && newPassword) {
      // Step 2: Reset password with token

      if (newPassword.length < 8) {
        return NextResponse.json(
          { error: "Password must be at least 8 characters long" },
          { status: 400 }
        );
      }

      // Find user with valid reset token
      const { data: userData, error: userError } = await supabaseAdmin
        .from("users")
        .select("id, email, reset_token, reset_token_expiry")
        .eq("reset_token", token)
        .single();

      if (userError || !userData) {
        return NextResponse.json(
          { error: "Invalid or expired reset token" },
          { status: 400 }
        );
      }

      // Check if token is expired
      if (!userData.reset_token_expiry || new Date() > new Date(userData.reset_token_expiry)) {
        return NextResponse.json(
          { error: "Reset token has expired" },
          { status: 400 }
        );
      }

      // Hash new password
      const passwordHash = await hashPassword(newPassword);

      // Update password and clear reset token
      const { error: updateError } = await supabaseAdmin
        .from("users")
        .update({
          password_hash: passwordHash,
          reset_token: null,
          reset_token_expiry: null
        })
        .eq("id", userData.id);

      if (updateError) {
        console.error("Error updating password:", updateError);
        return NextResponse.json(
          { error: "Failed to update password" },
          { status: 500 }
        );
      }

      // Log the password reset completion activity
      try {
        await logActivity({
          userId: userData.id,
          action: "password_reset_complete",
          entityType: "user",
          entityId: userData.id,
          category: ActivityCategory.AUTH,
          details: {
            email: userData.email,
            reset_method: "token",
          },
        });
      } catch (logError) {
        // Don't fail password reset if logging fails
        console.error("Error logging password reset completion activity:", logError);
      }

      return NextResponse.json({
        success: true,
        message: "Password has been reset successfully"
      });

    } else {
      return NextResponse.json(
        { error: "Invalid request parameters" },
        { status: 400 }
      );
    }

  } catch (error: any) {
    console.error("Reset password error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
