import { getSupabaseAdmin } from '../supabase'
import type { ActivityLogParams, ActivityCategory, ActivityLog, ActivityLogFilter, ActivityLogResponse } from './types'

/**
 * Core activity logging functionality
 */

/**
 * Log an activity in the system
 */
export async function logActivity(params: ActivityLogParams): Promise<void> {
  try {
    const supabaseAdmin = getSupabaseAdmin()

    const {
      userId,
      action,
      entityType,
      entityId,
      category,
      details = {},
      ipAddress,
      userAgent
    } = params

    await supabaseAdmin.from("activity_logs").insert([
      {
        user_id: userId,
        action,
        entity_type: entityType,
        entity_id: entityId,
        category,
        details,
        ip_address: ipAddress,
        user_agent: userAgent,
        created_at: new Date().toISOString(),
      },
    ])
  } catch (error) {
    // Log to monitoring system instead of console
  }
}

/**
 * Get activity logs with filtering and pagination
 */
export async function getActivityLogs(filter: ActivityLogFilter = {}): Promise<ActivityLogResponse> {
  try {
    const supabaseAdmin = getSupabaseAdmin()

    let query = supabaseAdmin
      .from("activity_logs")
      .select(`
        *,
        user:user_id(id, full_name, email),
        event:entity_id(id, title),
        organization:entity_id(id, name)
      `, { count: 'exact' })
      .order('created_at', { ascending: false })

    // Apply filters
    if (filter.userId) {
      query = query.eq('user_id', filter.userId)
    }

    if (filter.category) {
      query = query.eq('category', filter.category)
    }

    if (filter.entityType) {
      query = query.eq('entity_type', filter.entityType)
    }

    if (filter.entityId) {
      query = query.eq('entity_id', filter.entityId)
    }

    if (filter.action) {
      query = query.eq('action', filter.action)
    }

    if (filter.startDate) {
      query = query.gte('created_at', filter.startDate)
    }

    if (filter.endDate) {
      query = query.lte('created_at', filter.endDate)
    }

    // Apply pagination
    const limit = filter.limit || 50
    const offset = filter.offset || 0

    query = query.range(offset, offset + limit - 1)

    const { data, error, count } = await query

    if (error) {
      console.error('Error fetching activity logs:', error)
      return { logs: [], total: 0, hasMore: false }
    }

    const total = count || 0
    const hasMore = offset + limit < total

    return {
      logs: data || [],
      total,
      hasMore
    }
  } catch (error) {
    console.error('Error in getActivityLogs:', error)
    return { logs: [], total: 0, hasMore: false }
  }
}

/**
 * Get activity logs for a specific entity
 */
export async function getEntityActivityLogs(
  entityType: string,
  entityId: string,
  limit: number = 20
): Promise<ActivityLog[]> {
  try {
    const result = await getActivityLogs({
      entityType,
      entityId,
      limit
    })

    return result.logs
  } catch (error) {
    console.error('Error getting entity activity logs:', error)
    return []
  }
}

/**
 * Get activity logs for a specific user
 */
export async function getUserActivityLogs(
  userId: string,
  limit: number = 50
): Promise<ActivityLog[]> {
  try {
    const result = await getActivityLogs({
      userId,
      limit
    })

    return result.logs
  } catch (error) {
    console.error('Error getting user activity logs:', error)
    return []
  }
}

/**
 * Get recent activity logs (last 24 hours)
 */
export async function getRecentActivityLogs(limit: number = 100): Promise<ActivityLog[]> {
  try {
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)

    const result = await getActivityLogs({
      startDate: yesterday.toISOString(),
      limit
    })

    return result.logs
  } catch (error) {
    console.error('Error getting recent activity logs:', error)
    return []
  }
}

/**
 * Get activity statistics
 */
export async function getActivityStats(days: number = 7): Promise<{
  totalActivities: number
  activitiesByCategory: Record<string, number>
  activitiesByDay: Array<{ date: string; count: number }>
}> {
  try {
    const supabaseAdmin = getSupabaseAdmin()

    const startDate = new Date()
    startDate.setDate(startDate.getDate() - days)

    // Get total activities
    const { count: totalActivities } = await supabaseAdmin
      .from('activity_logs')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDate.toISOString())

    // Get activities by category
    const { data: categoryData } = await supabaseAdmin
      .from('activity_logs')
      .select('category')
      .gte('created_at', startDate.toISOString())

    const activitiesByCategory: Record<string, number> = {}
    categoryData?.forEach(log => {
      activitiesByCategory[log.category] = (activitiesByCategory[log.category] || 0) + 1
    })

    // Get activities by day (simplified)
    const activitiesByDay: Array<{ date: string; count: number }> = []

    return {
      totalActivities: totalActivities || 0,
      activitiesByCategory,
      activitiesByDay
    }
  } catch (error) {
    console.error('Error getting activity stats:', error)
    return {
      totalActivities: 0,
      activitiesByCategory: {},
      activitiesByDay: []
    }
  }
}

/**
 * Clean up old activity logs (older than specified days)
 */
export async function cleanupOldLogs(olderThanDays: number = 90): Promise<number> {
  try {
    const supabaseAdmin = getSupabaseAdmin()

    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays)

    const { data, error } = await supabaseAdmin
      .from('activity_logs')
      .delete()
      .lt('created_at', cutoffDate.toISOString())
      .select('id')

    if (error) {
      // Log to monitoring system instead of console
      return 0
    }

    return data?.length || 0
  } catch (error) {
    // Log to monitoring system instead of console
    return 0
  }
}
