"use client"

import { useState, use<PERSON>emo, useEffect } from "react"
import { useSearchParams, useRout<PERSON> } from "next/navigation"
import { Search, X } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import EventCard from "@/components/event-card"
import { PageLayout } from "@/components/page-layout"
import { HeroSection } from "@/components/hero-section"
import { useToast } from "@/hooks/use-toast"
import { EventType } from "@/contexts/event-context"
import { stripHtmlTags } from "@/lib/utils"

export default function EventsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [events, setEvents] = useState<EventType[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [organizerFilter, setOrganizerFilter] = useState<string | null>(null)
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null)
  const [organizerInfo, setOrganizerInfo] = useState<{ id: string; name: string } | null>(null)
  const [categoryInfo, setCategoryInfo] = useState<{ id: string; name: string; color: string } | null>(null)
  const { toast } = useToast()
  const searchParams = useSearchParams()
  const router = useRouter()

  // Set page title
  useEffect(() => {
    document.title = "Events | mTicket.my - Event Management Platform"
  }, [])

  // Handle URL parameters and fetch events
  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true)
        setError(null)

        // Get current URL parameters
        const organizer = searchParams.get('organizer')
        const category = searchParams.get('category')
        console.log('URL parameters:', { organizer, category })

        // Update filter states
        setOrganizerFilter(organizer)
        setCategoryFilter(category)

        // Determine API URL based on filters
        let url = '/api/events'
        if (organizer) {
          url = `/api/events/organizer/${organizer}`
          console.log('Fetching events by organizer:', organizer)
        } else if (category) {
          if (category === 'others') {
            url = `/api/events/category/others`
            console.log('Fetching events without category (others)')
          } else {
            url = `/api/events/category/${category}`
            console.log('Fetching events by category:', category)
          }
        } else {
          console.log('Fetching all events')
        }

        console.log('API URL:', url)
        const response = await fetch(url)

        if (!response.ok) {
          throw new Error(`Failed to fetch events: ${response.status}`)
        }

        const data = await response.json()
        console.log('API Response:', data)

        if (data.error) {
          throw new Error(data.error)
        }

        setEvents(data.events || [])
        if (data.organizer) {
          setOrganizerInfo(data.organizer)
        } else {
          setOrganizerInfo(null)
        }

        if (data.category) {
          setCategoryInfo(data.category)
        } else {
          setCategoryInfo(null)
        }
      } catch (err: any) {
        console.error('Error fetching events:', err)
        setError(err.message || 'Failed to load events')
        toast({
          title: "Error",
          description: "Failed to load events. Please try again later.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchEvents()
  }, [searchParams, toast])

  // Clear organizer filter
  const clearOrganizerFilter = () => {
    router.push('/events')
  }

  // Clear category filter
  const clearCategoryFilter = () => {
    router.push('/events')
  }

  // Filter events based on search query
  const filterEvents = (events: EventType[], query: string) => {
    if (!query) return events

    const lowerQuery = query.toLowerCase()
    return events.filter(event => {
      // Get description text (strip HTML if it's WYSIWYG content)
      const descriptionText = event.description_html
        ? stripHtmlTags(event.description_html)
        : event.description || ''

      return event.title.toLowerCase().includes(lowerQuery) ||
        descriptionText.toLowerCase().includes(lowerQuery) ||
        (event.location && event.location.toLowerCase().includes(lowerQuery)) ||
        (event.organizations?.name && event.organizations.name.toLowerCase().includes(lowerQuery))
    })
  }

  // Memoize filtered events
  const filteredEvents = useMemo(() =>
    filterEvents(events, searchQuery),
    [events, searchQuery]
  )

  // Separate events into paid and free
  const paidEvents = useMemo(() =>
    filteredEvents.filter(event => (event.price || 0) > 0),
    [filteredEvents]
  )

  const freeEvents = useMemo(() =>
    filteredEvents.filter(event => (event.price || 0) === 0),
    [filteredEvents]
  )

  return (
    <PageLayout>
      {/* Corporate Hero Section */}
      <section className="relative w-full py-20 md:py-32 lg:py-40 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}
          ></div>
        </div>

        <div className="container relative px-4 md:px-6">
          <div className="flex flex-col items-center space-y-8 text-center">
            {/* Main Heading */}
            <div className="space-y-4 max-w-4xl">
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl lg:text-7xl">
                Browse All
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> Events</span>
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-gray-300 md:text-2xl leading-relaxed">
                No matter your mood or budget — there's something here for you!
                Discover amazing events happening near you.
              </p>
            </div>

            {/* Search Section */}
            <div className="w-full max-w-2xl pt-4">
              <div className="relative">
                <Search className="absolute left-4 top-4 h-5 w-5 text-gray-400" />
                <Input
                  type="search"
                  placeholder="Search events by name, location, or category..."
                  className="w-full bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder:text-gray-300 pl-12 py-4 text-lg rounded-xl"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              {/* Filter badges */}
              <div className="flex flex-col items-center gap-3 mt-4">
                {/* Organizer filter badge */}
                {organizerFilter && organizerInfo && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-300">Showing events by:</span>
                    <Badge className="bg-white/20 text-white border-white/30 flex items-center gap-1">
                      {organizerInfo.name}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-white/20 text-white"
                        onClick={clearOrganizerFilter}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  </div>
                )}

                {/* Category filter badge */}
                {categoryFilter && categoryInfo && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-300">Showing events in:</span>
                    <Badge
                      className="bg-white/20 text-white border-white/30 flex items-center gap-1"
                    >
                      {categoryInfo.name}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-white/20 text-white"
                        onClick={clearCategoryFilter}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Events Section */}
      <section className="w-full py-16 md:py-20 bg-white">
        <div className="container px-4 md:px-6">
          {/* Show loading state */}
          {loading && (
            <div className="flex justify-center items-center py-16">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
              <span className="ml-4 text-lg text-gray-600">Loading amazing events...</span>
            </div>
          )}

          {/* Show error state */}
          {error && !loading && (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                  <p className="text-red-600 mb-4 font-medium">{error}</p>
                  <Button
                    onClick={() => window.location.reload()}
                    variant="outline"
                    className="border-red-300 text-red-600 hover:bg-red-50"
                  >
                    Try Again
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Show content when not loading and no error */}
          {!loading && !error && (
            <div className="space-y-8">
              <Tabs defaultValue="all" className="w-full">
                <div className="flex justify-center mb-8">
                  <TabsList className="grid w-full max-w-lg grid-cols-3 h-12 bg-gray-100 rounded-xl p-1">
                    <TabsTrigger
                      value="all"
                      className="rounded-lg font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm"
                    >
                      All Events
                    </TabsTrigger>
                    <TabsTrigger
                      value="paid"
                      className="rounded-lg font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm"
                    >
                      Paid Events
                    </TabsTrigger>
                    <TabsTrigger
                      value="free"
                      className="rounded-lg font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm"
                    >
                      Free Events
                    </TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="all" className="mt-8">
                  <div className="mx-auto grid max-w-7xl grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                    {filteredEvents.length === 0 ? (
                      <div className="col-span-full text-center py-16">
                        <div className="max-w-md mx-auto">
                          <Search className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-xl font-semibold text-gray-600 mb-2">No Events Found</h3>
                          <p className="text-gray-500">
                            {searchQuery || organizerFilter || categoryFilter
                              ? "Try adjusting your search criteria or filters."
                              : "Check back soon for new events!"
                            }
                          </p>
                        </div>
                      </div>
                    ) : (
                      filteredEvents.map((event) => (
                        <EventCard key={event.id} event={event} />
                      ))
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="paid" className="mt-8">
                  <div className="mx-auto grid max-w-7xl grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                    {paidEvents.length === 0 ? (
                      <div className="col-span-full text-center py-16">
                        <div className="max-w-md mx-auto">
                          <Search className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-xl font-semibold text-gray-600 mb-2">No Paid Events Found</h3>
                          <p className="text-gray-500">
                            {searchQuery || organizerFilter || categoryFilter
                              ? "Try adjusting your search criteria or filters."
                              : "Check back soon for paid events!"
                            }
                          </p>
                        </div>
                      </div>
                    ) : (
                      paidEvents.map((event) => (
                        <EventCard key={event.id} event={event} />
                      ))
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="free" className="mt-8">
                  <div className="mx-auto grid max-w-7xl grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                    {freeEvents.length === 0 ? (
                      <div className="col-span-full text-center py-16">
                        <div className="max-w-md mx-auto">
                          <Search className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-xl font-semibold text-gray-600 mb-2">No Free Events Found</h3>
                          <p className="text-gray-500">
                            {searchQuery || organizerFilter || categoryFilter
                              ? "Try adjusting your search criteria or filters."
                              : "Check back soon for free events!"
                            }
                          </p>
                        </div>
                      </div>
                    ) : (
                      freeEvents.map((event) => (
                        <EventCard key={event.id} event={event} />
                      ))
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}
        </div>
      </section>
    </PageLayout>
  )
}
