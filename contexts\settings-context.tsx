"use client"

import { create<PERSON>ontext, useContext, useState, useEffect, type ReactNode } from "react"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { supabase } from "@/lib/supabase"

// Types
export type SystemSettings = {
  id: string
  event_fee_percentage: number
  withdrawal_fee_percentage: number
  min_withdrawal_amount: number
  payment_gateways: {
    name: string
    enabled: boolean
    api_key?: string
    secret_key?: string
  }[]
  certificate_templates: {
    id: string
    name: string
    thumbnail_url: string
    is_premium: boolean
  }[]
  updated_at: string
  updated_by: string
}

// Default settings in case the database doesn't return any
const DEFAULT_SETTINGS: Omit<SystemSettings, "id" | "updated_at" | "updated_by"> = {
  event_fee_percentage: 5,
  withdrawal_fee_percentage: 1.5,
  min_withdrawal_amount: 50,
  payment_gateways: [
    { name: "Stripe", enabled: true },
    { name: "PayPal", enabled: false },
  ],
  certificate_templates: [
    { id: "default", name: "Default Template", thumbnail_url: "/templates/default.png", is_premium: false },
    { id: "premium1", name: "Premium Template 1", thumbnail_url: "/templates/premium1.png", is_premium: true },
  ],
}

type SettingsContextType = {
  settings: SystemSettings | null
  loading: boolean
  error: string | null
  updateSettings: (data: Partial<SystemSettings>) => Promise<SystemSettings | null>
  calculateEventFee: (amount: number) => number
  calculateWithdrawalFee: (amount: number) => number
  getEnabledPaymentGateways: () => { name: string; enabled: boolean }[]
  getCertificateTemplates: (
    includePremium: boolean,
  ) => { id: string; name: string; thumbnail_url: string; is_premium: boolean }[]
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined)

export function SettingsProvider({ children }: { children: ReactNode }) {
  const [settings, setSettings] = useState<SystemSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()
  const { user, isAdmin } = useAuth()

  // Fetch settings on mount
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        // First try to fetch from our server-side API endpoint
        const response = await fetch('/api/settings');

        if (!response.ok) {
          throw new Error(`Failed to fetch settings: ${response.status} ${response.statusText}`);
        }

        let data;
        try {
          data = await response.json();
        } catch (jsonError) {
          console.error("Error parsing JSON response from settings API:", jsonError);
          throw new Error("Invalid JSON response from settings API");
        }

        if (data && !data.error) {
          setSettings(data as SystemSettings);
        } else {
          // Fallback to direct Supabase query
          console.log("API endpoint returned error, falling back to direct query");
          const { data: supabaseData, error } = await supabase.from("system_settings").select("*").single();

          if (error) {
            // If the error is because no rows were returned, we'll create default settings
            if (error.code === "PGRST116") {
              console.log("No settings found, using defaults");
              // Use default settings if none exist
              setSettings({
                id: "default",
                ...DEFAULT_SETTINGS,
                updated_at: new Date().toISOString(),
                updated_by: "",
              });
            } else {
              throw error;
            }
          } else {
            setSettings(supabaseData as SystemSettings);
          }
        }
      } catch (err) {
        console.error("Error fetching settings:", err);
        // Use default settings in case of error
        setSettings({
          id: "default",
          ...DEFAULT_SETTINGS,
          updated_at: new Date().toISOString(),
          updated_by: "",
        });
        setError("Failed to fetch system settings, using defaults");
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [])

  // Update settings
  const updateSettings = async (data: Partial<SystemSettings>): Promise<SystemSettings | null> => {
    if (!user || !isAdmin()) {
      toast({
        title: "Error",
        description: "You don't have permission to update settings",
        variant: "destructive",
      })
      return null
    }

    setLoading(true)
    try {
      // If we're using default settings (no record in DB yet), create a new record
      if (settings?.id === "default") {
        const { data: newData, error } = await supabase
          .from("system_settings")
          .insert({
            ...DEFAULT_SETTINGS,
            ...data,
            updated_at: new Date().toISOString(),
            updated_by: user.id,
          })
          .select()
          .single()

        if (error) throw error

        const newSettings = newData as SystemSettings
        setSettings(newSettings)

        toast({
          title: "Success",
          description: "Settings created successfully",
        })

        return newSettings
      } else {
        // Otherwise update existing settings
        const { data: updatedData, error } = await supabase
          .from("system_settings")
          .update({
            ...data,
            updated_at: new Date().toISOString(),
            updated_by: user.id,
          })
          .eq("id", settings?.id)
          .select()
          .single()

        if (error) throw error

        const updatedSettings = updatedData as SystemSettings
        setSettings(updatedSettings)

        toast({
          title: "Success",
          description: "Settings updated successfully",
        })

        return updatedSettings
      }
    } catch (err) {
      console.error("Error updating settings:", err)
      setError("Failed to update settings")
      toast({
        title: "Error",
        description: "Failed to update settings",
        variant: "destructive",
      })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Calculate event fee
  const calculateEventFee = (amount: number): number => {
    if (!settings) return 0
    return (amount * settings.event_fee_percentage) / 100
  }

  // Calculate withdrawal fee
  const calculateWithdrawalFee = (amount: number): number => {
    if (!settings) return 0
    return (amount * settings.withdrawal_fee_percentage) / 100
  }

  // Get enabled payment gateways
  const getEnabledPaymentGateways = () => {
    if (!settings) return []
    return settings.payment_gateways.filter((gateway) => gateway.enabled)
  }

  // Get certificate templates
  const getCertificateTemplates = (includePremium: boolean) => {
    if (!settings) return []
    if (includePremium) {
      return settings.certificate_templates
    }
    return settings.certificate_templates.filter((template) => !template.is_premium)
  }

  const value = {
    settings,
    loading,
    error,
    updateSettings,
    calculateEventFee,
    calculateWithdrawalFee,
    getEnabledPaymentGateways,
    getCertificateTemplates,
  }

  return <SettingsContext.Provider value={value}>{children}</SettingsContext.Provider>
}

export function useSettings() {
  const context = useContext(SettingsContext)
  if (context === undefined) {
    throw new Error("useSettings must be used within a SettingsProvider")
  }
  return context
}
