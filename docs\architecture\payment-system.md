# Payment System Documentation

> **Note**: This documentation has been updated to reflect the new modular payment system architecture.

## Overview

The mTicket.my payment system provides comprehensive payment processing for event registrations with support for multiple payment gateways and real-time status tracking. The system has been refactored into a modular architecture for better maintainability and extensibility.

## Features

### ✅ **Core Payment Features**
- **Multiple Payment Gateways**: Billplz, ToyyibPay, Chip, Stripe
- **Real-time Status Updates**: Instant payment confirmation
- **Secure Processing**: HMAC-signed transactions with replay protection
- **Automatic Verification**: Payment gateway webhook integration
- **Activity Logging**: Complete audit trail for all payment activities
- **User-friendly UI**: Clear payment status indicators and actions

## Payment System Architecture

The modular payment system architecture provides flexibility and maintainability:

```mermaid
graph TB
    subgraph "Payment Module Structure"
        Factory[Payment Factory<br/>lib/payment/factory.ts]
        Config[Gateway Config<br/>lib/payment/config.ts]
        Types[Type Definitions<br/>lib/payment/types.ts]

        subgraph "Gateway Implementations"
            Billplz[Billplz Gateway<br/>lib/payment/gateways/billplz.ts]
            ToyyibPay[ToyyibPay Gateway<br/>lib/payment/gateways/toyyibpay.ts]
            Chip[Chip Gateway<br/>lib/payment/gateways/chip.ts]
            Stripe[Stripe Gateway<br/>lib/payment/gateways/stripe.ts]
        end
    end

    subgraph "API Layer"
        PaymentAPI[Payment API<br/>app/api/registrations/payment/route.ts]
        VerifyAPI[Verify API<br/>app/api/registrations/verify-payment/route.ts]
        WebhookAPI[Webhook API<br/>app/api/webhooks/payment/route.ts]
    end

    subgraph "Database"
        Transactions[(transactions table)]
        Registrations[(registrations table)]
        Gateways[(payment_gateway_settings table)]
    end

    subgraph "External Services"
        BillplzAPI[Billplz API]
        ToyyibPayAPI[ToyyibPay API]
        ChipAPI[Chip API]
        StripeAPI[Stripe API]
    end

    Factory --> Config
    Factory --> Types
    Factory --> Billplz
    Factory --> ToyyibPay
    Factory --> Chip
    Factory --> Stripe

    PaymentAPI --> Factory
    VerifyAPI --> Factory
    WebhookAPI --> Factory

    Factory --> Transactions
    Factory --> Registrations
    Config --> Gateways

    Billplz --> BillplzAPI
    ToyyibPay --> ToyyibPayAPI
    Chip --> ChipAPI
    Stripe --> StripeAPI

    style Factory fill:#e1f5fe
    style Config fill:#f3e5f5
    style Types fill:#e8f5e8
    style Transactions fill:#fff3e0
```

### 🔄 **Payment Flow**

#### 1. **Registration Creation**
- User registers for an event
- Registration created with `payment_status: 'pending'`
- User can access "My Tickets" page

#### 2. **Payment Initiation**
- User sees "Payment Pending" indicator on ticket card
- Clicks "Pay Now" button showing amount
- System calls `/api/registrations/payment` endpoint
- Payment gateway URL generated and user redirected

#### 3. **Payment Processing**
- User completes payment on gateway (Billplz/ToyyibPay/etc.)
- Gateway redirects back to success/cancel URL
- System updates registration status to 'processing'

#### 4. **Payment Verification**
- System calls `/api/registrations/verify-payment` endpoint
- Payment gateway verification performed
- Registration status updated to 'confirmed'
- Payment status set to 'paid'

#### 5. **Post-Payment Access**
- User sees "Payment Confirmed" indicator
- Access to "View Receipt" and "View Ticket" buttons
- Certificate generation available for attended events

## Payment Transaction Flow

The following diagram illustrates the complete payment transaction lifecycle:

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant PaymentAPI
    participant PaymentFactory
    participant Gateway
    participant Database
    participant WebhookAPI

    Note over User,WebhookAPI: Payment Initiation Phase
    User->>Frontend: Click "Pay Now"
    Frontend->>PaymentAPI: POST /api/registrations/payment
    PaymentAPI->>Database: Validate registration
    PaymentAPI->>PaymentFactory: Create payment request
    PaymentFactory->>Gateway: Generate payment URL
    Gateway-->>PaymentFactory: Return payment URL
    PaymentFactory-->>PaymentAPI: Payment URL + transaction ID
    PaymentAPI->>Database: Update registration status to 'processing'
    PaymentAPI-->>Frontend: Return payment URL
    Frontend->>User: Redirect to payment gateway

    Note over User,WebhookAPI: Payment Processing Phase
    User->>Gateway: Complete payment
    Gateway->>User: Show payment result
    Gateway->>WebhookAPI: Send webhook notification
    WebhookAPI->>Database: Update transaction status
    WebhookAPI->>Database: Update registration to 'paid'
    Gateway->>Frontend: Redirect to success/cancel page

    Note over User,WebhookAPI: Payment Verification Phase
    Frontend->>PaymentAPI: POST /api/registrations/verify-payment
    PaymentAPI->>PaymentFactory: Verify payment status
    PaymentFactory->>Gateway: Check transaction status
    Gateway-->>PaymentFactory: Return status
    PaymentFactory-->>PaymentAPI: Verification result
    PaymentAPI->>Database: Final status update
    PaymentAPI-->>Frontend: Confirmation
    Frontend->>User: Show payment confirmed

    Note over User,WebhookAPI: Post-Payment Actions
    Database->>Database: Generate invoice/receipt numbers
    Database->>Database: Trigger certificate eligibility
    Database->>Database: Log activity
```

## Payment Status States

### Database Fields
```sql
-- registrations table
payment_status VARCHAR(20) DEFAULT 'pending'  -- pending, processing, paid, failed
status VARCHAR(20) DEFAULT 'pending'          -- pending, confirmed, attended, cancelled
payment_amount DECIMAL(10,2)                  -- Amount paid
payment_id VARCHAR(255)                       -- Transaction ID from gateway
payment_date TIMESTAMP                        -- When payment was confirmed
```

### Status Mapping
- **`pending`**: Registration created, payment not initiated
- **`processing`**: Payment initiated, waiting for gateway confirmation
- **`paid`**: Payment confirmed by gateway
- **`failed`**: Payment failed or cancelled

## UI Components

### My Tickets Page (`/dashboard/my-tickets`)

#### Pending Payment State
```jsx
<div className="w-full space-y-2">
  <div className="flex items-center justify-center gap-2 p-2 bg-yellow-50 rounded-lg border border-yellow-200">
    <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
    <span className="text-sm font-medium text-yellow-700">Payment Pending</span>
  </div>
  <Button onClick={() => handlePayNow(ticket)} className="w-full bg-green-600 hover:bg-green-700">
    <CreditCard className="mr-2 h-4 w-4" />
    Pay Now - RM {(ticket.event?.price || 0).toFixed(2)}
  </Button>
</div>
```

#### Confirmed Payment State
```jsx
<div className="w-full space-y-2">
  <div className="flex items-center justify-center gap-2 p-2 bg-green-50 rounded-lg border border-green-200">
    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
    <span className="text-sm font-medium text-green-700">Payment Confirmed</span>
  </div>
  <div className="flex gap-2">
    <Button variant="outline" onClick={() => handleViewTicket(ticket, "receipt")}>
      <Receipt className="mr-2 h-4 w-4" />
      Receipt
    </Button>
    <Button onClick={() => handleViewTicket(ticket, "ticket")}>
      <Eye className="mr-2 h-4 w-4" />
      View Ticket
    </Button>
  </div>
</div>
```

## API Endpoints

### Payment Initiation
```typescript
POST /api/registrations/payment
{
  registration_id: string,
  amount: number,
  currency?: string,
  description?: string
}

Response:
{
  success: boolean,
  payment_url: string,
  transaction_id: string,
  gateway: string
}
```

### Payment Verification
```typescript
POST /api/registrations/verify-payment
{
  transaction_id?: string,
  registration_id?: string,
  status?: string
}

Response:
{
  success: boolean,
  registration: {
    id: string,
    payment_status: string,
    status: string,
    payment_date: string
  }
}
```

## Payment Gateway Integration

### Supported Gateways
1. **Billplz** - Malaysian payment gateway (FPX, cards, e-wallets)
2. **ToyyibPay** - Alternative Malaysian solution
3. **Chip** - Credit/debit cards, e-wallets
4. **Stripe** - International payments

### Gateway Configuration
Payment gateways are configured in the `payment_gateway_settings` table:
```sql
CREATE TABLE payment_gateway_settings (
  id UUID PRIMARY KEY,
  gateway_name TEXT NOT NULL,
  gateway_type TEXT NOT NULL,
  is_enabled BOOLEAN DEFAULT false,
  configuration JSONB DEFAULT '{}',
  test_configuration JSONB DEFAULT '{}',
  live_configuration JSONB DEFAULT '{}',
  display_order INTEGER DEFAULT 0
);
```

## Security Features

### Transaction Security
- **HMAC Signatures**: All payment requests signed with secret keys
- **Replay Protection**: Timestamp validation prevents replay attacks
- **Secure Redirects**: Success/cancel URLs validated
- **Token Expiry**: Payment tokens have limited validity

### Data Protection
- **PCI Compliance**: No card data stored locally
- **Encrypted Storage**: Sensitive gateway credentials encrypted
- **Audit Logging**: All payment activities logged
- **Access Control**: Payment functions require authentication

## Error Handling

### Common Error Scenarios
1. **Invalid Registration**: Registration not found or access denied
2. **Already Paid**: Registration already has confirmed payment
3. **Gateway Unavailable**: No active payment gateways configured
4. **Payment Failed**: Gateway returns failure response
5. **Verification Failed**: Payment cannot be verified with gateway

### Error Response Format
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": "Additional context"
}
```

## Testing

### Test Payment Flow
1. Create event registration with pending payment
2. Navigate to `/dashboard/my-tickets`
3. Verify "Payment Pending" status displayed
4. Click "Pay Now" button
5. Complete payment on gateway (test mode)
6. Verify redirect back to success page
7. Check "Payment Confirmed" status
8. Test receipt and ticket viewing

### Mock Gateway Responses
The system includes mock implementations for all payment gateways that simulate:
- Successful payment creation
- Payment URL generation
- Transaction ID assignment
- Payment verification responses

## Monitoring and Analytics

### Activity Logging
All payment activities are logged with:
- User ID and registration ID
- Payment gateway used
- Transaction amounts and IDs
- Success/failure status
- Timestamps and IP addresses

### Payment Metrics
Track key metrics:
- Payment success rates by gateway
- Average payment completion time
- Failed payment reasons
- Revenue by event and time period

## Webhook Integration

### Payment Gateway Webhooks
Configure webhooks for real-time payment updates:
- Payment success notifications
- Payment failure alerts
- Refund processing
- Chargeback notifications

### Internal Webhooks
Trigger internal webhooks for:
- Payment confirmation
- Registration status changes
- Certificate generation eligibility
- Email notifications
