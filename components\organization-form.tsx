"use client"

import type React from "react"

import { useState, useCallback, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { useAuth } from "@/contexts/auth-context"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, CheckCircle2, Search, Building, Link, Plus } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"

export default function OrganizationForm() {
  const router = useRouter()
  const { toast } = useToast()
  const { user, refreshUser, loading: authLoading } = useAuth()
  const supabase = createClientComponentClient()

  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  // Search functionality state
  const [searchQuery, setSearchQuery] = useState("")
  const [ssmSearchQuery, setSsmSearchQuery] = useState("")
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [activeTab, setActiveTab] = useState("search")

  // Show loading state while auth is loading
  if (authLoading) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="flex items-center justify-center py-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading user data...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Show error if user is not authenticated
  if (!user) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="py-10">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Authentication Required</AlertTitle>
            <AlertDescription>
              You must be logged in to create an organization. Please refresh the page or log in again.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  const [formData, setFormData] = useState({
    organizationName: "",
    ssmNumber: "",
    picName: "",
    picPhone: "",
    picEmail: "",
  })

  // Search organizations function
  const searchOrganizations = useCallback(async () => {
    if (!searchQuery.trim() && !ssmSearchQuery.trim()) {
      setSearchResults([])
      return
    }

    setIsSearching(true)
    try {
      const params = new URLSearchParams()
      if (searchQuery.trim()) params.append("search", searchQuery.trim())
      if (ssmSearchQuery.trim()) params.append("ssm_number", ssmSearchQuery.trim())
      params.append("limit", "20")

      const response = await fetch(`/api/organizations?${params.toString()}`)
      if (response.ok) {
        const data = await response.json()
        setSearchResults(data.organizations || [])
      } else {
        console.error("Failed to search organizations")
        setSearchResults([])
      }
    } catch (error) {
      console.error("Error searching organizations:", error)
      setSearchResults([])
    } finally {
      setIsSearching(false)
    }
  }, [searchQuery, ssmSearchQuery])

  // Debounced search effect
  useEffect(() => {
    const timer = setTimeout(() => {
      searchOrganizations()
    }, 500)

    return () => clearTimeout(timer)
  }, [searchOrganizations])

  // Link to existing organization
  const handleLinkToOrganization = async (organization: any) => {
    setLoading(true)
    setError(null)

    try {
      if (!user?.id) {
        throw new Error("User not authenticated")
      }

      const token = document.cookie
        .split('; ')
        .find(row => row.startsWith('auth_token='))
        ?.split('=')[1];

      if (!token) {
        throw new Error("Authentication token not found")
      }

      const response = await fetch("/api/organizations/link", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          organizationId: organization.id,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to link to organization")
      }

      // Success
      setSuccess(true)
      await refreshUser()

      toast({
        title: "Successfully linked!",
        description: `You are now linked to ${organization.name} and have been upgraded to manager role.`,
      })

      // Redirect after a short delay
      setTimeout(() => {
        router.push("/dashboard/events/create")
      }, 2000)
    } catch (err: any) {
      console.error("Error linking to organization:", err)
      setError(err.message || "Failed to link to organization")
      toast({
        variant: "destructive",
        title: "Error",
        description: err.message || "Failed to link to organization",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    try {
      if (!user?.id) {
        throw new Error("User not authenticated")
      }

      // Create organization
      const { data: orgData, error: orgError } = await supabase
        .from("organizations")
        .insert([
          {
            name: formData.organizationName,
            ssm_number: formData.ssmNumber,
            pic_name: formData.picName,
            pic_phone: formData.picPhone,
            pic_email: formData.picEmail,
            created_by: user.id,
          },
        ])
        .select()
        .single()

      if (orgError) throw orgError

      // Get the manager role ID from user_roles table
      const { data: managerRole, error: roleError } = await supabase
        .from("user_roles")
        .select("id")
        .eq("role_name", "manager")
        .single()

      if (roleError || !managerRole) {
        throw new Error("Manager role not found in database")
      }

      // Update user role to manager and link to organization
      const { error: userError } = await supabase
        .from("users")
        .update({
          role_id: managerRole.id,
          organization_id: orgData.id,
        })
        .eq("id", user.id)

      if (userError) throw userError

      // Log activity
      await supabase.from("activity_logs").insert([
        {
          user_id: user.id,
          action: "create_organization",
          entity_type: "organization",
          entity_id: orgData.id,
          details: { organization_name: formData.organizationName },
        },
      ])

      // Success
      setSuccess(true)
      await refreshUser()

      toast({
        title: "Organization created!",
        description: "You can now create events.",
      })

      // Redirect after a short delay
      setTimeout(() => {
        router.push("/dashboard/events/create")
      }, 2000)
    } catch (err: any) {
      console.error("Error creating organization:", err)
      setError(err.message || "Failed to create organization")
      toast({
        variant: "destructive",
        title: "Error",
        description: err.message || "Failed to create organization",
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building className="h-5 w-5" />
          Organization Setup
        </CardTitle>
        <CardDescription>
          Search for your existing organization or create a new one to start managing events
        </CardDescription>
      </CardHeader>

      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-6 bg-green-50 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">Success!</AlertTitle>
            <AlertDescription className="text-green-700">
              You have been successfully linked to the organization and upgraded to manager role.
              You will be redirected to create your first event.
            </AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="search" className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              Search Existing
            </TabsTrigger>
            <TabsTrigger value="create" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Create New
            </TabsTrigger>
          </TabsList>

          <TabsContent value="search" className="space-y-6 mt-6">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="searchQuery">Organization Name</Label>
                  <Input
                    id="searchQuery"
                    placeholder="Search by organization name..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    disabled={loading}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="ssmSearchQuery">SSM/Registration Number</Label>
                  <Input
                    id="ssmSearchQuery"
                    placeholder="Search by SSM number..."
                    value={ssmSearchQuery}
                    onChange={(e) => setSsmSearchQuery(e.target.value)}
                    disabled={loading}
                  />
                </div>
              </div>

              {isSearching && (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                  <span className="ml-2 text-gray-600">Searching organizations...</span>
                </div>
              )}

              {!isSearching && searchResults.length > 0 && (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold">Found Organizations</h3>
                  <div className="grid gap-3 max-h-96 overflow-y-auto">
                    {searchResults.map((org) => (
                      <div
                        key={org.id}
                        className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-semibold text-lg">{org.name}</h4>
                            <div className="space-y-1 mt-2">
                              <p className="text-sm text-gray-600">
                                <span className="font-medium">SSM:</span> {org.ssm_number}
                              </p>
                              <p className="text-sm text-gray-600">
                                <span className="font-medium">PIC:</span> {org.pic_name}
                              </p>
                              <p className="text-sm text-gray-600">
                                <span className="font-medium">Email:</span> {org.pic_email}
                              </p>
                              {org.website && (
                                <p className="text-sm text-gray-600">
                                  <span className="font-medium">Website:</span> {org.website}
                                </p>
                              )}
                            </div>
                          </div>
                          <Button
                            onClick={() => handleLinkToOrganization(org)}
                            disabled={loading}
                            className="ml-4"
                          >
                            <Link className="h-4 w-4 mr-2" />
                            {loading ? "Linking..." : "Link to This"}
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {!isSearching && (searchQuery.trim() || ssmSearchQuery.trim()) && searchResults.length === 0 && (
                <div className="text-center py-8">
                  <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No organizations found</h3>
                  <p className="text-gray-600 mb-4">
                    We couldn't find any organizations matching your search criteria.
                  </p>
                  <Button
                    onClick={() => setActiveTab("create")}
                    variant="outline"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create New Organization
                  </Button>
                </div>
              )}

              {!searchQuery.trim() && !ssmSearchQuery.trim() && (
                <div className="text-center py-8 text-gray-500">
                  <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Enter an organization name or SSM number to search</p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="create" className="space-y-6 mt-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="organizationName">Organization Name *</Label>
                <Input
                  id="organizationName"
                  name="organizationName"
                  value={formData.organizationName}
                  onChange={handleChange}
                  required
                  disabled={loading || success}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="ssmNumber">SSM Number / Registration Number *</Label>
                <Input
                  id="ssmNumber"
                  name="ssmNumber"
                  value={formData.ssmNumber}
                  onChange={handleChange}
                  required
                  disabled={loading || success}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="picName">Person In Charge (PIC) *</Label>
                <Input
                  id="picName"
                  name="picName"
                  value={formData.picName}
                  onChange={handleChange}
                  required
                  disabled={loading || success}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="picPhone">PIC Phone Number *</Label>
                  <Input
                    id="picPhone"
                    name="picPhone"
                    type="tel"
                    value={formData.picPhone}
                    onChange={handleChange}
                    required
                    disabled={loading || success}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="picEmail">PIC Email *</Label>
                  <Input
                    id="picEmail"
                    name="picEmail"
                    type="email"
                    value={formData.picEmail}
                    onChange={handleChange}
                    required
                    disabled={loading || success}
                  />
                </div>
              </div>

              <div className="flex justify-end">
                <Button type="submit" disabled={loading || success} className="min-w-[200px]">
                  {loading ? "Creating..." : success ? "Created!" : "Create Organization"}
                </Button>
              </div>
            </form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
