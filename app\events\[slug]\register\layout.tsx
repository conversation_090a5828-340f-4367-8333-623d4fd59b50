import type { Metadata } from "next"
import type React from "react"

export const metadata: Metadata = {
  title: {
    template: '%s | Event Registration - mTicket.my',
    default: 'Event Registration | mTicket.my - Event Management Platform',
  },
  description: 'Register for events and manage your tickets with mTicket.my professional event management platform.',
}

interface RegistrationLayoutProps {
  children: React.ReactNode
}

export default function RegistrationLayout({ children }: RegistrationLayoutProps) {
  return (
    <div className="registration-layout">
      {children}
    </div>
  )
}
