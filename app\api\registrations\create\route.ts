import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth"
import { logActivity, ActivityCategory } from "@/utils/activity-logger"

/**
 * POST /api/registrations/create
 * Creates a new event registration with participants
 * Requires authentication
 */
export async function POST(request: Request) {
  try {
    console.log("Registration Create API: Starting request")

    // Get JWT token from request
    const token = getJWTTokenFromRequest(request)
    console.log("Registration Create API: Token present:", !!token)

    if (!token) {
      return NextResponse.json(
        { error: "Missing or invalid Authorization header" },
        { status: 401 }
      )
    }

    // Verify the JWT token and get user data
    const authResult = await verifyJWTToken(token)
    if (!authResult || !authResult.user) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 }
      )
    }

    const userId = authResult.user.id
    console.log("Registration Create API: User ID:", userId)

    // Parse request body
    const body = await request.json()
    const { event_id, participants, main_contact } = body



    // Validate required fields
    if (!event_id || !participants || !Array.isArray(participants) || participants.length === 0) {
      return NextResponse.json(
        { error: "Event ID and participants are required" },
        { status: 400 }
      )
    }

    if (!main_contact || !main_contact.name || !main_contact.email) {
      return NextResponse.json(
        { error: "Main contact information is required" },
        { status: 400 }
      )
    }

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin()

    // Verify the event exists and is published
    console.log("Registration Create API: Fetching event with ID:", event_id)
    const { data: event, error: eventError } = await supabaseAdmin
      .from("events")
      .select("id, title, slug, is_published, max_participants, price")
      .eq("id", event_id)
      .single()

    if (eventError) {
      console.error("Registration Create API: Error fetching event:", eventError)
      return NextResponse.json(
        { error: "Event not found", details: eventError },
        { status: 404 }
      )
    }

    if (!event) {
      console.error("Registration Create API: Event not found for ID:", event_id)
      return NextResponse.json(
        { error: "Event not found" },
        { status: 404 }
      )
    }

    console.log("Registration Create API: Event found:", event.title)

    if (!event.is_published) {
      return NextResponse.json(
        { error: "Event is not published" },
        { status: 400 }
      )
    }

    // Check if event has capacity for new participants
    if (event.max_participants) {
      // Get current participant count by counting registrations
      // Count both "registered" (paid events) and "confirmed" (free events or completed payments)
      const { count: currentParticipants, error: countError } = await supabaseAdmin
        .from("registrations")
        .select("*", { count: "exact", head: true })
        .eq("event_id", event_id)
        .in("status", ["registered", "confirmed"])

      if (countError) {
        console.error("Error counting participants:", countError)
        return NextResponse.json(
          { error: "Failed to check event capacity" },
          { status: 500 }
        )
      }

      if ((currentParticipants || 0) + participants.length > event.max_participants) {
        return NextResponse.json(
          { error: "Event has reached maximum capacity" },
          { status: 400 }
        )
      }
    }

    // Generate a group registration ID for linking multiple participants
    const groupRegistrationId = crypto.randomUUID()

    // Check if this is a free event
    const isFreeEvent = !event.price || event.price === 0

    // Create registration records for each participant
    const registrations = participants.map((participant: any) => {
      // Ensure we always have a valid ticket type that meets database constraints
      let ticketType = "Standard" // Default fallback
      if (participant.ticketTypeName && typeof participant.ticketTypeName === 'string') {
        const trimmedTicketType = participant.ticketTypeName.trim()
        if (trimmedTicketType.length > 0) {
          ticketType = trimmedTicketType
        }
      }

      const registration = {
        id: crypto.randomUUID(),
        event_id: event_id,
        user_id: userId,
        created_by: userId,
        attendee_name: participant.name,
        attendee_email: participant.email,
        attendee_phone: participant.phone || null,
        ic_reg: participant.ic || null,
        ticket_type: ticketType, // Store the actual selected ticket type
        payment_status: isFreeEvent ? "paid" : "pending", // Auto-confirm free events
        payment_amount: isFreeEvent ? 0 : 0, // Will be calculated based on ticket types for paid events
        payment_date: isFreeEvent ? new Date().toISOString() : null, // Set payment date for free events
        group_registration_id: groupRegistrationId,
        status: isFreeEvent ? "confirmed" : "registered", // Auto-confirm free events
        custom_field_responses: participant.custom_field_responses || {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      return registration
    })

    // Insert the registrations
    const { data: insertedRegistrations, error: insertError } = await supabaseAdmin
      .from("registrations")
      .insert(registrations)
      .select(`
        *,
        event:event_id (
          id,
          title,
          slug,
          location,
          start_date,
          end_date,
          image_url,
          is_published
        )
      `)

    if (insertError) {
      console.error("Error creating registrations:", insertError)
      return NextResponse.json(
        { error: "Failed to create registrations", details: insertError },
        { status: 500 }
      )
    }

    // Note: We don't need to update a participant count column since we calculate it dynamically
    // This approach is more reliable and avoids data inconsistency issues

    // For free events, create a transaction record to maintain consistency
    if (isFreeEvent && insertedRegistrations && insertedRegistrations.length > 0) {
      try {
        const transactionId = crypto.randomUUID()
        const transaction = {
          id: transactionId,
          user_id: userId,
          registration_id: insertedRegistrations[0].id, // Link to first registration
          transaction_type: "registration_payment",
          amount: 0,
          currency: "MYR",
          status: "paid",
          gateway_transaction_id: `FREE-${transactionId.substring(0, 8).toUpperCase()}`,
          group_transaction_id: groupRegistrationId,
          processed_at: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }

        const { error: transactionError } = await supabaseAdmin
          .from("transactions")
          .insert([transaction])

        if (transactionError) {
          console.error("Error creating transaction for free event:", transactionError)
          // Don't fail the registration if transaction creation fails
        } else {
          // Update registrations with transaction_id
          await supabaseAdmin
            .from("registrations")
            .update({ transaction_id: transactionId })
            .eq("group_registration_id", groupRegistrationId)
        }
      } catch (transactionError) {
        console.error("Error handling free event transaction:", transactionError)
        // Don't fail the registration if transaction handling fails
      }
    }

    // Log activity
    try {
      await logActivity({
        userId: userId,
        action: "registration_created",
        entityType: "registration",
        entityId: groupRegistrationId,
        category: ActivityCategory.REGISTRATION,
        details: {
          event_id: event_id,
          event_title: event.title,
          participant_count: participants.length,
          group_registration_id: groupRegistrationId,
          main_contact: main_contact.name,
        },
      })
    } catch (logError) {
      // Don't fail registration if logging fails
      console.error("Error logging registration activity:", logError)
    }

    console.log("Registration Create API: Successfully created registrations")

    return NextResponse.json({
      success: true,
      registration_id: groupRegistrationId,
      registrations: insertedRegistrations,
      message: "Registration created successfully"
    })

  } catch (error: any) {
    console.error("Registration Create API: Error:", error)
    return NextResponse.json(
      { error: "Internal server error", details: error.message },
      { status: 500 }
    )
  }
}
