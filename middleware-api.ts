import { NextResponse } from "next/server";
import { NextRequest } from "next/server";
import { validateApiKey } from "@/lib/api-key-validator";

/**
 * Middleware for API routes that require API key authentication
 */
export async function middleware(req: NextRequest) {
  // Skip API key validation for certain routes
  const skipValidation = [
    "/api/auth",
    "/api/webhooks/receive", // Public webhook receiver endpoint
  ];

  // Check if the current path should skip validation
  const shouldSkipValidation = skipValidation.some(path => req.nextUrl.pathname.startsWith(path));
  if (shouldSkipValidation) {
    return NextResponse.next();
  }

  // Only apply to API routes
  if (!req.nextUrl.pathname.startsWith('/api/')) {
    return NextResponse.next();
  }

  // Extract the API key from the Authorization header
  const authHeader = req.headers.get("Authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return NextResponse.json(
      { error: "Missing or invalid Authorization header" },
      { status: 401 }
    );
  }

  const apiKey = authHeader.substring(7); // Remove "Bearer " prefix
  
  // Validate the API key
  const isValid = await validateApiKey(apiKey);
  
  if (!isValid) {
    return NextResponse.json(
      { error: "Invalid API key" },
      { status: 401 }
    );
  }
  
  // API key is valid, continue with the request
  return NextResponse.next();
}

export const config = {
  matcher: ["/api/:path*"],
};
