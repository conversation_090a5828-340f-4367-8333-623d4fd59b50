# Mobile API Specification for mTicket.my Android App

## Overview

This document provides detailed API specifications specifically for the Android mobile application integration with mTicket.my platform.

## Authentication

### Base URL
```
Production: https://api.mticket.my/
Staging: https://staging-api.mticket.my/
```

### Headers
```http
Content-Type: application/json
Authorization: Bearer <jwt_token>
User-Agent: mTicket-Android/1.0.0
```

## Authentication Endpoints

### 1. User Login
```http
POST /api/auth/login
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "userpassword"
}
```

**Success Response (200):**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "550e8400-e29b-41d4-a716-************",
    "email": "<EMAIL>",
    "full_name": "<PERSON>",
    "role_id": "550e8400-e29b-41d4-a716-446655440001",
    "profile_image_url": "https://example.com/profile.jpg",
    "phone": "+60123456789",
    "organization_id": "550e8400-e29b-41d4-a716-446655440002"
  },
  "role": "user"
}
```

**Error Response (401):**
```json
{
  "error": "Invalid credentials",
  "code": "INVALID_CREDENTIALS"
}
```

### 2. Token Verification
```http
GET /api/auth/verify
Authorization: Bearer <jwt_token>
```

**Success Response (200):**
```json
{
  "valid": true,
  "user": {
    "id": "550e8400-e29b-41d4-a716-************",
    "email": "<EMAIL>",
    "full_name": "John Doe",
    "role": "user"
  }
}
```

**Error Response (401):**
```json
{
  "valid": false,
  "error": "Token expired or invalid"
}
```

## Ticket Management Endpoints

### 1. Get User Tickets
```http
GET /api/dashboard/tickets
Authorization: Bearer <jwt_token>
```

**Success Response (200):**
```json
[
  {
    "id": "550e8400-e29b-41d4-a716-************",
    "event_id": "550e8400-e29b-41d4-a716-************",
    "attendee_name": "John Doe",
    "attendee_email": "<EMAIL>",
    "attendee_phone": "+60123456789",
    "ticket_type": "Standard",
    "payment_status": "paid",
    "status": "confirmed",
    "checked_in": false,
    "checked_in_at": null,
    "created_at": "2024-01-15T10:30:00Z",
    "event": {
      "id": "550e8400-e29b-41d4-a716-************",
      "title": "Tech Conference 2024",
      "slug": "tech-conf-2024",
      "description": "Annual technology conference",
      "start_date": "2024-02-15T09:00:00Z",
      "end_date": "2024-02-15T18:00:00Z",
      "location": "Kuala Lumpur Convention Centre",
      "image_url": "https://example.com/event-image.jpg",
      "status": "published",
      "organizer": {
        "id": "550e8400-e29b-41d4-a716-446655440005",
        "name": "Tech Events Sdn Bhd",
        "email": "<EMAIL>"
      }
    }
  }
]
```

### 2. Generate Secure QR Code
```http
POST /api/tickets/secure-qr
Authorization: Bearer <jwt_token>
```

**Request Body:**
```json
{
  "ticketData": {
    "id": "550e8400-e29b-41d4-a716-************",
    "event_id": "550e8400-e29b-41d4-a716-************",
    "guest_name": "John Doe"
  }
}
```

**Success Response (200):**
```json
{
  "success": true,
  "qrData": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************.signature",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Error Response (404):**
```json
{
  "success": false,
  "error": "Ticket not found",
  "code": "TICKET_NOT_FOUND"
}
```

## Data Models

### User Model
```typescript
interface User {
  id: string;                    // UUID
  email: string;                 // Email address
  full_name: string;            // Full name
  role_id: string;              // Role UUID
  profile_image_url?: string;   // Profile image URL
  phone?: string;               // Phone number
  organization_id?: string;     // Organization UUID
}
```

### Ticket Model
```typescript
interface Ticket {
  id: string;                   // UUID
  event_id: string;            // Event UUID
  attendee_name: string;       // Attendee name
  attendee_email: string;      // Attendee email
  attendee_phone?: string;     // Attendee phone
  ticket_type: string;         // Ticket type (Standard, VIP, etc.)
  payment_status: string;      // pending, paid, failed
  status: string;              // registered, confirmed, cancelled
  checked_in: boolean;         // Check-in status
  checked_in_at?: string;      // Check-in timestamp
  created_at: string;          // Registration timestamp
  event: EventDetails;         // Event information
}
```

### Event Model
```typescript
interface EventDetails {
  id: string;                  // UUID
  title: string;               // Event title
  slug: string;                // URL slug
  description: string;         // Event description
  start_date: string;          // ISO 8601 timestamp
  end_date: string;            // ISO 8601 timestamp
  location: string;            // Event location
  image_url?: string;          // Event image URL
  status: string;              // published, draft, cancelled
  organizer: OrganizerDetails; // Organizer information
}
```

### Organizer Model
```typescript
interface OrganizerDetails {
  id: string;                  // UUID
  name: string;                // Organization name
  email: string;               // Contact email
}
```

## Error Handling

### Standard Error Response
```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": "Additional error details"
}
```

### Common Error Codes
| Code | HTTP Status | Description |
|------|-------------|-------------|
| `INVALID_CREDENTIALS` | 401 | Invalid email or password |
| `TOKEN_EXPIRED` | 401 | JWT token has expired |
| `TOKEN_INVALID` | 401 | JWT token is malformed or invalid |
| `USER_NOT_FOUND` | 404 | User account not found |
| `TICKET_NOT_FOUND` | 404 | Ticket not found |
| `EVENT_NOT_FOUND` | 404 | Event not found |
| `UNAUTHORIZED` | 403 | Insufficient permissions |
| `RATE_LIMITED` | 429 | Too many requests |
| `SERVER_ERROR` | 500 | Internal server error |

## Security Features

### JWT Token Structure
```json
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "userId": "550e8400-e29b-41d4-a716-************",
    "email": "<EMAIL>",
    "role": "user",
    "iat": **********,
    "exp": **********
  }
}
```

### QR Code Security
- **Time-based tokens**: QR codes expire after 30 seconds
- **HMAC signing**: All QR data is cryptographically signed
- **Replay protection**: Each QR code can only be used once
- **Event validation**: QR codes are tied to specific events

### Rate Limiting
- **Authentication endpoints**: 10 requests per minute per IP
- **Ticket endpoints**: 100 requests per minute per user
- **QR generation**: 60 requests per minute per user

## Mobile-Specific Considerations

### Offline Support
- Cache user tickets locally using Room database
- Store last sync timestamp
- Implement pull-to-refresh for data synchronization
- Show cached data when network is unavailable

### QR Code Refresh Strategy
```kotlin
// Refresh QR code every 25 seconds (5-second buffer before expiry)
class QRRefreshManager {
    private val refreshInterval = 25_000L // 25 seconds
    
    fun startAutoRefresh(ticketId: String, callback: (String) -> Unit) {
        // Implementation for automatic QR refresh
    }
}
```

### Network Optimization
- Use HTTP/2 for improved performance
- Implement request caching for static data
- Compress images and responses
- Use connection pooling

### Error Recovery
- Implement exponential backoff for failed requests
- Provide clear error messages to users
- Allow manual retry for failed operations
- Cache failed requests for retry when network is restored

## Testing Endpoints

### Development/Staging Environment
```
Base URL: https://staging-api.mticket.my/
```

### Test User Accounts
```json
{
  "test_user_1": {
    "email": "<EMAIL>",
    "password": "TestPassword123!",
    "role": "user"
  },
  "test_user_2": {
    "email": "<EMAIL>", 
    "password": "TestPassword123!",
    "role": "user"
  }
}
```

### Sample Test Data
- Test users have pre-created tickets for testing
- Events include both upcoming and past events
- Various ticket statuses (pending, confirmed, checked-in)
- Different payment statuses for testing edge cases

## API Versioning

### Current Version: v1
- All endpoints are prefixed with `/api/`
- Version is included in User-Agent header
- Breaking changes will increment version number

### Future Considerations
- GraphQL endpoint for more efficient data fetching
- WebSocket support for real-time updates
- Push notification integration
- Biometric authentication support

## Support and Documentation

### Additional Resources
- Main API Documentation: `docs/api/README.md`
- Database Schema: `docs/database-schema.md`
- Authentication Guide: `docs/security/rbac.md`
- Error Handling: `docs/api-endpoints-reference.md`

### Contact Information
- Technical Support: <EMAIL>
- API Issues: <EMAIL>
- Documentation Updates: <EMAIL>
