/** @type {import('next').NextConfig} */
const nextConfig = {
  // External packages that should be bundled by Next.js
  serverExternalPackages: ['crypto', 'stream', 'buffer'],
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 60,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '*.supabase.co',
        port: '',
        pathname: '/**',
      },
    ],
  },
  env: {
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
  },
  // Redirects for better URL structure
  async redirects() {
    return [
      {
        source: '/subscriptions',
        destination: '/pricing',
        permanent: true,
      },
    ]
  },
  // Configure webpack for polyfills
  webpack: (config, { isServer }) => {
    // Only apply these polyfills in the browser build
    if (!isServer) {
      // Provide polyfills for Node.js core modules
      config.resolve.fallback = {
        ...config.resolve.fallback,
        crypto: 'crypto-browserify',
        stream: 'stream-browserify',
        buffer: 'buffer',
      };
    }
    return config;
  },
}

export default nextConfig