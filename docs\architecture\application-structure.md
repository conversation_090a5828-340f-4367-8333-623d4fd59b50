# Application Structure

This document provides a comprehensive overview of the mTicket.my application architecture, including all pages, components, API routes, and functions.

## Overview

mTicket.my is a comprehensive event management platform built with Next.js 15, featuring:
- Role-based access control (5 user roles)
- Subscription management
- Integrated payment processing
- Certificate generation system
- Activity logging and audit trails
- Mobile-responsive design

## System Architecture Overview

The mTicket.my platform follows a layered architecture pattern with clear separation of concerns:

### Architecture Layers
- **Frontend Layer**: Next.js 15 UI components and pages
- **API Layer**: RESTful API endpoints for data operations
- **Business Logic**: Core services for authentication, events, payments, and certificates
- **Data Layer**: PostgreSQL database with Supabase integration
- **External Services**: Payment gateways, email, and file storage

### Module Dependencies

The system is organized into modular components with well-defined dependencies:

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[Next.js 15 UI]
        Auth[Authentication Pages]
        Dashboard[Dashboard Pages]
        Public[Public Pages]
    end

    subgraph "API Layer"
        API[Next.js API Routes]
        AuthAPI[Auth APIs]
        EventAPI[Event APIs]
        PaymentAPI[Payment APIs]
        AdminAPI[Admin APIs]
    end

    subgraph "Business Logic"
        AuthService[Authentication Service]
        EventService[Event Management]
        PaymentService[Payment Processing]
        CertService[Certificate System]
        ActivityService[Activity Logging]
    end

    subgraph "Data Layer"
        DB[(PostgreSQL Database)]
        Storage[Supabase Storage]
        Cache[Redis Cache]
    end

    subgraph "External Services"
        PaymentGW[Payment Gateways]
        EmailService[Email Service]
        FileStorage[File Storage]
    end

    UI --> API
    Auth --> AuthAPI
    Dashboard --> API
    Public --> API

    API --> AuthService
    AuthAPI --> AuthService
    EventAPI --> EventService
    PaymentAPI --> PaymentService
    AdminAPI --> AuthService

    AuthService --> DB
    EventService --> DB
    PaymentService --> DB
    PaymentService --> PaymentGW
    CertService --> DB
    CertService --> Storage
    ActivityService --> DB

    PaymentService --> EmailService
    AuthService --> EmailService
    CertService --> FileStorage

    style UI fill:#e1f5fe
    style API fill:#f3e5f5
    style DB fill:#e8f5e8
    style PaymentGW fill:#fff3e0
```

## Directory Structure

```
app/
├── [slug]/                    # Dynamic slug routing for events
├── about/                     # About page
├── admin/                     # Admin-specific pages
├── api/                       # API routes
├── auth/                      # Authentication pages
├── certificates/              # Certificate verification
├── contact/                   # Contact page
├── dashboard/                 # Main dashboard area
├── events/                    # Event listing and details
├── pricing/                   # Subscription pricing
├── privacy/                   # Privacy policy
├── subscriptions/             # Subscription management
├── terms/                     # Terms of service
└── verify/                    # Email verification
```

## Public Pages

### Main Pages
- **Home Page** (`app/page.tsx`)
  - Hero section with event search
  - Featured/Latest/Popular event tabs
  - Event filtering and search functionality
  - Public access, no authentication required

- **About Page** (`app/about/page.tsx`)
  - Company information and features
  - Call-to-action for registration
  - Public access

- **Events Page** (`app/events/page.tsx`)
  - Event listing with filtering
  - Category-based filtering
  - Search functionality
  - Tabs for All/Paid/Free events
  - Public access

- **Event Details** (`app/events/[slug]/page.tsx`)
  - Individual event information
  - "Select Tickets" button (navigates to ticket selection)
  - QR code generation for sharing
  - Public access

- **✅ Ticket Selection** (`app/events/[slug]/tickets/page.tsx`)
  - ✅ **PRODUCTION-READY** Multi-step ticket selection process
  - ✅ Professional page layout with progress tracking
  - ✅ Desktop sidebar cart + mobile fixed bottom cart
  - ✅ Multiple ticket types with dynamic pricing (Early Bird, Standard, VIP)
  - ✅ Real-time price calculation and order summary
  - ✅ Touch-friendly quantity controls with validation
  - ✅ Session storage integration for seamless flow
  - **Steps**: Selection → Review → Registration
  - **Public access** with responsive design

- **Event Registration** (`app/events/[slug]/register/page.tsx`)
  - Registration form with selected tickets integration
  - Payment processing with dynamic pricing
  - Attendee information collection
  - Multi-step process

- **Pricing Page** (`app/pricing/page.tsx`)
  - Subscription plan display
  - Feature comparison
  - Payment integration
  - Public access

- **Contact Page** (`app/contact/page.tsx`)
  - Contact form and information
  - Public access

### Legal Pages
- **Privacy Policy** (`app/privacy/page.tsx`)
- **Terms of Service** (`app/terms/page.tsx`)

## Authentication Pages

### Auth Layout (`app/auth/layout.tsx`)
- Shared layout for all authentication pages
- Consistent styling and branding

### Authentication Routes
- **Login** (`app/auth/login/page.tsx`)
  - Email/password authentication
  - JWT token generation
  - Role-based redirection

- **Register** (`app/auth/register/page.tsx`)
  - User registration with subscription selection
  - Email verification
  - Automatic role assignment

- **Forgot Password** (`app/auth/forgot-password/page.tsx`)
  - Password reset request
  - Email-based reset flow

- **Reset Password** (`app/auth/reset-password/page.tsx`)
  - Password reset with token validation

- **Error Page** (`app/auth/error/page.tsx`)
  - Authentication error handling

## Dashboard Area

### Dashboard Layout (`app/dashboard/layout.tsx`)
- Sidebar navigation with role-based menu items
- Authentication requirement
- Responsive design

### Main Dashboard (`app/dashboard/page.tsx`)
- Overview statistics
- Revenue charts
- Event analytics
- User metrics
- Role-based content display

### User Management
- **Users Page** (`app/dashboard/users/page.tsx`)
  - User listing with role information
  - Search and filtering
  - Admin-only access
  - User role management

- **Profile Page** (`app/dashboard/profile/page.tsx`)
  - ✅ Comprehensive user profile management with tabbed interface
  - ✅ Profile editing with image upload and automatic compression
  - ✅ Advanced organization management (create, edit, link, unlink)
  - ✅ Subscription details and management
  - ✅ Notification preferences
  - ✅ API & webhook management
  - ✅ Modular tab components for maintainability
  - **Tabbed Interface**: Profile, Organization, Subscription, Notifications, API & Webhooks
  - **✅ Organization Features**:
    - Real-time search and select existing organizations
    - Create new organizations with full details (SSM, PIC info, address, website)
    - Edit organization information with permission validation (creator/admin/manager)
    - Safe link/unlink organizations to user profile
    - Visual status indicators (Linked/Selected with green badges)
    - Real-time organization search with debouncing and limit controls
    - Duplicate prevention and validation
    - Activity logging for all operations

### Event Management
- **Events Dashboard** (`app/dashboard/events/page.tsx`)
  - User's event listing
  - Event creation/editing
  - Registration statistics

- **Event Details** (`app/dashboard/events/[id]/page.tsx`)
  - Individual event management
  - Attendee management
  - Analytics

- **Create Event** (`app/dashboard/events/create/page.tsx`)
  - Event creation form
  - Image upload
  - Category selection

### Admin Functions
- **Roles Management** (`app/dashboard/admin/roles/page.tsx`)
  - Role creation and editing
  - Permission management
  - Admin-only access

- **Activity Logs** (`app/dashboard/activity-logs/page.tsx`)
  - System activity monitoring
  - User action tracking
  - Admin-only access

### Settings
- **Payment Gateways** (`app/dashboard/settings/payment-gateways/page.tsx`)
  - Payment gateway configuration
  - Admin/Manager access
  - Gateway status management

- **Subscriptions** (`app/dashboard/settings/subscriptions/page.tsx`)
  - Subscription plan management
  - Feature configuration
  - Admin-only access

- **Webhooks** (`app/dashboard/settings/webhooks/page.tsx`)
  - Webhook configuration
  - API key management
  - Security settings

- **Password Settings** (`app/dashboard/settings/password/page.tsx`)
  - Password change functionality
  - Security settings

### Certificates
- **Certificate Templates** (`app/dashboard/certificates/templates/page.tsx`)
  - Template management
  - Design customization
  - Field positioning

- **Certificate Verification** (`app/certificates/verify/page.tsx`)
  - QR code verification
  - Certificate authenticity check
  - Public access

### Financial Management
- **Wallet** (`app/dashboard/wallet/page.tsx`)
  - Balance management
  - Transaction history
  - Withdrawal requests

- **Reports** (`app/dashboard/reports/page.tsx`)
  - Financial reporting
  - Event analytics
  - Revenue tracking

### Subscription Management
- **Subscription Dashboard** (`app/dashboard/subscriptions/page.tsx`)
  - Current subscription status
  - Usage metrics
  - Plan management

- **Analytics** (`app/dashboard/subscriptions/analytics/page.tsx`)
  - Subscription analytics
  - Usage patterns
  - Performance metrics

## API Routes

### Public APIs
- **Events API** (`app/api/events/route.ts`)
  - GET: Fetch all published events
  - Public access, no authentication

- **Categories API** (`app/api/categories/route.ts`)
  - GET: Fetch active event categories
  - Public access

- **Environment Check** (`app/api/check-env/route.ts`)
  - GET: Check environment variables
  - Development utility

### Authentication APIs
- **Login** (`app/api/auth/login/route.ts`)
  - POST: User authentication
  - JWT token generation

- **Register** (`app/api/auth/register/route.ts`)
  - POST: User registration
  - Email verification

- **Password Reset** (`app/api/auth/reset-password/route.ts`)
  - POST: Password reset functionality

- **Admin Password Update** (`app/api/auth/admin/update-password/route.ts`)
  - POST: Admin-initiated password changes
  - Admin-only access

### Admin APIs
- **Users Management**
  - `app/api/admin/users/list/route.ts` - GET: List all users
  - `app/api/admin/users/create/route.ts` - POST: Create new users
  - `app/api/admin/users/update-role/route.ts` - POST: Update user roles

- **Roles Management**
  - `app/api/admin/roles/list/route.ts` - GET: List all roles
  - Admin-only access

- **Payment Gateways**
  - `app/api/admin/payment-gateways/list/route.ts` - GET: List gateways
  - `app/api/admin/payment-gateways/create/route.ts` - POST: Create gateway
  - `app/api/admin/payment-gateways/update/route.ts` - PUT: Update gateway
  - `app/api/admin/payment-gateways/toggle/route.ts` - POST: Toggle status

### Dashboard APIs
- **Events** (`app/api/dashboard/events/route.ts`)
  - GET: User's events with registration counts
  - Authenticated access

- **Tickets** (`app/api/dashboard/tickets/route.ts`)
  - GET: User's ticket registrations
  - Sample data generation

### Webhook APIs
- **Webhook Management** (`app/api/webhooks/route.ts`)
  - GET: List user webhooks
  - POST: Create new webhook
  - Authenticated access

- **Webhook Receiver** (`app/api/webhooks/receive/route.ts`)
  - POST: Receive webhook notifications
  - API key authentication

### Organization APIs
- **Organization Search** (`app/api/organizations/route.ts`)
  - GET: Search organizations with filters
  - Public access for organization lookup

- **Organization Create** (`app/api/organizations/create/route.ts`)
  - POST: Create new organization
  - Authenticated access with duplicate prevention

- **Organization Update** (`app/api/organizations/update/route.ts`)
  - PUT: Update existing organization
  - Permission-based access (creator/admin/manager)

- **Organization Link** (`app/api/organizations/link/route.ts`)
  - POST: Link user to organization and upgrade role
  - Authenticated access with role management

### Security APIs
- **Secure QR Generation** (`app/api/tickets/secure-qr/route.ts`)
  - POST: Generate time-based secure QR codes
  - HMAC-signed tokens with 30-second expiry

- **Secure Verification** (`app/api/verify/secure/route.ts`)
  - POST: Verify secure ticket tokens
  - GET: Direct QR code verification
  - Public access for QR scanners

### Testing APIs
- **Test API** (`app/api/test/route.ts`)
  - GET: API key authentication test
  - Activity logging demonstration

- **API Key Validation** (`app/api/api-key/route.ts`)
  - GET: Validate API keys
  - Security testing

## Role-Based Access Control

### User Roles (5 roles in database)
1. **admin** - Full system administrator with access to all systems
2. **user** - Registered users/participants
3. **manager** - Users who created organizations and can manage their own events and participants
4. **supermanager** - Same as manager but with commission access (future feature)
5. **event_admin** - Can manage all events created in the system (not just their own)

### Access Levels
1. **Public** - No authentication required
2. **Authenticated** - Valid JWT token required
3. **Admin** - Only "admin" role has full access to all functions
4. **Manager** - Manager roles: `["manager", "supermanager"]` can manage own events and participants
5. **Event Admin** - "event_admin" role can manage all events but not full system access
6. **Elevated** - Admin + Manager + Event Admin roles: `["admin", "manager", "supermanager", "event_admin"]`

### Protected Routes
- All `/dashboard/*` routes require authentication
- Admin-specific routes require "admin" role only
- Manager routes allow manager-level roles
- API endpoints have varying access requirements based on function sensitivity

## Key Features

### Authentication System
- JWT-based authentication
- Role-based access control
- Password reset functionality
- Email verification

### Event Management
- Event creation and editing
- Registration management
- QR code generation
- Category-based organization

### Subscription System
- Multiple subscription plans
- Feature-based limitations
- Payment integration
- Usage tracking

### Certificate System
- Template management
- QR code verification
- Drag-drop field positioning
- Authenticity verification

### Activity Logging
- Comprehensive action tracking
- Admin monitoring
- Security auditing
- User behavior analysis

### Webhook System
- User-specific API keys
- Event notifications
- Third-party integrations
- Security validation

This documentation serves as the single source of truth for the application structure and should be referenced to avoid duplication when implementing new features or modifications.
