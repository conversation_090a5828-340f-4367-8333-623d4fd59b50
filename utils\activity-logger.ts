import { supabase } from "@/lib/supabase"

/**
 * Activity log categories
 */
export enum ActivityCategory {
  AUTH = "auth",
  USER = "user",
  EVENT = "event",
  REGISTRATION = "registration",
  PAYMENT = "payment",
  CERTIFICATE = "certificate",
  EXPORT = "export",
  SETTINGS = "settings",
  SYSTEM = "system",
  ORGANIZATION = "organization",
  WEBHOOK = "webhook",
  ATTENDANCE = "attendance",
  SUBSCRIPTION = "subscription",
}

/**
 * Parameters for logging an activity
 */
export type ActivityLogParams = {
  userId?: string // Optional for system actions or anonymous users
  action: string // The action performed (e.g., 'login', 'create_event')
  entityType: string // The type of entity (e.g., 'user', 'event')
  entityId?: string // Optional ID of the entity (legacy field)
  category?: ActivityCategory // Category for filtering
  details?: Record<string, any> // Additional details about the action
  ipAddress?: string // IP address of the user
  userAgent?: string // User agent of the browser

  // Specific foreign key fields for better relationships
  eventId?: string // UUID reference to events table
  registrationId?: string // UUID reference to registrations table
  organizationId?: string // UUID reference to organizations table
  certificateId?: string // UUID reference to certificates table
  subscriptionId?: string // UUID reference to user_subscription table
  apiKeyId?: string // UUID reference to api_keys table
  webhookId?: string // UUID reference to webhooks table
  targetUserId?: string // UUID reference to users table (for admin actions)
  paymentId?: string // External payment gateway ID
  sessionId?: string // Session identifier for auth activities
}

/**
 * Log an activity in the system
 *
 * @param params Activity log parameters
 * @returns Promise<void>
 */
export async function logActivity({
  userId,
  action,
  entityType,
  entityId,
  category,
  details = {},
  ipAddress,
  userAgent,
  eventId,
  registrationId,
  organizationId,
  certificateId,
  subscriptionId,
  apiKeyId,
  webhookId,
  targetUserId,
  paymentId,
  sessionId,
}: ActivityLogParams): Promise<void> {
  try {
    // Use provided IP and user agent
    const ip = ipAddress || 'not_provided'
    const ua = userAgent || 'not_provided'

    // Determine category if not provided
    const logCategory = category || getCategoryFromEntityType(entityType)

    // Build the log entry with both legacy and new foreign key fields
    const logEntry: any = {
      user_id: userId,
      action,
      entity_type: entityType,
      entity_id: entityId, // Keep for backward compatibility
      category: logCategory,
      details,
      ip_address: ip,
      user_agent: ua,
      created_at: new Date().toISOString(),
    }

    // Add specific foreign key fields if provided
    if (eventId) logEntry.event_id = eventId
    if (registrationId) logEntry.registration_id = registrationId
    if (organizationId) logEntry.organization_id = organizationId
    if (certificateId) logEntry.certificate_id = certificateId
    if (subscriptionId) logEntry.subscription_id = subscriptionId
    if (apiKeyId) logEntry.api_key_id = apiKeyId
    if (webhookId) logEntry.webhook_id = webhookId
    if (targetUserId) logEntry.target_user_id = targetUserId
    if (paymentId) logEntry.payment_id = paymentId
    if (sessionId) logEntry.session_id = sessionId

    // Insert activity log
    await supabase.from("activity_logs").insert([logEntry])
  } catch (error) {
    console.error("Error logging activity:", error)
  }
}

/**
 * Helper function to determine category from entity type
 */
function getCategoryFromEntityType(entityType: string): ActivityCategory {
  switch (entityType.toLowerCase()) {
    case 'user':
    case 'profile':
      return ActivityCategory.USER
    case 'event':
      return ActivityCategory.EVENT
    case 'registration':
    case 'ticket':
      return ActivityCategory.REGISTRATION
    case 'payment':
    case 'transaction':
      return ActivityCategory.PAYMENT
    case 'certificate':
      return ActivityCategory.CERTIFICATE
    case 'auth':
    case 'login':
    case 'logout':
    case 'password':
      return ActivityCategory.AUTH
    case 'export':
    case 'report':
      return ActivityCategory.EXPORT
    case 'setting':
    case 'configuration':
      return ActivityCategory.SETTINGS
    case 'organization':
      return ActivityCategory.ORGANIZATION
    case 'webhook':
    case 'api':
      return ActivityCategory.WEBHOOK
    case 'attendance':
    case 'check_in':
      return ActivityCategory.ATTENDANCE
    case 'subscription':
    case 'plan':
      return ActivityCategory.SUBSCRIPTION
    default:
      return ActivityCategory.SYSTEM
  }
}
