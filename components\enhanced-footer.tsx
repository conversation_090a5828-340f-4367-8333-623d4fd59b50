import Link from "next/link"
import Image from "next/image"
import { Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin, Youtube } from "lucide-react"

export function EnhancedFooter() {
  return (
    <footer className="relative bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-20">
        <div
          className="w-full h-full"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}
        ></div>
      </div>
      {/* Main Footer Content */}
      <div className="relative container px-4 md:px-6 py-12">
        <div className="mx-auto max-w-7xl grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Image
              src="https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//logo.png"
              alt="mTicket.my logo"
              width={142}
              height={42}
              className="h-8 w-auto object-contain"
              priority
            />
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              The complete event management platform for creating, managing, and selling tickets to events.
              Trusted by thousands of event organizers worldwide.
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-gray-300 hover:text-purple-300 transition-colors">
                <Facebook className="h-5 w-5" />
                <span className="sr-only">Facebook</span>
              </Link>
              <Link href="#" className="text-gray-300 hover:text-purple-300 transition-colors">
                <Twitter className="h-5 w-5" />
                <span className="sr-only">Twitter</span>
              </Link>
              <Link href="#" className="text-gray-300 hover:text-purple-300 transition-colors">
                <Instagram className="h-5 w-5" />
                <span className="sr-only">Instagram</span>
              </Link>
              <Link href="#" className="text-gray-300 hover:text-purple-300 transition-colors">
                <Linkedin className="h-5 w-5" />
                <span className="sr-only">LinkedIn</span>
              </Link>
              <Link href="#" className="text-gray-300 hover:text-purple-300 transition-colors">
                <Youtube className="h-5 w-5" />
                <span className="sr-only">YouTube</span>
              </Link>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Company</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about" className="text-gray-300 hover:text-purple-300 transition-colors text-sm">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/careers" className="text-gray-300 hover:text-purple-300 transition-colors text-sm">
                  Careers
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-300 hover:text-purple-300 transition-colors text-sm">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/blog" className="text-gray-300 hover:text-purple-300 transition-colors text-sm">
                  Blog
                </Link>
              </li>
              <li>
                <Link href="/company" className="text-gray-300 hover:text-purple-300 transition-colors text-sm">
                  Company
                </Link>
              </li>
            </ul>
          </div>

          {/* Product Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Product</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/features" className="text-gray-300 hover:text-purple-300 transition-colors text-sm">
                  Features
                </Link>
              </li>
              <li>
                <Link href="/pricing" className="text-gray-300 hover:text-purple-300 transition-colors text-sm">
                  Pricing
                </Link>
              </li>
              <li>
                <Link href="/docs" className="text-gray-300 hover:text-purple-300 transition-colors text-sm">
                  Documentation
                </Link>
              </li>
              <li>
                <Link href="/api-docs" className="text-gray-300 hover:text-purple-300 transition-colors text-sm">
                  API
                </Link>
              </li>
              <li>
                <Link href="/integrations" className="text-gray-300 hover:text-purple-300 transition-colors text-sm">
                  Integrations
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white">Contact Info</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <Mail className="h-4 w-4 mt-0.5 text-purple-300 flex-shrink-0" />
                <div>
                  <p className="text-sm text-gray-300"><EMAIL></p>
                  <p className="text-xs text-gray-400">General inquiries</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <Phone className="h-4 w-4 mt-0.5 text-purple-300 flex-shrink-0" />
                <div>
                  <p className="text-sm text-gray-300">+60 3 1234 5678</p>
                  <p className="text-xs text-gray-400">Mon-Fri 9AM-6PM MYT</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <MapPin className="h-4 w-4 mt-0.5 text-purple-300 flex-shrink-0" />
                <div>
                  <p className="text-sm text-gray-300">Kuala Lumpur, Malaysia</p>
                  <p className="text-xs text-gray-400">Southeast Asia</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Legal Links & Copyright */}
      <div className="relative border-t border-white/20">
        <div className="container px-4 md:px-6 py-6">
          <div className="mx-auto max-w-7xl flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex flex-wrap gap-6">
              <Link href="/privacy" className="text-sm text-gray-300 hover:text-purple-300 transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-sm text-gray-300 hover:text-purple-300 transition-colors">
                Terms of Service
              </Link>
              <Link href="/cookies" className="text-sm text-gray-300 hover:text-purple-300 transition-colors">
                Cookie Policy
              </Link>
              <Link href="/security" className="text-sm text-gray-300 hover:text-purple-300 transition-colors">
                Security
              </Link>
            </div>
            <p className="text-sm text-gray-300">
              © 2025 mTicket.my. All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}
