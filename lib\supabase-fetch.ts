/**
 * Utility functions for making fetch requests to Supabase REST API
 */

import { supabase } from './supabase';

// Base URL for Supabase REST API
const SUPABASE_REST_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

/**
 * Get the authorization headers for Supabase REST API
 * @returns Headers object with authorization headers
 */
export async function getSupabaseHeaders() {
  // Get the current session
  const { data: sessionData } = await supabase.auth.getSession();

  // Base headers that are always needed
  const headers: HeadersInit = {
    'apikey': SUPABASE_ANON_KEY as string,
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Prefer': 'return=representation',
  };

  // Add authorization header if user is logged in
  if (sessionData?.session) {
    headers['Authorization'] = `Bearer ${sessionData.session.access_token}`;
  } else {
    headers['Authorization'] = `<PERSON><PERSON> ${SUPABASE_ANON_KEY}`;
  }

  return headers;
}

/**
 * Fetch data from Supabase REST API
 * @param table Table name
 * @param query Query parameters
 * @returns Promise with the response data
 */
export async function fetchFromSupabase<T>(
  table: string,
  query: Record<string, string> = {}
): Promise<T> {
  if (!SUPABASE_REST_URL || !SUPABASE_ANON_KEY) {
    throw new Error('Supabase URL or Anon Key not found in environment variables');
  }

  // Build the query string
  const queryString = Object.entries(query)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');

  // Build the URL
  const url = `${SUPABASE_REST_URL}/rest/v1/${table}${queryString ? `?${queryString}` : ''}`;

  // Get the headers
  const headers = await getSupabaseHeaders();

  // Make the request
  const response = await fetch(url, {
    method: 'GET',
    headers,
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Error fetching from Supabase: ${response.status} ${response.statusText} - ${errorText}`);
  }

  return response.json() as Promise<T>;
}

/**
 * Fetch a single record from Supabase REST API
 * @param table Table name
 * @param query Query parameters
 * @returns Promise with the response data
 */
export async function fetchSingleFromSupabase<T>(
  table: string,
  query: Record<string, string> = {}
): Promise<T> {
  // Add the single=true parameter to get a single record
  const singleQuery = { ...query, 'select': query.select || '*' };

  const headers = await getSupabaseHeaders();

  // Make sure we have the right Accept header for single object
  headers['Accept'] = 'application/json';

  if (!SUPABASE_REST_URL || !SUPABASE_ANON_KEY) {
    throw new Error('Supabase URL or Anon Key not found in environment variables');
  }

  // Build the query string
  const queryString = Object.entries(singleQuery)
    .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
    .join('&');

  // Build the URL
  const url = `${SUPABASE_REST_URL}/rest/v1/${table}${queryString ? `?${queryString}` : ''}`;

  // Make the request
  const response = await fetch(url, {
    method: 'GET',
    headers,
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`Error fetching from Supabase: ${response.status} ${response.statusText} - ${errorText}`);
  }

  return response.json() as Promise<T>;
}
