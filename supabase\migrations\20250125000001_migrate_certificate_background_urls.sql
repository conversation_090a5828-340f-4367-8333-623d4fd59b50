-- Migration to update certificate background image URLs to use new certificates storage bucket
-- This assumes the files have been manually copied to the new bucket structure

-- Update background_image_url to point to certificates/bg/ instead of avatars/certificate-backgrounds/
UPDATE certificate_templates
SET background_image_url = REPLACE(
  background_image_url,
  '/storage/v1/object/public/avatars/certificate-backgrounds/',
  '/storage/v1/object/public/certificates/bg/'
)
WHERE background_image_url LIKE '%/avatars/certificate-backgrounds/%';

-- Also update any URLs that might already be in certificate-backgrounds bucket
UPDATE certificate_templates
SET background_image_url = REPLACE(
  background_image_url,
  '/storage/v1/object/public/certificate-backgrounds/',
  '/storage/v1/object/public/certificates/bg/'
)
WHERE background_image_url LIKE '%/certificate-backgrounds/%';

-- Log the changes
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % certificate template background image URLs to use certificates/bg/ storage', updated_count;
END $$;
