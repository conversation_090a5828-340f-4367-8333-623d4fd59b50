/**
 * Utility functions for QR code generation and short URL handling
 */

/**
 * Generate a short URL for an event
 * @param slug - The event slug
 * @param baseUrl - The base URL of the application (optional, defaults to current domain)
 * @returns The short URL
 */
export function generateShortUrl(slug: string, baseUrl?: string): string {
  const base = baseUrl || (typeof window !== 'undefined' ? window.location.origin : '')
  return `${base}/${slug}`
}

/**
 * Generate a full event URL
 * @param slug - The event slug
 * @param baseUrl - The base URL of the application (optional, defaults to current domain)
 * @returns The full event URL
 */
export function generateEventUrl(slug: string, baseUrl?: string): string {
  const base = baseUrl || (typeof window !== 'undefined' ? window.location.origin : '')
  return `${base}/events/${slug}`
}

/**
 * Get QR code data for an event
 * @param slug - The event slug
 * @param baseUrl - The base URL of the application (optional)
 * @returns QR code configuration object
 */
export function getQRCodeData(slug: string, baseUrl?: string) {
  const shortUrl = generateShortUrl(slug, baseUrl)
  
  return {
    data: shortUrl,
    size: 256,
    level: 'M', // Error correction level
    margin: 4,
    color: {
      dark: '#000000',
      light: '#FFFFFF',
    },
  }
}

/**
 * Validate if a string is a valid event slug format
 * @param slug - The slug to validate
 * @returns True if valid slug format
 */
export function isValidSlugFormat(slug: string): boolean {
  // Event slugs should be 4-5 characters, Base62 format
  const slugRegex = /^[0-9a-zA-Z]{4,5}$/
  return slugRegex.test(slug)
}

/**
 * Generate QR code API URL for an event
 * @param slug - The event slug
 * @param baseUrl - The base URL of the application (optional)
 * @returns API URL for QR code data
 */
export function getQRCodeApiUrl(slug: string, baseUrl?: string): string {
  const base = baseUrl || (typeof window !== 'undefined' ? window.location.origin : '')
  return `${base}/api/events/${slug}/qr`
}
