"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Check, CreditCard, Edit, Plus, Trash } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"

type SubscriptionPlan = {
  id: string
  name: string
  price: number
  description: string
  features: string[]
  is_popular?: boolean
  max_events?: number | null
  max_attendees_per_event?: number | null
  // Feature toggles
  certificates_enabled?: boolean
  attendance_enabled?: boolean
  webhooks_enabled?: boolean
  analytics_enabled?: boolean
  reports_enabled?: boolean
}



export default function SubscriptionsPage() {
  const router = useRouter()
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [loading, setLoading] = useState(true)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<SubscriptionPlan | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { user, isAdmin } = useAuth()
  const { toast } = useToast()

  // Helper function to get cookie value
  const getCookie = (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  useEffect(() => {
    const fetchPlans = async () => {
      setLoading(true)
      try {
        const token = getCookie('auth_token')
        if (!token) {
          throw new Error('No authentication token found')
        }

        const response = await fetch('/api/admin/subscription-plans/list', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        })

        if (!response.ok) {
          throw new Error('Failed to fetch subscription plans')
        }

        const data = await response.json()
        setPlans(data.plans || [])
      } catch (error) {
        console.error("Error fetching subscription plans:", error)
        toast({
          title: "Error",
          description: "Failed to fetch subscription plans",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchPlans()
  }, [])

  const handleEditPlan = (plan: SubscriptionPlan) => {
    router.push(`/dashboard/settings/subscriptions/${plan.id}/edit`)
  }

  const handleDeletePlan = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan)
    setIsDeleteDialogOpen(true)
  }

  const handleAddPlan = () => {
    router.push('/dashboard/settings/subscriptions/add')
  }



  const handleConfirmDelete = async () => {
    if (!selectedPlan) return

    setIsSubmitting(true)
    try {
      const token = getCookie('auth_token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(`/api/admin/subscription-plans/${selectedPlan.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete subscription plan')
      }

      setPlans(plans.filter((plan) => plan.id !== selectedPlan.id))
      setIsDeleteDialogOpen(false)
      toast({
        title: "Success",
        description: "Subscription plan deleted successfully",
      })
    } catch (error: any) {
      console.error("Error deleting subscription plan:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to delete subscription plan",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Auto-generate features based on plan settings
  const generateAutoFeatures = (plan: SubscriptionPlan): string[] => {
    const autoFeatures: string[] = []

    // Add event limit features
    if (plan.max_events === null) {
      autoFeatures.push("Unlimited events")
    } else if (plan.max_events === 1) {
      autoFeatures.push("1 event at a time")
    } else {
      autoFeatures.push(`${plan.max_events} events at a time`)
    }

    // Add attendee limit features
    if (plan.max_attendees_per_event === null) {
      autoFeatures.push("Unlimited attendees")
    } else {
      autoFeatures.push(`Up to ${plan.max_attendees_per_event} attendees per event`)
    }

    // Add toggle-based features
    if (plan.certificates_enabled) {
      autoFeatures.push("Event certificates")
    }
    if (plan.attendance_enabled) {
      autoFeatures.push("Secure QR code check-in")
    }
    if (plan.webhooks_enabled) {
      autoFeatures.push("API & Webhook access")
    }
    if (plan.analytics_enabled) {
      autoFeatures.push("Event analytics")
    }
    if (plan.reports_enabled) {
      autoFeatures.push("Event reporting")
    }

    return autoFeatures
  }

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <h1 className="text-2xl font-bold">Subscription Plans</h1>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="space-y-2">
                <div className="h-6 w-24 bg-muted rounded"></div>
                <div className="h-8 w-32 bg-muted rounded"></div>
                <div className="h-4 w-full bg-muted rounded"></div>
              </CardHeader>
              <CardContent className="space-y-2">
                {[1, 2, 3, 4, 5].map((j) => (
                  <div key={j} className="h-4 w-full bg-muted rounded"></div>
                ))}
              </CardContent>
              <CardFooter>
                <div className="h-10 w-full bg-muted rounded"></div>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Subscription Plans</h1>
        {isAdmin() && (
          <Button onClick={handleAddPlan}>
            <Plus className="mr-2 h-4 w-4" />
            Add Plan
          </Button>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {plans.map((plan) => (
          <Card key={plan.id} className={`relative flex flex-col ${plan.is_popular ? "border-primary" : ""}`}>
            {plan.is_popular && (
              <div className="absolute -top-2 -right-2 z-10">
                <Badge className="bg-primary text-primary-foreground">Popular</Badge>
              </div>
            )}
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {plan.name}
                {isAdmin() && (
                  <div className="flex items-center gap-1">
                    <Button variant="ghost" size="icon" onClick={() => handleEditPlan(plan)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" onClick={() => handleDeletePlan(plan)}>
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </CardTitle>
              <div className="flex items-baseline">
                <span className="text-3xl font-bold">RM{plan.price.toFixed(2)}</span>
                <span className="text-muted-foreground ml-1">/month</span>
              </div>
              <CardDescription>{plan.description}</CardDescription>
            </CardHeader>
            <CardContent className="flex-grow">
              <ul className="space-y-2">
                {/* Auto-generated features */}
                {generateAutoFeatures(plan).map((feature, index) => (
                  <li key={`auto-${index}`} className="flex items-start">
                    <Check className="mr-2 h-4 w-4 text-primary mt-1" />
                    <span className="text-sm text-primary">{feature}</span>
                  </li>
                ))}
                {/* Custom features */}
                {plan.features.filter(f => !generateAutoFeatures(plan).includes(f)).map((feature, index) => (
                  <li key={`custom-${index}`} className="flex items-start">
                    <Check className="mr-2 h-4 w-4 text-primary mt-1" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
            <CardFooter className="mt-auto">
              <Button className="w-full" variant={plan.is_popular ? "default" : "outline"}>
                <CreditCard className="mr-2 h-4 w-4" />
                {user?.subscription_status === "active" && user?.role_name?.toLowerCase() === plan.name.toLowerCase()
                  ? "Current Plan"
                  : "Subscribe"}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>



      {/* Delete Plan Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Subscription Plan</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the "{selectedPlan?.name}" plan? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleConfirmDelete} disabled={isSubmitting}>
              {isSubmitting ? "Deleting..." : "Delete Plan"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
