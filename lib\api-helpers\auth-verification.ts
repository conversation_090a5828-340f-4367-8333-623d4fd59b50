import { NextRequest } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import jwt from "jsonwebtoken"

export interface AuthResult {
  isAuthenticated: boolean
  isAdmin: boolean
  isManager: boolean
  userId?: string
  user?: any
  error?: string
}

/**
 * Helper function to verify admin access from request
 * Reduces code duplication across API routes
 */
export async function verifyAdminAccess(request: NextRequest): Promise<AuthResult> {
  try {
    // Get auth token from cookies
    const authToken = request.cookies.get("auth_token")?.value

    if (!authToken) {
      return {
        isAuthenticated: false,
        isAdmin: false,
        isManager: false,
        error: "No auth token found"
      }
    }

    // Decode the JWT token
    const decoded = jwt.decode(authToken) as any
    if (!decoded || !decoded.userId) {
      return {
        isAuthenticated: false,
        isAdmin: false,
        isManager: false,
        error: "Invalid auth token"
      }
    }

    // Get user details with role from database
    const supabaseAdmin = getSupabaseAdmin()
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .select(`
        id,
        email,
        full_name,
        role_id,
        user_roles!inner(role_name)
      `)
      .eq('id', decoded.userId)
      .single()

    if (userError || !userData) {
      return {
        isAuthenticated: false,
        isAdmin: false,
        isManager: false,
        error: "User not found"
      }
    }

    // Extract role name
    const roleName = (userData.user_roles as any)?.role_name

    // Check permissions
    const isAdmin = roleName === "admin"
    const isManager = ["manager", "supermanager", "event_admin", "admin"].includes(roleName)

    return {
      isAuthenticated: true,
      isAdmin,
      isManager,
      userId: decoded.userId,
      user: {
        ...userData,
        role_name: roleName
      },
      error: undefined
    }
  } catch (error) {
    console.error("Error verifying admin access:", error)
    return {
      isAuthenticated: false,
      isAdmin: false,
      isManager: false,
      error: "Failed to verify admin access"
    }
  }
}

/**
 * Helper function to verify user authentication (any authenticated user)
 */
export async function verifyUserAccess(request: NextRequest): Promise<AuthResult> {
  try {
    // Get auth token from cookies
    const authToken = request.cookies.get("auth_token")?.value

    if (!authToken) {
      return {
        isAuthenticated: false,
        isAdmin: false,
        isManager: false,
        error: "No auth token found"
      }
    }

    // Decode the JWT token
    const decoded = jwt.decode(authToken) as any
    if (!decoded || !decoded.userId) {
      return {
        isAuthenticated: false,
        isAdmin: false,
        isManager: false,
        error: "Invalid auth token"
      }
    }

    // Get user details with role from database
    const supabaseAdmin = getSupabaseAdmin()
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .select(`
        id,
        email,
        full_name,
        role_id,
        user_roles!inner(role_name)
      `)
      .eq('id', decoded.userId)
      .single()

    if (userError || !userData) {
      return {
        isAuthenticated: false,
        isAdmin: false,
        isManager: false,
        error: "User not found"
      }
    }

    // Extract role name
    const roleName = (userData.user_roles as any)?.role_name

    // Check permissions
    const isAdmin = roleName === "admin"
    const isManager = ["manager", "supermanager", "event_admin", "admin"].includes(roleName)

    return {
      isAuthenticated: true,
      isAdmin,
      isManager,
      userId: decoded.userId,
      user: {
        ...userData,
        role_name: roleName
      },
      error: undefined
    }
  } catch (error) {
    return {
      isAuthenticated: false,
      isAdmin: false,
      isManager: false,
      error: "Failed to verify user access"
    }
  }
}

/**
 * Helper function to create standardized error responses
 */
export function createAuthErrorResponse(error: string, status: number = 401) {
  return new Response(
    JSON.stringify({ error }),
    {
      status,
      headers: { 'Content-Type': 'application/json' }
    }
  )
}
