"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { ArrowLeft, Mail, AlertCircle } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>onte<PERSON>, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AuthFormLayout } from "@/components/auth-form-layout"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"

export default function ForgotPasswordPage() {
  const [email, setEmail] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [isPasswordResetEnabled, setIsPasswordResetEnabled] = useState<boolean | null>(null)
  const [checkingSettings, setCheckingSettings] = useState(true)
  const { toast } = useToast()
  const { resetPassword } = useAuth()

  // Check if password reset is enabled
  useEffect(() => {
    const checkPasswordResetEnabled = async () => {
      try {
        const response = await fetch('/api/app-settings/check-feature', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ feature: 'password_reset' }),
        })

        if (response.ok) {
          const data = await response.json()
          setIsPasswordResetEnabled(data.enabled)
        } else {
          // Default to enabled if check fails
          setIsPasswordResetEnabled(true)
        }
      } catch (error) {
        console.error('Error checking password reset status:', error)
        // Default to enabled if check fails
        setIsPasswordResetEnabled(true)
      } finally {
        setCheckingSettings(false)
      }
    }

    checkPasswordResetEnabled()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const success = await resetPassword(email)
      if (success) {
        setIsSubmitted(true)
      }
    } catch (error: any) {
      console.error("Reset password error:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to send reset email. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Show loading state while checking settings
  if (checkingSettings) {
    return (
      <AuthFormLayout
        title="Forgot password"
        description="Loading..."
        footer={
          <div className="text-center text-sm">
            <Link
              href="/auth/login"
              className="flex items-center justify-center font-medium text-primary underline-offset-4 hover:underline"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to login
            </Link>
          </div>
        }
      >
        <CardContent className="space-y-4">
          <div className="flex items-center justify-center py-8">
            <svg
              className="animate-spin h-8 w-8 text-primary"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </div>
        </CardContent>
      </AuthFormLayout>
    )
  }

  // Show disabled message if password reset is disabled
  if (isPasswordResetEnabled === false) {
    return (
      <AuthFormLayout
        title="Password Reset Currently Disabled"
        description="Password reset functionality is currently disabled by the administrator"
        footer={
          <div className="text-center text-sm">
            <Link
              href="/auth/login"
              className="flex items-center justify-center font-medium text-primary underline-offset-4 hover:underline"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to login
            </Link>
          </div>
        }
      >
        <CardContent className="space-y-4">
          <div className="rounded-lg bg-orange-50 p-4 text-orange-800 dark:bg-orange-950 dark:text-orange-300">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2" />
              <div>
                <h3 className="font-medium">Password Reset Disabled</h3>
                <p className="text-sm mt-1">
                  Password reset functionality is currently disabled. Please contact the administrator for assistance with your password.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button asChild className="w-full" variant="outline">
            <Link href="/auth/login">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Login
            </Link>
          </Button>
        </CardFooter>
      </AuthFormLayout>
    )
  }

  return (
    <AuthFormLayout
      title="Forgot password"
      description="Enter your email address and we'll send you a link to reset your password"
      footer={
        !isSubmitted ? (
          <div className="text-center text-sm">
            <Link
              href="/auth/login"
              className="flex items-center justify-center font-medium text-primary underline-offset-4 hover:underline"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to login
            </Link>
          </div>
        ) : (
          <div className="text-center">
            <Link
              href="/auth/login"
              className="inline-flex items-center font-medium text-primary underline-offset-4 hover:underline"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to login
            </Link>
          </div>
        )
      }
    >
      {!isSubmitted ? (
            <form onSubmit={handleSubmit}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                  />
                </div>
              </CardContent>
              <CardFooter className="flex flex-col space-y-4">
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <div className="flex items-center">
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Sending reset link...
                    </div>
                  ) : (
                    <div className="flex items-center">
                      <Mail className="mr-2 h-4 w-4" />
                      Send Reset Link
                    </div>
                  )}
                </Button>
              </CardFooter>
            </form>
          ) : (
            <CardContent className="space-y-4">
              <div className="rounded-lg bg-green-50 p-4 text-green-800 dark:bg-green-950 dark:text-green-300">
                <p>
                  We've sent a password reset link to <strong>{email}</strong>. Please check your email and follow the
                  instructions to reset your password.
                </p>
              </div>
            </CardContent>
          )}
    </AuthFormLayout>
  )
}
