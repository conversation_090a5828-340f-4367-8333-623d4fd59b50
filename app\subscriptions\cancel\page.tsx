import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { XCircle, ArrowLeft, CreditCard } from "lucide-react"
import MainNav from "@/components/main-nav"
import Footer from "@/components/footer"

export const metadata = {
  title: "Subscription Cancelled | mTicket.my - Event Management Platform",
  description: "Your subscription process was cancelled",
}

export default function SubscriptionCancelPage() {
  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1">
        {/* Hero Section with Purple Background */}
        <section className="w-full py-12 md:py-24 lg:py-32 bg-gradient-to-r from-purple-100 to-indigo-100 dark:from-purple-950 dark:to-indigo-950">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center space-y-4 text-center">
              <div className="space-y-2">
                <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl/none">
                  Subscription Cancelled
                </h1>
                <p className="mx-auto max-w-[700px] text-gray-500 md:text-xl dark:text-gray-400">
                  Your subscription process was cancelled. No charges were made to your account.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Cancel Status Section */}
        <div className="container mx-auto py-12 px-4">
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader className="text-center">
                <div className="flex justify-center mb-4">
                  <XCircle className="h-16 w-16 text-orange-500" />
                </div>
                <CardTitle className="text-2xl">Subscription Cancelled</CardTitle>
                <CardDescription>
                  You cancelled the subscription process. Don't worry, no payment was processed and no charges were made to your account.
                </CardDescription>
              </CardHeader>
              <CardContent className="text-center space-y-6">
                <div className="bg-muted p-4 rounded-lg">
                  <h3 className="font-semibold mb-2">What happened?</h3>
                  <div className="space-y-2 text-sm text-muted-foreground">
                    <p>• You cancelled the payment process</p>
                    <p>• No charges were made to your payment method</p>
                    <p>• Your account remains on the current plan</p>
                    <p>• You can try subscribing again anytime</p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button asChild size="lg">
                    <Link href="/pricing">
                      <CreditCard className="mr-2 h-4 w-4" />
                      Try Again
                    </Link>
                  </Button>
                  <Button asChild variant="outline" size="lg">
                    <Link href="/dashboard">
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Back to Dashboard
                    </Link>
                  </Button>
                </div>

                <div className="text-center">
                  <p className="text-sm text-muted-foreground mb-4">
                    Need help choosing the right plan?
                  </p>
                  <Button asChild variant="link">
                    <Link href="/contact">Contact our sales team</Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Alternative Options */}
            <div className="mt-8">
              <h3 className="text-xl font-semibold text-center mb-6">Other Options</h3>
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Start with Free Plan</CardTitle>
                    <CardDescription>
                      Get started with our free plan and upgrade later when you're ready.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button asChild className="w-full" variant="outline">
                      <Link href="/auth/register">Sign Up Free</Link>
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Contact Support</CardTitle>
                    <CardDescription>
                      Have questions about our plans? Our team is here to help.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button asChild className="w-full" variant="outline">
                      <Link href="/contact">Get Help</Link>
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
