import { PageLayout } from "@/components/page-layout"
import { HeroSection } from "@/components/hero-section"

export const metadata = {
  title: "Careers | mTicket.my",
  description: "Join our team at mTicket.my - explore career opportunities in event management technology",
}

export default function CareersPage() {
  return (
    <PageLayout>
      {/* Corporate Hero Section */}
      <section className="relative w-full py-20 md:py-32 lg:py-40 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}
          ></div>
        </div>

        <div className="container relative px-4 md:px-6">
          <div className="flex flex-col items-center space-y-8 text-center">
            {/* Main Heading */}
            <div className="space-y-4 max-w-4xl">
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl lg:text-7xl">
                Join Our
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> Team</span>
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-gray-300 md:text-2xl leading-relaxed">
                Help us revolutionize event management and build the future of event technology.
              </p>
            </div>
          </div>
        </div>
      </section>

      <div className="container px-4 md:px-6 py-12">
        <div className="mx-auto max-w-7xl">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">We're Hiring!</h2>
              <p className="text-lg text-muted-foreground">
                Be part of a dynamic team that's transforming how events are managed and experienced.
              </p>
            </div>

            <div className="grid gap-8 md:grid-cols-2">
              <div className="bg-card p-6 rounded-lg border">
                <h3 className="text-xl font-semibold mb-3">Why Work With Us?</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li>• Flexible remote work options</li>
                  <li>• Competitive salary and benefits</li>
                  <li>• Professional development opportunities</li>
                  <li>• Collaborative and innovative environment</li>
                  <li>• Make a real impact in the events industry</li>
                </ul>
              </div>

              <div className="bg-card p-6 rounded-lg border">
                <h3 className="text-xl font-semibold mb-3">Open Positions</h3>
                <p className="text-muted-foreground mb-4">
                  We're always looking for talented individuals to join our team.
                </p>
                <p className="text-sm text-muted-foreground">
                  Currently updating our job listings. Please check back soon or send us your <NAME_EMAIL>
                </p>
              </div>
            </div>

            <div className="text-center mt-12">
              <h3 className="text-2xl font-bold mb-4">Interested in Joining Us?</h3>
              <p className="text-muted-foreground mb-6">
                Send us your resume and let us know how you'd like to contribute to our mission.
              </p>
              <a
                href="mailto:<EMAIL>"
                className="inline-flex items-center justify-center rounded-md bg-primary px-8 py-3 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90"
              >
                Send Your Resume
              </a>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  )
}
