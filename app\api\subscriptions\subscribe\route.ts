import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@/lib/supabase"
import { createPayment, getActivePaymentGateways } from "@/lib/payment-gateway"
import { logActivity, ActivityCategory } from "@/utils/activity-logger"
import jwt from "jsonwebtoken"

// Helper function to get user from token
async function getUserFromToken(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    const cookieHeader = request.headers.get('cookie')
    
    let token = null
    
    // Try to get token from Authorization header
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7)
    }
    
    // Try to get token from cookies
    if (!token && cookieHeader) {
      const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=')
        acc[key] = value
        return acc
      }, {} as Record<string, string>)
      
      token = cookies.auth_token
    }

    if (!token) {
      return null
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
    return decoded
  } catch (error) {
    console.error('Error verifying token:', error)
    return null
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { planId, planName, price, paymentGatewayId } = body

    // Validate required fields
    if (!planId || !planName || price === undefined) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required fields: planId, planName, price",
        },
        { status: 400 }
      )
    }

    // Get user from token
    const userToken = await getUserFromToken(request)
    if (!userToken) {
      return NextResponse.json(
        {
          success: false,
          error: "Authentication required",
        },
        { status: 401 }
      )
    }

    const supabase = createClient()

    // Get user details
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userToken.userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        {
          success: false,
          error: "User not found",
        },
        { status: 404 }
      )
    }

    // Handle free plan subscription
    if (price === 0) {
      // Update user subscription directly for free plan
      const subscriptionEndDate = new Date()
      subscriptionEndDate.setFullYear(subscriptionEndDate.getFullYear() + 10) // 10 years for free plan

      // Update user_subscription table
      const { error: subscriptionError } = await supabase
        .from('user_subscription')
        .upsert({
          user_id: user.id,
          subscription_type: planName.toLowerCase(),
          start_date: new Date().toISOString(),
          end_date: subscriptionEndDate.toISOString(),
          is_active: true,
          payment_id: null,
        })

      if (subscriptionError) {
        console.error('Error updating user subscription:', subscriptionError)
        return NextResponse.json(
          {
            success: false,
            error: "Failed to update subscription",
          },
          { status: 500 }
        )
      }

      // Update users table
      const { error: userUpdateError } = await supabase
        .from('users')
        .update({
          subscription_status: 'active',
          subscription_end_date: subscriptionEndDate.toISOString(),
        })
        .eq('id', user.id)

      if (userUpdateError) {
        console.error('Error updating user:', userUpdateError)
        return NextResponse.json(
          {
            success: false,
            error: "Failed to update user subscription status",
          },
          { status: 500 }
        )
      }

      // Log activity
      try {
        await logActivity({
          userId: user.id,
          action: "subscribe_free_plan",
          entityType: "subscription",
          entityId: planId,
          category: ActivityCategory.SUBSCRIPTION,
          details: {
            plan_name: planName,
            price: price,
          },
        })
      } catch (logError) {
        console.error("Error logging subscription activity:", logError)
      }

      return NextResponse.json({
        success: true,
        message: "Successfully subscribed to free plan",
      })
    }

    // Handle paid plan subscription
    if (!paymentGatewayId) {
      return NextResponse.json(
        {
          success: false,
          error: "Payment gateway ID is required for paid plans",
        },
        { status: 400 }
      )
    }

    // Verify payment gateway is available
    const activeGateways = await getActivePaymentGateways()
    const selectedGateway = activeGateways.find(g => g.id === paymentGatewayId)
    
    if (!selectedGateway) {
      return NextResponse.json(
        {
          success: false,
          error: "Selected payment gateway is not available",
        },
        { status: 400 }
      )
    }

    // Create payment request
    const paymentRequest = {
      amount: price,
      currency: "MYR",
      description: `Subscription to ${planName} plan`,
      customer_email: user.email,
      customer_name: user.full_name || user.email,
      reference_id: `sub_${user.id}_${planId}_${Date.now()}`,
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/subscriptions/success?plan=${planId}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/subscriptions/cancel?plan=${planId}`,
    }

    // Create payment with selected gateway
    const paymentResponse = await createPayment(paymentGatewayId, paymentRequest)

    if (!paymentResponse.success) {
      return NextResponse.json(
        {
          success: false,
          error: paymentResponse.error || "Failed to create payment",
        },
        { status: 500 }
      )
    }

    // Store pending subscription in database
    const { error: pendingSubError } = await supabase
      .from('user_subscription')
      .upsert({
        user_id: user.id,
        subscription_type: planName.toLowerCase(),
        start_date: new Date().toISOString(),
        end_date: null, // Will be set after payment confirmation
        is_active: false, // Will be activated after payment
        payment_id: paymentResponse.transaction_id,
      })

    if (pendingSubError) {
      console.error('Error creating pending subscription:', pendingSubError)
      return NextResponse.json(
        {
          success: false,
          error: "Failed to create subscription record",
        },
        { status: 500 }
      )
    }

    // Log activity
    try {
      await logActivity({
        userId: user.id,
        action: "initiate_subscription",
        entityType: "subscription",
        entityId: planId,
        category: ActivityCategory.SUBSCRIPTION,
        details: {
          plan_name: planName,
          price: price,
          payment_gateway: selectedGateway.name,
          transaction_id: paymentResponse.transaction_id,
        },
      })
    } catch (logError) {
      console.error("Error logging subscription activity:", logError)
    }

    return NextResponse.json({
      success: true,
      paymentUrl: paymentResponse.payment_url,
      transactionId: paymentResponse.transaction_id,
    })

  } catch (error: any) {
    console.error("Error processing subscription:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    )
  }
}
