"use client"

import React, { useEffect, useState, use<PERSON>emo, use<PERSON><PERSON>back } from "react"
import { CalendarDays, DollarSign, PercentCircle, Users, PlusCircle, ArrowRight, LineChart, BarChart3 } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"
import { <PERSON><PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Progress } from "@/components/ui/progress"

import { supabase } from "@/lib/supabase"
import { useAuth } from "@/contexts/auth-context"
import { logActivity } from "@/utils/activity-logger"
import { UserDashboard } from "@/components/dashboard/user-dashboard"
import { MiniChart } from "@/components/mini-chart"
import { perf } from "@/lib/performance/monitoring"

// Helper function to get auth token
const getAuthToken = () => {
  return document.cookie
    .split('; ')
    .find(row => row.startsWith('auth_token='))
    ?.split('=')[1];
}

export default function DashboardPage() {
  const [stats, setStats] = useState({
    totalEvents: 0,
    totalUsers: 0,
    totalRevenue: 0,
    totalWithdrawals: 0,
    attendanceRate: 0,
  })
  const [loading, setLoading] = useState(true)
  const [events, setEvents] = useState<any[]>([])
  const [registrations, setRegistrations] = useState<any[]>([])
  const [isOrgDialogOpen, setIsOrgDialogOpen] = useState(false)
  const [timeRange, setTimeRange] = useState<'7days' | '30days'>('30days')
  const [chartData, setChartData] = useState<{
    revenueData: any
    registrationData: any
  }>({
    revenueData: null,
    registrationData: null
  })
  const [chartLoading, setChartLoading] = useState(true)
  const [orgFormData, setOrgFormData] = useState({
    name: "",
    ssmNumber: "",
    picName: "",
    picPhone: "",
    picEmail: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { user, updateProfile, refreshUser } = useAuth()
  const router = useRouter()
  const { toast } = useToast()

  // Memoize user role checks for performance
  const isRegularUser = useMemo(() => user?.role_name === "user", [user?.role_name])
  const canCreateEvent = useMemo(() =>
    user && (user.role === "admin" || user.role === "manager"),
    [user?.role]
  )
  const showCreateFirstEvent = useMemo(() =>
    user && user.role === "free" && user.events_created === 0,
    [user?.role, user?.events_created]
  )

  // User data validation for role-based rendering
  useEffect(() => {
    // Validate user data for proper role-based rendering
    // This ensures the dashboard displays correctly based on user permissions
  }, [user, isRegularUser])

  // Generate chart data
  useEffect(() => {
    const generateChartData = () => {
      setChartLoading(true)
      const days = timeRange === '7days' ? 7 : 30

      // Generate date labels for the selected time range
      const labels = Array.from({ length: days }, (_, i) => {
        const date = new Date()
        date.setDate(date.getDate() - (days - 1 - i))
        return date.toLocaleDateString('en-US', {
          ...(days > 7
            ? { month: 'short', day: 'numeric' }
            : { weekday: 'short', day: 'numeric' })
        })
      })

      // Mock data for revenue chart
      const revenueData = {
        labels,
        datasets: [
          {
            label: 'Revenue',
            data: labels.map(() => Math.floor(Math.random() * 5000) + 1000),
            borderColor: 'rgba(16, 185, 129, 0.8)',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            borderWidth: 2,
            tension: 0.3,
            fill: true
          }
        ]
      }

      // Mock data for registration chart
      const registrationData = {
        labels,
        datasets: [
          {
            label: 'Registrations',
            data: labels.map(() => Math.floor(Math.random() * 50) + 10),
            borderColor: 'rgba(99, 102, 241, 0.8)',
            backgroundColor: 'rgba(99, 102, 241, 0.1)',
            borderWidth: 2,
            tension: 0.3,
            fill: true
          }
        ]
      }

      setChartData({ revenueData, registrationData })
      setChartLoading(false)
    }

    generateChartData()
  }, [timeRange])

  // Memoize the fetch function to prevent unnecessary re-renders
  const fetchData = useCallback(async () => {
    setLoading(true)

    try {
      await perf.measure('dashboard-data-fetch', async () => {
        // Get auth token
        const token = getAuthToken();

        if (!token) {
          throw new Error("Authentication token not found")
        }

        // Fetch dashboard statistics with performance monitoring
        const statsData = await perf.measure('dashboard-stats-fetch', async () => {
          const statsResponse = await fetch("/api/dashboard/stats", {
            method: "GET",
            headers: {
              "Authorization": `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          })

          if (!statsResponse.ok) {
            throw new Error("Failed to fetch dashboard statistics")
          }

          return statsResponse.json()
        })

        setStats({
          totalEvents: statsData.stats.totalEvents,
          totalUsers: statsData.stats.totalRegistrations, // Show registrations instead of total users
          totalRevenue: statsData.stats.totalRevenue,
          totalWithdrawals: statsData.stats.totalWithdrawals,
          attendanceRate: statsData.stats.attendanceRate,
        })

        // Fetch events and registrations in parallel for better performance
        const [eventsData, registrationsData] = await Promise.all([
          perf.measure('dashboard-events-fetch', async () => {
            const eventsResponse = await fetch("/api/dashboard/events", {
              method: "GET",
              headers: {
                "Authorization": `Bearer ${token}`,
                "Content-Type": "application/json",
              },
            })

            if (!eventsResponse.ok) {
              console.warn("Failed to fetch events, using empty array")
              return { events: [] }
            }

            return eventsResponse.json()
          }),

          perf.measure('dashboard-registrations-fetch', async () => {
            const registrationsResponse = await fetch("/api/dashboard/registrations", {
              method: "GET",
              headers: {
                "Authorization": `Bearer ${token}`,
                "Content-Type": "application/json",
              },
            })

            if (!registrationsResponse.ok) {
              console.warn("Failed to fetch registrations, using empty array")
              return { registrations: [] }
            }

            return registrationsResponse.json()
          })
        ])

        // Set events data
        setEvents(eventsData.events?.slice(0, 5) || [])

        // Transform and set registrations data
        const transformedRegistrations = (registrationsData.registrations || [])
          .slice(0, 5)
          .map((reg: any) => ({
            ...reg,
            events: { title: reg.event?.title || "Unknown Event" }
          }))
        setRegistrations(transformedRegistrations)
      })
    } catch (error) {
      console.error("Error fetching dashboard data:", error)

      // Use empty data if there's an error
      setStats({
        totalEvents: 0,
        totalUsers: 0,
        totalRevenue: 0,
        totalWithdrawals: 0,
        attendanceRate: 0,
      })

      setEvents([])
      setRegistrations([])
    } finally {
      setLoading(false)
    }
  }, [])

  // Effect to trigger data fetching when user changes
  useEffect(() => {
    // Only fetch data if user is authenticated
    if (user) {
      fetchData()
    } else {
      setLoading(false)
    }
  }, [user, fetchData])

  const handleOrgFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Use the organization creation API endpoint which handles role updates properly
      const token = getAuthToken();

      if (!token) {
        throw new Error("Authentication token not found")
      }

      const response = await fetch("/api/organizations/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          name: orgFormData.name,
          ssmNumber: orgFormData.ssmNumber,
          picName: orgFormData.picName,
          picPhone: orgFormData.picPhone,
          picEmail: orgFormData.picEmail,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to create organization")
      }

      const data = await response.json()
      const orgData = data.organization

      // Update user profile with organization details (without role, as it's handled by the API)
      const updatedUser = await updateProfile({
        organization_id: orgData.id,
        organization: orgData.name,
      })

      if (updatedUser) {
        // Refresh user data to get the updated role
        await refreshUser()

        toast({
          title: "Success",
          description: "Organization details saved successfully. You can now create events.",
        })

        setIsOrgDialogOpen(false)

        // Redirect to event creation page
        router.push("/dashboard/events/create")
      }
    } catch (error) {
      console.error("Error saving organization details:", error)
      toast({
        title: "Error",
        description: "Failed to save organization details. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Loading state moved to the top of the return statement

  // Loading state
  if (loading) {
    return (
      <div className="space-y-6 p-4 md:p-6">
        <div className="flex flex-col justify-between space-y-4 md:flex-row md:items-center md:space-y-0">
          <div>
            <Skeleton className="h-9 w-48" />
            <Skeleton className="h-5 w-64 mt-1" />
          </div>
          <Skeleton className="h-10 w-36" />
        </div>

        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
          {[1, 2, 3, 4].map((i) => (
            <Skeleton key={i} className="h-32 rounded-lg" />
          ))}
        </div>

        <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
          <Skeleton className="h-80 rounded-lg" />
          <Skeleton className="h-80 rounded-lg" />
        </div>

        <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
          <Skeleton className="h-96 rounded-lg" />
          <Skeleton className="h-96 rounded-lg" />
        </div>
      </div>
    )
  }

  // Show simple user dashboard for regular users
  if (isRegularUser) {
    return <UserDashboard />
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-sm text-muted-foreground">
            {user ? `Welcome back, ${user.full_name}!` : "Welcome to your dashboard"}
          </p>
        </div>
        {canCreateEvent && (
          <Link href="/dashboard/events/create" className="w-full sm:w-auto">
            <Button className="w-full gap-2" size="sm">
              <PlusCircle className="h-4 w-4" />
              <span>Create Event</span>
            </Button>
          </Link>
        )}
      </div>

      {showCreateFirstEvent && (
        <Card className="bg-gradient-to-r from-primary/5 to-primary/10 border-primary/20">
          <CardContent className="p-6">
            <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
              <div className="space-y-1">
                <h3 className="text-xl font-semibold tracking-tight">Ready to host your first event?</h3>
                <p className="text-sm text-muted-foreground">
                  Create your organization profile to start hosting events on mTicketz.
                </p>
              </div>
              <Button className="gap-2" size="lg" onClick={() => setIsOrgDialogOpen(true)}>
                <PlusCircle className="h-5 w-5" />
                Create Your First Event
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="My Events"
          value={stats.totalEvents}
          icon={CalendarDays}
          description="Events you've created"
          loading={loading}
        />
        <StatsCard
          title="Total Registrations"
          value={stats.totalUsers}
          icon={Users}
          description="Registrations for your events"
          loading={loading}
        />
        <StatsCard
          title="Total Revenue"
          value={`RM ${stats.totalRevenue.toFixed(2)}`}
          icon={DollarSign}
          description="Revenue from your events"
          loading={loading}
        />
        <StatsCard
          title="Attendance Rate"
          value={`${stats.attendanceRate.toFixed(1)}%`}
          icon={PercentCircle}
          description="Check-in rate for your events"
          loading={loading}
        />
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-base font-medium">
                  Revenue ({timeRange === '7days' ? 'Last 7 Days' : 'Last 30 Days'})
                </CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent className="pt-0">
                <MiniChart
                  title=""
                  data={chartData.revenueData}
                  type="line"
                  height={240}
                  timeRange={timeRange}
                  onTimeRangeChange={setTimeRange}
                  loading={chartLoading}
                />
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
                <CardTitle className="text-base font-medium">
                  Registrations ({timeRange === '7days' ? 'Last 7 Days' : 'Last 30 Days'})
                </CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent className="pt-0">
                <MiniChart
                  title=""
                  data={chartData.registrationData}
                  type="bar"
                  height={240}
                  timeRange={timeRange}
                  onTimeRangeChange={setTimeRange}
                  loading={chartLoading}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Detailed Analytics</CardTitle>
              <CardDescription>Coming soon. More detailed analytics will be available here.</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-8">
              <div className="flex items-center justify-between space-y-2">
                <div>
                  <h3 className="text-lg font-medium">Event Performance</h3>
                  <p className="text-sm text-muted-foreground">Track your event performance over time</p>
                </div>
                <Button variant="outline" size="sm">
                  <span>View Full Report</span>
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium">Registration Conversion</div>
                  <div className="text-sm font-medium">68%</div>
                </div>
                <Progress value={68} className="h-2" />
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>Last 30 days</span>
                  <span>+12% from last month</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-3">
            <div>
              <CardTitle className="text-base">My Recent Events</CardTitle>
              <CardDescription className="text-xs">Your most recently created events</CardDescription>
            </div>
            <Link href="/dashboard/events">
              <Button variant="outline" size="sm" className="gap-1 h-7 text-xs">
                View All
                <ArrowRight className="h-3 w-3" />
              </Button>
            </Link>
          </CardHeader>
          <CardContent className="pt-0">
            {loading ? (
              <div className="space-y-3">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center space-x-3">
                    <Skeleton className="h-8 w-8 rounded-md" />
                    <div className="space-y-1 flex-1">
                      <Skeleton className="h-3 w-3/4" />
                      <Skeleton className="h-2 w-1/2" />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <RecentEventsList events={events.slice(0, 5)} />
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-3">
            <div>
              <CardTitle className="text-base">Recent Registrations</CardTitle>
              <CardDescription className="text-xs">Latest registrations for your events</CardDescription>
            </div>
            <Link href="/dashboard/my-tickets">
              <Button variant="outline" size="sm" className="gap-1 h-7 text-xs">
                View All
                <ArrowRight className="h-3 w-3" />
              </Button>
            </Link>
          </CardHeader>
          <CardContent className="pt-0">
            {loading ? (
              <div className="space-y-3">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="flex items-center space-x-3">
                    <Skeleton className="h-7 w-7 rounded-full" />
                    <div className="space-y-1 flex-1">
                      <Skeleton className="h-3 w-3/4" />
                      <Skeleton className="h-2 w-1/2" />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <RecentRegistrationsList registrations={registrations.slice(0, 5)} />
            )}
          </CardContent>
        </Card>
      </div>

      {/* Organization Form Dialog */}
      <Dialog open={isOrgDialogOpen} onOpenChange={setIsOrgDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Organization Details</DialogTitle>
            <DialogDescription>Provide your organization details to start creating events.</DialogDescription>
          </DialogHeader>
          <form onSubmit={handleOrgFormSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="org-name">Organization Name</Label>
                <Input
                  id="org-name"
                  value={orgFormData.name}
                  onChange={(e) => setOrgFormData({ ...orgFormData, name: e.target.value })}
                  placeholder="Enter organization name"
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="ssm-number">SSM Number</Label>
                <Input
                  id="ssm-number"
                  value={orgFormData.ssmNumber}
                  onChange={(e) => setOrgFormData({ ...orgFormData, ssmNumber: e.target.value })}
                  placeholder="Enter SSM number"
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="pic-name">Person In Charge (PIC)</Label>
                <Input
                  id="pic-name"
                  value={orgFormData.picName}
                  onChange={(e) => setOrgFormData({ ...orgFormData, picName: e.target.value })}
                  placeholder="Enter PIC name"
                  required
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="pic-phone">PIC Phone Number</Label>
                  <Input
                    id="pic-phone"
                    value={orgFormData.picPhone}
                    onChange={(e) => setOrgFormData({ ...orgFormData, picPhone: e.target.value })}
                    placeholder="Enter phone number"
                    required
                  />
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="pic-email">PIC Email</Label>
                  <Input
                    id="pic-email"
                    type="email"
                    value={orgFormData.picEmail}
                    onChange={(e) => setOrgFormData({ ...orgFormData, picEmail: e.target.value })}
                    placeholder="Enter email address"
                    required
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsOrgDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Saving..." : "Save & Continue"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  )
}

const StatsCard = React.memo(function StatsCard({
  title,
  value,
  icon: Icon,
  description,
  loading = false,
}: {
  title: string
  value: string | number
  icon: any
  description: string
  loading?: boolean
}) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  )
})

const RecentEventsList = React.memo(function RecentEventsList({ events }: { events: any[] }) {
  if (events.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-6 space-y-2">
        <CalendarDays className="h-6 w-6 text-muted-foreground" />
        <p className="text-xs text-muted-foreground">No events found</p>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      {events.map((event) => (
        <Link key={event.id} href={`/dashboard/events/${event.slug}`}>
          <div className="flex items-center p-2 rounded-md transition-colors hover:bg-accent/50 cursor-pointer group">
            <div className="flex-shrink-0 mr-3">
              <div className="w-8 h-8 rounded-md bg-primary/10 flex items-center justify-center">
                <CalendarDays className="h-4 w-4 text-primary" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-medium truncate group-hover:text-primary transition-colors">{event.title}</h3>
                <Badge variant={event.is_published ? 'default' : 'outline'} className="ml-2 text-xs h-5">
                  {event.is_published ? 'Published' : 'Draft'}
                </Badge>
              </div>
              <div className="flex items-center justify-between mt-0.5">
                <p className="text-xs text-muted-foreground truncate">
                  {new Date(event.start_date).toLocaleDateString(undefined, {
                    month: 'short',
                    day: 'numeric',
                  })}
                  {event.location && ` • ${event.location.length > 20 ? event.location.substring(0, 20) + '...' : event.location}`}
                </p>
                <p className="text-xs font-medium text-primary ml-2">
                  {event.price === 0 ? 'Free' : `RM ${event.price?.toFixed(2) || '0.00'}`}
                </p>
              </div>
            </div>
            <ArrowRight className="h-3 w-3 text-muted-foreground ml-2 group-hover:text-primary transition-colors" />
          </div>
        </Link>
      ))}
    </div>
  )
})

const RecentRegistrationsList = React.memo(function RecentRegistrationsList({ registrations }: { registrations: any[] }) {
  if (registrations.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-6 space-y-2">
        <Users className="h-6 w-6 text-muted-foreground" />
        <p className="text-xs text-muted-foreground">No registrations found</p>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      {registrations.map((registration) => (
        <div key={registration.id} className="flex items-center p-2 rounded-md transition-colors hover:bg-accent/50 group">
          <Avatar className="h-7 w-7 mr-3">
            <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(registration.attendee_name)}`} />
            <AvatarFallback className="text-xs">{registration.attendee_name?.charAt(0) || 'U'}</AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium truncate group-hover:text-primary transition-colors">{registration.attendee_name}</h3>
              <Badge
                variant={registration.payment_status === "completed" ? "default" : "outline"}
                className="ml-2 text-xs h-5"
              >
                {registration.payment_status}
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground truncate mt-0.5">
              {registration.attendee_email}
            </p>
            <div className="flex items-center justify-between text-xs text-muted-foreground mt-0.5">
              <span className="truncate max-w-[60%]">
                {registration.events?.title}
              </span>
              <span className="text-xs">
                {registration.created_at && new Date(registration.created_at).toLocaleDateString(undefined, {
                  month: 'short',
                  day: 'numeric',
                })}
              </span>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
})
