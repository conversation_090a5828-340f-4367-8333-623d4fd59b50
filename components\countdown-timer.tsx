"use client"

import { useState, useEffect } from "react"
import { Clock, Calendar } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface CountdownTimerProps {
  targetDate: string | Date
  eventTitle?: string
  className?: string
  variant?: 'default' | 'minimal'
}

interface TimeRemaining {
  days: number
  hours: number
  minutes: number
  seconds: number
  total: number
}

export function CountdownTimer({ targetDate, eventTitle, className, variant = 'default' }: CountdownTimerProps) {
  const [timeRemaining, setTimeRemaining] = useState<TimeRemaining>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    total: 0
  })
  const [status, setStatus] = useState<'upcoming' | 'live' | 'ended'>('upcoming')

  const calculateTimeRemaining = (target: Date): TimeRemaining => {
    const now = new Date().getTime()
    const targetTime = target.getTime()
    const difference = targetTime - now

    if (difference <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0, total: 0 }
    }

    const days = Math.floor(difference / (1000 * 60 * 60 * 24))
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((difference % (1000 * 60)) / 1000)

    return { days, hours, minutes, seconds, total: difference }
  }

  useEffect(() => {
    const target = new Date(targetDate)
    const now = new Date()

    // Determine initial status
    if (now < target) {
      setStatus('upcoming')
    } else {
      // For simplicity, we'll consider it ended if past start time
      // In a real app, you'd compare with end_date
      setStatus('ended')
    }

    const updateTimer = () => {
      const remaining = calculateTimeRemaining(target)
      setTimeRemaining(remaining)

      // Update status based on time remaining
      if (remaining.total <= 0) {
        setStatus('ended')
      }
    }

    // Initial calculation
    updateTimer()

    // Set up interval to update every second
    const interval = setInterval(updateTimer, 1000)

    return () => clearInterval(interval)
  }, [targetDate])

  const formatNumber = (num: number): string => {
    return num.toString().padStart(2, '0')
  }

  if (status === 'ended') {
    if (variant === 'minimal') {
      return (
        <div className={`text-center ${className}`}>
          <Badge variant="secondary" className="bg-black/60 text-white border-white/20">
            Event Started
          </Badge>
        </div>
      )
    }

    return (
      <Card className={className}>
        <CardContent className="p-4">
          <div className="text-center">
            <Badge variant="secondary" className="mb-2">
              Event Started
            </Badge>
            <div className="flex items-center justify-center text-sm text-muted-foreground">
              <Calendar className="w-4 h-4 mr-1" />
              <span>This event has already begun</span>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (variant === 'minimal') {
    return (
      <div className={`text-center ${className}`}>
        <div className="grid grid-cols-4 gap-2">
          <div className="text-center">
            <div className="bg-black/60 backdrop-blur-sm rounded-lg p-3 border border-white/20">
              <div className="text-lg font-bold text-white">
                {formatNumber(timeRemaining.days)}
              </div>
            </div>
            <div className="text-sm text-white/80 mt-1.5 drop-shadow-sm">Days</div>
          </div>
          <div className="text-center">
            <div className="bg-black/60 backdrop-blur-sm rounded-lg p-3 border border-white/20">
              <div className="text-lg font-bold text-white">
                {formatNumber(timeRemaining.hours)}
              </div>
            </div>
            <div className="text-sm text-white/80 mt-1.5 drop-shadow-sm">Hours</div>
          </div>
          <div className="text-center">
            <div className="bg-black/60 backdrop-blur-sm rounded-lg p-3 border border-white/20">
              <div className="text-lg font-bold text-white">
                {formatNumber(timeRemaining.minutes)}
              </div>
            </div>
            <div className="text-sm text-white/80 mt-1.5 drop-shadow-sm">Mins</div>
          </div>
          <div className="text-center">
            <div className="bg-black/60 backdrop-blur-sm rounded-lg p-3 border border-white/20">
              <div className="text-lg font-bold text-white">
                {formatNumber(timeRemaining.seconds)}
              </div>
            </div>
            <div className="text-sm text-white/80 mt-1.5 drop-shadow-sm">Secs</div>
          </div>
        </div>

        {timeRemaining.days === 0 && timeRemaining.hours === 0 && (
          <Badge variant="destructive" className="text-sm mt-3 bg-red-600/80 text-white border-white/20">
            Starting Soon!
          </Badge>
        )}
      </div>
    )
  }

  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="text-center">
          <div className="flex items-center justify-center mb-3">
            <Clock className="w-4 h-4 mr-2 text-primary" />
            <span className="text-sm font-medium">Event Starts In</span>
          </div>

          <div className="grid grid-cols-4 gap-2 mb-3">
            <div className="text-center">
              <div className="bg-primary/10 rounded-lg p-2">
                <div className="text-lg font-bold text-primary">
                  {formatNumber(timeRemaining.days)}
                </div>
              </div>
              <div className="text-xs text-muted-foreground mt-1">Days</div>
            </div>
            <div className="text-center">
              <div className="bg-primary/10 rounded-lg p-2">
                <div className="text-lg font-bold text-primary">
                  {formatNumber(timeRemaining.hours)}
                </div>
              </div>
              <div className="text-xs text-muted-foreground mt-1">Hours</div>
            </div>
            <div className="text-center">
              <div className="bg-primary/10 rounded-lg p-2">
                <div className="text-lg font-bold text-primary">
                  {formatNumber(timeRemaining.minutes)}
                </div>
              </div>
              <div className="text-xs text-muted-foreground mt-1">Mins</div>
            </div>
            <div className="text-center">
              <div className="bg-primary/10 rounded-lg p-2">
                <div className="text-lg font-bold text-primary">
                  {formatNumber(timeRemaining.seconds)}
                </div>
              </div>
              <div className="text-xs text-muted-foreground mt-1">Secs</div>
            </div>
          </div>

          {timeRemaining.days === 0 && timeRemaining.hours === 0 && (
            <Badge variant="destructive" className="text-xs">
              Starting Soon!
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
