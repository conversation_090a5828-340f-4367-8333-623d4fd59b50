import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import jwt from "jsonwebtoken";

export async function POST(request: Request) {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get("Authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Authorization token required" },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7); // Remove "Bearer " prefix

    // Verify JWT token
    let decoded: any;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET || "your-secret-key");
    } catch (jwtError) {
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 401 }
      );
    }

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin();

    const updateData = await request.json();

    // Remove sensitive fields that shouldn't be updated via this endpoint
    const allowedFields = [
      'full_name',
      'organization',
      'organization_id',
      'profile_image_url',
      'phone',
      'bio'
    ];

    const filteredData: any = {};
    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    }

    if (Object.keys(filteredData).length === 0) {
      return NextResponse.json(
        { error: "No valid fields to update" },
        { status: 400 }
      );
    }

    // Update user profile
    const { data: updatedUser, error: updateError } = await supabaseAdmin
      .from("users")
      .update(filteredData)
      .eq("id", decoded.userId)
      .select(`
        *,
        user_roles!role_id (
          id,
          role_name,
          description
        )
      `)
      .single();

    if (updateError) {
      console.error("Error updating profile:", updateError);
      return NextResponse.json(
        { error: "Failed to update profile" },
        { status: 500 }
      );
    }

    // Prepare user data (exclude sensitive fields)
    const user = {
      id: updatedUser.id,
      email: updatedUser.email,
      full_name: updatedUser.full_name,
      role_id: updatedUser.role_id,
      role_name: updatedUser.user_roles?.role_name || null,
      subscription_status: updatedUser.subscription_status,
      subscription_end_date: updatedUser.subscription_end_date,
      created_at: updatedUser.created_at,
      organization: updatedUser.organization,
      organization_id: updatedUser.organization_id,
      profile_image_url: updatedUser.profile_image_url,
      phone: updatedUser.phone,
      bio: updatedUser.bio,
      events_created: updatedUser.events_created || 0,
      total_earnings: updatedUser.total_earnings || 0,
      available_balance: updatedUser.available_balance || 0,
    };

    return NextResponse.json({
      success: true,
      user,
    });

  } catch (error: any) {
    console.error("Update profile error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
