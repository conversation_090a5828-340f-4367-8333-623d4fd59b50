import { PageLayout } from "@/components/page-layout"
import { HeroSection } from "@/components/hero-section"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Book, Code, Users, Zap } from "lucide-react"

export const metadata = {
  title: "Documentation | mTicket.my",
  description: "Complete documentation and guides for using mTicket.my platform",
}

export default function DocsPage() {
  return (
    <PageLayout>
      {/* Corporate Hero Section */}
      <section className="relative w-full py-20 md:py-32 lg:py-40 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}
          ></div>
        </div>

        <div className="container relative px-4 md:px-6">
          <div className="flex flex-col items-center space-y-8 text-center">
            {/* Main Heading */}
            <div className="space-y-4 max-w-4xl">
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl lg:text-7xl">
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Documentation</span>
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-gray-300 md:text-2xl leading-relaxed">
                Everything you need to know about using mTicket.my effectively.
              </p>
            </div>
          </div>
        </div>
      </section>

      <div className="container px-4 md:px-6 py-12">
        <div className="mx-auto max-w-7xl">
          <div className="max-w-6xl mx-auto">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-12">
              <Card>
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2">
                    <Book className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle>Getting Started</CardTitle>
                  <CardDescription>
                    Learn the basics of creating and managing events
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• Creating your first event</li>
                    <li>• Setting up ticket types</li>
                    <li>• Managing registrations</li>
                    <li>• Event publishing</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2">
                    <Users className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle>User Guides</CardTitle>
                  <CardDescription>
                    Detailed guides for different user roles
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• Event organizer guide</li>
                    <li>• Attendee registration</li>
                    <li>• Admin panel overview</li>
                    <li>• Organization management</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2">
                    <Zap className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle>Advanced Features</CardTitle>
                  <CardDescription>
                    Make the most of powerful features
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• Event certificates</li>
                    <li>• Attendance tracking</li>
                    <li>• Analytics & reports</li>
                    <li>• Webhook integration</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2">
                    <Code className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle>API Reference</CardTitle>
                  <CardDescription>
                    Technical documentation for developers
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <li>• REST API endpoints</li>
                    <li>• Authentication</li>
                    <li>• Webhook events</li>
                    <li>• Code examples</li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            <div className="text-center">
              <h3 className="text-2xl font-bold mb-4">Need More Help?</h3>
              <p className="text-muted-foreground mb-6">
                Our documentation is continuously being updated. For immediate assistance,
                please contact our support team.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="/contact"
                  className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90"
                >
                  Contact Support
                </a>
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground"
                >
                  Request Documentation
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  )
}
