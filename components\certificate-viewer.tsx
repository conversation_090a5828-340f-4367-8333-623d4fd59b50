"use client"

import { useState } from "react"
import { Download, Eye, ExternalLink, QrCode } from "lucide-react"
import { QRCodeSVG } from "qrcode.react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"

interface Certificate {
  id: string
  recipient_name: string
  unique_code: string
  file_path: string | null
  verification_url: string
  generated_at: string
  template_id: string
  event_id: string
  event_title: string
  registration_id: string
}

interface CertificateViewerProps {
  certificate: Certificate
  eventTitle: string
  eventDate: string
  participantName: string
}

export function CertificateViewer({
  certificate,
  eventTitle,
  eventDate,
  participantName
}: CertificateViewerProps) {
  const [isViewerOpen, setIsViewerOpen] = useState(false)
  const { toast } = useToast()

  const handleDownload = () => {
    // In a real implementation, this would generate and download a PDF
    toast({
      title: "Download Started",
      description: "Your certificate is being prepared for download",
    })
  }

  const handleViewOnline = () => {
    window.open(`/certificates/verify/${certificate.unique_code}`, '_blank')
  }

  const verificationUrl = `https://mticket.my/certificates/verify/${certificate.unique_code}`

  return (
    <>
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsViewerOpen(true)}
        >
          <Eye className="h-4 w-4 mr-1" />
          View
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={handleViewOnline}
        >
          <ExternalLink className="h-4 w-4 mr-1" />
          Verify
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={handleDownload}
        >
          <Download className="h-4 w-4 mr-1" />
          Download
        </Button>
      </div>

      <Dialog open={isViewerOpen} onOpenChange={setIsViewerOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Certificate Preview</DialogTitle>
            <DialogDescription>
              Certificate of completion for {eventTitle}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Certificate Preview */}
            <Card className="bg-gradient-to-br from-blue-50 to-purple-50">
              <CardContent className="p-8">
                <div className="relative bg-white rounded-lg shadow-lg p-8 border-4 border-blue-200">
                  {/* Header */}
                  <div className="text-center mb-8">
                    <div className="w-16 h-16 bg-blue-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                      <QrCode className="w-8 h-8 text-white" />
                    </div>
                    <h1 className="text-3xl font-bold text-gray-800 mb-2">
                      Certificate of Completion
                    </h1>
                    <div className="w-24 h-1 bg-blue-600 mx-auto"></div>
                  </div>

                  {/* Body */}
                  <div className="text-center space-y-6 mb-8">
                    <p className="text-lg text-gray-600">This is to certify that</p>
                    <h2 className="text-4xl font-bold text-gray-800 font-serif">
                      {participantName}
                    </h2>
                    <p className="text-lg text-gray-600">has successfully completed</p>
                    <h3 className="text-2xl font-bold text-blue-600">
                      {eventTitle}
                    </h3>
                    <p className="text-lg text-gray-600">
                      on {new Date(eventDate).toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </p>
                  </div>

                  {/* Footer */}
                  <div className="flex justify-between items-end">
                    <div className="text-left">
                      <div className="w-48 h-px bg-gray-400 mb-2"></div>
                      <p className="text-sm text-gray-600">Authorized Signature</p>
                      <p className="text-xs text-gray-500">mTicket.my Platform</p>
                    </div>

                    <div className="text-center">
                      <div className="bg-white p-2 border border-gray-300 rounded">
                        <QRCodeSVG
                          value={verificationUrl}
                          size={80}
                          level="M"
                          includeMargin={false}
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Scan to verify</p>
                    </div>

                    <div className="text-right">
                      <p className="text-sm text-gray-600">
                        Issued: {new Date(certificate.generated_at).toLocaleDateString()}
                      </p>
                      <p className="text-xs text-gray-500">
                        ID: {certificate.unique_code}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Certificate Details */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Certificate Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Recipient</label>
                    <p className="font-semibold">{participantName}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Event</label>
                    <p className="font-semibold">{eventTitle}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Event Date</label>
                    <p>{new Date(eventDate).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Issue Date</label>
                    <p>{new Date(certificate.generated_at).toLocaleDateString()}</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Verification</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Verification Code</label>
                    <code className="block bg-gray-100 px-2 py-1 rounded text-sm">
                      {certificate.unique_code}
                    </code>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Status</label>
                    <div>
                      <Badge variant="default">Valid</Badge>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Verification URL</label>
                    <a
                      href={verificationUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline text-sm break-all"
                    >
                      {verificationUrl}
                    </a>
                  </div>
                  {certificate.template_id && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Template</label>
                      <p>{certificate.template_id}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Actions */}
            <div className="flex justify-center gap-4">
              <Button onClick={handleDownload}>
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
              <Button variant="outline" onClick={handleViewOnline}>
                <ExternalLink className="h-4 w-4 mr-2" />
                View Online
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
