import { NextResponse } from "next/server";
import { hashPassword, verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth";
import { createClient } from "@supabase/supabase-js";
import crypto from "crypto";

// We'll create the client inside the handler to ensure we have the latest environment variables
// and to handle any API key issues more gracefully

export async function POST(request: Request) {
  try {
    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === "development";
    console.log("API users/create - Is development mode:", isDevelopment);

    // Get JWT token from request
    const token = getJWTTokenFromRequest(request);
    console.log("API users/create - Token present:", !!token);

    // In production, verify authentication and admin role
    // In development, allow access without authentication for easier testing
    if (!isDevelopment) {
      if (!token) {
        console.log("API users/create - Access denied: No token provided");
        return NextResponse.json(
          { error: "Unauthorized. Admin access required." },
          { status: 403 }
        );
      }

      // Verify the JW<PERSON> token and get user data
      const authResult = await verifyJWTToken(token);
      if (!authResult?.user) {
        console.log("API users/create - Access denied: Invalid token");
        return NextResponse.json(
          { error: "Unauthorized. Invalid token." },
          { status: 403 }
        );
      }

      // Check if user has admin role (only "admin" role has full access)
      const userRole = authResult.user.role_name || authResult.user.role;

      if (userRole !== "admin") {
        console.log("API users/create - Access denied: Not admin role. User role:", userRole);
        return NextResponse.json(
          { error: "Unauthorized. Admin access required." },
          { status: 403 }
        );
      }

      console.log("API users/create - Access granted to admin user:", authResult.user.email);
    } else {
      console.log("API users/create - Development mode: Allowing access without authentication");
    }

    const { email, fullName, role, role_id, sendPasswordEmail } = await request.json();

    // Validate input
    if (!email || !fullName) {
      return NextResponse.json(
        { error: "Email and full name are required" },
        { status: 400 }
      );
    }

    // Generate a unique ID for the new user
    const userId = crypto.randomUUID();

    // Generate a temporary random password
    const tempPassword = crypto.randomBytes(12).toString("hex");

    // Hash the password
    const passwordHash = await hashPassword(tempPassword);

    try {
      // Create a Supabase client with direct database access
      // First try with the ANON key as a fallback
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
      const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || "";

      // Create the user data object
      const newUserData = {
        id: userId,
        email,
        password_hash: passwordHash,
        full_name: fullName,
        role: role || "free", // Keep for backward compatibility
        role_id: role_id || null, // Use role_id if provided
        subscription_status: "none",
        subscription_end_date: null,
        created_at: new Date().toISOString(),
        organization: null,
        profile_image_url: null,
        events_created: 0,
        total_earnings: 0,
        available_balance: 0,
      };

      // First attempt: Try with the regular client
      const supabase = createClient(supabaseUrl, supabaseAnonKey);
      let result = await supabase.from("users").insert(newUserData).select();

      // If we get an RLS error, try with direct SQL if possible
      if (result.error && (
          result.error.message.includes("violates row-level security policy") ||
          result.error.message.includes("new row violates row-level security")
        )) {
        // RLS policy violation, attempting direct SQL insertion

        // Try direct SQL insertion as a fallback
        // This bypasses RLS policies in some cases
        result = await supabase.rpc('insert_user_admin', {
          user_data: newUserData
        });
      }

      // Check for errors after all attempts
      if (result.error) {

        // Provide specific error messages based on the error type
        if (result.error.message.includes("duplicate key")) {
          return NextResponse.json(
            { error: "A user with this email already exists" },
            { status: 400 }
          );
        } else if (result.error.message.includes("violates row-level security")) {
          return NextResponse.json(
            { error: "Permission denied: Cannot add user due to security policies. Please contact the administrator." },
            { status: 403 }
          );
        } else if (result.error.message.includes("invalid api key")) {
          return NextResponse.json(
            { error: "Server configuration error: Invalid API key. Please contact the administrator." },
            { status: 500 }
          );
        } else {
          return NextResponse.json(
            { error: `Database error: ${result.error.message}` },
            { status: 500 }
          );
        }
      }

      // If sendPasswordEmail is true, we would send a password reset email here
      // For now, we'll just return the temporary password for testing purposes
      // In a production environment, you would use a proper email service

      return NextResponse.json({
        success: true,
        user: {
          id: userId,
          email,
          full_name: fullName,
          role: role || "free",
          role_id: role_id || null,
        },
        tempPassword: sendPasswordEmail ? null : tempPassword,
        message: sendPasswordEmail
          ? "User created successfully. Password reset email will be sent."
          : "User created successfully with temporary password.",
      });
    } catch (createError: any) {
      console.error("Error creating user:", createError);
      return NextResponse.json(
        { error: createError.message || "Failed to create user" },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("Error in admin user creation:", error);
    return NextResponse.json(
      { error: error.message || "Failed to create user" },
      { status: 500 }
    );
  }
}
