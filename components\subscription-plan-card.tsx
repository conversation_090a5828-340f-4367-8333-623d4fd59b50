"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Check, CreditCard } from "lucide-react"
import { useAuth } from "@/contexts/auth-context"
import { useToast } from "@/hooks/use-toast"
import SubscriptionPaymentModal from "./subscription-payment-modal"

type SubscriptionPlan = {
  id: string
  name: string
  price: number
  description: string
  features: string[]
  is_popular?: boolean
  max_events?: number | null
  max_attendees_per_event?: number | null
  // Feature toggles
  certificates_enabled?: boolean
  attendance_enabled?: boolean
  webhooks_enabled?: boolean
  analytics_enabled?: boolean
  reports_enabled?: boolean
}

interface SubscriptionPlanCardProps {
  plan: SubscriptionPlan
}

export default function SubscriptionPlanCard({ plan }: SubscriptionPlanCardProps) {
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { user } = useAuth()
  const { toast } = useToast()

  // Auto-generate features based on plan settings
  const generateAutoFeatures = (plan: SubscriptionPlan): string[] => {
    const autoFeatures: string[] = []

    // Add event limit features
    if (plan.max_events === null) {
      autoFeatures.push("Unlimited events")
    } else if (plan.max_events === 1) {
      autoFeatures.push("1 event at a time")
    } else {
      autoFeatures.push(`${plan.max_events} events at a time`)
    }

    // Add attendee limit features
    if (plan.max_attendees_per_event === null) {
      autoFeatures.push("Unlimited attendees")
    } else {
      autoFeatures.push(`Up to ${plan.max_attendees_per_event} attendees per event`)
    }

    // Add toggle-based features
    if (plan.certificates_enabled) {
      autoFeatures.push("Event certificates")
    }
    if (plan.attendance_enabled) {
      autoFeatures.push("Secure QR code check-in")
    }
    if (plan.webhooks_enabled) {
      autoFeatures.push("API & Webhook access")
    }
    if (plan.analytics_enabled) {
      autoFeatures.push("Event analytics")
    }
    if (plan.reports_enabled) {
      autoFeatures.push("Event reporting")
    }

    return autoFeatures
  }

  const handleSubscribe = async () => {
    if (!user) {
      // Store plan selection in sessionStorage and redirect to register
      sessionStorage.setItem('selectedPlan', JSON.stringify({
        planId: plan.id,
        planName: plan.name,
        price: plan.price
      }))
      window.location.href = '/auth/register'
      return
    }

    if (plan.price === 0) {
      // Handle free plan subscription
      setIsLoading(true)
      try {
        const response = await fetch('/api/subscriptions/subscribe', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            planId: plan.id,
            planName: plan.name,
            price: plan.price,
          }),
        })

        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.error || 'Failed to subscribe to free plan')
        }

        toast({
          title: "Success",
          description: "Successfully subscribed to the Free plan!",
        })

        // Refresh the page to update user subscription status
        window.location.reload()
      } catch (error: any) {
        console.error('Error subscribing to free plan:', error)
        toast({
          title: "Error",
          description: error.message || "Failed to subscribe to free plan",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    } else {
      // Open payment modal for paid plans
      setIsPaymentModalOpen(true)
    }
  }

  const isCurrentPlan = user?.subscription_status === "active" &&
    user?.role_name?.toLowerCase() === plan.name.toLowerCase()

  const getButtonText = () => {
    if (isCurrentPlan) {
      return "Current Plan"
    }
    if (plan.price === 0) {
      return "Get Started Free"
    }
    return "Subscribe Now"
  }

  const getButtonVariant = () => {
    if (isCurrentPlan) {
      return "secondary"
    }
    if (plan.is_popular) {
      return "default"
    }
    return "outline"
  }

  return (
    <>
      <Card className={`relative flex flex-col h-full ${plan.is_popular ? "border-primary shadow-lg" : ""}`}>
        {plan.is_popular && (
          <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
            <Badge className="bg-primary text-primary-foreground px-3 py-1">
              Most Popular
            </Badge>
          </div>
        )}

        <CardHeader className="text-center">
          <CardTitle className="text-2xl">{plan.name}</CardTitle>
          <div className="flex items-baseline justify-center">
            <span className="text-4xl font-bold">RM{plan.price.toFixed(2)}</span>
            {plan.price > 0 && (
              <span className="text-muted-foreground ml-1">/month</span>
            )}
          </div>
          <CardDescription className="text-center">
            {plan.description}
          </CardDescription>
        </CardHeader>

        <CardContent className="flex-grow">
          <ul className="space-y-2">
            {/* Auto-generated features */}
            {generateAutoFeatures(plan).map((feature, index) => (
              <li key={`auto-${index}`} className="flex items-start">
                <Check className="mr-2 h-4 w-4 text-primary mt-1" />
                <span className="text-sm text-primary">{feature}</span>
              </li>
            ))}
            {/* Custom features */}
            {plan.features.filter(f => !generateAutoFeatures(plan).includes(f)).map((feature, index) => (
              <li key={`custom-${index}`} className="flex items-start">
                <Check className="mr-2 h-4 w-4 text-primary mt-1" />
                <span className="text-sm">{feature}</span>
              </li>
            ))}
          </ul>
        </CardContent>

        <CardFooter className="mt-auto">
          <Button
            className="w-full"
            variant={getButtonVariant()}
            onClick={handleSubscribe}
            disabled={isCurrentPlan || isLoading}
          >
            <CreditCard className="mr-2 h-4 w-4" />
            {isLoading ? "Processing..." : getButtonText()}
          </Button>
        </CardFooter>
      </Card>

      <SubscriptionPaymentModal
        isOpen={isPaymentModalOpen}
        onClose={() => setIsPaymentModalOpen(false)}
        plan={plan}
      />
    </>
  )
}
