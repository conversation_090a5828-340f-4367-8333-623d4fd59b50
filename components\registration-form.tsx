"use client"

import { useState, use<PERSON>ffect, use<PERSON>allback } from "react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { User, Crown, Ticket } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { type SelectedTicket } from "@/types/ticket-types"
import { DynamicCustomFields } from "@/components/dynamic-custom-fields"
import { type CustomField } from "@/lib/db/supabase-schema"

// Form schema
const participantSchema = z.object({
  name: z.string().min(3, { message: "Name is required" }),
  ic: z.string().min(5, { message: "IC/Passport number is required" }),
  phone: z.string().min(8, { message: "Valid phone number is required" }),
  email: z.string().email({ message: "Valid email is required" }),
  custom_field_responses: z.record(z.any()).optional(),
})

const formSchema = z.object({
  mainContactIndex: z.number().min(0, { message: "Please select a main contact person" }),
  participants: z.array(participantSchema).min(1, { message: "At least one participant is required" }),
})

type FormValues = z.infer<typeof formSchema>

export type ParticipantType = {
  name: string
  ic: string
  phone: string
  email: string
  ticketTypeId: string
  ticketTypeName: string
  custom_field_responses?: Record<string, any>
}

interface RegistrationFormProps {
  event: any
  userId?: string | null
  selectedTickets: SelectedTicket[]
  onSuccess?: (participants: ParticipantType[], mainContact: any, mainContactIndex: number) => void
  savedFormData?: any
  onFormDataChange?: (data: any) => void
}

export function RegistrationForm({ event, userId, selectedTickets, onSuccess, savedFormData, onFormDataChange }: RegistrationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  // Calculate total number of tickets/participants needed
  const totalTickets = selectedTickets.reduce((total, ticket) => total + ticket.quantity, 0)

  // Generate participants array with ticket type associations
  const generateParticipants = useCallback(() => {
    console.log('🔧 generateParticipants called with selectedTickets:', selectedTickets)
    const participants: ParticipantType[] = []

    selectedTickets.forEach((selectedTicket) => {
      console.log(`🎫 Processing ticket: ${selectedTicket.ticketType.name} x ${selectedTicket.quantity}`)
      for (let i = 0; i < selectedTicket.quantity; i++) {
        participants.push({
          name: "",
          ic: "",
          phone: "",
          email: "",
          ticketTypeId: selectedTicket.ticketType.id,
          ticketTypeName: selectedTicket.ticketType.name,
          custom_field_responses: {}
        })
      }
    })

    console.log(`✅ Generated ${participants.length} participants:`, participants)
    return participants
  }, [selectedTickets])

  // Initialize form with saved data if available
  const getDefaultValues = () => {
    if (savedFormData && savedFormData.participants && savedFormData.participants.length > 0) {
      console.log("🔄 Restoring saved form data:", savedFormData)
      return {
        mainContactIndex: savedFormData.mainContactIndex ?? -1,
        participants: savedFormData.participants,
      }
    }
    console.log("🆕 Using fresh form data")
    return {
      mainContactIndex: -1, // No main contact selected initially
      participants: generateParticipants(),
    }
  }

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: getDefaultValues(),
  })

  // Field array for dynamic participants
  const { fields, replace } = useFieldArray({
    control: form.control,
    name: "participants",
  })

  // Watch for changes to mainContactIndex
  const mainContactIndex = form.watch("mainContactIndex")
  const participants = form.watch("participants")

  // Update participants array when ticket selection changes
  useEffect(() => {
    console.log('🔄 useEffect triggered - updating participants array')
    console.log('📊 Current selectedTickets:', selectedTickets)
    console.log('📊 Total tickets calculated:', totalTickets)
    console.log('📊 Current savedFormData:', savedFormData)

    // Skip if we have saved form data that matches the current ticket count
    if (savedFormData && savedFormData.participants && savedFormData.participants.length === totalTickets) {
      console.log('⏭️ Skipping participant generation - will use saved form data instead')
      return
    }

    // Generate participants directly in useEffect to avoid dependency issues
    const newParticipants: ParticipantType[] = []
    selectedTickets.forEach((selectedTicket) => {
      console.log(`🎫 Processing ticket: ${selectedTicket.ticketType.name} x ${selectedTicket.quantity}`)
      for (let i = 0; i < selectedTicket.quantity; i++) {
        newParticipants.push({
          name: "",
          ic: "",
          phone: "",
          email: "",
          ticketTypeId: selectedTicket.ticketType.id,
          ticketTypeName: selectedTicket.ticketType.name,
          custom_field_responses: {}
        })
      }
    })

    console.log(`✅ Generated ${newParticipants.length} participants:`, newParticipants)
    console.log('🔄 Current fields array length before replace:', fields.length)

    replace(newParticipants)

    console.log('🔄 Fields array length after replace should be:', newParticipants.length)

    // Auto-select main contact if only 1 ticket selected, otherwise reset
    if (totalTickets === 1) {
      console.log('👤 Auto-selecting single participant as main contact')
      form.setValue("mainContactIndex", 0) // Auto-select the only participant as main contact
    } else {
      console.log('👥 Multiple participants - resetting main contact selection')
      form.setValue("mainContactIndex", -1) // Reset main contact selection for multiple tickets
    }
  }, [selectedTickets, totalTickets, replace, form, savedFormData])

  // Restore form data when savedFormData changes (when navigating back)
  useEffect(() => {
    if (savedFormData && savedFormData.participants && savedFormData.participants.length > 0) {
      console.log("🔄 Restoring form data from props:", savedFormData)
      console.log("🔄 Saved participants count:", savedFormData.participants.length)
      console.log("🔄 Current selectedTickets total:", totalTickets)

      // Only restore if the saved data matches the current ticket selection
      if (savedFormData.participants.length === totalTickets) {
        console.log("✅ Saved data matches current tickets, restoring...")

        // Reset the form with saved data
        form.reset({
          mainContactIndex: savedFormData.mainContactIndex ?? -1,
          participants: savedFormData.participants,
        })

        // Update the field array to match saved participants
        replace(savedFormData.participants)
      } else {
        console.log("❌ Saved data doesn't match current tickets, ignoring saved data")
        console.log("❌ Expected participants:", totalTickets, "Saved participants:", savedFormData.participants.length)
      }
    }
  }, [savedFormData, form, replace, totalTickets])

  // Save form data whenever it changes
  useEffect(() => {
    if (onFormDataChange) {
      const subscription = form.watch((data) => {
        // Only save if form has meaningful data
        const hasData = data.participants?.some((p: any) => p?.name || p?.email || p?.phone || p?.ic)
        if (hasData || (data.mainContactIndex !== undefined && data.mainContactIndex >= 0)) {
          onFormDataChange(data)
        }
      })
      return () => subscription.unsubscribe()
    }
  }, [form, onFormDataChange])

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true)
    try {
      console.log("Form submitted:", values)

      // Validate that a main contact is selected
      if (values.mainContactIndex < 0 || values.mainContactIndex >= values.participants.length) {
        toast({
          title: "Error",
          description: "Please select a main contact person",
          variant: "destructive",
        })
        setIsSubmitting(false)
        return
      }

      // Get the main contact from the selected participant
      const mainContact = values.participants[values.mainContactIndex]

      // Ensure all participants have ticket type information
      const participantsWithTicketTypes = values.participants.map((participant, index) => {
        // Always ensure we have valid ticket type information
        let ticketTypeName = participant.ticketTypeName
        let ticketTypeId = participant.ticketTypeId

        // If ticketTypeName is missing or empty, try to get it from selectedTickets
        if (!ticketTypeName || (typeof ticketTypeName === 'string' && ticketTypeName.trim() === '')) {
          if (selectedTickets.length > 0) {
            // Find the corresponding ticket type for this participant
            let ticketIndex = 0
            let currentCount = 0
            for (let i = 0; i < selectedTickets.length; i++) {
              if (index >= currentCount && index < currentCount + selectedTickets[i].quantity) {
                ticketIndex = i
                break
              }
              currentCount += selectedTickets[i].quantity
            }
            ticketTypeId = selectedTickets[ticketIndex]?.ticketType.id || "standard"
            ticketTypeName = selectedTickets[ticketIndex]?.ticketType.name || "Standard"
          } else {
            // Fallback if no selected tickets
            ticketTypeId = "standard"
            ticketTypeName = "Standard"
          }
        }

        // Ensure ticketTypeName is never empty
        if (!ticketTypeName || (typeof ticketTypeName === 'string' && ticketTypeName.trim() === '')) {
          ticketTypeName = "Standard"
        }

        return {
          ...participant,
          ticketTypeId,
          ticketTypeName
        }
      })

      // Don't create registration yet, just proceed to summary
      toast({
        title: "Success",
        description: "Registration details saved. Proceed to payment.",
      })

      // Clear saved form data on successful submission
      if (onFormDataChange) {
        onFormDataChange(null)
      }

      if (onSuccess) {
        onSuccess(participantsWithTicketTypes, {
          name: mainContact.name,
          email: mainContact.email,
          phone: mainContact.phone,
        }, values.mainContactIndex)
      }
    } catch (error: any) {
      console.error("Error submitting form:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to register for event",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Calculate total amount based on selected tickets
  const calculateTotal = () => {
    return selectedTickets.reduce((total, ticket) => {
      return total + (ticket.ticketType.price * ticket.quantity)
    }, 0)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <div className="space-y-4">
          {/* Ticket Summary */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Selected Tickets</CardTitle>
              <p className="text-sm text-muted-foreground">
                You need to provide information for {totalTickets} participant{totalTickets !== 1 ? 's' : ''}
              </p>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {selectedTickets.map((ticket) => (
                  <div key={ticket.ticketType.id} className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-2">
                      <span>{ticket.ticketType.name}</span>
                      <span className="text-muted-foreground">× {ticket.quantity}</span>
                    </div>
                    <span className="font-medium">
                      RM {(ticket.ticketType.price * ticket.quantity).toFixed(2)}
                    </span>
                  </div>
                ))}
                <div className="pt-2 border-t">
                  <div className="flex items-center justify-between font-medium">
                    <span>Total: {totalTickets} ticket{totalTickets !== 1 ? 's' : ''}</span>
                    <span>RM {calculateTotal().toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <h3 className="text-lg font-medium">Participant Information</h3>
          <p className="text-sm text-muted-foreground">
            {totalTickets === 1
              ? `Fill in the details for the participant. They will automatically be set as the main contact person.`
              : `Fill in the details for all ${totalTickets} participants and select who will be the main contact person.`
            }
          </p>

          {fields.map((field, index) => {
            const participant = participants[index]
            return (
              <Card key={field.id} className={`relative ${mainContactIndex === index ? 'ring-2 ring-primary' : ''}`}>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between text-base">
                    <div className="flex items-center gap-2">
                      <span>Participant {index + 1}</span>
                      {participant && (
                        <div className="flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                          <Ticket className="h-3 w-3" />
                          <span>{participant.ticketTypeName}</span>
                        </div>
                      )}
                      {mainContactIndex === index && (
                        <div className="flex items-center gap-1 px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
                          <Crown className="h-3 w-3" />
                          <span>Main Contact</span>
                        </div>
                      )}
                    </div>
                  <FormField
                    control={form.control}
                    name="mainContactIndex"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              checked={field.value === index}
                              disabled={totalTickets === 1} // Disable checkbox when only 1 participant
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  field.onChange(index)
                                } else if (field.value === index) {
                                  field.onChange(-1)
                                }
                              }}
                            />
                            <Label className={`text-sm font-normal ${totalTickets === 1 ? 'text-muted-foreground' : 'cursor-pointer'}`}>
                              {totalTickets === 1 ? 'Automatically selected as main contact' : 'Set as main contact'}
                            </Label>
                          </div>
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name={`participants.${index}.name`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Full Name
                          <span className="text-red-500 ml-1">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Enter full name" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`participants.${index}.ic`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          IC/Passport Number
                          <span className="text-red-500 ml-1">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Enter IC or passport number" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name={`participants.${index}.phone`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Phone Number
                          <span className="text-red-500 ml-1">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Enter phone number" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`participants.${index}.email`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Email
                          <span className="text-red-500 ml-1">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input placeholder="Enter email address" type="email" {...field} value={field.value || ""} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Custom Fields */}
                {event.custom_fields && event.custom_fields.length > 0 && (
                  <DynamicCustomFields
                    customFields={event.custom_fields}
                    participantIndex={index}
                  />
                )}

                {mainContactIndex === index && (
                  <div className="mt-4 p-3 bg-primary/5 rounded-lg border border-primary/20">
                    <div className="flex items-center gap-2 text-sm text-primary">
                      <User className="h-4 w-4" />
                      <span className="font-medium">Main Contact Person</span>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      This person will receive all event communications and confirmations
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
            )
          })}

          {/* Main Contact Selection Validation */}
          {mainContactIndex < 0 && totalTickets > 1 && (
            <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-center gap-2 text-amber-800">
                <User className="h-4 w-4" />
                <span className="font-medium">Main Contact Required</span>
              </div>
              <p className="text-sm text-amber-700 mt-1">
                Please select one participant as the main contact person who will receive all event communications.
              </p>
            </div>
          )}
        </div>

        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Registration Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span>Event:</span>
                <span className="font-medium">{event.title}</span>
              </div>
              <div className="flex justify-between">
                <span>Date:</span>
                <span>{new Date(event.start_date).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span>Total Participants:</span>
                <span>{totalTickets}</span>
              </div>
              <div className="flex justify-between">
                <span>Main Contact:</span>
                <span>
                  {mainContactIndex >= 0
                    ? (participants[mainContactIndex]?.name || `Participant ${mainContactIndex + 1}`)
                    : "Not selected"}
                </span>
              </div>
              <div className="pt-2 border-t">
                <div className="flex justify-between font-medium">
                  <span>Total Amount:</span>
                  <span>RM {calculateTotal().toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button type="submit" className="w-full" disabled={isSubmitting || mainContactIndex < 0}>
                {isSubmitting ? "Processing..." : "Continue to Review"}
              </Button>
            </CardFooter>
          </Card>
        </div>
      </form>
    </Form>
  )
}
