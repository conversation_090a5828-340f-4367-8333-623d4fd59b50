"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { Loader2, Settings, Shield, Globe, Key, Wrench } from "lucide-react"
import type { AppSettings } from "@/lib/db/supabase-schema"

export default function AdminSettingsPage() {
  const { user, isAdmin } = useAuth()
  const { toast } = useToast()
  const [settings, setSettings] = useState<AppSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  // Check admin access
  if (!isAdmin()) {
    return (
      <Card className="m-6">
        <CardHeader>
          <CardTitle>Admin Settings</CardTitle>
          <CardDescription>You don't have permission to access this page</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  // Fetch settings on component mount
  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/app-settings')

      if (!response.ok) {
        throw new Error('Failed to fetch settings')
      }

      const data = await response.json()
      setSettings(data.settings)
    } catch (error) {
      console.error('Error fetching settings:', error)
      toast({
        title: "Error",
        description: "Failed to fetch admin settings",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const updateSettings = async (updatedSettings: Partial<AppSettings>) => {
    try {
      setSaving(true)
      const response = await fetch('/api/admin/app-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedSettings),
      })

      if (!response.ok) {
        throw new Error('Failed to update settings')
      }

      const data = await response.json()
      setSettings(data.settings)

      toast({
        title: "Success",
        description: "Admin settings updated successfully",
      })
    } catch (error) {
      console.error('Error updating settings:', error)
      toast({
        title: "Error",
        description: "Failed to update admin settings",
        variant: "destructive",
      })
    } finally {
      setSaving(false)
    }
  }

  const handleToggle = (field: keyof AppSettings, value: boolean) => {
    if (!settings) return

    const updatedSettings = { ...settings, [field]: value }
    setSettings(updatedSettings)

    // Send all current settings with the updated field
    updateSettings({
      login_enabled: field === 'login_enabled' ? value : settings.login_enabled,
      register_enabled: field === 'register_enabled' ? value : settings.register_enabled,
      password_reset_enabled: field === 'password_reset_enabled' ? value : settings.password_reset_enabled,
      api_enabled: field === 'api_enabled' ? value : settings.api_enabled,
      maintenance_mode: field === 'maintenance_mode' ? value : settings.maintenance_mode,
      maintenance_message: settings.maintenance_message
    })
  }

  const handleMaintenanceMessageChange = (message: string) => {
    if (!settings) return

    const updatedSettings = { ...settings, maintenance_message: message }
    setSettings(updatedSettings)
  }

  const saveMaintenanceMessage = () => {
    if (!settings) return

    // Send all current settings with the updated message
    updateSettings({
      login_enabled: settings.login_enabled,
      register_enabled: settings.register_enabled,
      password_reset_enabled: settings.password_reset_enabled,
      api_enabled: settings.api_enabled,
      maintenance_mode: settings.maintenance_mode,
      maintenance_message: settings.maintenance_message
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!settings) {
    return (
      <Card className="m-6">
        <CardHeader>
          <CardTitle>Admin Settings</CardTitle>
          <CardDescription>Failed to load settings</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center gap-2">
        <Settings className="h-6 w-6" />
        <h1 className="text-2xl font-bold">Admin Settings</h1>
      </div>

      {/* Authentication Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Authentication Settings
          </CardTitle>
          <CardDescription>
            Control user authentication features
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="login-enabled">Login</Label>
              <p className="text-sm text-muted-foreground">
                Allow users to log into the platform
              </p>
            </div>
            <Switch
              id="login-enabled"
              checked={settings.login_enabled}
              onCheckedChange={(checked) => handleToggle('login_enabled', checked)}
              disabled={saving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="register-enabled">Registration</Label>
              <p className="text-sm text-muted-foreground">
                Allow new users to register accounts
              </p>
            </div>
            <Switch
              id="register-enabled"
              checked={settings.register_enabled}
              onCheckedChange={(checked) => handleToggle('register_enabled', checked)}
              disabled={saving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="password-reset-enabled">Password Reset</Label>
              <p className="text-sm text-muted-foreground">
                Allow users to reset their passwords
              </p>
            </div>
            <Switch
              id="password-reset-enabled"
              checked={settings.password_reset_enabled}
              onCheckedChange={(checked) => handleToggle('password_reset_enabled', checked)}
              disabled={saving}
            />
          </div>
        </CardContent>
      </Card>

      {/* API Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            API Settings
          </CardTitle>
          <CardDescription>
            Control API access and functionality
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="api-enabled">API Access</Label>
              <p className="text-sm text-muted-foreground">
                Enable API endpoints for external integrations
              </p>
            </div>
            <Switch
              id="api-enabled"
              checked={settings.api_enabled}
              onCheckedChange={(checked) => handleToggle('api_enabled', checked)}
              disabled={saving}
            />
          </div>
        </CardContent>
      </Card>

      {/* Maintenance Mode */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wrench className="h-5 w-5" />
            Maintenance Mode
          </CardTitle>
          <CardDescription>
            Control website availability and maintenance messaging
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="maintenance-mode">Maintenance Mode</Label>
              <p className="text-sm text-muted-foreground">
                Enable maintenance mode to restrict access to the website
              </p>
            </div>
            <Switch
              id="maintenance-mode"
              checked={settings.maintenance_mode}
              onCheckedChange={(checked) => handleToggle('maintenance_mode', checked)}
              disabled={saving}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="maintenance-message">Maintenance Message</Label>
            <Textarea
              id="maintenance-message"
              placeholder="Enter maintenance message..."
              value={settings.maintenance_message}
              onChange={(e) => handleMaintenanceMessageChange(e.target.value)}
              disabled={saving}
              rows={3}
            />
            <Button
              onClick={saveMaintenanceMessage}
              disabled={saving}
              size="sm"
            >
              {saving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Message'
              )}
            </Button>
          </div>

          {/* Maintenance Mode Warning */}
          {settings.maintenance_mode && (
            <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-amber-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div>
                  <h4 className="text-sm font-medium text-amber-800">Maintenance Mode Active</h4>
                  <p className="text-sm text-amber-700 mt-1">
                    The website is currently in maintenance mode. Only admin users can access the system.
                    Authentication pages (login, register, password reset) remain accessible so admins can log in to disable maintenance mode.
                  </p>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
