import type { Metadata } from "next";

export const metadata: Metadata = {
  title: {
    template: '%s | mTicket.my',
    default: 'Authentication | mTicket.my - Event Management Platform',
  },
  description: 'Sign in to your mTicket.my account to manage events, tickets, and more.',
}

import type React from "react"

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="auth-layout">
      {children}
    </div>
  )
}
