"use client"

import { useEffect, useState } from "react"
import { seedAll } from "@/utils/seed-data"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"

export function DataInitializer() {
  const [isInitialized, setIsInitialized] = useState(true)
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    const checkData = async () => {
      try {
        // Check if user is logged in
        const { data: sessionData } = await supabase.auth.getSession()
        if (!sessionData.session) {
          setIsInitialized(true)
          return
        }

        // Check if events exist
        const { data: events, error: eventsError } = await supabase
          .from("events")
          .select("id")
          .eq("created_by", sessionData.session.user.id)
          .limit(1)

        if (eventsError) {
          console.error("Error checking events:", eventsError)
          setIsInitialized(false)
          return
        }

        // If no events, show initialization button
        if (!events || events.length === 0) {
          setIsInitialized(false)
        } else {
          setIsInitialized(true)
        }
      } catch (error) {
        console.error("Error checking data:", error)
        setIsInitialized(false)
      }
    }

    checkData()
  }, [])

  const handleInitialize = async () => {
    setIsLoading(true)
    try {
      const result = await seedAll()

      if (result.eventsSeeded && result.templatesSeeded) {
        toast({
          title: "Success",
          description: "Demo data initialized successfully!",
        })
        setIsInitialized(true)
      } else {
        toast({
          title: "Error",
          description: "Failed to initialize some demo data. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error initializing data:", error)
      toast({
        title: "Error",
        description: "Failed to initialize demo data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (isInitialized) {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-white rounded-lg shadow-lg p-4 border">
        <h3 className="font-medium mb-2">Initialize Demo Data</h3>
        <p className="text-sm text-muted-foreground mb-4">
          No events or templates found. Initialize demo data to explore all features.
        </p>
        <Button onClick={handleInitialize} disabled={isLoading}>
          {isLoading ? "Initializing..." : "Initialize Demo Data"}
        </Button>
      </div>
    </div>
  )
}
