# Android App Deployment and Testing Guide

## Testing Strategy

### 1. Unit Testing

#### Authentication Tests
```kotlin
@RunWith(MockitoJUnitRunner::class)
class AuthRepositoryTest {
    
    @Mock
    private lateinit var apiService: MTicketApiService
    
    @Mock
    private lateinit var tokenManager: SecureTokenManager
    
    private lateinit var authRepository: AuthRepository
    
    @Before
    fun setup() {
        authRepository = AuthRepository(apiService, tokenManager)
    }
    
    @Test
    fun `login success should return user data`() = runTest {
        // Given
        val email = "<EMAIL>"
        val password = "password123"
        val loginRequest = LoginRequest(email, password)
        val expectedResponse = LoginResponse(
            token = "jwt_token",
            user = User(id = "user_id", email = email, full_name = "Test User"),
            role = "user"
        )
        
        `when`(apiService.login(loginRequest)).thenReturn(Response.success(expectedResponse))
        
        // When
        val result = authRepository.login(email, password)
        
        // Then
        assertTrue(result.isSuccess)
        assertEquals(expectedResponse, result.getOrNull())
        verify(tokenManager).saveToken("jwt_token")
    }
    
    @Test
    fun `login failure should return error`() = runTest {
        // Given
        val email = "<EMAIL>"
        val password = "wrong_password"
        val loginRequest = LoginRequest(email, password)
        
        `when`(apiService.login(loginRequest)).thenReturn(
            Response.error(401, "Unauthorized".toResponseBody())
        )
        
        // When
        val result = authRepository.login(email, password)
        
        // Then
        assertTrue(result.isFailure)
        verify(tokenManager, never()).saveToken(any())
    }
}
```

#### QR Code Generation Tests
```kotlin
@RunWith(RobolectricTestRunner::class)
class QRCodeGeneratorTest {
    
    private lateinit var qrCodeGenerator: QRCodeGenerator
    
    @Before
    fun setup() {
        qrCodeGenerator = QRCodeGenerator()
    }
    
    @Test
    fun `generateQRCodeBitmap should return valid bitmap for valid data`() {
        // Given
        val testData = "test_qr_data"
        val size = 256
        
        // When
        val bitmap = qrCodeGenerator.generateQRCodeBitmap(testData, size)
        
        // Then
        assertNotNull(bitmap)
        assertEquals(size, bitmap?.width)
        assertEquals(size, bitmap?.height)
    }
    
    @Test
    fun `generateQRCodeBitmap should return null for empty data`() {
        // Given
        val emptyData = ""
        
        // When
        val bitmap = qrCodeGenerator.generateQRCodeBitmap(emptyData)
        
        // Then
        assertNull(bitmap)
    }
}
```

### 2. Integration Testing

#### API Integration Tests
```kotlin
@RunWith(AndroidJUnit4::class)
@LargeTest
class ApiIntegrationTest {
    
    private lateinit var apiService: MTicketApiService
    private lateinit var mockWebServer: MockWebServer
    
    @Before
    fun setup() {
        mockWebServer = MockWebServer()
        mockWebServer.start()
        
        val retrofit = Retrofit.Builder()
            .baseUrl(mockWebServer.url("/"))
            .addConverterFactory(GsonConverterFactory.create())
            .build()
        
        apiService = retrofit.create(MTicketApiService::class.java)
    }
    
    @After
    fun teardown() {
        mockWebServer.shutdown()
    }
    
    @Test
    fun `login API should return success response`() = runTest {
        // Given
        val mockResponse = """
            {
                "token": "mock_jwt_token",
                "user": {
                    "id": "user_123",
                    "email": "<EMAIL>",
                    "full_name": "Test User"
                },
                "role": "user"
            }
        """.trimIndent()
        
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody(mockResponse)
                .addHeader("Content-Type", "application/json")
        )
        
        // When
        val response = apiService.login(LoginRequest("<EMAIL>", "password"))
        
        // Then
        assertTrue(response.isSuccessful)
        assertEquals("mock_jwt_token", response.body()?.token)
        assertEquals("Test User", response.body()?.user?.full_name)
    }
    
    @Test
    fun `getUserTickets should return ticket list`() = runTest {
        // Given
        val mockResponse = """
            [
                {
                    "id": "ticket_123",
                    "event_id": "event_456",
                    "attendee_name": "John Doe",
                    "attendee_email": "<EMAIL>",
                    "ticket_type": "Standard",
                    "payment_status": "paid",
                    "status": "confirmed",
                    "checked_in": false,
                    "created_at": "2024-01-15T10:30:00Z",
                    "event": {
                        "id": "event_456",
                        "title": "Test Event",
                        "start_date": "2024-02-15T09:00:00Z",
                        "end_date": "2024-02-15T18:00:00Z",
                        "location": "Test Venue"
                    }
                }
            ]
        """.trimIndent()
        
        mockWebServer.enqueue(
            MockResponse()
                .setResponseCode(200)
                .setBody(mockResponse)
                .addHeader("Content-Type", "application/json")
        )
        
        // When
        val response = apiService.getUserTickets("Bearer mock_token")
        
        // Then
        assertTrue(response.isSuccessful)
        assertEquals(1, response.body()?.size)
        assertEquals("Test Event", response.body()?.first()?.event?.title)
    }
}
```

### 3. UI Testing

#### Login Screen Tests
```kotlin
@RunWith(AndroidJUnit4::class)
@LargeTest
class LoginScreenTest {
    
    @get:Rule
    val composeTestRule = createComposeRule()
    
    @Test
    fun loginScreen_displaysCorrectElements() {
        composeTestRule.setContent {
            MTicketTheme {
                LoginScreen(
                    viewModel = FakeAuthViewModel(),
                    onLoginSuccess = {}
                )
            }
        }
        
        // Verify UI elements are displayed
        composeTestRule.onNodeWithText("Email").assertIsDisplayed()
        composeTestRule.onNodeWithText("Password").assertIsDisplayed()
        composeTestRule.onNodeWithText("Login").assertIsDisplayed()
    }
    
    @Test
    fun loginScreen_enablesLoginButtonWhenFieldsFilled() {
        composeTestRule.setContent {
            MTicketTheme {
                LoginScreen(
                    viewModel = FakeAuthViewModel(),
                    onLoginSuccess = {}
                )
            }
        }
        
        // Initially login button should be disabled
        composeTestRule.onNodeWithText("Login").assertIsNotEnabled()
        
        // Fill in email and password
        composeTestRule.onNodeWithText("Email").performTextInput("<EMAIL>")
        composeTestRule.onNodeWithText("Password").performTextInput("password123")
        
        // Login button should now be enabled
        composeTestRule.onNodeWithText("Login").assertIsEnabled()
    }
    
    @Test
    fun loginScreen_showsErrorMessage() {
        val viewModel = FakeAuthViewModel()
        
        composeTestRule.setContent {
            MTicketTheme {
                LoginScreen(
                    viewModel = viewModel,
                    onLoginSuccess = {}
                )
            }
        }
        
        // Simulate error state
        viewModel.setErrorState("Invalid credentials")
        
        // Error message should be displayed
        composeTestRule.onNodeWithText("Invalid credentials").assertIsDisplayed()
    }
}
```

## Build Configuration

### 1. Gradle Build Scripts

#### app/build.gradle
```gradle
android {
    namespace 'com.mticket.mobile'
    compileSdk 34

    defaultConfig {
        applicationId "com.mticket.mobile"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            buildConfigField "String", "API_BASE_URL", "\"https://staging-api.mticket.my/api/\""
            buildConfigField "String", "API_VERSION", "\"v1\""
            buildConfigField "boolean", "ENABLE_LOGGING", "true"
        }
        
        staging {
            debuggable false
            minifyEnabled true
            shrinkResources true
            buildConfigField "String", "API_BASE_URL", "\"https://staging-api.mticket.my/api/\""
            buildConfigField "String", "API_VERSION", "\"v1\""
            buildConfigField "boolean", "ENABLE_LOGGING", "false"
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        
        release {
            debuggable false
            minifyEnabled true
            shrinkResources true
            buildConfigField "String", "API_BASE_URL", "\"https://api.mticket.my/api/\""
            buildConfigField "String", "API_VERSION", "\"v1\""
            buildConfigField "boolean", "ENABLE_LOGGING", "false"
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            signingConfig signingConfigs.release
        }
    }
    
    signingConfigs {
        release {
            storeFile file(project.findProperty("RELEASE_STORE_FILE") ?: "release.keystore")
            storePassword project.findProperty("RELEASE_STORE_PASSWORD") ?: ""
            keyAlias project.findProperty("RELEASE_KEY_ALIAS") ?: ""
            keyPassword project.findProperty("RELEASE_KEY_PASSWORD") ?: ""
        }
    }
    
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    kotlinOptions {
        jvmTarget = '1.8'
    }
    
    buildFeatures {
        compose true
        buildConfig true
    }
    
    composeOptions {
        kotlinCompilerExtensionVersion '1.5.8'
    }
    
    packaging {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
    
    testOptions {
        unitTests {
            includeAndroidResources = true
        }
    }
}
```

### 2. ProGuard Configuration

#### proguard-rules.pro
```proguard
# Keep data classes used for API responses
-keep class com.mticket.mobile.data.model.** { *; }

# Keep Retrofit interfaces
-keep interface com.mticket.mobile.data.api.** { *; }

# Gson specific classes
-keepattributes Signature
-keepattributes *Annotation*
-keep class sun.misc.Unsafe { *; }
-keep class com.google.gson.stream.** { *; }

# OkHttp
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**

# ZXing QR Code library
-keep class com.google.zxing.** { *; }
-dontwarn com.google.zxing.**

# Room database
-keep class * extends androidx.room.RoomDatabase
-keep @androidx.room.Entity class *
-dontwarn androidx.room.paging.**

# Hilt
-keep class dagger.hilt.** { *; }
-keep class javax.inject.** { *; }
-keep class * extends dagger.hilt.android.lifecycle.HiltViewModel { *; }
```

## CI/CD Pipeline

### 1. GitHub Actions Workflow

#### .github/workflows/android.yml
```yaml
name: Android CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
      
    - name: Run unit tests
      run: ./gradlew testDebugUnitTest
      
    - name: Run instrumented tests
      uses: reactivecircus/android-emulator-runner@v2
      with:
        api-level: 29
        script: ./gradlew connectedDebugAndroidTest
        
    - name: Upload test reports
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-reports
        path: app/build/reports/tests/

  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
      
    - name: Build debug APK
      run: ./gradlew assembleDebug
      
    - name: Upload debug APK
      uses: actions/upload-artifact@v3
      with:
        name: debug-apk
        path: app/build/outputs/apk/debug/app-debug.apk

  release:
    if: github.ref == 'refs/heads/main'
    needs: [test, build]
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        
    - name: Decode keystore
      run: |
        echo "${{ secrets.RELEASE_KEYSTORE }}" | base64 -d > release.keystore
        
    - name: Build release APK
      run: ./gradlew assembleRelease
      env:
        RELEASE_STORE_FILE: release.keystore
        RELEASE_STORE_PASSWORD: ${{ secrets.RELEASE_STORE_PASSWORD }}
        RELEASE_KEY_ALIAS: ${{ secrets.RELEASE_KEY_ALIAS }}
        RELEASE_KEY_PASSWORD: ${{ secrets.RELEASE_KEY_PASSWORD }}
        
    - name: Upload release APK
      uses: actions/upload-artifact@v3
      with:
        name: release-apk
        path: app/build/outputs/apk/release/app-release.apk
```

## Security Testing

### 1. Static Analysis
```bash
# Run static analysis with detekt
./gradlew detekt

# Run security analysis with MobSF
# Upload APK to Mobile Security Framework for analysis
```

### 2. Network Security Testing
```kotlin
@Test
fun testCertificatePinning() {
    // Test that app properly validates SSL certificates
    // and rejects connections to servers with invalid certificates
}

@Test
fun testTokenEncryption() {
    // Verify that JWT tokens are properly encrypted in storage
    val tokenManager = SecureTokenManager(context)
    val testToken = "test_jwt_token"
    
    tokenManager.saveToken(testToken)
    val retrievedToken = tokenManager.getToken()
    
    assertEquals(testToken, retrievedToken)
    
    // Verify token is encrypted in SharedPreferences
    val prefs = context.getSharedPreferences("secure_prefs", Context.MODE_PRIVATE)
    val storedValue = prefs.getString("jwt_token", null)
    assertNotEquals(testToken, storedValue) // Should be encrypted
}
```

## Performance Testing

### 1. Memory Leak Detection
```kotlin
@Test
fun testMemoryLeaks() {
    // Use LeakCanary for memory leak detection
    // Verify ViewModels are properly cleared
    // Check for retained fragments/activities
}
```

### 2. Network Performance
```kotlin
@Test
fun testAPIResponseTimes() {
    // Measure API response times
    // Verify responses are under acceptable thresholds
    val startTime = System.currentTimeMillis()
    
    // Make API call
    val response = apiService.getUserTickets("Bearer token")
    
    val endTime = System.currentTimeMillis()
    val responseTime = endTime - startTime
    
    assertTrue("API response time should be under 3 seconds", responseTime < 3000)
}
```

## Deployment Checklist

### Pre-Release Checklist
- [ ] All unit tests passing
- [ ] All integration tests passing
- [ ] UI tests passing
- [ ] Security analysis completed
- [ ] Performance testing completed
- [ ] Code review completed
- [ ] Release notes prepared
- [ ] App signing configured
- [ ] ProGuard rules tested
- [ ] API endpoints verified in production
- [ ] Crash reporting configured (Firebase Crashlytics)
- [ ] Analytics configured (Firebase Analytics)

### Release Process
1. **Create release branch** from main
2. **Update version code** and version name
3. **Run full test suite**
4. **Generate signed APK**
5. **Test signed APK** on physical devices
6. **Upload to Google Play Console** (Internal Testing)
7. **Conduct beta testing** with selected users
8. **Address feedback** and fix critical issues
9. **Promote to production** after approval
10. **Monitor crash reports** and user feedback

### Post-Release Monitoring
- Monitor crash reports in Firebase Crashlytics
- Track user engagement in Firebase Analytics
- Monitor API error rates and response times
- Collect user feedback through in-app feedback or Play Store reviews
- Plan next iteration based on user feedback and analytics data

This comprehensive testing and deployment guide ensures the Android app meets quality standards and provides a smooth user experience while maintaining security and performance requirements.
