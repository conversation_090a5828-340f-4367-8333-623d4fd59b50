import { NextRequest, NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";

export async function POST(request: NextRequest) {
  try {
    console.log("Testing user creation...");

    const supabaseAdmin = getSupabaseAdmin();
    console.log("Admin client created successfully");

    // Test 1: Check if user exists
    console.log("1. Testing user lookup...");
    const { data: existingUser, error: userCheckError } = await supabaseAdmin
      .from("users")
      .select("id")
      .eq("email", "<EMAIL>")
      .single();

    console.log("User check error:", userCheckError);
    console.log("User check result:", existingUser);

    // Test 2: Get user role_id
    console.log("2. Getting user role_id...");
    const { data: roleData, error: roleError } = await supabaseAdmin
      .from("user_roles")
      .select("id")
      .eq("role_name", "user")
      .single();

    console.log("Role data:", roleData, "Role error:", roleError);

    // Test 3: Try to create user
    console.log("3. Testing user creation...");
    const userId = crypto.randomUUID();
    const userData = {
      id: userId,
      email: "<EMAIL>",
      password_hash: "test_hash_123",
      full_name: "Noor Admin",
      role: "free", // Legacy field
      role_id: roleData?.id, // New role system
      subscription_status: "none",
      subscription_end_date: null,
      created_at: new Date().toISOString(),
      organization: null,
      profile_image_url: null,
      events_created: 0,
      total_earnings: 0,
      available_balance: 0,
    };

    console.log("Attempting to insert user data:", userData);

    const { data: newUser, error: createError } = await supabaseAdmin
      .from("users")
      .insert(userData)
      .select()
      .single();

    console.log("User creation error:", createError);
    console.log("User created successfully:", newUser);

    return NextResponse.json({
      success: true,
      userCheckError,
      existingUser,
      roleData,
      roleError,
      createError,
      newUser,
      userData
    });

  } catch (error) {
    console.error("Test failed:", error);
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
