import { NextResponse } from "next/server"
import { getJWTTokenFromRequest, verifyJWTToken } from "@/lib/auth"
import { getSupabaseAdmin } from "@/lib/supabase"

/**
 * GET /api/tickets/view-pdf?ticketId=xxx&type=ticket|receipt
 * GET /api/tickets/view-pdf?groupId=xxx&type=group-receipt
 * View ticket or receipt as HTML that can be printed as PDF
 */
export async function GET(request: Request) {
  try {
    const url = new URL(request.url)
    const ticketId = url.searchParams.get('ticketId')
    const groupId = url.searchParams.get('groupId')
    const type = url.searchParams.get('type') || 'ticket'

    console.log("View PDF API: Starting request for", { ticketId, groupId, type })

    if (!ticketId && !groupId) {
      return new Response("Ticket ID or Group ID is required", { status: 400 })
    }

    if (type === 'group-receipt' && !groupId) {
      return new Response("Group ID is required for group receipt", { status: 400 })
    }

    // Get JWT token from request
    const token = getJWTTokenFromRequest(request)

    if (!token) {
      return new Response("Authentication required", { status: 401 })
    }

    // Verify the JWT token and get user data
    const authResult = await verifyJWTToken(token)
    if (!authResult?.user) {
      return new Response("Invalid or expired token", { status: 401 })
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin()

    // Handle group receipt
    if (type === 'group-receipt' && groupId) {
      console.log("View PDF API: Processing group receipt for groupId:", groupId)

      // Fetch all registrations in the group with transaction details
      const { data: groupRegistrations, error: groupError } = await supabaseAdmin
        .from("registrations")
        .select(`
          *,
          event:event_id (
            id,
            title,
            slug,
            description,
            location,
            start_date,
            end_date,
            image_url,
            price
          ),
          transaction:transaction_id (
            id,
            status,
            amount,
            currency,
            gateway_transaction_id,
            invoice_number,
            receipt_number,
            group_transaction_id,
            processed_at,
            created_at
          )
        `)
        .eq("group_registration_id", groupId)
        .or(`user_id.eq.${authResult.user.id},created_by.eq.${authResult.user.id}`)
        .order("attendee_name", { ascending: true })

      console.log("View PDF API: Group query result:", {
        error: groupError,
        count: groupRegistrations?.length || 0
      })

      if (groupError || !groupRegistrations || groupRegistrations.length === 0) {
        return new Response("Group registration not found or access denied", { status: 404 })
      }

      // Get the main group transaction
      const mainTransaction = groupRegistrations.find(reg =>
        reg.transaction?.group_transaction_id === reg.transaction?.id
      )?.transaction

      // Calculate totals
      const totalAmount = groupRegistrations.reduce((sum, reg) =>
        sum + parseFloat(reg.payment_amount || '0'), 0
      )

      const totalFees = groupRegistrations.reduce((sum, reg) =>
        sum + parseFloat(reg.financial_transaction?.[0]?.fee_amount || '0'), 0
      )

      const event = groupRegistrations[0]?.event
      const eventDate = new Date(event?.start_date || "").toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })

      // Generate group receipt HTML
      const groupReceiptHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Group Payment Receipt - ${event?.title || "Unknown Event"}</title>
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }

        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 10px;
            background: white;
            color: #333;
            line-height: 1.4;
        }

        .receipt {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 24px;
        }

        .header p {
            margin: 5px 0 0 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .content {
            padding: 25px;
        }

        .section {
            margin-bottom: 20px;
        }

        .section h2 {
            font-size: 18px;
            color: #667eea;
            margin-bottom: 12px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 5px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            font-size: 14px;
        }

        .detail-row:nth-child(even) {
            background: #f8fafc;
            margin: 0 -15px;
            padding: 5px 15px;
        }

        .label {
            font-weight: bold;
            color: #4a5568;
        }

        .value {
            color: #2d3748;
        }

        .participants-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .participants-table th,
        .participants-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
            font-size: 12px;
        }

        .participants-table th {
            background: #f8fafc;
            font-weight: bold;
            color: #4a5568;
        }

        .summary-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .summary-item {
            text-align: center;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }

        .summary-value {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
        }

        .summary-label {
            font-size: 12px;
            color: #64748b;
            margin-top: 2px;
        }

        .total-section {
            background: #f0f9ff;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #0ea5e9;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 16px;
        }

        .total-final {
            font-size: 20px;
            font-weight: bold;
            border-top: 2px solid #0ea5e9;
            padding-top: 10px;
            margin-top: 10px;
            color: #0ea5e9;
        }

        .footer {
            text-align: center;
            padding: 20px;
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            font-size: 12px;
        }

        .status-paid {
            color: #10b981;
            font-weight: bold;
        }

        @media print {
            body {
                background: white;
            }
            .receipt {
                border: none;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <div class="header">
            <h1>mTicket.my</h1>
            <p>Group Payment Receipt</p>
            <p style="font-size: 16px; margin-top: 10px;">${event?.title || "Unknown Event"}</p>
        </div>

        <div class="content">
            <div class="section">
                <h2>Group Receipt Details</h2>
                <div class="detail-row">
                    <span class="label">Main Receipt Number:</span>
                    <span class="value">${mainTransaction?.receipt_number || 'N/A'}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Main Invoice Number:</span>
                    <span class="value">${mainTransaction?.invoice_number || 'N/A'}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Transaction ID:</span>
                    <span class="value">${mainTransaction?.gateway_transaction_id || 'N/A'}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Payment Date:</span>
                    <span class="value">${new Date(groupRegistrations[0]?.payment_date || groupRegistrations[0]?.created_at).toLocaleDateString()}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Status:</span>
                    <span class="value status-paid">PAID</span>
                </div>
            </div>

            <div class="section">
                <h2>Event Details</h2>
                <div class="detail-row">
                    <span class="label">Event:</span>
                    <span class="value">${event?.title || "Unknown Event"}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Date:</span>
                    <span class="value">${eventDate}</span>
                </div>
                <div class="detail-row">
                    <span class="label">Location:</span>
                    <span class="value">${event?.location || "TBA"}</span>
                </div>
            </div>

            <div class="section">
                <h2>Group Registration Summary</h2>
                <div class="summary-section">
                    <div class="summary-grid">
                        <div class="summary-item">
                            <div class="summary-value">${groupRegistrations.length}</div>
                            <div class="summary-label">Participants</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-value">RM ${totalAmount.toFixed(2)}</div>
                            <div class="summary-label">Total Amount</div>
                        </div>
                        <div class="summary-item">
                            <div class="summary-value">RM ${totalFees.toFixed(2)}</div>
                            <div class="summary-label">Processing Fees</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>Participants & Individual Receipts</h2>
                <table class="participants-table">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Receipt Number</th>
                            <th>Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${groupRegistrations.map(participant => `
                        <tr>
                            <td>${participant.attendee_name}</td>
                            <td>${participant.attendee_email}</td>
                            <td style="font-family: monospace; font-size: 10px;">${participant.transaction?.receipt_number || 'N/A'}</td>
                            <td>RM ${parseFloat(participant.payment_amount || '0').toFixed(2)}</td>
                        </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>

            <div class="section">
                <h2>Payment Summary</h2>
                <div class="total-section">
                    <div class="total-row">
                        <span>Subtotal (${groupRegistrations.length} participants):</span>
                        <span>RM ${totalAmount.toFixed(2)}</span>
                    </div>
                    <div class="total-row">
                        <span>Processing Fees:</span>
                        <span>RM ${totalFees.toFixed(2)}</span>
                    </div>
                    <div class="total-row total-final">
                        <span>Total Paid:</span>
                        <span>RM ${totalAmount.toFixed(2)}</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p style="margin: 0 0 8px 0; font-weight: bold;">Thank you for your group registration!</p>
            <p style="margin: 0 0 5px 0;">This receipt covers all ${groupRegistrations.length} participants in this group registration.</p>
            <p style="margin: 0 0 5px 0;">Individual receipts are available for each participant.</p>
            <p style="font-size: 10px; color: #64748b; margin: 0;">
                Official group receipt from mTicket.my | Generated: ${new Date().toLocaleString()}
            </p>
        </div>
    </div>

    <script>
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 1000);
        }
    </script>
</body>
</html>
      `

      return new Response(groupReceiptHtml, {
        headers: {
          'Content-Type': 'text/html',
        },
      })
    }

    // Handle individual ticket/receipt
    if (!ticketId) {
      console.log("View PDF API: Missing ticket ID for individual ticket/receipt")
      return new Response("Ticket ID is required for individual ticket/receipt", { status: 400 })
    }

    console.log("View PDF API: Processing individual ticket/receipt for ticketId:", ticketId, "type:", type)

    // Fetch ticket data with event and transaction details
    // Support both direct registrations (user_id) and group registrations (created_by)
    const { data: ticket, error: ticketError } = await supabaseAdmin
      .from("registrations")
      .select(`
        *,
        event:event_id (
          id,
          title,
          slug,
          description,
          location,
          start_date,
          end_date,
          image_url,
          price
        ),
        transaction:transaction_id (
          id,
          status,
          amount,
          currency,
          gateway_transaction_id,
          invoice_number,
          receipt_number,
          processed_at,
          created_at
        )
      `)
      .eq("id", ticketId)
      .or(`user_id.eq.${authResult.user.id},created_by.eq.${authResult.user.id}`)
      .single()

    console.log("View PDF API: Ticket query result:", {
      error: ticketError,
      found: !!ticket,
      ticketUserId: ticket?.user_id,
      ticketCreatedBy: ticket?.created_by,
      currentUserId: authResult.user.id
    })

    if (ticketError || !ticket) {
      console.log("View PDF API: Ticket not found or access denied")
      return new Response("Ticket not found or access denied", { status: 404 })
    }

    // Generate ticket HTML
    const eventDate = new Date(ticket.event?.start_date || "").toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })

    const eventTime = new Date(ticket.event?.start_date || "").toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })

    const qrCodeUrl = `https://mticket.my/verify?data=${btoa(JSON.stringify({
      id: ticket.id,
      event: ticket.event?.title || "Unknown Event",
      attendee: ticket.attendee_name,
      ticket: ticket.id,
      timestamp: new Date().toISOString(),
    }))}`

    // Generate different HTML based on type
    if (type === 'receipt') {
      console.log("View PDF API: Generating receipt for ticket:", {
        paymentStatus: ticket.payment_status,
        hasReceiptNumber: !!ticket.transaction?.receipt_number,
        hasPaymentDate: !!ticket.payment_date,
        transactionId: ticket.transaction?.id
      })

      // Check if payment is confirmed and has transaction data
      if (ticket.payment_status !== 'paid' || (!ticket.transaction?.receipt_number && !ticket.payment_date)) {
        console.log("View PDF API: Receipt not available - payment not confirmed or transaction data missing")
        return new Response("Receipt not available - payment not confirmed or transaction data missing", { status: 400 })
      }

      const receiptHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Receipt - ${ticket.event?.title || "Unknown Event"}</title>
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }

        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 10px;
            background: white;
            color: #333;
            line-height: 1.4;
        }

        .receipt {
            max-width: 700px;
            margin: 0 auto;
            background: white;
        }

        .header {
            text-align: center;
            margin-bottom: 16px;
        }

        .header-box {
            background: linear-gradient(to right, #2563eb, #9333ea);
            color: white;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 12px;
        }

        .header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: bold;
        }

        .header p {
            margin: 3px 0 0 0;
            font-size: 12px;
            opacity: 0.9;
        }

        .event-title {
            font-size: 18px;
            font-weight: 600;
            color: #374151;
            margin: 0;
        }

        .content {
            max-width: 700px;
            margin: 0 auto;
        }

        .section {
            margin-bottom: 16px;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }

        .section.blue {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
        }

        .section.white {
            background: white;
            border: 1px solid #d1d5db;
        }

        .section.green {
            background: linear-gradient(to right, #f0fdf4, #dbeafe);
            border: 1px solid #10b981;
        }

        .section.footer {
            background: linear-gradient(to right, #f9fafb, #dbeafe);
            border: 1px solid #e5e7eb;
        }

        .section h2 {
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            margin: 0 0 8px 0;
        }

        .section h2.blue {
            color: #1e40af;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2px 0;
            margin-bottom: 4px;
        }

        .detail-row:last-child {
            margin-bottom: 0;
        }

        .label {
            font-size: 12px;
            color: #6b7280;
        }

        .label.blue {
            color: #1e40af;
        }

        .value {
            font-size: 12px;
            font-weight: 500;
            color: #374151;
        }

        .value.mono {
            font-family: 'Courier New', monospace;
            color: #1e40af;
        }

        .payment-item {
            border: 1px solid #f3f4f6;
            border-radius: 8px;
            padding: 8px;
            margin-bottom: 8px;
        }

        .payment-item-details {
            flex: 1;
        }

        .payment-item-title {
            font-size: 14px;
            font-weight: 500;
            color: #374151;
        }

        .payment-item-subtitle {
            font-size: 12px;
            color: #6b7280;
            margin-top: 2px;
        }

        .payment-amount {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
        }

        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .total-final {
            font-size: 16px;
            font-weight: bold;
            color: #374151;
        }

        .total-amount {
            font-size: 20px;
            font-weight: bold;
            color: #059669;
        }

        .currency-note {
            font-size: 10px;
            color: #6b7280;
        }

        .status-paid {
            background: #dcfce7;
            color: #166534;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-pending {
            background: #fef3c7;
            color: #92400e;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        @media print {
            body {
                background: white;
            }
            .receipt {
                border: none;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <!-- Receipt Header -->
        <div class="header">
            <div class="header-box">
                <h1>mTicket.my</h1>
                <p>Official Payment Receipt</p>
            </div>
            <h2 class="event-title">${ticket.event?.title || "Unknown Event"}</h2>
        </div>

        <!-- Receipt Content -->
        <div class="content">
            <!-- Receipt Information -->
            <div class="section blue">
                <h2 class="blue">Receipt Information</h2>
                <div class="two-column">
                    <div class="detail-row">
                        <span class="label blue">Receipt Number:</span>
                        <span class="value mono">${ticket.transaction?.receipt_number || `RCP-${ticket.id.substring(0, 8).toUpperCase()}`}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label blue">Invoice Number:</span>
                        <span class="value mono">${ticket.transaction?.invoice_number || `INV-${ticket.id.substring(0, 8).toUpperCase()}`}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label blue">Transaction ID:</span>
                        <span class="value mono">${ticket.transaction?.gateway_transaction_id || ticket.payment_id || 'N/A'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label blue">Payment Date:</span>
                        <span class="value">${ticket.payment_date ?
                          new Date(ticket.payment_date).toLocaleDateString('en-MY', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          }) :
                          new Date(ticket.created_at).toLocaleDateString('en-MY', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })
                        }</span>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="section white">
                <h2>Customer Information</h2>
                <div class="two-column">
                    <div class="detail-row">
                        <span class="label">Name:</span>
                        <span class="value">${ticket.guest_name || ticket.attendee_name || "Unknown Attendee"}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Email:</span>
                        <span class="value">${ticket.guest_email || ticket.attendee_email || "N/A"}</span>
                    </div>
                    ${ticket.ic_reg ? `
                    <div class="detail-row">
                        <span class="label">IC/Registration No:</span>
                        <span class="value">${ticket.ic_reg}</span>
                    </div>
                    ` : ''}
                    <div class="detail-row">
                        <span class="label">Registration Type:</span>
                        <span class="value">${ticket.group_registration_id ? 'Group Registration' : 'Individual Registration'}</span>
                    </div>
                    <div class="detail-row">
                        <span class="label">Payment Status:</span>
                        <span class="value status-${ticket.payment_status || 'pending'}">${(ticket.payment_status || 'pending').toUpperCase()}</span>
                    </div>
                </div>
            </div>

            <!-- Payment Details -->
            <div class="section white">
                <h2>Payment Details</h2>
                <div class="payment-item">
                    <div style="display: flex; justify-content: space-between; align-items: start;">
                        <div class="payment-item-details">
                            <div class="payment-item-title">${ticket.event?.title || "Event Registration"}</div>
                            <div class="payment-item-subtitle">Registration for ${ticket.guest_name || ticket.attendee_name || "Unknown Attendee"}</div>
                        </div>
                        <div style="text-align: right;">
                            <div class="payment-amount">RM ${(ticket.payment_amount || ticket.event?.price || 0).toFixed(2)}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total -->
            <div class="section green">
                <div class="total-row">
                    <span class="total-final">Total Paid</span>
                    <span class="total-amount">RM ${(ticket.payment_amount || ticket.event?.price || 0).toFixed(2)}</span>
                </div>
                <div class="currency-note">Currency: Malaysian Ringgit (MYR)</div>
            </div>

            <!-- Receipt Footer -->
            <div class="section footer">
                <h3 style="font-size: 12px; font-weight: 500; color: #374151; margin: 0 0 4px 0;">Important Notes</h3>
                <ul style="list-style: none; padding: 0; margin: 0;">
                    <li style="font-size: 10px; color: #6b7280; margin-bottom: 2px;">• This is an official payment receipt from mTicket.my</li>
                    <li style="font-size: 10px; color: #6b7280; margin-bottom: 2px;">• Keep this receipt for your records and tax purposes</li>
                    <li style="font-size: 10px; color: #6b7280; margin-bottom: 2px;">• For support, contact us with receipt number: ${ticket.transaction?.receipt_number || `RCP-${ticket.id.substring(0, 8).toUpperCase()}`}</li>
                    <li style="font-size: 10px; color: #6b7280; margin-bottom: 0;">• This receipt confirms your registration for the event</li>
                </ul>
                <div style="border-top: 1px solid #d1d5db; margin-top: 8px; padding-top: 4px; text-align: center;">
                    <p style="font-size: 10px; color: #6b7280; margin: 0;">Generated on ${new Date().toLocaleDateString('en-MY', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 1000);
        }
    </script>
</body>
</html>
      `

      return new Response(receiptHtml, {
        headers: {
          'Content-Type': 'text/html',
        },
      })
    }

    const ticketHtml = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Ticket - ${ticket.event?.title || "Unknown Event"}</title>
    <style>
        @page {
            size: A4 landscape;
            margin: 10mm;
        }

        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .ticket {
            width: 800px;
            height: 400px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 30px;
            box-sizing: border-box;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }

        .decorative-circle-1 {
            position: absolute;
            width: 200px;
            height: 200px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            top: -100px;
            right: -100px;
        }

        .decorative-circle-2 {
            position: absolute;
            width: 150px;
            height: 150px;
            background: rgba(255,255,255,0.05);
            border-radius: 50%;
            bottom: -75px;
            left: -75px;
        }

        .perforation {
            position: absolute;
            right: 200px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: repeating-linear-gradient(
                to bottom,
                transparent 0px,
                transparent 8px,
                rgba(255,255,255,0.3) 8px,
                rgba(255,255,255,0.3) 12px
            );
        }

        .content {
            display: flex;
            height: 100%;
            gap: 30px;
            position: relative;
            z-index: 1;
        }

        .left-section {
            flex: 2;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .logo {
            background: white;
            padding: 8px 16px;
            border-radius: 8px;
            margin-right: 15px;
            color: #667eea;
            font-weight: bold;
            font-size: 16px;
        }

        .event-title {
            font-size: 28px;
            font-weight: bold;
            margin: 0 0 15px 0;
            line-height: 1.2;
        }

        .event-details {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 10px;
        }

        .event-details div {
            margin-bottom: 8px;
        }

        .attendee-info {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .right-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
        }

        .qr-container {
            background: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 15px;
        }

        .qr-code {
            width: 120px;
            height: 120px;
            display: block;
        }

        .security-footer {
            position: absolute;
            bottom: 10px;
            left: 30px;
            right: 30px;
            display: flex;
            justify-content: space-between;
            font-size: 8px;
            opacity: 0.6;
        }

        @media print {
            body {
                background: white;
            }
            .ticket {
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="ticket">
        <div class="decorative-circle-1"></div>
        <div class="decorative-circle-2"></div>
        <div class="perforation"></div>

        <div class="content">
            <div class="left-section">
                <div>
                    <div class="header">
                        <div class="logo">mTicket.my</div>
                        <span style="font-size: 14px; opacity: 0.9;">Event Ticket</span>
                    </div>
                </div>

                <div>
                    <h1 class="event-title">${ticket.event?.title || "Unknown Event"}</h1>
                    <div class="event-details">
                        <div>📅 ${eventDate}</div>
                        <div>🕐 ${eventTime}</div>
                        <div>📍 ${ticket.event?.location || "TBA"}</div>
                    </div>
                </div>

                <div class="attendee-info">
                    <div style="font-size: 14px; opacity: 0.8; margin-bottom: 5px;">Attendee</div>
                    <div style="font-size: 18px; font-weight: bold;">${ticket.attendee_name || "Unknown Attendee"}</div>
                    ${ticket.ic_reg ? `<div style="font-size: 12px; opacity: 0.7; margin-top: 2px;">IC/Reg: ${ticket.ic_reg}</div>` : ''}
                    <div style="font-size: 12px; opacity: 0.7; margin-top: 5px;">Ticket #REG${ticket.id.substring(0, 8).toUpperCase()}</div>
                </div>
            </div>

            <div class="right-section">
                <div class="qr-container">
                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=120x120&data=${encodeURIComponent(qrCodeUrl)}"
                         alt="QR Code"
                         class="qr-code" />
                </div>
                <div style="font-size: 12px; opacity: 0.8;">Scan at entrance</div>
                <div style="font-size: 10px; opacity: 0.6; margin-top: 5px;">Security verified</div>
            </div>
        </div>

        <div class="security-footer">
            <span>AUTHENTIC TICKET - mTicket.my</span>
            <span>Generated: ${new Date().toLocaleString()}</span>
        </div>
    </div>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 1000);
        }
    </script>
</body>
</html>
    `

    console.log("View PDF API: Successfully generated ticket HTML")
    return new Response(ticketHtml, {
      headers: {
        'Content-Type': 'text/html',
      },
    })

  } catch (error) {
    console.error("Error in view PDF API:", error)
    console.error("Error details:", {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    })
    return new Response("Internal server error", { status: 500 })
  }
}
