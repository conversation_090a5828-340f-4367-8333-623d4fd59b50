import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth"

/**
 * POST /api/organizations/link
 * Links a user to an existing organization and upgrades them to manager role
 * Requires authentication
 */
export async function POST(request: Request) {
  try {
    // Get JWT token from request
    const token = getJWTTokenFromRequest(request)

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Verify the token
    const authResult = await verifyJWTToken(token)
    if (!authResult || !authResult.user) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate required fields
    const { organizationId } = body

    if (!organizationId) {
      return NextResponse.json(
        { error: "Missing required field: organizationId" },
        { status: 400 }
      )
    }

    console.log("Organizations Link API: Linking user to organization", { organizationId, userId })

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin()

    // Check if organization exists
    const { data: existingOrg, error: checkError } = await supabaseAdmin
      .from("organizations")
      .select("id, name, ssm_number, pic_name, pic_email")
      .eq("id", organizationId)
      .single()

    if (checkError || !existingOrg) {
      console.error("Error checking organization:", checkError)
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 404 }
      )
    }

    // Get the manager role ID from user_roles table
    const { data: managerRole, error: roleError } = await supabaseAdmin
      .from("user_roles")
      .select("id")
      .eq("role_name", "manager")
      .single()

    if (roleError || !managerRole) {
      console.error("Error fetching manager role:", roleError)
      return NextResponse.json(
        { error: "Manager role not found in database" },
        { status: 500 }
      )
    }

    // Update user role to manager and link to organization
    const { error: userUpdateError } = await supabaseAdmin
      .from("users")
      .update({
        role_id: managerRole.id,
        organization_id: existingOrg.id,
      })
      .eq("id", userId)

    if (userUpdateError) {
      console.error("Error updating user role:", userUpdateError)
      return NextResponse.json(
        { error: "Failed to link user to organization" },
        { status: 500 }
      )
    }

    // Log activity
    await supabaseAdmin.from("activity_logs").insert([
      {
        user_id: userId,
        action: "link_organization",
        entity_type: "organization",
        entity_id: existingOrg.id,
        details: { organization_name: existingOrg.name },
      },
    ])

    console.log("Organizations Link API: Successfully linked user to organization", existingOrg.id)

    return NextResponse.json({
      success: true,
      organization: existingOrg,
      message: "Successfully linked to organization"
    })
  } catch (error: any) {
    console.error("Error in organizations link API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
