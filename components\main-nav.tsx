"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { LogIn, Menu, User, LayoutDashboard, CreditCard } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { useAuth } from "@/contexts/auth-context"

export default function MainNav() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const { user } = useAuth()

  return (
    <header className="w-full border-b bg-background">
      <div className="container flex h-16 items-center px-4 md:px-6">
        <Link href="/" className="flex items-center gap-2">
          <>
            <Image
              src="https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//logo.png"
              alt="mTicket.my logo"
              width={142}
              height={42}
              className="h-8 w-auto object-contain"
              priority
            />
            <span className="sr-only">mTicket.my</span>
          </>
        </Link>
        <nav className="ml-auto hidden gap-6 md:flex">
          <Link
            href="/"
            className="text-sm font-medium text-muted-foreground transition-colors hover:text-foreground"
          >
            Home
          </Link>
          <Link
            href="/events"
            className="text-sm font-medium text-muted-foreground transition-colors hover:text-foreground"
          >
            Events
          </Link>
          <Link
            href="/about"
            className="text-sm font-medium text-muted-foreground transition-colors hover:text-foreground"
          >
            About
          </Link>
          <Link
            href="/pricing"
            className="text-sm font-medium text-muted-foreground transition-colors hover:text-foreground"
          >
            Pricing
          </Link>
          <Link
            href="/contact"
            className="text-sm font-medium text-muted-foreground transition-colors hover:text-foreground"
          >
            Contact
          </Link>
        </nav>
        <div className="ml-auto md:ml-6 flex items-center gap-2">
          {user ? (
            <>
              <Link href="/dashboard">
                <Button variant="default" size="sm" className="hidden md:flex">
                  <LayoutDashboard className="mr-2 h-4 w-4" />
                  Dashboard
                </Button>
              </Link>
              {/* Mobile Dashboard Icon */}
              <Link href="/dashboard">
                <Button variant="ghost" size="icon" className="md:hidden">
                  <LayoutDashboard className="h-5 w-5" />
                  <span className="sr-only">Dashboard</span>
                </Button>
              </Link>
            </>
          ) : (
            <>
              <Link href="/auth/login">
                <Button variant="ghost" size="sm" className="hidden md:flex">
                  <LogIn className="mr-2 h-4 w-4" />
                  Login
                </Button>
              </Link>
              <Link href="/auth/register">
                <Button size="sm" className="hidden md:flex">
                  <User className="mr-2 h-4 w-4" />
                  Sign Up
                </Button>
              </Link>
              {/* Mobile Login Icon */}
              <Link href="/auth/login">
                <Button variant="ghost" size="icon" className="md:hidden">
                  <LogIn className="h-5 w-5" />
                  <span className="sr-only">Login</span>
                </Button>
              </Link>
            </>
          )}
          <Sheet open={isMenuOpen} onOpenChange={setIsMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <div className="flex flex-col space-y-4 pt-4">
                <Link href="/" className="text-sm font-medium" onClick={() => setIsMenuOpen(false)}>
                  Home
                </Link>
                <Link href="/events" className="text-sm font-medium" onClick={() => setIsMenuOpen(false)}>
                  Events
                </Link>
                <Link href="/about" className="text-sm font-medium" onClick={() => setIsMenuOpen(false)}>
                  About
                </Link>
                <Link href="/pricing" className="text-sm font-medium" onClick={() => setIsMenuOpen(false)}>
                  Pricing
                </Link>
                <Link href="/contact" className="text-sm font-medium" onClick={() => setIsMenuOpen(false)}>
                  Contact
                </Link>
                <div className="flex flex-col gap-2 pt-4">
                  {user ? (
                    <Link href="/dashboard" onClick={() => setIsMenuOpen(false)}>
                      <Button className="w-full">
                        <LayoutDashboard className="mr-2 h-4 w-4" />
                        Dashboard
                      </Button>
                    </Link>
                  ) : (
                    <>
                      <Link href="/auth/login" onClick={() => setIsMenuOpen(false)}>
                        <Button variant="outline" className="w-full">
                          <LogIn className="mr-2 h-4 w-4" />
                          Login
                        </Button>
                      </Link>
                      <Link href="/auth/register" onClick={() => setIsMenuOpen(false)}>
                        <Button className="w-full">
                          <User className="mr-2 h-4 w-4" />
                          Sign Up
                        </Button>
                      </Link>
                    </>
                  )}
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}
