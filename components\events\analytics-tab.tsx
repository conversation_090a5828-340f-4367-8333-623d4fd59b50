"use client"

import { DollarSign, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, TrendingUp } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { formatCurrency } from "@/lib/utils"

interface AnalyticsTabProps {
  analytics: any
}

export function AnalyticsTab({ analytics }: AnalyticsTabProps) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Revenue Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(analytics?.totalRevenue || 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              From {analytics?.paidRegistrations || 0} paid registrations
            </p>
          </CardContent>
        </Card>

        {/* Registrations Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Registrations</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.totalRegistrations || 0}</div>
            <p className="text-xs text-muted-foreground">
              {analytics?.conversionRate?.toFixed(1) || 0}% conversion rate
            </p>
          </CardContent>
        </Card>

        {/* Attendance Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Attendance Rate</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.attendanceRate?.toFixed(1) || 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              {analytics?.checkedInCount || 0} of {analytics?.totalRegistrations || 0} attended
            </p>
          </CardContent>
        </Card>

        {/* Payment Rate Card */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Payment Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.totalRegistrations > 0 ?
                `${((analytics?.paidRegistrations || 0) / analytics?.totalRegistrations * 100).toFixed(1)}%` :
                '0%'
              }
            </div>
            <p className="text-xs text-muted-foreground">
              {analytics?.paidRegistrations || 0} of {analytics?.totalRegistrations || 0} paid
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Registration Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Registration Timeline</CardTitle>
          <CardDescription>
            Registration activity over time
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center text-muted-foreground">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Registration timeline chart would be displayed here</p>
              <p className="text-sm">Integration with charting library needed</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
