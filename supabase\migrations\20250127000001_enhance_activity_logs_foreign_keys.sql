-- Enhanced Activity Logs with Proper Foreign Key Relationships
-- This migration adds specific foreign key columns for different entity types
-- to enable direct joins and better data integrity

DO $$
BEGIN
    -- Add specific foreign key columns for different entity types
    
    -- Event-related activities
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'event_id') THEN
        ALTER TABLE activity_logs ADD COLUMN event_id UUID REFERENCES events(id) ON DELETE SET NULL;
    END IF;
    
    -- Registration-related activities
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'registration_id') THEN
        ALTER TABLE activity_logs ADD COLUMN registration_id UUID REFERENCES registrations(id) ON DELETE SET NULL;
    END IF;
    
    -- Organization-related activities
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'organization_id') THEN
        ALTER TABLE activity_logs ADD COLUMN organization_id UUID REFERENCES organizations(id) ON DELETE SET NULL;
    END IF;
    
    -- Certificate-related activities
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'certificate_id') THEN
        ALTER TABLE activity_logs ADD COLUMN certificate_id UUID REFERENCES certificates(id) ON DELETE SET NULL;
    END IF;
    
    -- Payment/Transaction-related activities
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'payment_id') THEN
        ALTER TABLE activity_logs ADD COLUMN payment_id TEXT; -- External payment gateway ID
    END IF;
    
    -- Subscription-related activities
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'subscription_id') THEN
        ALTER TABLE activity_logs ADD COLUMN subscription_id UUID REFERENCES user_subscription(id) ON DELETE SET NULL;
    END IF;
    
    -- API Key-related activities
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'api_key_id') THEN
        ALTER TABLE activity_logs ADD COLUMN api_key_id UUID REFERENCES api_keys(id) ON DELETE SET NULL;
    END IF;
    
    -- Webhook-related activities
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'webhook_id') THEN
        ALTER TABLE activity_logs ADD COLUMN webhook_id UUID REFERENCES webhooks(id) ON DELETE SET NULL;
    END IF;
    
    -- Target user for admin actions (different from the user performing the action)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'target_user_id') THEN
        ALTER TABLE activity_logs ADD COLUMN target_user_id UUID REFERENCES users(id) ON DELETE SET NULL;
    END IF;
    
    -- Session tracking for authentication activities
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'session_id') THEN
        ALTER TABLE activity_logs ADD COLUMN session_id TEXT;
    END IF;

END $$;

-- Add indexes for the new foreign key columns
CREATE INDEX IF NOT EXISTS idx_activity_logs_event_id ON activity_logs(event_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_registration_id ON activity_logs(registration_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_organization_id ON activity_logs(organization_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_certificate_id ON activity_logs(certificate_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_subscription_id ON activity_logs(subscription_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_api_key_id ON activity_logs(api_key_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_webhook_id ON activity_logs(webhook_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_target_user_id ON activity_logs(target_user_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_payment_id ON activity_logs(payment_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_session_id ON activity_logs(session_id);

-- Create a composite index for common queries
CREATE INDEX IF NOT EXISTS idx_activity_logs_user_category_date ON activity_logs(user_id, category, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_activity_logs_entity_type_date ON activity_logs(entity_type, created_at DESC);

-- Add a function to automatically populate the specific foreign key columns based on entity_type and entity_id
CREATE OR REPLACE FUNCTION populate_activity_log_foreign_keys()
RETURNS TRIGGER AS $$
BEGIN
    -- Populate specific foreign key columns based on entity_type
    CASE NEW.entity_type
        WHEN 'event' THEN
            NEW.event_id := NEW.entity_id::UUID;
        WHEN 'registration' THEN
            NEW.registration_id := NEW.entity_id::UUID;
        WHEN 'organization' THEN
            NEW.organization_id := NEW.entity_id::UUID;
        WHEN 'certificate' THEN
            NEW.certificate_id := NEW.entity_id::UUID;
        WHEN 'subscription' THEN
            NEW.subscription_id := NEW.entity_id::UUID;
        WHEN 'api_key' THEN
            NEW.api_key_id := NEW.entity_id::UUID;
        WHEN 'webhook' THEN
            NEW.webhook_id := NEW.entity_id::UUID;
        WHEN 'user' THEN
            -- For user-related activities, set target_user_id if it's different from user_id
            IF NEW.entity_id::UUID != NEW.user_id THEN
                NEW.target_user_id := NEW.entity_id::UUID;
            END IF;
        WHEN 'payment' THEN
            NEW.payment_id := NEW.entity_id;
        ELSE
            -- For other entity types, keep entity_id as is
            NULL;
    END CASE;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically populate foreign keys
DROP TRIGGER IF EXISTS trigger_populate_activity_log_foreign_keys ON activity_logs;
CREATE TRIGGER trigger_populate_activity_log_foreign_keys
    BEFORE INSERT ON activity_logs
    FOR EACH ROW
    EXECUTE FUNCTION populate_activity_log_foreign_keys();

-- Update existing records to populate the new foreign key columns
-- This is safe to run multiple times
UPDATE activity_logs 
SET event_id = entity_id::UUID 
WHERE entity_type = 'event' 
  AND entity_id IS NOT NULL 
  AND entity_id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
  AND event_id IS NULL;

UPDATE activity_logs 
SET registration_id = entity_id::UUID 
WHERE entity_type = 'registration' 
  AND entity_id IS NOT NULL 
  AND entity_id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
  AND registration_id IS NULL;

UPDATE activity_logs 
SET organization_id = entity_id::UUID 
WHERE entity_type = 'organization' 
  AND entity_id IS NOT NULL 
  AND entity_id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
  AND organization_id IS NULL;

UPDATE activity_logs 
SET certificate_id = entity_id::UUID 
WHERE entity_type = 'certificate' 
  AND entity_id IS NOT NULL 
  AND entity_id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
  AND certificate_id IS NULL;

UPDATE activity_logs 
SET subscription_id = entity_id::UUID 
WHERE entity_type = 'subscription' 
  AND entity_id IS NOT NULL 
  AND entity_id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
  AND subscription_id IS NULL;

UPDATE activity_logs 
SET target_user_id = entity_id::UUID 
WHERE entity_type = 'user' 
  AND entity_id IS NOT NULL 
  AND entity_id ~ '^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$'
  AND entity_id::UUID != user_id
  AND target_user_id IS NULL;

UPDATE activity_logs 
SET payment_id = entity_id 
WHERE entity_type = 'payment' 
  AND entity_id IS NOT NULL 
  AND payment_id IS NULL;
