import { NextRequest, NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { getUserFromToken } from "@/lib/auth/token"

export async function GET(request: NextRequest) {
  try {
    // Get user from token
    const userToken = await getUserFromToken(request)
    if (!userToken) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin()

    // Get user details to check admin role
    const { data: user, error: userError } = await supabaseAdmin
      .from("users")
      .select(`
        *,
        role_info:role_id(id, role_name, description, permissions)
      `)
      .eq("id", userToken.userId)
      .single()

    if (userError || !user) {
      console.error("Error fetching user:", userError)
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Check if user has admin role (only "admin" role has full access)
    const userRole = user.role_name || user.role_info?.role_name || user.role
    if (userRole !== "admin") {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 403 }
      )
    }

    // Fetch attendance statistics
    const [
      eventsResult,
      registrationsResult,
      attendanceResult
    ] = await Promise.all([
      // Total events
      supabaseAdmin
        .from('events')
        .select('id, is_published'),

      // Total registrations and attendance
      supabaseAdmin
        .from('registrations')
        .select('id, status, event_id'),

      // Today's QR scans (mock for now)
      Promise.resolve({ data: [], error: null })
    ])

    if (eventsResult.error) {
      console.error("Error fetching events:", eventsResult.error)
      return NextResponse.json(
        { error: "Failed to fetch attendance statistics" },
        { status: 500 }
      )
    }

    if (registrationsResult.error) {
      console.error("Error fetching registrations:", registrationsResult.error)
      return NextResponse.json(
        { error: "Failed to fetch attendance statistics" },
        { status: 500 }
      )
    }

    // Calculate statistics
    const totalEvents = eventsResult.data?.length || 0
    const eventsWithTracking = eventsResult.data?.filter(event => event.is_published).length || 0
    const totalAttendees = registrationsResult.data?.length || 0
    const checkedInAttendees = registrationsResult.data?.filter(reg => reg.status === 'attended').length || 0

    // Mock data for QR scans and average check-in time
    const qrScansToday = 156
    const averageCheckInTime = "2.3 min"

    const stats = {
      totalEvents,
      eventsWithTracking,
      totalAttendees,
      checkedInAttendees,
      qrScansToday,
      averageCheckInTime
    }

    return NextResponse.json({ stats })
  } catch (error: any) {
    console.error("Error in attendance stats API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
