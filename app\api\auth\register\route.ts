import { NextResponse } from "next/server";
import { createUser } from "@/lib/auth";
import { isFeatureEnabled } from "@/lib/app-settings";
import { logActivity, ActivityCategory } from "@/utils/activity-logger";

export async function POST(request: Request) {
  try {
    // Check if registration is enabled
    const registrationEnabled = await isFeatureEnabled('register');
    if (!registrationEnabled) {
      return NextResponse.json(
        { error: "Registration is currently disabled" },
        { status: 403 }
      );
    }

    const { email, password, name } = await request.json();

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 }
      );
    }

    // Create the user
    const user = await createUser(email, password, name || "");

    // Log the registration activity
    try {
      await logActivity({
        userId: user.id,
        action: "register",
        entityType: "user",
        entityId: user.id,
        category: ActivityCategory.AUTH,
        details: {
          email: user.email,
          registration_method: "email",
          role: user.role || "user",
        },
      });
    } catch (logError) {
      // Don't fail registration if logging fails
      console.error("Error logging registration activity:", logError);
    }

    return NextResponse.json({ success: true, user: { id: user.id, email: user.email } });
  } catch (error: any) {
    console.error("Registration error:", error);
    return NextResponse.json(
      { error: error.message || "Registration failed" },
      { status: 500 }
    );
  }
}
