"use client"

import { <PERSON><PERSON>ir<PERSON>, Clock, XCircle, AlertCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface TimelineItem {
  id: string
  step: string
  title: string
  description: string
  timestamp: string
  status: "completed" | "pending" | "failed" | "processing"
  details?: Record<string, any>
}

interface TimelineProps {
  items: TimelineItem[]
  className?: string
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case "completed":
      return <CheckCircle className="h-5 w-5 text-green-500" />
    case "pending":
      return <Clock className="h-5 w-5 text-yellow-500" />
    case "failed":
      return <XCircle className="h-5 w-5 text-red-500" />
    case "processing":
      return <AlertCircle className="h-5 w-5 text-blue-500" />
    default:
      return <Clock className="h-5 w-5 text-gray-400" />
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "completed":
      return "border-green-500 bg-green-50"
    case "pending":
      return "border-yellow-500 bg-yellow-50"
    case "failed":
      return "border-red-500 bg-red-50"
    case "processing":
      return "border-blue-500 bg-blue-50"
    default:
      return "border-gray-300 bg-gray-50"
  }
}

const getStepIcon = (step: string) => {
  switch (step) {
    case "registration":
      return "📝"
    case "payment":
      return "💳"
    case "checkin":
      return "✅"
    case "certificate":
      return "🏆"
    default:
      return "📋"
  }
}

export function Timeline({ items, className }: TimelineProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {items.map((item, index) => (
        <div key={item.id} className="relative flex items-start space-x-4">
          {/* Timeline line */}
          {index < items.length - 1 && (
            <div className="absolute left-6 top-12 h-full w-0.5 bg-gray-200" />
          )}

          {/* Step icon */}
          <div className={cn(
            "flex h-12 w-12 items-center justify-center rounded-full border-2 text-lg",
            getStatusColor(item.status)
          )}>
            {getStepIcon(item.step)}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <h3 className="text-sm font-medium text-gray-900">{item.title}</h3>
                {getStatusIcon(item.status)}
              </div>
              <time className="text-xs text-gray-500">
                {new Date(item.timestamp).toLocaleString()}
              </time>
            </div>

            <p className="mt-1 text-sm text-gray-600">{item.description}</p>

            {/* Details */}
            {item.details && Object.keys(item.details).length > 0 && (
              <div className="mt-2 rounded-md bg-gray-50 p-3">
                <div className="grid grid-cols-1 gap-1 text-xs">
                  {Object.entries(item.details).map(([key, value]) => {
                    if (!value || key === "registration_id" || key === "certificate_id" || key === "transaction_id") return null

                    const displayKey = key.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase())
                    let displayValue = typeof value === "object" ? JSON.stringify(value) : String(value)

                    // Format specific fields
                    if (key === "amount" && typeof value === "number") {
                      displayValue = `RM${value.toFixed(2)}`
                    } else if (key.includes("date") || key.includes("time")) {
                      displayValue = new Date(value).toLocaleString()
                    }

                    return (
                      <div key={key} className="flex justify-between">
                        <span className="font-medium text-gray-600">{displayKey}:</span>
                        <span className="text-gray-900 break-all">{displayValue}</span>
                      </div>
                    )
                  })}
                </div>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  )
}
