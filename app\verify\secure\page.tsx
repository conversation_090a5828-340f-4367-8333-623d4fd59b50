"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { Shield, CheckCircle, XCircle, Clock, User, Calendar, MapPin } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"

export default function SecureVerifyPage() {
  const searchParams = useSearchParams()
  const [verificationResult, setVerificationResult] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const token = searchParams.get("token")

    if (!token) {
      setError("No verification token provided")
      setLoading(false)
      return
    }

    verifyToken(token)
  }, [searchParams])

  const verifyToken = async (token: string) => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(`/api/verify/secure?token=${encodeURIComponent(token)}`)
      const result = await response.json()

      if (response.ok) {
        setVerificationResult(result)
      } else {
        setError(result.error || "Verification failed")
      }
    } catch (err: any) {
      console.error("Error verifying token:", err)
      setError("Failed to verify token")
    } finally {
      setLoading(false)
    }
  }

  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleString()
  }

  const getTimeRemaining = (windowEnd: number) => {
    const now = Math.floor(Date.now() / 1000)
    const remaining = windowEnd - now
    return Math.max(0, remaining)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="flex flex-col items-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <p className="text-gray-600">Verifying secure ticket...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-600">
              <XCircle className="h-5 w-5" />
              <span>Verification Failed</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button
              onClick={() => window.location.reload()}
              variant="outline"
              className="w-full"
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  const { valid, reason, token: tokenData } = verificationResult

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className={`flex items-center space-x-2 ${valid ? 'text-green-600' : 'text-red-600'}`}>
            {valid ? (
              <>
                <CheckCircle className="h-5 w-5" />
                <span>Valid Ticket</span>
              </>
            ) : (
              <>
                <XCircle className="h-5 w-5" />
                <span>Invalid Ticket</span>
              </>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {valid ? (
            <>
              <div className="flex items-center justify-center mb-4">
                <Badge className="bg-green-100 text-green-800">
                  <Shield className="w-3 h-3 mr-1" />
                  Secure Verification
                </Badge>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Attendee:</span>
                  <span className="font-medium">{tokenData.attendeeName}</span>
                </div>

                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Event ID:</span>
                  <span className="font-mono text-sm">{tokenData.eventId}</span>
                </div>

                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Valid until:</span>
                  <span className="text-sm">{formatTimestamp(tokenData.windowEnd)}</span>
                </div>

                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Time remaining:</strong> {getTimeRemaining(tokenData.windowEnd)} seconds
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    This ticket expires automatically for security
                  </p>
                </div>
              </div>

              <div className="pt-4 border-t">
                <p className="text-xs text-gray-500 text-center">
                  This is a secure, time-based ticket that updates every 30 seconds.
                  Present this at the event entrance for check-in.
                </p>
              </div>
            </>
          ) : (
            <>
              <div className="flex items-center justify-center mb-4">
                <Badge variant="destructive">
                  <XCircle className="w-3 h-3 mr-1" />
                  Verification Failed
                </Badge>
              </div>

              <div className="bg-red-50 p-3 rounded-lg">
                <p className="text-sm text-red-800">
                  <strong>Reason:</strong> {reason}
                </p>
              </div>

              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  This ticket could not be verified. Common reasons include:
                </p>
                <ul className="text-xs text-gray-500 space-y-1 ml-4">
                  <li>• Token has expired (tickets expire every 30 seconds)</li>
                  <li>• Invalid or corrupted QR code</li>
                  <li>• Ticket has already been used</li>
                  <li>• System clock synchronization issues</li>
                </ul>
              </div>

              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                className="w-full"
              >
                Refresh Verification
              </Button>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
