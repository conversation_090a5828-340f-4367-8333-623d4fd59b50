import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth";
import { logActivity } from "@/lib/activity-logger";

export async function POST(request: Request) {
  try {
    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === "development";
    console.log("API payment-gateways/create - Is development mode:", isDevelopment);

    // Get JWT token from request
    const token = getJWTTokenFromRequest(request);
    console.log("API payment-gateways/create - Token present:", !!token);

    let currentUser = null;

    // In production, verify authentication and admin role
    // In development, allow access without authentication for easier testing
    if (!isDevelopment) {
      if (!token) {
        console.log("API payment-gateways/create - Access denied: No token provided");
        return NextResponse.json(
          { error: "Unauthorized. Admin access required." },
          { status: 403 }
        );
      }

      // Verify the JWT token and get user data
      const authResult = await verifyJWTToken(token);
      if (!authResult?.user) {
        console.log("API payment-gateways/create - Access denied: Invalid token");
        return NextResponse.json(
          { error: "Unauthorized. Invalid token." },
          { status: 403 }
        );
      }

      // Check if user has admin role (only "admin" role has full access)
      const userRole = authResult.user.role_name || authResult.user.role;

      if (userRole !== "admin") {
        console.log("API payment-gateways/create - Access denied: Not admin role. User role:", userRole);
        return NextResponse.json(
          { error: "Unauthorized. Admin access required." },
          { status: 403 }
        );
      }

      currentUser = authResult.user;
      console.log("API payment-gateways/create - Access granted to admin user:", authResult.user.email);
    } else {
      console.log("API payment-gateways/create - Development mode: Allowing access without authentication");
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin();
    const body = await request.json();

    // Validate required fields
    if (!body.gateway_name) {
      return NextResponse.json(
        { error: "Gateway name is required" },
        { status: 400 }
      );
    }

    // Get the highest display_order value
    const { data: maxOrderData, error: maxOrderError } = await supabaseAdmin
      .from("payment_gateway_settings")
      .select("display_order")
      .order("display_order", { ascending: false })
      .limit(1);

    if (maxOrderError) {
      console.error("Error getting max display order:", maxOrderError);
      return NextResponse.json(
        { error: "Failed to create payment gateway" },
        { status: 500 }
      );
    }

    const nextDisplayOrder = maxOrderData && maxOrderData.length > 0 ? maxOrderData[0].display_order + 1 : 1;

    // Create new payment gateway
    const newGateway = {
      gateway_name: body.gateway_name,
      is_enabled: body.is_enabled ?? false,
      is_test_mode: body.is_test_mode ?? true,
      configuration: body.configuration ?? {},
      test_configuration: body.test_configuration ?? {},
      live_configuration: body.live_configuration ?? {},
      display_order: nextDisplayOrder,
      created_by: currentUser?.id || null,
      updated_by: currentUser?.id || null,
    };

    const { data, error } = await supabaseAdmin
      .from("payment_gateway_settings")
      .insert([newGateway])
      .select();

    if (error) {
      console.error("Error creating payment gateway:", error);
      return NextResponse.json(
        { error: "Failed to create payment gateway" },
        { status: 500 }
      );
    }

    // Log activity
    if (currentUser?.id) {
      await logActivity({
        user_id: currentUser.id,
        action: "create",
        entity_type: "payment_gateway",
        entity_id: data[0].id,
        details: { gateway_name: data[0].gateway_name },
        category: "settings",
      });
    }

    return NextResponse.json(data[0], { status: 201 });
  } catch (error: any) {
    console.error("Error in payment gateways create API:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
