import { NextResponse } from "next/server"
import { supabase } from "@/lib/supabase"
import { triggerCertificateIssued } from "@/utils/webhook-trigger"
import { validateApiKeyMiddleware } from "@/app/api/middleware"

export async function POST(request: Request) {
  try {
    // Validate API key
    const apiKeyResponse = await validateApiKeyMiddleware(request);
    if (apiKeyResponse) {
      return apiKeyResponse; // Return the error response if API key is invalid
    }
    const { eventId, participantId, templateId, participantName, customFields } = await request.json()

    // Validate required fields
    if (!eventId || !participantId || !templateId || !participantName) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 })
    }

    // Check if event exists
    const { data: eventData, error: eventError } = await supabase.from("events").select("*").eq("id", eventId).single()

    if (eventError || !eventData) {
      return NextResponse.json({ error: "Event not found" }, { status: 404 })
    }

    // Check if participant exists and has attended
    const { data: registrationData, error: registrationError } = await supabase
      .from("registrations")
      .select("*")
      .eq("id", participantId)
      .eq("event_id", eventId)
      .single()

    if (registrationError || !registrationData) {
      return NextResponse.json({ error: "Registration not found" }, { status: 404 })
    }

    if (registrationData.status !== "attended") {
      return NextResponse.json({ error: "Participant has not attended the event" }, { status: 400 })
    }

    // Check if certificate already exists
    const { data: existingCertificate, error: certificateError } = await supabase
      .from("certificates")
      .select("*")
      .eq("registration_id", participantId)
      .eq("event_id", eventId)
      .single()

    if (existingCertificate) {
      return NextResponse.json(
        {
          message: "Certificate already exists",
          certificate: existingCertificate,
        },
        { status: 200 },
      )
    }

    // Generate verification code
    const verificationCode = Math.random().toString(36).substring(2, 10).toUpperCase()

    // In a real implementation, this would generate a PDF certificate
    // For demo purposes, we'll just create a record with a placeholder URL

    // Create certificate record
    const certificateData = {
      event_id: eventId,
      registration_id: participantId,
      template_id: templateId,
      participant_name: participantName,
      issued_at: new Date().toISOString(),
      certificate_url: `https://mtickets.com/certificates/${verificationCode}`,
      created_at: new Date().toISOString(),
    }

    const { data: newCertificate, error: insertError } = await supabase
      .from("certificates")
      .insert([certificateData])
      .select()
      .single()

    if (insertError) {
      return NextResponse.json({ error: "Failed to create certificate" }, { status: 500 })
    }

    // Log activity
    await supabase.from("activity_logs").insert([
      {
        event_id: eventId,
        action: "certificate_generated",
        details: {
          registration_id: participantId,
          certificate_id: newCertificate.id,
          template_id: templateId,
        },
        created_at: new Date().toISOString(),
      },
    ])

    // Trigger webhook event
    await triggerCertificateIssued({
      certificate_id: newCertificate.id,
      event_id: eventId,
      registration_id: participantId,
      participant_name: participantName,
      template_id: templateId,
      issued_at: newCertificate.issued_at,
      certificate_url: newCertificate.certificate_url,
    })

    return NextResponse.json(
      {
        message: "Certificate generated successfully",
        certificate: newCertificate,
      },
      { status: 201 },
    )
  } catch (error) {
    console.error("Error generating certificate:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
