import { NextRequest, NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { verifyUserAccess } from "@/lib/api-helpers/auth-verification"

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify user authentication
    const authResult = await verifyUserAccess(request)
    if (!authResult.isAuthenticated || !authResult.userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const templateId = params.id
    const body = await request.json()
    const {
      name,
      description,
      fields,
      html_template,
      css_styles,
      background_image_url,
      background_color,
      show_frame,
      orientation,
      is_shared
    } = body

    const supabaseAdmin = getSupabaseAdmin()

    // First, check if the template exists and user has permission to update it
    const { data: existingTemplate, error: fetchError } = await supabaseAdmin
      .from("certificate_templates")
      .select("*")
      .eq("id", templateId)
      .single()

    if (fetchError || !existingTemplate) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 }
      )
    }

    // Check permissions: user must own the template or be admin
    if (existingTemplate.created_by !== authResult.userId && !authResult.isAdmin) {
      return NextResponse.json(
        { error: "Permission denied" },
        { status: 403 }
      )
    }

    const updateData = {
      name,
      description: description || "",
      fields: fields || [],
      html_template: html_template || "",
      css_styles: css_styles || "",
      background_image_url: background_image_url || null,
      background_color: background_color || '#ffffff',
      show_frame: show_frame !== undefined ? show_frame : true,
      orientation: orientation || 'landscape',
      updated_at: new Date().toISOString(),
    }

    // Only allow updating is_shared if user owns the template
    if (existingTemplate.created_by === authResult.userId && is_shared !== undefined) {
      updateData.is_shared = is_shared
    }

    const { data, error } = await supabaseAdmin
      .from("certificate_templates")
      .update(updateData)
      .eq("id", templateId)
      .select()
      .single()

    if (error) {
      console.error("Error updating template:", error)
      return NextResponse.json(
        { error: "Failed to update template" },
        { status: 500 }
      )
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error("Error in PUT /api/certificate-templates/[id]:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify user authentication
    const authResult = await verifyUserAccess(request)
    if (!authResult.isAuthenticated || !authResult.userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const templateId = params.id
    const supabaseAdmin = getSupabaseAdmin()

    // First, check if the template exists and user has permission to delete it
    const { data: existingTemplate, error: fetchError } = await supabaseAdmin
      .from("certificate_templates")
      .select("*")
      .eq("id", templateId)
      .single()

    if (fetchError || !existingTemplate) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 }
      )
    }

    // Check permissions: user must own the template or be admin
    if (existingTemplate.created_by !== authResult.userId && !authResult.isAdmin) {
      return NextResponse.json(
        { error: "Permission denied" },
        { status: 403 }
      )
    }

    // Soft delete by setting is_active to false
    const { error } = await supabaseAdmin
      .from("certificate_templates")
      .update({ is_active: false })
      .eq("id", templateId)

    if (error) {
      console.error("Error deleting template:", error)
      return NextResponse.json(
        { error: "Failed to delete template" },
        { status: 500 }
      )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error in DELETE /api/certificate-templates/[id]:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
