-- Fix duplicate foreign key relationships between events and event_categories
-- Remove the old 'category' column foreign key constraint and keep only 'category_id'

-- Drop the foreign key constraint on the 'category' column
ALTER TABLE events DROP CONSTRAINT IF EXISTS events_category_fkey;

-- Update any remaining data in 'category' column to 'category_id' if needed
UPDATE events 
SET category_id = category 
WHERE category IS NOT NULL 
AND category_id IS NULL;

-- Drop the 'category' column entirely since we now use 'category_id'
ALTER TABLE events DROP COLUMN IF EXISTS category;

-- Ensure the category_id foreign key constraint exists
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'events_category_id_fkey' 
    AND table_name = 'events'
  ) THEN
    ALTER TABLE events ADD CONSTRAINT events_category_id_fkey 
    FOREIGN KEY (category_id) REFERENCES event_categories(id);
  END IF;
END $$;

-- Add comment to document the relationship
COMMENT ON COLUMN events.category_id IS 'Foreign key reference to event_categories.id';
