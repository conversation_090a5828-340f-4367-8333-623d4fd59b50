import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { verifyPayment } from "@/lib/payment-gateway";
import { logActivity } from "@/lib/activity-logger";

/**
 * POST /api/registrations/verify-payment
 * Verifies payment for a registration
 * Can be called by payment gateway webhooks or manually
 */
export async function POST(request: Request) {
  try {
    console.log("Registration Payment Verification API: Starting request");

    // Parse request body
    const body = await request.json();
    const {
      transaction_id,
      registration_id,
      status = 'paid',
      amount,
      currency = 'MYR',
      gateway_id,
      invoice_number,
      receipt_number,
      payment_method
    } = body;

    // Validate required fields
    if (!transaction_id && !registration_id) {
      return NextResponse.json(
        { error: "Transaction ID or Registration ID is required" },
        { status: 400 }
      );
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin();

    // Find the registration by transaction_id or registration_id
    let registration;
    if (transaction_id) {
      const { data: regData, error: regError } = await supabaseAdmin
        .from("registrations")
        .select(`
          *,
          event:event_id (
            id,
            title,
            price
          )
        `)
        .eq("payment_id", transaction_id)
        .single();

      if (regError || !regData) {
        console.error("Registration not found by transaction_id:", regError);
        return NextResponse.json(
          { error: "Registration not found" },
          { status: 404 }
        );
      }
      registration = regData;
    } else {
      const { data: regData, error: regError } = await supabaseAdmin
        .from("registrations")
        .select(`
          *,
          event:event_id (
            id,
            title,
            price
          )
        `)
        .eq("id", registration_id)
        .single();

      if (regError || !regData) {
        console.error("Registration not found by registration_id:", regError);
        return NextResponse.json(
          { error: "Registration not found" },
          { status: 404 }
        );
      }
      registration = regData;
    }

    // Check if payment is already verified
    if (registration.payment_status === 'paid') {
      return NextResponse.json({
        success: true,
        message: "Payment already verified",
        registration: {
          id: registration.id,
          payment_status: registration.payment_status,
          status: registration.status
        }
      });
    }

    // Verify payment with payment gateway (if transaction_id is provided)
    let isPaymentVerified = true;
    if (transaction_id) {
      try {
        isPaymentVerified = await verifyPayment(transaction_id);
      } catch (error) {
        console.error("Payment verification failed:", error);
        isPaymentVerified = false;
      }
    }

    if (!isPaymentVerified) {
      // Log failed verification
      await logActivity({
        action: "payment_verification_failed",
        user_id: registration.user_id,
        details: {
          registration_id: registration.id,
          transaction_id: transaction_id,
          reason: "Payment gateway verification failed"
        },
      });

      return NextResponse.json(
        {
          success: false,
          error: "Payment verification failed",
        },
        { status: 400 }
      );
    }

    // Update or create transaction record
    if (registration.transaction_id) {
      // Update existing transaction
      const transactionUpdateData = {
        status: 'paid',
        processed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      // Update gateway_transaction_id if transaction_id is provided
      if (transaction_id) {
        transactionUpdateData.gateway_transaction_id = transaction_id;
      }

      // Add additional fields if provided
      if (amount !== undefined) transactionUpdateData.amount = amount;
      if (currency) transactionUpdateData.currency = currency;
      if (gateway_id) transactionUpdateData.gateway_id = gateway_id;
      if (invoice_number) transactionUpdateData.invoice_number = invoice_number;
      if (receipt_number) transactionUpdateData.receipt_number = receipt_number;
      if (payment_method) {
        transactionUpdateData.gateway_response = {
          ...transactionUpdateData.gateway_response || {},
          payment_method: payment_method
        };
      }

      const { error: transactionUpdateError } = await supabaseAdmin
        .from("transactions")
        .update(transactionUpdateData)
        .eq("id", registration.transaction_id);

      if (transactionUpdateError) {
        console.error("Error updating transaction:", transactionUpdateError);
        // Don't fail the payment verification if transaction update fails
      }
    } else {
      // Create new transaction record
      const transactionData = {
        id: crypto.randomUUID(),
        registration_id: registration.id,
        gateway_transaction_id: transaction_id || `TXN-${Date.now()}`,
        amount: amount || registration.payment_amount || 0,
        currency: currency,
        status: 'paid',
        gateway_id: gateway_id,
        invoice_number: invoice_number,
        receipt_number: receipt_number,
        gateway_response: { payment_method: payment_method || 'Online Payment' },
        processed_at: new Date().toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { error: transactionCreateError } = await supabaseAdmin
        .from("transactions")
        .insert([transactionData]);

      if (transactionCreateError) {
        console.error("Error creating transaction:", transactionCreateError);
        // Don't fail the payment verification if transaction creation fails
      }
    }

    // Update registration to confirmed
    const updateData = {
      payment_status: 'paid',
      status: 'confirmed',
      payment_date: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Add payment amount if provided
    if (amount !== undefined) {
      updateData.payment_amount = amount;
    }

    const { error: updateError } = await supabaseAdmin
      .from("registrations")
      .update(updateData)
      .eq("id", registration.id);

    if (updateError) {
      console.error("Error updating registration:", updateError);
      return NextResponse.json(
        { error: "Failed to update registration" },
        { status: 500 }
      );
    }

    // Log successful payment
    await logActivity({
      action: "payment_confirmed",
      user_id: registration.user_id,
      details: {
        registration_id: registration.id,
        transaction_id: transaction_id,
        amount: registration.payment_amount,
        event_id: registration.event_id,
        event_title: registration.event?.title
      },
    });

    // Log webhook trigger (simplified for now)
    try {
      console.log("Payment succeeded webhook would be triggered here:", {
        registration_id: registration.id,
        transaction_id: transaction_id,
        amount: registration.payment_amount,
        currency: 'MYR',
        event: {
          id: registration.event?.id,
          title: registration.event?.title
        },
        attendee: {
          name: registration.attendee_name,
          email: registration.attendee_email
        },
        payment_date: new Date().toISOString()
      });
    } catch (webhookError) {
      console.error("Error with webhook logging:", webhookError);
      // Don't fail the payment verification if webhook fails
    }

    console.log("Registration Payment Verification API: Payment verified successfully");

    return NextResponse.json({
      success: true,
      message: "Payment verified and registration confirmed",
      registration: {
        id: registration.id,
        payment_status: 'paid',
        status: 'confirmed',
        payment_date: updateData.payment_date
      },
      payment_details: {
        transaction_id: transaction_id,
        invoice_number: invoice_number,
        receipt_number: receipt_number,
        amount: amount,
        currency: currency,
        payment_method: payment_method
      }
    });

  } catch (error) {
    console.error("Registration Payment Verification API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/registrations/verify-payment
 * Check payment status for a registration
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const registrationId = searchParams.get('registration_id');
    const transactionId = searchParams.get('transaction_id');

    if (!registrationId && !transactionId) {
      return NextResponse.json(
        { error: "Registration ID or Transaction ID is required" },
        { status: 400 }
      );
    }

    const supabaseAdmin = getSupabaseAdmin();

    // Find the registration
    const query = registrationId
      ? supabaseAdmin.from("registrations").select("*").eq("id", registrationId)
      : supabaseAdmin.from("registrations").select("*").eq("payment_id", transactionId);

    const { data: registration, error } = await query.single();

    if (error || !registration) {
      return NextResponse.json(
        { error: "Registration not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      registration: {
        id: registration.id,
        payment_status: registration.payment_status,
        status: registration.status,
        payment_date: registration.payment_date
      }
    });

  } catch (error) {
    console.error("Registration Payment Status API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
