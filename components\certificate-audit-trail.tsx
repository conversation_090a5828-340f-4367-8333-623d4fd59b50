"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Loader2, CheckCircle, Clock, XCircle, User, Calendar, CreditCard, Award, Eye, Download, MapPin, Users, FileText } from "lucide-react"

interface AuditTrailEntry {
  id: string
  created_at: string
  action: string
  category: string
  timeline_step: string
  step_order: number
  performed_by: string
  performed_by_email: string | null
  details: Record<string, any>
}

interface AuditTrailSummary {
  total_activities: number
  registration_date: string
  certificate_issued_date: string
  participant_name: string
  event_title: string
  payment_status: string
  timeline_steps: {
    registration: boolean
    payment_initiated: boolean
    payment_completed: boolean
    attendance_marked: boolean
    certificate_generated: boolean
    certificate_downloaded: boolean
  }
}

interface CertificateAuditTrailProps {
  certificateId: string
}

export function CertificateAuditTrail({ certificateId }: CertificateAuditTrailProps) {
  const [auditTrail, setAuditTrail] = useState<AuditTrailEntry[]>([])
  const [summary, setSummary] = useState<AuditTrailSummary | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchAuditTrail()
  }, [certificateId])

  const fetchAuditTrail = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch(`/api/certificates/${certificateId}/audit-trail`)

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Certificate not found or no audit trail available')
        }
        throw new Error('Failed to fetch audit trail')
      }

      const data = await response.json()

      if (data.success && data.audit_trail) {
        setAuditTrail(data.audit_trail)
        setSummary(data.summary)
      } else {
        throw new Error('No audit trail data available')
      }
    } catch (err) {
      console.error('Error fetching audit trail:', err)
      setError(err instanceof Error ? err.message : 'An error occurred while fetching audit trail')
    } finally {
      setLoading(false)
    }
  }

  const getStepIcon = (step: string) => {
    if (step.includes('Registration')) return <User className="h-5 w-5" />
    if (step.includes('Payment')) return <CreditCard className="h-5 w-5" />
    if (step.includes('Attendance')) return <Users className="h-5 w-5" />
    if (step.includes('Certificate Generated')) return <Award className="h-5 w-5" />
    if (step.includes('Certificate Downloaded')) return <Download className="h-5 w-5" />
    if (step.includes('Certificate Verified')) return <Eye className="h-5 w-5" />
    return <Clock className="h-5 w-5" />
  }

  const getStepColor = (step: string) => {
    if (step.includes('Registration')) return 'bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg shadow-blue-500/25'
    if (step.includes('Payment Initiated')) return 'bg-gradient-to-br from-amber-500 to-orange-500 shadow-lg shadow-amber-500/25'
    if (step.includes('Payment Completed')) return 'bg-gradient-to-br from-emerald-500 to-green-600 shadow-lg shadow-emerald-500/25'
    if (step.includes('Payment Failed')) return 'bg-gradient-to-br from-red-500 to-red-600 shadow-lg shadow-red-500/25'
    if (step.includes('Attendance')) return 'bg-gradient-to-br from-purple-500 to-purple-600 shadow-lg shadow-purple-500/25'
    if (step.includes('Certificate Generated')) return 'bg-gradient-to-br from-emerald-500 to-teal-600 shadow-lg shadow-emerald-500/25'
    if (step.includes('Certificate Downloaded')) return 'bg-gradient-to-br from-cyan-500 to-blue-500 shadow-lg shadow-cyan-500/25'
    if (step.includes('Certificate Verified')) return 'bg-gradient-to-br from-indigo-500 to-purple-600 shadow-lg shadow-indigo-500/25'
    return 'bg-gradient-to-br from-gray-400 to-gray-500 shadow-lg shadow-gray-400/25'
  }

  const getProgressStepIcon = (stepKey: string) => {
    switch (stepKey) {
      case 'registration': return <User className="h-4 w-4" />
      case 'payment_completed': return <CreditCard className="h-4 w-4" />
      case 'attendance_marked': return <Users className="h-4 w-4" />
      case 'certificate_generated': return <Award className="h-4 w-4" />
      case 'certificate_downloaded': return <Download className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const getProgressStepLabel = (stepKey: string) => {
    switch (stepKey) {
      case 'registration': return 'Registration'
      case 'payment_completed': return 'Payment'
      case 'attendance_marked': return 'Attendance'
      case 'certificate_generated': return 'Certificate'
      case 'certificate_downloaded': return 'Downloaded'
      default: return stepKey.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
    }
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-MY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Kuala_Lumpur'
    })
  }

  if (loading) {
    return (
      <Card className="border-0 shadow-lg">
        <CardContent className="flex flex-col items-center justify-center p-12">
          <div className="relative">
            <div className="w-16 h-16 border-4 border-blue-200 rounded-full animate-pulse"></div>
            <Loader2 className="absolute inset-0 w-16 h-16 text-blue-600 animate-spin" />
          </div>
          <div className="mt-6 text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Audit Trail</h3>
            <p className="text-gray-600">Fetching complete certificate journey...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="border-0 shadow-lg">
        <CardContent className="p-12">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Unable to Load Audit Trail</h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">{error}</p>
            <Button
              onClick={fetchAuditTrail}
              variant="outline"
              className="bg-white hover:bg-gray-50"
            >
              <Clock className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!loading && (!auditTrail || auditTrail.length === 0)) {
    return (
      <Card className="border-0 shadow-lg">
        <CardContent className="p-12">
          <div className="text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Clock className="h-8 w-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Audit Trail Available</h3>
            <p className="text-gray-600 mb-2">This certificate doesn't have activity logging data</p>
            <p className="text-sm text-gray-500 max-w-md mx-auto">
              This may occur if the certificate was created before the audit system was implemented,
              or if activity logging was not enabled for this event.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-8">
      {/* Summary Card */}
      {summary && (
        <Card className="border-0 shadow-lg bg-gradient-to-br from-slate-50 to-white">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-semibold text-gray-900">Certificate Journey Overview</CardTitle>
            <CardDescription className="text-base text-gray-600">
              Complete audit trail for <span className="font-medium text-gray-900">{summary.participant_name}</span> - <span className="font-medium text-gray-900">{summary.event_title}</span>
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white rounded-xl p-4 border border-gray-100 shadow-sm">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-600">{summary.total_activities}</div>
                    <div className="text-sm text-gray-600">Total Activities</div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl p-4 border border-gray-100 shadow-sm">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Calendar className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">Registration</div>
                    <div className="text-xs text-gray-600">
                      {formatDateTime(summary.registration_date)}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl p-4 border border-gray-100 shadow-sm">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Award className="h-5 w-5 text-purple-600" />
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">Certificate Issued</div>
                    <div className="text-xs text-gray-600">
                      {formatDateTime(summary.certificate_issued_date)}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl p-4 border border-gray-100 shadow-sm">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-emerald-100 rounded-lg">
                    <CreditCard className="h-5 w-5 text-emerald-600" />
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">Payment Status</div>
                    <Badge
                      variant={summary.payment_status === 'confirmed' ? 'default' : 'secondary'}
                      className="mt-1"
                    >
                      {summary.payment_status}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Enhanced Progress Steps */}
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-gray-900">Progress Timeline</h4>
              <div className="relative">
                {/* Progress line */}
                <div className="absolute top-6 left-6 right-6 h-0.5 bg-gray-200"></div>
                <div
                  className="absolute top-6 left-6 h-0.5 bg-gradient-to-r from-blue-500 to-emerald-500 transition-all duration-500"
                  style={{
                    width: `${(Object.values(summary.timeline_steps).filter(Boolean).length / Object.keys(summary.timeline_steps).length) * 100}%`
                  }}
                ></div>

                {/* Progress steps */}
                <div className="relative flex flex-wrap justify-between gap-4 sm:gap-0">
                  {Object.entries(summary.timeline_steps).map(([step, completed], index) => (
                    <div key={step} className="flex flex-col items-center group min-w-0 flex-1">
                      <div className={`relative z-10 w-10 h-10 sm:w-12 sm:h-12 rounded-full flex items-center justify-center transition-all duration-300 ${
                        completed
                          ? 'bg-gradient-to-br from-emerald-500 to-green-600 text-white shadow-lg shadow-emerald-500/25 scale-110'
                          : 'bg-white border-2 border-gray-300 text-gray-400 group-hover:border-gray-400'
                      }`}>
                        {completed ? (
                          <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5" />
                        ) : (
                          getProgressStepIcon(step)
                        )}
                      </div>
                      <div className="mt-2 sm:mt-3 text-center px-1">
                        <div className={`text-xs sm:text-sm font-medium ${completed ? 'text-gray-900' : 'text-gray-500'}`}>
                          {getProgressStepLabel(step)}
                        </div>
                        {completed && (
                          <div className="text-xs text-emerald-600 font-medium mt-1 hidden sm:block">
                            ✓ Completed
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Enhanced Timeline */}
      <Card className="border-0 shadow-lg">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <Clock className="h-5 w-5 text-blue-600" />
            Detailed Activity Timeline
          </CardTitle>
          <CardDescription className="text-base text-gray-600">
            Chronological record of all activities with complete audit trail
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative space-y-6 sm:space-y-8">
            {/* Timeline line */}
            <div className="absolute left-6 sm:left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-200 via-purple-200 to-emerald-200"></div>

            {auditTrail.map((entry, index) => (
              <div key={entry.id} className="relative group">
                {/* Timeline dot */}
                <div className={`absolute left-0 sm:left-2 w-12 h-12 sm:w-14 sm:h-14 rounded-full flex items-center justify-center ${getStepColor(entry.timeline_step)} text-white transition-all duration-300 group-hover:scale-110`}>
                  {getStepIcon(entry.timeline_step)}
                </div>

                {/* Content Card */}
                <div className="ml-16 sm:ml-20 bg-white rounded-xl border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 group-hover:border-gray-200">
                  <div className="p-6">
                    {/* Header */}
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
                      <h4 className="text-lg font-semibold text-gray-900 mb-2 sm:mb-0">
                        {entry.timeline_step}
                      </h4>
                      <div className="flex items-center gap-2">
                        <div className="p-1.5 bg-gray-100 rounded-lg">
                          <Calendar className="h-4 w-4 text-gray-600" />
                        </div>
                        <time className="text-sm font-medium text-gray-600">
                          {formatDateTime(entry.created_at)}
                        </time>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="space-y-3">
                      <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                        <div className="flex items-center gap-2">
                          <div className="p-1.5 bg-blue-100 rounded-lg">
                            <FileText className="h-4 w-4 text-blue-600" />
                          </div>
                          <span className="text-sm text-gray-600">Action:</span>
                        </div>
                        <code className="bg-gray-100 px-2 py-1 rounded-md text-sm font-mono text-gray-800 break-all">
                          {entry.action}
                        </code>
                      </div>

                      <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                        <div className="flex items-center gap-2">
                          <div className="p-1.5 bg-green-100 rounded-lg">
                            <User className="h-4 w-4 text-green-600" />
                          </div>
                          <span className="text-sm text-gray-600">Performed by:</span>
                        </div>
                        <span className="text-sm font-medium text-gray-900 break-words">
                          {entry.performed_by}
                        </span>
                      </div>

                      {/* Details */}
                      {Object.keys(entry.details).length > 0 && (
                        <details className="group/details">
                          <summary className="cursor-pointer text-blue-600 hover:text-blue-800 text-sm font-medium flex items-center gap-2 transition-colors">
                            <div className="p-1.5 bg-blue-100 rounded-lg group-open/details:bg-blue-200 transition-colors">
                              <Eye className="h-4 w-4 text-blue-600" />
                            </div>
                            View Technical Details
                          </summary>
                          <div className="mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200">
                            <pre className="text-xs text-gray-700 overflow-x-auto whitespace-pre-wrap font-mono">
                              {JSON.stringify(entry.details, null, 2)}
                            </pre>
                          </div>
                        </details>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
