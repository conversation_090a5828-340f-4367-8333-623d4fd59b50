"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { useAuth } from "@/contexts/auth-context"
import { ProfileTab, OrganizationTab, SubscriptionTab, NotificationsTab, WebhookTab } from "@/components/profile"

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState("profile")
  const [profileData, setProfileData] = useState({
    fullName: "",
    email: "",
    phone: "",
    bio: "",
    profileImage: "",
  })
  const [organizationData, setOrganizationData] = useState({
    name: "",
    ssmNumber: "",
    picName: "",
    picPhone: "",
    picEmail: "",
    address: "",
    website: "",
  })
  const [selectedOrganization, setSelectedOrganization] = useState<any>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isLoadingOrganization, setIsLoadingOrganization] = useState(false)

  const { user } = useAuth()

  // Helper function to get cookie value
  const getCookie = (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  // Fetch user's existing organization data
  const fetchUserOrganization = async () => {
    if (!user) return

    setIsLoadingOrganization(true)
    try {
      const token = getCookie('auth_token')
      if (!token) return

      const response = await fetch("/api/organizations/user", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      if (response.ok) {
        const data = await response.json()
        console.log("Profile page: Fetched organization data:", data)

        if (data.organization) {
          // User has a proper organization assigned
          console.log("Profile page: Setting organization data from database")
          setOrganizationData({
            name: data.organization.name || "",
            ssmNumber: data.organization.ssm_number || "",
            picName: data.organization.pic_name || "",
            picPhone: data.organization.pic_phone || "",
            picEmail: data.organization.pic_email || "",
            address: data.organization.address || "",
            website: data.organization.website || "",
          })
          // Also set the selected organization object
          setSelectedOrganization(data.organization)
        } else if (data.legacy_organization_name) {
          // User only has legacy organization name
          console.log("Profile page: Setting legacy organization name")
          setOrganizationData({
            name: data.legacy_organization_name,
            ssmNumber: "",
            picName: "",
            picPhone: "",
            picEmail: "",
            address: "",
            website: "",
          })
        } else {
          console.log("Profile page: No organization data found")
        }
      } else {
        console.error("Failed to fetch user organization")
      }
    } catch (error) {
      console.error("Error fetching user organization:", error)
    } finally {
      setIsLoadingOrganization(false)
    }
  }

  useEffect(() => {
    if (user) {
      // Set profile data from user
      setProfileData({
        fullName: user.full_name || "",
        email: user.email || "",
        phone: user.phone || "",
        bio: user.bio || "",
        profileImage: user.profile_image_url || "",
      })

      // Fetch organization data from database
      fetchUserOrganization()
    }
  }, [user])




  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold">My Profile</h1>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="organization">Organization</TabsTrigger>
          <TabsTrigger value="subscription">Subscription</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="webhooks">API & Webhooks</TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <ProfileTab
            profileData={profileData}
            setProfileData={setProfileData}
            isSubmitting={isSubmitting}
            setIsSubmitting={setIsSubmitting}
          />
        </TabsContent>

        <TabsContent value="organization">
          <OrganizationTab
            organizationData={organizationData}
            setOrganizationData={setOrganizationData}
            isSubmitting={isSubmitting}
            setIsSubmitting={setIsSubmitting}
            isLoadingOrganization={isLoadingOrganization}
            initialSelectedOrganization={selectedOrganization}
          />
        </TabsContent>

        <TabsContent value="subscription">
          <SubscriptionTab />
        </TabsContent>

        <TabsContent value="notifications">
          <NotificationsTab />
        </TabsContent>

        <TabsContent value="webhooks">
          <WebhookTab />
        </TabsContent>
      </Tabs>
    </div>
  )
}
