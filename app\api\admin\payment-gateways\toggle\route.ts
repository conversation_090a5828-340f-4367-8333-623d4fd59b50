import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth";
import { logActivity } from "@/lib/activity-logger";

export async function POST(request: Request) {
  try {
    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === "development";
    console.log("API payment-gateways/toggle - Is development mode:", isDevelopment);

    // Get JWT token from request
    const token = getJWTTokenFromRequest(request);
    console.log("API payment-gateways/toggle - Token present:", !!token);

    // Initialize session variable
    let session: any = null;

    // In production, verify authentication and admin/manager role
    // In development, allow access without authentication for easier testing
    if (!isDevelopment) {
      if (!token) {
        console.log("API payment-gateways/toggle - Access denied: No token provided");
        return NextResponse.json(
          { error: "Unauthorized. Admin or manager access required." },
          { status: 403 }
        );
      }

      // Verify the JW<PERSON> token and get user data
      const authResult = await verifyJWTToken(token);
      if (!authResult?.user) {
        console.log("API payment-gateways/toggle - Access denied: Invalid token");
        return NextResponse.json(
          { error: "Unauthorized. Invalid token." },
          { status: 403 }
        );
      }

      // Check if user has admin or manager role (admin has full access, others have elevated permissions)
      const userRole = authResult.user.role_name || authResult.user.role;
      const allowedRoles = ["admin", "manager", "super_admin", "supermanager", "event_admin"];

      if (!allowedRoles.includes(userRole)) {
        console.log("API payment-gateways/toggle - Access denied: Not admin/manager role. User role:", userRole);
        return NextResponse.json(
          { error: "Unauthorized. Admin or manager access required." },
          { status: 403 }
        );
      }

      // Set session for authenticated user
      session = { user: authResult.user };
      console.log("API payment-gateways/toggle - Access granted to admin/manager user:", authResult.user.email);
    } else {
      console.log("API payment-gateways/toggle - Development mode: Allowing access without authentication");
    }

    // Log access in development mode
    if (isDevelopment) {
      console.log("Development mode: Allowing access to toggle payment gateway without authentication");
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin();
    const body = await request.json();

    // Validate required fields
    if (!body.id) {
      return NextResponse.json(
        { error: "Payment gateway ID is required" },
        { status: 400 }
      );
    }

    // Get current gateway state
    const { data: gateway, error: getError } = await supabaseAdmin
      .from("payment_gateway_settings")
      .select("is_enabled, gateway_name")
      .eq("id", body.id)
      .single();

    if (getError) {
      console.error("Error getting payment gateway:", getError);
      return NextResponse.json(
        { error: "Failed to toggle payment gateway" },
        { status: 500 }
      );
    }

    if (!gateway) {
      return NextResponse.json(
        { error: "Payment gateway not found" },
        { status: 404 }
      );
    }

    // Toggle the enabled state
    const newEnabledState = !gateway.is_enabled;

    // Update payment gateway
    const { data, error } = await supabaseAdmin
      .from("payment_gateway_settings")
      .update({
        is_enabled: newEnabledState,
        updated_by: session?.user?.id || null,
        updated_at: new Date().toISOString(),
      })
      .eq("id", body.id)
      .select();

    if (error) {
      console.error("Error toggling payment gateway:", error);
      return NextResponse.json(
        { error: "Failed to toggle payment gateway" },
        { status: 500 }
      );
    }

    // Log activity
    if (session?.user?.id) {
      await logActivity({
        user_id: session.user.id,
        action: newEnabledState ? "enable" : "disable",
        entity_type: "payment_gateway",
        entity_id: body.id,
        details: {
          gateway_name: gateway.gateway_name,
          is_enabled: newEnabledState
        },
        category: "settings",
      });
    }

    return NextResponse.json(data[0]);
  } catch (error: any) {
    console.error("Error in payment gateways toggle API:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
