'use server'

import { Role } from '@/lib/db/supabase-schema'
import { getSupabaseAdmin } from '@/lib/supabase'

// Fetch all roles from the database
export async function fetchRoles(): Promise<{ data: Role[] | null; error: any }> {
  try {
    console.log('Fetching roles from user_roles table...')

    // Get the Supabase admin client with direct env var access
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    console.log('Environment variables in server action:')
    console.log('- NEXT_PUBLIC_SUPABASE_URL available:', !!supabaseUrl)
    console.log('- SUPABASE_SERVICE_ROLE_KEY available:', !!supabaseServiceKey)

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing required Supabase environment variables in server action')
    }

    // Use the imported function which now creates a fresh client each time
    const supabaseAdmin = getSupabaseAdmin()
    console.log('Supabase admin client initialized successfully')

    // Perform the actual query
    console.log('Executing query to fetch roles...')
    const { data, error } = await supabaseAdmin
      .from('user_roles')
      .select('*')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching roles:', error)
      return { data: null, error }
    }

    console.log('Roles fetched successfully:', data?.length || 0, 'roles found')
    if (data && data.length > 0) {
      console.log('First role:', JSON.stringify(data[0]))
    }

    return { data, error: null }
  } catch (error) {
    console.error('Exception fetching roles:', error)
    return { data: null, error }
  }
}

// Create a new role
export async function createRole(role: Partial<Role>): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Creating new role:', role.role_name)

    // Get the Supabase admin client with direct env var access
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing required Supabase environment variables in server action')
    }

    const supabaseAdmin = getSupabaseAdmin()

    // Check if role name already exists
    console.log('Checking if role name already exists...')
    const { data: existingRoles, error: checkError } = await supabaseAdmin
      .from('user_roles')
      .select('id')
      .eq('role_name', role.role_name!)

    if (checkError) {
      console.error('Error checking existing roles:', checkError)
      return { success: false, error: checkError }
    }

    if (existingRoles && existingRoles.length > 0) {
      console.log('Role with this name already exists')
      return {
        success: false,
        error: { message: 'A role with this name already exists' }
      }
    }

    console.log('Inserting new role into database...')
    const { error } = await supabaseAdmin.from('user_roles').insert({
      role_name: role.role_name,
      description: role.description,
      permissions: role.permissions || {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })

    if (error) {
      console.error('Error inserting role:', error)
      return { success: false, error }
    }

    console.log('Role created successfully')
    return { success: true, error: null }
  } catch (error) {
    console.error('Exception creating role:', error)
    return { success: false, error }
  }
}

// Update an existing role
export async function updateRole(role: Role): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Updating role:', role.id, role.role_name)

    // Get the Supabase admin client with direct env var access
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing required Supabase environment variables in server action')
    }

    const supabaseAdmin = getSupabaseAdmin()

    const { error } = await supabaseAdmin
      .from('user_roles')
      .update({
        role_name: role.role_name,
        description: role.description,
        permissions: role.permissions,
        updated_at: new Date().toISOString(),
      })
      .eq('id', role.id)

    if (error) {
      console.error('Error updating role:', error)
      return { success: false, error }
    }

    console.log('Role updated successfully')
    return { success: true, error: null }
  } catch (error) {
    console.error('Exception updating role:', error)
    return { success: false, error }
  }
}

// Delete a role
export async function deleteRole(roleId: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Deleting role with ID:', roleId)

    // Get the Supabase admin client with direct env var access
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!supabaseUrl || !supabaseServiceKey) {
      throw new Error('Missing required Supabase environment variables in server action')
    }

    const supabaseAdmin = getSupabaseAdmin()

    // Check if any users are using this role
    console.log('Checking if any users are using this role...')
    const { data: usersWithRole, error: checkError } = await supabaseAdmin
      .from('users')
      .select('id')
      .eq('role_id', roleId)

    if (checkError) {
      console.error('Error checking users with role:', checkError)
      return { success: false, error: checkError }
    }

    if (usersWithRole && usersWithRole.length > 0) {
      console.log(`Cannot delete role: ${usersWithRole.length} users are currently assigned to this role`)
      return {
        success: false,
        error: {
          message: `Cannot delete role: ${usersWithRole.length} users are currently assigned to this role`
        }
      }
    }

    console.log('Deleting role from database...')
    const { error } = await supabaseAdmin
      .from('user_roles')
      .delete()
      .eq('id', roleId)

    if (error) {
      console.error('Error deleting role:', error)
      return { success: false, error }
    }

    console.log('Role deleted successfully')
    return { success: true, error: null }
  } catch (error) {
    console.error('Exception deleting role:', error)
    return { success: false, error }
  }
}
