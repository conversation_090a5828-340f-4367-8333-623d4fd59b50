import { NextResponse } from "next/server"
import { parseSecureQRData, verifySecureTicketToken } from "@/lib/security-token"
import { getSupabaseAdmin } from "@/lib/supabase"
import { logActivity, ActivityCategory } from "@/utils/activity-logger"

const supabaseAdmin = getSupabaseAdmin()

/**
 * POST /api/verify/secure
 * Verify a secure ticket token and mark attendance
 * Used by QR scanner for secure ticket verification
 */
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { token: tokenString, eventId } = body

    if (!tokenString) {
      return NextResponse.json(
        { error: "Token is required" },
        { status: 400 }
      )
    }

    if (!eventId) {
      return NextResponse.json(
        { error: "Event ID is required" },
        { status: 400 }
      )
    }

    // Parse the secure token
    const secureToken = parseSecureQRData(tokenString)
    if (!secureToken) {
      return NextResponse.json(
        { error: "Invalid token format" },
        { status: 400 }
      )
    }

    // Verify the token signature and timing
    const verification = verifySecureTicketToken(secureToken)
    if (!verification.valid) {
      return NextResponse.json(
        {
          error: "Token verification failed",
          reason: verification.reason
        },
        { status: 400 }
      )
    }

    // Verify the event ID matches
    if (secureToken.eventId !== eventId) {
      return NextResponse.json(
        { error: "Token is not valid for this event" },
        { status: 400 }
      )
    }

    // Get the registration from database
    const { data: registration, error: regError } = await supabaseAdmin
      .from("registrations")
      .select(`
        *,
        events (
          id,
          title,
          start_date,
          end_date
        )
      `)
      .eq("id", secureToken.ticketId)
      .eq("event_id", eventId)
      .single()

    if (regError || !registration) {
      return NextResponse.json(
        { error: "Registration not found or invalid" },
        { status: 404 }
      )
    }

    // Verify attendee name matches (basic security check)
    if (registration.attendee_name !== secureToken.attendeeName) {
      return NextResponse.json(
        { error: "Attendee name mismatch" },
        { status: 400 }
      )
    }

    // Check if already attended
    if (registration.status === "attended") {
      return NextResponse.json(
        {
          error: "Ticket already used",
          registration: {
            id: registration.id,
            guest_name: registration.attendee_name,
            status: registration.status,
            event: registration.events
          }
        },
        { status: 409 }
      )
    }

    // Check if registration is confirmed
    if (registration.status !== "confirmed") {
      return NextResponse.json(
        {
          error: `Registration status is ${registration.status}. Only confirmed registrations can be checked in.`,
          registration: {
            id: registration.id,
            guest_name: registration.attendee_name,
            status: registration.status,
            event: registration.events
          }
        },
        { status: 400 }
      )
    }

    // Check if already checked in
    if (registration.checked_in) {
      return NextResponse.json(
        {
          error: "Attendee has already been checked in",
          registration: {
            id: registration.id,
            guest_name: registration.attendee_name,
            status: registration.status,
            checked_in_at: registration.checked_in_at,
            event: registration.events
          }
        },
        { status: 400 }
      )
    }

    // Mark attendance
    const { error: updateError } = await supabaseAdmin
      .from("registrations")
      .update({
        status: "attended",
        checked_in: true,
        checked_in_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq("id", secureToken.ticketId)

    if (updateError) {
      console.error("Error updating attendance:", updateError)
      return NextResponse.json(
        { error: "Failed to mark attendance" },
        { status: 500 }
      )
    }

    // Log the attendance activity
    try {
      await logActivity({
        userId: registration.user_id || null,
        action: "attendance_marked",
        entityType: "registration",
        entityId: registration.id,
        category: ActivityCategory.EVENT,
        details: {
          event_id: eventId,
          event_title: registration.events?.title,
          attendee_name: registration.attendee_name,
          attendee_email: registration.attendee_email,
          verification_method: "secure_qr",
          token_window_start: secureToken.windowStart,
          token_window_end: secureToken.windowEnd,
        },
      })
    } catch (logError) {
      console.error("Error logging attendance activity:", logError)
      // Don't fail the attendance marking if logging fails
    }

    return NextResponse.json({
      success: true,
      message: "Attendance marked successfully",
      registration: {
        id: registration.id,
        guest_name: registration.attendee_name,
        guest_email: registration.attendee_email,
        status: "attended",
        event: registration.events,
        checked_in_at: new Date().toISOString(),
      },
      security: {
        verification_method: "secure_qr",
        token_valid: true,
        window_start: secureToken.windowStart,
        window_end: secureToken.windowEnd,
      }
    })

  } catch (error: any) {
    console.error("Error in secure verification:", error)
    return NextResponse.json(
      { error: error.message || "Verification failed" },
      { status: 500 }
    )
  }
}

/**
 * GET /api/verify/secure
 * Verify a secure token from QR code URL (for direct scanning)
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const token = searchParams.get("token")

    if (!token) {
      return NextResponse.json(
        { error: "Token parameter is required" },
        { status: 400 }
      )
    }

    // Reconstruct the full QR data URL
    const url = new URL(request.url)
    const qrData = `${url.protocol}//${url.host}${url.pathname}?token=${token}`

    // Parse and verify the token
    const secureToken = parseSecureQRData(qrData)
    if (!secureToken) {
      return NextResponse.json(
        { error: "Invalid token format" },
        { status: 400 }
      )
    }

    const verification = verifySecureTicketToken(secureToken)

    return NextResponse.json({
      valid: verification.valid,
      reason: verification.reason,
      token: verification.valid ? {
        ticketId: secureToken.ticketId,
        eventId: secureToken.eventId,
        attendeeName: secureToken.attendeeName,
        windowStart: secureToken.windowStart,
        windowEnd: secureToken.windowEnd,
      } : null,
    })

  } catch (error: any) {
    console.error("Error in secure token verification:", error)
    return NextResponse.json(
      { error: error.message || "Verification failed" },
      { status: 500 }
    )
  }
}
