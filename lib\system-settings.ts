/**
 * Utility functions for working with system settings
 */

import { SystemSettings } from './db/supabase-schema';
import { fetchSingleFromSupabase } from './supabase-fetch';
import { supabase } from './supabase';

/**
 * Fetch system settings from the database
 * @returns Promise with the system settings
 */
export async function getSystemSettings(): Promise<SystemSettings> {
  try {
    // First try using the Supabase client
    const { data, error } = await supabase
      .from('system_settings')
      .select('*')
      .single();

    if (error) {
      console.error('Error fetching system settings with Supabase client:', error);
      
      // Fall back to using the REST API
      console.log('Falling back to REST API for system settings');
      return await fetchSingleFromSupabase<SystemSettings>('system_settings');
    }

    return data;
  } catch (error) {
    console.error('Exception fetching system settings:', error);
    throw error;
  }
}

/**
 * Update system settings in the database
 * @param settings Partial system settings to update
 * @returns Promise with the updated system settings
 */
export async function updateSystemSettings(
  settings: Partial<SystemSettings>
): Promise<SystemSettings> {
  try {
    const { data, error } = await supabase
      .from('system_settings')
      .update({
        ...settings,
        updated_at: new Date().toISOString(),
      })
      .select('*')
      .single();

    if (error) {
      console.error('Error updating system settings:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Exception updating system settings:', error);
    throw error;
  }
}
