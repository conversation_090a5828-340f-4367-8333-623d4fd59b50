import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth";

export async function GET(request: Request) {
  try {
    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === "development";
    console.log("API payment-gateways/list - Is development mode:", isDevelopment);

    // Get JWT token from request
    const token = getJWTTokenFromRequest(request);
    console.log("API payment-gateways/list - Token present:", !!token);

    // In production, verify authentication and admin/manager role
    // In development, allow access without authentication for easier testing
    if (!isDevelopment) {
      if (!token) {
        console.log("API payment-gateways/list - Access denied: No token provided");
        return NextResponse.json(
          { error: "Unauthorized. Admin or manager access required." },
          { status: 403 }
        );
      }

      // Verify the JWT token and get user data
      const authResult = await verifyJ<PERSON><PERSON>oken(token);
      if (!authResult?.user) {
        console.log("API payment-gateways/list - Access denied: Invalid token");
        return NextResponse.json(
          { error: "Unauthorized. Invalid token." },
          { status: 403 }
        );
      }

      // Check if user has admin or manager role (admin has full access, others have elevated permissions)
      const userRole = authResult.user.role_name || authResult.user.role;
      const allowedRoles = ["admin", "manager", "super_admin", "supermanager", "event_admin"];

      if (!allowedRoles.includes(userRole)) {
        console.log("API payment-gateways/list - Access denied: Not admin/manager role. User role:", userRole);
        return NextResponse.json(
          { error: "Unauthorized. Admin or manager access required." },
          { status: 403 }
        );
      }

      console.log("API payment-gateways/list - Access granted to admin/manager user:", authResult.user.email);
    } else {
      console.log("API payment-gateways/list - Development mode: Allowing access without authentication");
    }

    // Log access in development mode
    if (isDevelopment) {
      console.log("Development mode: Allowing access to payment gateways list without authentication");
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin();

    // Fetch all payment gateways
    const { data, error } = await supabaseAdmin
      .from("payment_gateway_settings")
      .select("*")
      .order("display_order", { ascending: true });

    if (error) {
      console.error("Error fetching payment gateways:", error);
      return NextResponse.json(
        { error: "Failed to fetch payment gateways" },
        { status: 500 }
      );
    }

    return NextResponse.json({ payment_gateways: data });
  } catch (error: any) {
    console.error("Error in payment gateways list API:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
