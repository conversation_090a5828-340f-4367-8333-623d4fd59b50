import { NextRequest, NextResponse } from "next/server"
import { isFeatureEnabled } from "@/lib/app-settings"

/**
 * POST /api/app-settings/check-feature
 * Check if a specific feature is enabled
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { feature } = body

    if (!feature) {
      return NextResponse.json(
        { error: "Feature parameter is required" },
        { status: 400 }
      )
    }

    // Validate feature type
    const validFeatures = ['login', 'register', 'password_reset', 'api']
    if (!validFeatures.includes(feature)) {
      return NextResponse.json(
        { error: "Invalid feature type" },
        { status: 400 }
      )
    }

    // Check if feature is enabled
    const enabled = await isFeatureEnabled(feature as 'login' | 'register' | 'password_reset' | 'api')

    return NextResponse.json({ 
      enabled,
      feature 
    })
  } catch (error: any) {
    console.error("Error checking feature status:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
