"use client"

import React, { useState, useEffect } from "react"
import { usePathname } from "next/navigation"
import Image from "next/image"
import { useAuth } from "@/contexts/auth-context"
import { DashboardSidebar } from "@/components/dashboard/sidebar"
import { Button } from "@/components/ui/button"
import Link from "next/link"
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb"
import { useBreadcrumbs } from "@/lib/breadcrumbs"

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default function DashboardLayout({ children }: DashboardLayoutProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const pathname = usePathname()
  const { user, loading } = useAuth()

  // Check if mobile on mount and on window resize
  useEffect(() => {
    const checkIfMobile = () => {
      const isMobileView = window.innerWidth < 768
      setIsMobile(isMobileView)

      // Auto-collapse sidebar on mobile
      if (isMobileView) {
        setIsCollapsed(true)
      } else {
        setIsCollapsed(false)
      }
    }

    // Initial check
    checkIfMobile()

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile)

    // Cleanup
    return () => {
      window.removeEventListener('resize', checkIfMobile)
    }
  }, [])

  // Toggle sidebar
  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed)
  }

  // Show loading state while auth is loading
  if (loading) {
    return (
      <div className="flex min-h-screen bg-gray-50 items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen bg-gray-50">
      {/* Sidebar container - sticky for better scroll behavior */}
      <div
        className={`fixed md:sticky top-0 left-0 h-screen z-30 transform transition-all duration-300 ease-in-out ${
          isCollapsed ? '-translate-x-full md:translate-x-0 md:w-16' : 'translate-x-0 w-64'
        }`}
      >
        <DashboardSidebar
          isCollapsed={isCollapsed}
          toggleSidebar={toggleSidebar}
          isMobile={isMobile}
        />
      </div>

      {/* Mobile overlay */}
      {!isCollapsed && isMobile && (
        <div
          className="fixed inset-0 z-20 bg-black/50 transition-opacity"
          onClick={() => setIsCollapsed(true)}
          aria-hidden="true"
        />
      )}

      {/* Main content */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* Mobile header - sticky at top */}
        <header className="sticky top-0 z-10 flex h-16 items-center border-b bg-white px-4 md:hidden">
          <Button
            variant="ghost"
            size="icon"
            className="mr-2"
            onClick={toggleSidebar}
            aria-label="Toggle menu"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-5 w-5"
            >
              <line x1="4" x2="20" y1="12" y2="12" />
              <line x1="4" x2="20" y1="6" y2="6" />
              <line x1="4" x2="20" y1="18" y2="18" />
            </svg>
          </Button>
          <Link href="/dashboard" className="flex items-center">
            <Image
              src="https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//logo.png"
              alt="mTicket.my logo"
              width={142}
              height={42}
              className="h-8 w-auto object-contain"
              priority
            />
            <span className="sr-only">mTicket.my</span>
          </Link>
        </header>

        {/* Page content - scrollable area */}
        <main className="flex-1 overflow-y-auto">
          <div className="mx-auto w-full max-w-7xl p-4 md:p-6">
            <DashboardBreadcrumbs />
            <div className="mt-4">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

function DashboardBreadcrumbs() {
  const breadcrumbs = useBreadcrumbs()
  const pathname = usePathname()

  // Don't show breadcrumbs on dashboard home
  if (pathname === '/dashboard') {
    return null
  }

  if (breadcrumbs.length === 0) {
    return null
  }

  return (
    <Breadcrumb className="mb-4">
      <BreadcrumbList>
        {breadcrumbs.map((breadcrumb, index) => (
          <React.Fragment key={breadcrumb.href}>
            <BreadcrumbItem>
              {breadcrumb.isCurrent ? (
                <BreadcrumbPage className="text-sm">
                  {breadcrumb.label}
                </BreadcrumbPage>
              ) : (
                <BreadcrumbLink
                  href={breadcrumb.href}
                  className="text-sm hover:underline"
                >
                  {breadcrumb.label}
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
            {index < breadcrumbs.length - 1 && (
              <BreadcrumbSeparator />
            )}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  )
}
