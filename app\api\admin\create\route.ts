import { NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

// Create a Supabase client with the service role key for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || "",
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ""
)

export async function POST(request: Request) {
  try {
    const { email, password, fullName, adminKey } = await request.json()

    // Verify admin key
    if (adminKey !== process.env.ADMIN_CREATION_KEY) {
      return NextResponse.json({ error: "Invalid admin key" }, { status: 401 })
    }

    // Create user in Auth
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true,
      user_metadata: {
        full_name: fullName,
        role: "admin",
      },
    })

    if (authError) {
      return NextResponse.json({ error: authError.message }, { status: 400 })
    }

    // Create user record in users table with admin role
    // Using service role key bypasses RLS
    const { error: userError } = await supabaseAdmin.from("users").insert([
      {
        id: authData.user.id,
        email,
        full_name: fullName,
        role: "admin",
        subscription_status: "active",
        created_at: new Date().toISOString(),
        organization: null,
        profile_image_url: null,
        events_created: 0,
        total_earnings: 0,
        available_balance: 0,
      },
    ])

    if (userError) {
      return NextResponse.json({ error: userError.message }, { status: 400 })
    }

    return NextResponse.json({ success: true, message: "Admin user created successfully" })
  } catch (error: any) {
    return NextResponse.json({ error: error.message || "Failed to create admin user" }, { status: 500 })
  }
}
