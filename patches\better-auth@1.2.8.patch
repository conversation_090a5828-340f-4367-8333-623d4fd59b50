diff --git a/node_modules/.pnpm/better-auth@1.2.8/node_modules/better-auth/dist/client/react/index.mjs b/node_modules/.pnpm/better-auth@1.2.8/node_modules/better-auth/dist/client/react/index.mjs
index 1234567..7654321 100644
--- a/node_modules/.pnpm/better-auth@1.2.8/node_modules/better-auth/dist/client/react/index.mjs
+++ b/node_modules/.pnpm/better-auth@1.2.8/node_modules/better-auth/dist/client/react/index.mjs
@@ -1,5 +1,5 @@
 import { createClient } from '../core/index.mjs';
-import { useRef } from 'react';
+import React from 'react';
 import { useSyncExternalStore } from 'use-sync-external-store/shim';
 
 // Fix for React 18.3.1 compatibility
@@ -10,7 +10,7 @@ function createAuthClient(options) {
   const useSession = (options2) => {
     const client2 = client;
     const store = client2.session.store;
-    const optionsRef = useRef(options2);
+    const optionsRef = React.useRef(options2);
     const subscribe = (callback) => {
       return store.subscribe(callback);
     };
