import Link from "next/link"
import { ArrowRight, Sparkles, Users } from "lucide-react"

export function CTASection() {
  return (
    <section className="py-12 bg-gradient-to-b from-gray-50 to-white">
      <div className="container px-4 md:px-6">
        <div className="mx-auto max-w-7xl">
          {/* Enhanced CTA Card */}
          <div className="relative overflow-hidden bg-gradient-to-br from-purple-600 via-purple-700 to-indigo-800 rounded-3xl shadow-2xl">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div
                className="absolute top-0 left-0 w-full h-full"
                style={{
                  backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                }}
              ></div>
            </div>

            {/* Decorative Elements */}
            <div className="absolute top-6 right-6 text-white/20">
              <Sparkles className="h-8 w-8" />
            </div>
            <div className="absolute bottom-6 left-6 text-white/20">
              <Users className="h-8 w-8" />
            </div>

            <div className="relative p-6 md:p-8 lg:p-10">
              <div className="flex flex-col lg:flex-row items-center justify-between gap-6">
                {/* Content Section */}
                <div className="text-center lg:text-left lg:flex-1">
                  <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-4">
                    <Sparkles className="h-4 w-4 text-yellow-300" />
                    <span className="text-sm font-medium text-white">Trusted Platform</span>
                  </div>

                  <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-3 leading-tight">
                    Ready to create your
                    <span className="block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                      first event?
                    </span>
                  </h2>

                  <p className="text-purple-100 text-sm md:text-base mb-1 max-w-2xl">
                    Start your event management journey today with Malaysia's most trusted platform.
                  </p>

                  <p className="text-purple-200/80 text-xs md:text-sm">
                    Join thousands of successful organizers
                  </p>
                </div>

                {/* Buttons Section */}
                <div className="flex flex-col sm:flex-row gap-3 lg:shrink-0">
                  <Link
                    href="/auth/register"
                    className="group inline-flex items-center justify-center rounded-xl bg-white text-purple-700 hover:bg-gray-50 px-6 py-3 text-base font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  >
                    Get Started Free
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Link>

                  <Link
                    href="/contact"
                    className="group inline-flex items-center justify-center rounded-xl border-2 border-white/30 bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 hover:border-white/50 px-6 py-3 text-base font-semibold transition-all duration-200"
                  >
                    Schedule Demo
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
