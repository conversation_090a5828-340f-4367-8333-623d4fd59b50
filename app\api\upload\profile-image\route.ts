import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import jwt from "jsonwebtoken";

export async function POST(request: Request) {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get("Authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Authorization token required" },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7); // Remove "Bearer " prefix

    // Verify JWT token
    let decoded: any;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET || "your-secret-key");
    } catch (jwtError) {
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 401 }
      );
    }

    // Get the form data
    const formData = await request.formData();
    const file = formData.get("file") as File;
    const oldImageUrl = formData.get("oldImageUrl") as string;

    if (!file) {
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.type.startsWith("image/")) {
      return NextResponse.json(
        { error: "File must be an image" },
        { status: 400 }
      );
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { error: "File size must be less than 5MB" },
        { status: 400 }
      );
    }

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin();

    // Generate unique filename
    const fileExt = file.name.split(".").pop();
    const fileName = `${decoded.userId}-${Math.random().toString(36).substring(2)}.${fileExt}`;
    const filePath = `profile-images/${fileName}`;

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = new Uint8Array(arrayBuffer);

    // Upload to Supabase storage
    const { error: uploadError, data } = await supabaseAdmin.storage
      .from("avatars")
      .upload(filePath, buffer, {
        contentType: file.type,
        upsert: true,
      });

    if (uploadError) {
      console.error("Upload error:", uploadError);
      return NextResponse.json(
        { error: "Failed to upload image" },
        { status: 500 }
      );
    }

    // Get public URL
    const { data: urlData } = supabaseAdmin.storage
      .from("avatars")
      .getPublicUrl(filePath);

    // Delete old image if it exists and is different from the new one
    if (oldImageUrl && oldImageUrl !== urlData.publicUrl) {
      try {
        // Extract the file path from the old URL
        // URL format: https://[project].supabase.co/storage/v1/object/public/avatars/profile-images/filename
        const urlParts = oldImageUrl.split('/storage/v1/object/public/avatars/');
        if (urlParts.length === 2) {
          const oldFilePath = urlParts[1];

          console.log(`Attempting to delete old profile image: ${oldFilePath}`);

          const { error: deleteError } = await supabaseAdmin.storage
            .from("avatars")
            .remove([oldFilePath]);

          if (deleteError) {
            console.error("Error deleting old profile image:", deleteError);
            // Don't fail the upload if deletion fails, just log it
          } else {
            console.log(`Successfully deleted old profile image: ${oldFilePath}`);
          }
        } else {
          console.warn("Could not parse old image URL for deletion:", oldImageUrl);
        }
      } catch (deleteError) {
        console.error("Error during old image cleanup:", deleteError);
        // Don't fail the upload if cleanup fails
      }
    }

    return NextResponse.json({
      success: true,
      url: urlData.publicUrl,
      path: filePath,
    });

  } catch (error: any) {
    console.error("Upload profile image error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
