"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>hart, Bar, XAxis, <PERSON><PERSON><PERSON>s, ResponsiveContainer } from "recharts"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { cn } from "@/lib/utils"

type TimeRange = '7days' | '30days'

interface MiniChartProps {
  title: string
  data: any
  type: 'line' | 'bar'
  height?: number
  className?: string
  timeRange: TimeRange
  onTimeRangeChange: (range: TimeRange) => void
  loading?: boolean
}

export function MiniChart({
  title,
  data,
  type,
  height = 200,
  className = '',
  timeRange,
  onTimeRangeChange,
  loading = false
}: MiniChartProps) {
  if (!data || loading) {
    return (
      <div className={cn("flex flex-col", className)}>
        <div className="flex justify-between items-center mb-3">
          <div className="text-sm font-medium text-muted-foreground">{title}</div>
          <div className="h-6 w-20 bg-muted rounded-md animate-pulse" />
        </div>
        <Skeleton className="w-full" style={{ height: `${height}px` }} />
      </div>
    )
  }

  // Convert Chart.js format to Recharts format
  const chartData = data?.labels?.map((label: string, index: number) => ({
    name: label,
    value: data.datasets?.[0]?.data?.[index] || 0,
  })) || []

  const chartConfig = {
    value: {
      label: data?.datasets?.[0]?.label || "Value",
      color: type === 'line' ? "hsl(var(--primary))" : "hsl(var(--primary))",
    },
  }

  return (
    <div className={cn("flex flex-col", className)}>
      <div className="flex justify-between items-center mb-3">
        <div className="text-sm font-medium text-muted-foreground">{title}</div>
        <div className="inline-flex items-center rounded-md border border-input bg-background p-0.5">
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              'h-6 px-2 text-xs',
              timeRange === '7days' && 'bg-accent text-accent-foreground'
            )}
            onClick={() => onTimeRangeChange('7days')}
          >
            7d
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className={cn(
              'h-6 px-2 text-xs',
              timeRange === '30days' && 'bg-accent text-accent-foreground'
            )}
            onClick={() => onTimeRangeChange('30days')}
          >
            30d
          </Button>
        </div>
      </div>
      <ChartContainer config={chartConfig} className="w-full" style={{ height: `${height}px` }}>
        {type === 'line' ? (
          <LineChart data={chartData}>
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 10 }}
              interval="preserveStartEnd"
            />
            <YAxis hide />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Line
              type="monotone"
              dataKey="value"
              stroke="var(--color-value)"
              strokeWidth={2}
              dot={false}
            />
          </LineChart>
        ) : (
          <BarChart data={chartData}>
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 10 }}
              interval="preserveStartEnd"
            />
            <YAxis hide />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Bar
              dataKey="value"
              fill="var(--color-value)"
              radius={[2, 2, 0, 0]}
            />
          </BarChart>
        )}
      </ChartContainer>
    </div>
  )
}
