-- Update certificates table schema to match API expectations
-- This migration adds missing columns and updates existing data

DO $$ 
BEGIN
  -- Add unique_code column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificates' AND column_name = 'unique_code') THEN
    ALTER TABLE certificates ADD COLUMN unique_code TEXT UNIQUE;
  END IF;

  -- Add recipient_name column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificates' AND column_name = 'recipient_name') THEN
    ALTER TABLE certificates ADD COLUMN recipient_name TEXT;
  END IF;

  -- Add event_title column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificates' AND column_name = 'event_title') THEN
    ALTER TABLE certificates ADD COLUMN event_title TEXT;
  END IF;

  -- Add generated_at column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificates' AND column_name = 'generated_at') THEN
    ALTER TABLE certificates ADD COLUMN generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
  END IF;

  -- Add file_path column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificates' AND column_name = 'file_path') THEN
    ALTER TABLE certificates ADD COLUMN file_path TEXT;
  END IF;

  -- Add verification_url column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificates' AND column_name = 'verification_url') THEN
    ALTER TABLE certificates ADD COLUMN verification_url TEXT;
  END IF;

  -- Add qr_code_data column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificates' AND column_name = 'qr_code_data') THEN
    ALTER TABLE certificates ADD COLUMN qr_code_data TEXT;
  END IF;
END $$;

-- Update existing certificates to populate new columns
UPDATE certificates 
SET 
  unique_code = COALESCE(verification_code, UPPER(SUBSTRING(id::text, 1, 8))),
  recipient_name = COALESCE(participant_name, 'Unknown Participant'),
  generated_at = COALESCE(issue_date, created_at, NOW())
WHERE unique_code IS NULL OR recipient_name IS NULL OR generated_at IS NULL;

-- Update event_title from events table
UPDATE certificates 
SET event_title = e.title
FROM events e
WHERE certificates.event_id = e.id AND certificates.event_title IS NULL;

-- Set verification_url for existing certificates
UPDATE certificates 
SET verification_url = CONCAT('https://mticket.my/certificates/verify/', unique_code)
WHERE verification_url IS NULL AND unique_code IS NOT NULL;

-- Set qr_code_data for existing certificates
UPDATE certificates 
SET qr_code_data = verification_url
WHERE qr_code_data IS NULL AND verification_url IS NOT NULL;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_certificates_unique_code ON certificates(unique_code);
CREATE INDEX IF NOT EXISTS idx_certificates_recipient_name ON certificates(recipient_name);
CREATE INDEX IF NOT EXISTS idx_certificates_event_title ON certificates(event_title);
CREATE INDEX IF NOT EXISTS idx_certificates_generated_at ON certificates(generated_at);

-- Add constraints
ALTER TABLE certificates 
ADD CONSTRAINT certificates_unique_code_not_null 
CHECK (unique_code IS NOT NULL);

ALTER TABLE certificates 
ADD CONSTRAINT certificates_recipient_name_not_null 
CHECK (recipient_name IS NOT NULL);
