"use client"

import * as React from "react"
import { Check, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"

export interface StepperStep {
  id: string
  title: string
  description?: string
  icon?: React.ReactNode
  status: "completed" | "current" | "upcoming"
  clickable?: boolean
}

interface StepperProps {
  steps: StepperStep[]
  currentStep: number
  onStepClick?: (stepIndex: number) => void
  className?: string
  orientation?: "horizontal" | "vertical"
  showConnector?: boolean
}

const Stepper = React.forwardRef<HTMLDivElement, StepperProps>(
  ({
    steps,
    currentStep,
    onStepClick,
    className,
    orientation = "horizontal",
    showConnector = true,
    ...props
  }, ref) => {
    const isHorizontal = orientation === "horizontal"

    return (
      <div
        ref={ref}
        className={cn(
          "flex",
          isHorizontal ? "flex-row items-center" : "flex-col",
          className
        )}
        {...props}
      >
        {steps.map((step, index) => {
          const isCompleted = step.status === "completed"
          const isCurrent = step.status === "current"
          const isClickable = step.clickable && onStepClick && (isCompleted || index <= currentStep)

          return (
            <React.Fragment key={step.id}>
              {/* Step Item */}
              <div
                className={cn(
                  "flex items-center",
                  isHorizontal ? "flex-col text-center" : "flex-row",
                  isClickable && "cursor-pointer group",
                  !isHorizontal && "w-full"
                )}
                onClick={isClickable ? () => onStepClick(index) : undefined}
              >
                {/* Step Circle */}
                <div
                  className={cn(
                    "relative flex items-center justify-center rounded-full border-2 transition-all duration-300 shadow-lg",
                    isHorizontal ? "h-10 w-10 sm:h-12 sm:w-12 mb-3" : "h-10 w-10 mr-4 flex-shrink-0",
                    isCompleted && "border-green-400 bg-gradient-to-br from-green-400 to-emerald-500 text-white shadow-lg shadow-green-400/30",
                    isCurrent && "border-purple-400 bg-gradient-to-br from-purple-400 to-pink-500 text-white shadow-lg shadow-purple-400/30 scale-110",
                    !isCompleted && !isCurrent && "border-white/30 bg-white/10 text-white/60 shadow-sm backdrop-blur-sm",
                    isClickable && "group-hover:border-purple-300 group-hover:shadow-lg group-hover:scale-105 cursor-pointer group-hover:bg-white/20"
                  )}
                >
                  {isCompleted ? (
                    <Check className="h-4 w-4" />
                  ) : step.icon ? (
                    <div className="h-4 w-4 flex items-center justify-center">
                      {step.icon}
                    </div>
                  ) : (
                    <span className="text-sm font-medium">{index + 1}</span>
                  )}
                </div>

                {/* Step Content */}
                <div className={cn(
                  "flex flex-col",
                  isHorizontal ? "items-center" : "items-start flex-1 min-w-0"
                )}>
                  <div
                    className={cn(
                      "text-sm font-semibold transition-colors duration-300",
                      isCurrent && "text-purple-200",
                      isCompleted && "text-green-200",
                      !isCompleted && !isCurrent && "text-white/60",
                      isClickable && "group-hover:text-purple-200"
                    )}
                  >
                    {step.title}
                  </div>
                  {step.description && (
                    <div
                      className={cn(
                        "text-xs mt-1 transition-colors duration-300",
                        isCurrent && "text-purple-300",
                        isCompleted && "text-green-300",
                        !isCompleted && !isCurrent && "text-white/40",
                        isHorizontal ? "text-center max-w-[140px] hidden sm:block" : "max-w-full"
                      )}
                    >
                      {step.description}
                    </div>
                  )}
                </div>
              </div>

              {/* Connector */}
              {showConnector && index < steps.length - 1 && (
                <div
                  className={cn(
                    "transition-all duration-300",
                    isHorizontal
                      ? "flex-1 h-0.5 mx-3 sm:mx-4 mt-[-24px]"
                      : "w-0.5 ml-5 h-8 my-3",
                    index < currentStep
                      ? "bg-gradient-to-r from-green-400 to-emerald-400 shadow-sm"
                      : "bg-white/20"
                  )}
                />
              )}
            </React.Fragment>
          )
        })}
      </div>
    )
  }
)

Stepper.displayName = "Stepper"

export { Stepper }
