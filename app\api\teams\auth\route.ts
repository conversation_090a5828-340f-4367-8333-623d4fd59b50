import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"

const supabaseAdmin = getSupabaseAdmin()

/**
 * POST /api/teams/auth
 * Authenticate a team using access token
 */
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { access_token, event_slug } = body

    if (!access_token) {
      return NextResponse.json(
        { error: "Access token is required" },
        { status: 400 }
      )
    }

    if (!event_slug) {
      return NextResponse.json(
        { error: "Event slug is required" },
        { status: 400 }
      )
    }

    // Get event by slug
    const { data: event, error: eventError } = await supabaseAdmin
      .from("events")
      .select("id, title, slug")
      .eq("slug", event_slug)
      .single()

    if (eventError || !event) {
      return NextResponse.json(
        { error: "Event not found" },
        { status: 404 }
      )
    }

    // Find team by access token and event
    const { data: team, error: teamError } = await supabaseAdmin
      .from("event_teams")
      .select(`
        id,
        team_name,
        location,
        permissions,
        is_active,
        expires_at,
        event_id,
        events:event_id(id, title, slug)
      `)
      .eq("access_token", access_token)
      .eq("event_id", event.id)
      .eq("is_active", true)
      .single()

    if (teamError || !team) {
      return NextResponse.json(
        { error: "Invalid access token or team not found" },
        { status: 401 }
      )
    }

    // Check if team access has expired
    if (team.expires_at && new Date(team.expires_at) < new Date()) {
      return NextResponse.json(
        { error: "Team access has expired" },
        { status: 401 }
      )
    }

    // Update last used timestamp
    await supabaseAdmin
      .from("event_teams")
      .update({ last_used_at: new Date().toISOString() })
      .eq("id", team.id)

    // Return team info (without sensitive data)
    return NextResponse.json({
      success: true,
      team: {
        id: team.id,
        team_name: team.team_name,
        location: team.location,
        permissions: team.permissions,
        event: team.events
      },
      message: "Team authenticated successfully"
    })

  } catch (error: any) {
    console.error("Error in team auth:", error)
    return NextResponse.json(
      { error: error.message || "Authentication failed" },
      { status: 500 }
    )
  }
}

/**
 * GET /api/teams/auth?token=xxx&slug=xxx
 * Quick token validation (for checking if token is valid)
 */
export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const access_token = searchParams.get("token")
    const event_slug = searchParams.get("slug")

    if (!access_token || !event_slug) {
      return NextResponse.json(
        { valid: false, error: "Missing token or event slug" },
        { status: 400 }
      )
    }

    // Get event by slug
    const { data: event, error: eventError } = await supabaseAdmin
      .from("events")
      .select("id")
      .eq("slug", event_slug)
      .single()

    if (eventError || !event) {
      return NextResponse.json(
        { valid: false, error: "Event not found" },
        { status: 404 }
      )
    }

    // Check if team exists and is active
    const { data: team, error: teamError } = await supabaseAdmin
      .from("event_teams")
      .select("id, team_name, location, expires_at")
      .eq("access_token", access_token)
      .eq("event_id", event.id)
      .eq("is_active", true)
      .single()

    if (teamError || !team) {
      return NextResponse.json({
        valid: false,
        error: "Invalid token"
      })
    }

    // Check expiration
    const isExpired = team.expires_at && new Date(team.expires_at) < new Date()

    return NextResponse.json({
      valid: !isExpired,
      team_name: team.team_name,
      location: team.location,
      expired: isExpired
    })

  } catch (error: any) {
    console.error("Error in team auth validation:", error)
    return NextResponse.json(
      { valid: false, error: "Validation failed" },
      { status: 500 }
    )
  }
}
