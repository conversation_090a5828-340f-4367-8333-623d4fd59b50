"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Check, X, Clock, AlertCircle, Download, Ticket } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export default function PaymentReturnPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();

  const [status, setStatus] = useState<'loading' | 'success' | 'pending' | 'failed'>('loading');
  const [details, setDetails] = useState<any>(null);
  const [receiptToken, setReceiptToken] = useState<string | null>(null);
  const [ticketToken, setTicketToken] = useState<string | null>(null);
  const [isDownloadingReceipt, setIsDownloadingReceipt] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    const status_id = searchParams.get('status_id');
    const billcode = searchParams.get('billcode');
    const order_id = searchParams.get('order_id');
    const msg = searchParams.get('msg');
    const transaction_id = searchParams.get('transaction_id');

    console.log("Payment return parameters:", { status_id, billcode, order_id, msg, transaction_id });

    // Prepare the payment return data to save
    const paymentReturnData = {
      status_id,
      billcode,
      order_id,
      msg,
      transaction_id,
      timestamp: new Date().toISOString(),
      url_params: Object.fromEntries(searchParams.entries())
    };

    // Save payment return data to database and then update status
    const processPaymentReturn = async () => {
      try {
        const response = await fetch('/api/payments/save-return-data', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(paymentReturnData),
        });

        if (!response.ok) {
          console.error('Failed to save payment return data:', await response.text());
        } else {
          const result = await response.json();
          console.log('Payment return data saved successfully:', result);

          // Store receipt and ticket tokens for public access
          if (result.receipt_token) {
            setReceiptToken(result.receipt_token);
            // Store in session storage for access from registration success page
            sessionStorage.setItem('receiptToken', result.receipt_token);
          }
          if (result.ticket_token) {
            setTicketToken(result.ticket_token);
            // Store in session storage for access from registration success page
            sessionStorage.setItem('ticketToken', result.ticket_token);
          }
          if (result.registration_id) {
            // Store registration ID for access from registration success page
            sessionStorage.setItem('registrationId', result.registration_id);
            console.log("Registration ID stored:", result.registration_id);
          }
        }
      } catch (error) {
        console.error('Error saving payment return data:', error);
      }

      // Update status after API call completes (whether successful or not)
      updatePaymentStatus(status_id, billcode, order_id, transaction_id);
    };

    // Process the payment return
    processPaymentReturn();
  }, [searchParams, toast]);

  const updatePaymentStatus = (status_id: string | null, billcode: string | null, order_id: string | null, transaction_id: string | null) => {
    switch (status_id) {
      case '1':
        setStatus('success');
        setDetails({
          message: 'Payment completed successfully!',
          description: 'Your registration has been confirmed.',
          billcode,
          order_id,
          transaction_id
        });
        toast({
          title: "Payment Successful",
          description: "Your registration has been confirmed",
        });

        // Auto-redirect to registration success page for better UX
        setTimeout(() => {
          const eventSlug = sessionStorage.getItem('currentEventSlug');
          if (eventSlug) {
            console.log("Auto-redirecting to registration success page for event:", eventSlug);
            setIsRedirecting(true);
            router.push(`/events/${eventSlug}/register?step=4`);
          }
        }, 2000); // 2 second delay to show success message
        break;
      case '2':
        setStatus('pending');
        setDetails({
          message: 'Payment is being processed',
          description: 'Your payment is pending. You will receive a confirmation once it is processed.',
          billcode,
          order_id,
          transaction_id
        });
        toast({
          title: "Payment Pending",
          description: "Your payment is being processed",
        });
        break;
      case '3':
        setStatus('failed');
        setDetails({
          message: 'Payment failed',
          description: 'There was an issue processing your payment. Please try again.',
          billcode,
          order_id,
          transaction_id
        });
        toast({
          title: "Payment Failed",
          description: "There was an issue processing your payment",
          variant: "destructive",
        });
        break;
      default:
        setStatus('failed');
        setDetails({
          message: 'Unknown payment status',
          description: 'Unable to determine payment status. Please contact support.',
          billcode,
          order_id,
          transaction_id
        });
    }
  };

  const handleDownloadReceipt = async () => {
    if (!receiptToken) {
      toast({
        title: "Receipt not available",
        description: "Receipt token not found. Please try again later.",
        variant: "destructive",
      });
      return;
    }

    setIsDownloadingReceipt(true);

    try {
      // Open public receipt in new tab for printing as PDF (no authentication required)
      const receiptUrl = `/api/receipts/public?token=${receiptToken}`;
      window.open(receiptUrl, '_blank');

      toast({
        title: "Receipt opened",
        description: "Your receipt has been opened in a new tab. Use Ctrl+P to print as PDF.",
      });
    } catch (error) {
      console.error("Error opening receipt:", error);
      toast({
        title: "Failed to open receipt",
        description: "Failed to open receipt. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDownloadingReceipt(false);
    }
  };

  const handleViewTickets = async () => {
    if (!ticketToken) {
      toast({
        title: "Tickets not available",
        description: "Ticket token not found. Please try again later.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Open public ticket view in new tab (no authentication required)
      const ticketUrl = `/api/tickets/public?token=${ticketToken}&type=pdf`;
      window.open(ticketUrl, '_blank');

      toast({
        title: "Ticket opened",
        description: "Your ticket has been opened in a new tab. Use Ctrl+P to print as PDF.",
      });
    } catch (error) {
      console.error("Error opening ticket:", error);
      toast({
        title: "Failed to open ticket",
        description: "Failed to open ticket. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getIcon = () => {
    switch (status) {
      case 'success':
        return <Check className="h-16 w-16 text-green-600" />;
      case 'pending':
        return <Clock className="h-16 w-16 text-yellow-600" />;
      case 'failed':
        return <X className="h-16 w-16 text-red-600" />;
      default:
        return <AlertCircle className="h-16 w-16 text-gray-600" />;
    }
  };

  const getBackgroundColor = () => {
    switch (status) {
      case 'success':
        return 'bg-green-100';
      case 'pending':
        return 'bg-yellow-100';
      case 'failed':
        return 'bg-red-100';
      default:
        return 'bg-gray-100';
    }
  };

  if (status === 'loading') {
    return (
      <div className="flex min-h-screen flex-col">
        <main className="flex-1">
          <div className="container mx-auto px-4 py-8">
            <div className="flex flex-col items-center justify-center min-h-[50vh]">
              <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
              <p className="mt-4 text-lg">Processing payment status...</p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col items-center justify-center min-h-[50vh]">
            <Card className="w-full max-w-md">
              <CardHeader className="text-center">
                <div className={`mx-auto mb-4 flex h-20 w-20 items-center justify-center rounded-full ${getBackgroundColor()}`}>
                  {getIcon()}
                </div>
                <CardTitle className="text-2xl">
                  {details?.message || 'Payment Status'}
                </CardTitle>
                <CardDescription>
                  {details?.description || 'Processing your payment...'}
                  {isRedirecting && (
                    <div className="mt-2 text-blue-600 font-medium">
                      Redirecting to registration details...
                    </div>
                  )}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {details?.billcode && (
                  <div className="text-center text-sm text-muted-foreground space-y-1">
                    <p>Bill Code: <span className="font-mono">{details.billcode}</span></p>
                    {details?.transaction_id && (
                      <p>Transaction ID: <span className="font-mono">{details.transaction_id}</span></p>
                    )}
                  </div>
                )}

                <div className="flex flex-col gap-2">
                  {/* Download Receipt Button - Only show for successful payments */}
                  {status === 'success' && receiptToken && (
                    <Button
                      onClick={handleDownloadReceipt}
                      disabled={isDownloadingReceipt}
                      className="w-full"
                      variant="default"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      {isDownloadingReceipt ? "Opening Receipt..." : "Download Receipt"}
                    </Button>
                  )}

                  {/* View Tickets Button - Only show for successful payments */}
                  {status === 'success' && ticketToken && (
                    <Button
                      onClick={handleViewTickets}
                      className="w-full"
                      variant="outline"
                    >
                      <Ticket className="h-4 w-4 mr-2" />
                      View My Tickets
                    </Button>
                  )}

                  {/* Go to Registration Success - Only show for successful payments */}
                  {status === 'success' && (receiptToken || ticketToken) && (
                    <Button
                      onClick={() => {
                        // Try to get event slug from referrer or session storage
                        const eventSlug = sessionStorage.getItem('currentEventSlug') || 'event';
                        router.push(`/events/${eventSlug}/register?step=4`);
                      }}
                      className="w-full"
                      variant="default"
                    >
                      View Registration Details
                    </Button>
                  )}

                  <Button
                    onClick={() => router.push("/dashboard/my-tickets")}
                    className="w-full"
                  >
                    Go to Dashboard
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push("/")}
                    className="w-full"
                  >
                    Return to Home
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
          </div>
        </main>
      </div>
    );
}
