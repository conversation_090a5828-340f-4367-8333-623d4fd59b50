import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

/**
 * GET /api/events/[slug]
 * Fetches a single event by slug with organizer information
 * This is a public endpoint - no authentication required
 */
export async function GET(
  request: Request,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    const { slug } = await params;
    console.log("Event by slug API: Fetching event with slug:", slug);

    if (!slug) {
      return NextResponse.json(
        { error: "Slug is required" },
        { status: 400 }
      );
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceKey) {
      console.error("Missing Supabase environment variables");
      return NextResponse.json(
        { error: "Server configuration error" },
        { status: 500 }
      );
    }

    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    });

    // Fetch the event with organizer and category information
    const { data, error } = await supabaseAdmin
      .from("events")
      .select(`
        *,
        organizations:organization_id (
          id,
          name,
          ssm_number,
          pic_name,
          pic_phone,
          pic_email,
          logo_url,
          address,
          city,
          state,
          postal_code,
          country,
          website
        ),
        event_categories (
          id,
          name,
          color,
          icon
        )
      `)
      .eq("slug", slug)
      .maybeSingle();

    if (error) {
      console.error("Error fetching event by slug:", error);
      return NextResponse.json(
        { error: "Failed to fetch event" },
        { status: 500 }
      );
    }

    if (!data) {
      console.log("No event found with slug:", slug);
      return NextResponse.json(
        { error: "Event not found" },
        { status: 404 }
      );
    }

    // Get current participant count by counting registrations
    // Count both "registered" (paid events) and "confirmed" (free events or completed payments)
    const { count: currentParticipants, error: countError } = await supabaseAdmin
      .from("registrations")
      .select("*", { count: "exact", head: true })
      .eq("event_id", data.id)
      .in("status", ["registered", "confirmed"]);

    if (countError) {
      console.error("Error counting participants:", countError);
      // Continue without participant count rather than failing
    }

    // Add participant count to the event data
    const eventWithParticipants = {
      ...data,
      current_participants: currentParticipants || 0
    };

    console.log("Event by slug API: Successfully fetched event:", data.title);
    console.log("Organization data:", data.organizations);
    console.log("Current participants:", currentParticipants || 0);

    return NextResponse.json(eventWithParticipants);
  } catch (error) {
    console.error("Event by slug API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
