import { supabase } from "@/lib/supabase"
import { v4 as uuidv4 } from "uuid"

// Seed events data
export async function seedEvents() {
  try {
    // Get current user
    const { data: sessionData } = await supabase.auth.getSession()
    if (!sessionData.session) {
      console.error("No user session found")
      return false
    }

    const userId = sessionData.session.user.id

    // Check if events already exist for this user
    const { data: existingEvents, error: checkError } = await supabase
      .from("events")
      .select("id")
      .eq("created_by", userId)

    if (checkError) {
      console.error("Error checking existing events:", checkError)
      return false
    }

    // If events already exist, don't seed
    if (existingEvents && existingEvents.length > 0) {
      console.log("Events already exist for this user, skipping seed")
      return true
    }

    // Sample events data
    const events = [
      {
        id: uuidv4(),
        title: "Tech Conference 2023",
        slug: "tech-conference-2023",
        description:
          "Join us for the biggest tech conference in the region. Network with industry leaders and learn about the latest technologies.",
        location: "Kuala Lumpur Convention Centre",
        start_date: new Date("2023-12-15").toISOString(),
        end_date: new Date("2023-12-16").toISOString(),
        price: 150.0,
        max_participants: 500,
        current_participants: 320,
        status: "published",
        created_by: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        payment_gateway_id: null,
        category: "Technology",
        is_featured: true,
        image_url: "/placeholder.svg?height=400&width=600",
      },
      {
        id: uuidv4(),
        title: "Digital Marketing Workshop",
        slug: "digital-marketing-workshop",
        description:
          "Learn the latest digital marketing strategies from industry experts. Hands-on workshop with practical exercises.",
        location: "Menara CIMB, KL Sentral",
        start_date: new Date("2023-11-25").toISOString(),
        end_date: new Date("2023-11-25").toISOString(),
        price: 75.0,
        max_participants: 50,
        current_participants: 42,
        status: "published",
        created_by: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        payment_gateway_id: null,
        category: "Marketing",
        is_featured: false,
        image_url: "/placeholder.svg?height=400&width=600",
      },
      {
        id: uuidv4(),
        title: "Startup Networking Event",
        slug: "startup-networking-event",
        description:
          "Connect with fellow entrepreneurs, investors, and mentors. Great opportunity to showcase your startup and find potential partners.",
        location: "MaGIC Cyberjaya",
        start_date: new Date("2023-12-05").toISOString(),
        end_date: new Date("2023-12-05").toISOString(),
        price: 0.0,
        max_participants: 100,
        current_participants: 85,
        status: "published",
        created_by: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        payment_gateway_id: null,
        category: "Networking",
        is_featured: true,
        image_url: "/placeholder.svg?height=400&width=600",
      },
      {
        id: uuidv4(),
        title: "AI & Machine Learning Summit",
        slug: "ai-machine-learning-summit",
        description:
          "Explore the latest advancements in AI and machine learning. Featuring keynote speakers from leading tech companies.",
        location: "Connexion Conference & Event Centre",
        start_date: new Date("2024-01-10").toISOString(),
        end_date: new Date("2024-01-12").toISOString(),
        price: 200.0,
        max_participants: 300,
        current_participants: 180,
        status: "published",
        created_by: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        payment_gateway_id: null,
        category: "Technology",
        is_featured: false,
        image_url: "/placeholder.svg?height=400&width=600",
      },
      {
        id: uuidv4(),
        title: "Web Development Bootcamp",
        slug: "web-development-bootcamp",
        description:
          "Intensive 3-day bootcamp covering frontend and backend web development. Perfect for beginners and intermediate developers.",
        location: "Sunway University",
        start_date: new Date("2023-12-20").toISOString(),
        end_date: new Date("2023-12-22").toISOString(),
        price: 120.0,
        max_participants: 30,
        current_participants: 12,
        status: "draft",
        created_by: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        payment_gateway_id: null,
        category: "Education",
        is_featured: false,
        image_url: "/placeholder.svg?height=400&width=600",
      },
      {
        id: uuidv4(),
        title: "Photography Workshop",
        slug: "photography-workshop",
        description:
          "Learn photography techniques from professional photographers. Bring your own camera for hands-on practice.",
        location: "KLCC Park",
        start_date: new Date("2023-10-15").toISOString(),
        end_date: new Date("2023-10-15").toISOString(),
        price: 50.0,
        max_participants: 20,
        current_participants: 20,
        status: "completed",
        created_by: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        payment_gateway_id: null,
        category: "Arts",
        is_featured: false,
        image_url: "/placeholder.svg?height=400&width=600",
      },
      {
        id: uuidv4(),
        title: "Blockchain Conference",
        slug: "blockchain-conference",
        description:
          "Explore the future of blockchain technology and cryptocurrency. Network with industry leaders and innovators.",
        location: "JW Marriott Kuala Lumpur",
        start_date: new Date("2023-09-10").toISOString(),
        end_date: new Date("2023-09-12").toISOString(),
        price: 250.0,
        max_participants: 200,
        current_participants: 180,
        status: "completed",
        created_by: userId,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        payment_gateway_id: null,
        category: "Technology",
        is_featured: false,
        image_url: "/placeholder.svg?height=400&width=600",
      },
    ]

    // Insert events
    const { error: insertError } = await supabase.from("events").insert(events)

    if (insertError) {
      console.error("Error seeding events:", insertError)
      return false
    }

    console.log("Events seeded successfully")
    return true
  } catch (error) {
    console.error("Error in seedEvents:", error)
    return false
  }
}

// Seed certificate templates
export async function seedCertificateTemplates() {
  try {
    // Check if system_settings table exists
    const { data: tableExists, error: tableError } = await supabase
      .from("information_schema.tables")
      .select("table_name")
      .eq("table_name", "system_settings")
      .single()

    if (tableError || !tableExists) {
      // Create system_settings table if it doesn't exist
      const { error: createError } = await supabase.rpc("create_system_settings_if_not_exists")
      if (createError) {
        console.error("Error creating system_settings table:", createError)
        return false
      }
    }

    // Check if certificate templates already exist
    const { data: settings, error: settingsError } = await supabase
      .from("system_settings")
      .select("certificate_templates")
      .single()

    if (settingsError && settingsError.code !== "PGRST116") {
      console.error("Error checking certificate templates:", settingsError)
      return false
    }

    // If certificate templates already exist, don't seed
    if (settings && settings.certificate_templates && settings.certificate_templates.length > 0) {
      console.log("Certificate templates already exist, skipping seed")
      return true
    }

    // Sample certificate templates
    const certificateTemplates = [
      {
        id: uuidv4(),
        name: "Standard Certificate",
        thumbnail_url: "/placeholder.svg?height=800&width=1200&text=Standard+Certificate",
        is_premium: false,
        html_template: "<div>Standard Certificate Template</div>",
        css_styles: "body { font-family: Arial, sans-serif; }",
        created_at: new Date().toISOString(),
        created_by: "system",
      },
      {
        id: uuidv4(),
        name: "Premium Certificate",
        thumbnail_url: "/placeholder.svg?height=800&width=1200&text=Premium+Certificate",
        is_premium: true,
        html_template: "<div>Premium Certificate Template</div>",
        css_styles: "body { font-family: 'Times New Roman', serif; }",
        created_at: new Date().toISOString(),
        created_by: "system",
      },
      {
        id: uuidv4(),
        name: "Workshop Completion",
        thumbnail_url: "/placeholder.svg?height=800&width=1200&text=Workshop+Completion",
        is_premium: false,
        html_template: "<div>Workshop Completion Certificate</div>",
        css_styles: "body { font-family: Calibri, sans-serif; }",
        created_at: new Date().toISOString(),
        created_by: "system",
      },
      {
        id: uuidv4(),
        name: "Conference Attendance",
        thumbnail_url: "/placeholder.svg?height=800&width=1200&text=Conference+Attendance",
        is_premium: false,
        html_template: "<div>Conference Attendance Certificate</div>",
        css_styles: "body { font-family: Verdana, sans-serif; }",
        created_at: new Date().toISOString(),
        created_by: "system",
      },
      {
        id: uuidv4(),
        name: "Gold Award",
        thumbnail_url: "/placeholder.svg?height=800&width=1200&text=Gold+Award",
        is_premium: true,
        html_template: "<div>Gold Award Certificate</div>",
        css_styles: "body { font-family: 'Palatino Linotype', serif; }",
        created_at: new Date().toISOString(),
        created_by: "system",
      },
    ]

    // Insert or update certificate templates
    const { error: upsertError } = await supabase
      .from("system_settings")
      .upsert([{ id: 1, certificate_templates: certificateTemplates }])

    if (upsertError) {
      console.error("Error seeding certificate templates:", upsertError)
      return false
    }

    console.log("Certificate templates seeded successfully")
    return true
  } catch (error) {
    console.error("Error in seedCertificateTemplates:", error)
    return false
  }
}

// Seed registrations and tickets
export async function seedRegistrations() {
  try {
    // Get current user
    const { data: sessionData } = await supabase.auth.getSession()
    if (!sessionData.session) {
      console.error("No user session found")
      return false
    }

    const userId = sessionData.session.user.id

    // Check if registrations already exist for this user
    const { data: existingRegistrations, error: checkError } = await supabase
      .from("registrations")
      .select("id")
      .eq("user_id", userId)

    if (checkError) {
      console.error("Error checking existing registrations:", checkError)
      return false
    }

    // If registrations already exist, don't seed
    if (existingRegistrations && existingRegistrations.length > 0) {
      console.log("Registrations already exist for this user, skipping seed")
      return true
    }

    // Get events
    const { data: events, error: eventsError } = await supabase.from("events").select("id, title")

    if (eventsError) {
      console.error("Error fetching events:", eventsError)
      return false
    }

    if (!events || events.length === 0) {
      console.error("No events found")
      return false
    }

    // Sample registrations
    const registrations = events.slice(0, 5).map((event) => ({
      id: uuidv4(),
      event_id: event.id,
      user_id: userId,
      attendee_name: "John Doe",
      attendee_email: "<EMAIL>",
      attendee_phone: "+***********",
      registration_date: new Date().toISOString(),
      payment_status: ["pending", "completed", "completed", "completed", "failed"][Math.floor(Math.random() * 5)],
      payment_amount: Math.floor(Math.random() * 200) + 50,
      payment_date: new Date().toISOString(),
      payment_method: ["credit_card", "bank_transfer", "paypal"][Math.floor(Math.random() * 3)],
      ticket_issued: true,
      ticket_code: `TICKET-${Math.floor(100000 + Math.random() * 900000)}`,
      check_in_status: ["not_checked_in", "checked_in"][Math.floor(Math.random() * 2)],
      check_in_time: Math.random() > 0.5 ? new Date().toISOString() : null,
    }))

    // Insert registrations
    const { error: insertError } = await supabase.from("registrations").insert(registrations)

    if (insertError) {
      console.error("Error seeding registrations:", insertError)
      return false
    }

    console.log("Registrations seeded successfully")
    return true
  } catch (error) {
    console.error("Error in seedRegistrations:", error)
    return false
  }
}

// Seed certificates
export async function seedCertificates() {
  try {
    // Get current user
    const { data: sessionData } = await supabase.auth.getSession()
    if (!sessionData.session) {
      console.error("No user session found")
      return false
    }

    const userId = sessionData.session.user.id

    // Check if certificates already exist for this user
    const { data: existingCertificates, error: checkError } = await supabase
      .from("certificates")
      .select("id")
      .eq("user_id", userId)

    if (checkError && checkError.code !== "PGRST116") {
      console.error("Error checking existing certificates:", checkError)
      return false
    }

    // If certificates already exist, don't seed
    if (existingCertificates && existingCertificates.length > 0) {
      console.log("Certificates already exist for this user, skipping seed")
      return true
    }

    // Get registrations
    const { data: registrations, error: registrationsError } = await supabase
      .from("registrations")
      .select("id, event_id, attendee_name")
      .eq("user_id", userId)

    if (registrationsError) {
      console.error("Error fetching registrations:", registrationsError)
      return false
    }

    if (!registrations || registrations.length === 0) {
      console.log("No registrations found, skipping certificate seed")
      return true
    }

    // Get certificate templates
    const { data: settings, error: settingsError } = await supabase
      .from("system_settings")
      .select("certificate_templates")
      .single()

    if (settingsError) {
      console.error("Error fetching certificate templates:", settingsError)
      return false
    }

    if (!settings || !settings.certificate_templates || settings.certificate_templates.length === 0) {
      console.error("No certificate templates found")
      return false
    }

    // Sample certificates
    const certificates = registrations.slice(0, 3).map((registration, index) => ({
      id: uuidv4(),
      event_id: registration.event_id,
      registration_id: registration.id,
      user_id: userId,
      participant_id: userId,
      participant_name: registration.attendee_name,
      template_id: settings.certificate_templates[index % settings.certificate_templates.length].id,
      issue_date: new Date().toISOString(),
      certificate_url: `/certificates/view/${registration.id}`,
      verification_code: `CERT-${Math.floor(100000 + Math.random() * 900000)}`,
      is_revoked: false,
    }))

    // Insert certificates
    const { error: insertError } = await supabase.from("certificates").insert(certificates)

    if (insertError) {
      console.error("Error seeding certificates:", insertError)
      return false
    }

    console.log("Certificates seeded successfully")
    return true
  } catch (error) {
    console.error("Error in seedCertificates:", error)
    return false
  }
}

// Main seed function
export async function seedAll() {
  const eventsSeeded = await seedEvents()
  const templatesSeeded = await seedCertificateTemplates()
  const registrationsSeeded = await seedRegistrations()
  const certificatesSeeded = await seedCertificates()

  return {
    eventsSeeded,
    templatesSeeded,
    registrationsSeeded,
    certificatesSeeded,
  }
}
