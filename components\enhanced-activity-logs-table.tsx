"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { supabase } from "@/lib/supabase"
import { useToast } from "@/components/ui/use-toast"
import { ActivityCategory } from "@/utils/activity-logger"
import {
  CalendarIcon, User, Calendar, LogIn, Activity,
  Settings, CreditCard, FileText, CheckCircle, AlertCircle,
  Building, Webhook, ExternalLink, Eye
} from "lucide-react"
import { format } from "date-fns"

// Enhanced activity log interface with foreign key relationships
interface EnhancedActivityLog {
  id: string
  user_id?: string
  user_name?: string
  action: string
  entity_type: string
  entity_id?: string
  category: string
  details: Record<string, any>
  ip_address?: string
  user_agent?: string
  created_at: string
  
  // Foreign key relationships
  event_id?: string
  registration_id?: string
  organization_id?: string
  certificate_id?: string
  subscription_id?: string
  api_key_id?: string
  webhook_id?: string
  target_user_id?: string
  payment_id?: string
  session_id?: string
  
  // Related entity data (joined)
  event?: { title: string; slug: string }
  registration?: { attendee_name: string; attendee_email: string }
  organization?: { name: string }
  target_user?: { full_name: string; email: string }
}

interface EnhancedActivityLogsTableProps {
  logs: EnhancedActivityLog[]
  loading: boolean
  onRefresh: () => void
}

export function EnhancedActivityLogsTable({ logs, loading, onRefresh }: EnhancedActivityLogsTableProps) {
  const { toast } = useToast()

  // Get icon for log action
  const getActionIcon = (category: string) => {
    switch (category) {
      case ActivityCategory.AUTH:
        return <LogIn className="h-4 w-4" />
      case ActivityCategory.EVENT:
        return <Calendar className="h-4 w-4" />
      case ActivityCategory.USER:
        return <User className="h-4 w-4" />
      case ActivityCategory.REGISTRATION:
        return <Calendar className="h-4 w-4" />
      case ActivityCategory.PAYMENT:
        return <CreditCard className="h-4 w-4" />
      case ActivityCategory.CERTIFICATE:
        return <FileText className="h-4 w-4" />
      case ActivityCategory.SETTINGS:
        return <Settings className="h-4 w-4" />
      case ActivityCategory.ATTENDANCE:
        return <CheckCircle className="h-4 w-4" />
      case ActivityCategory.SYSTEM:
        return <AlertCircle className="h-4 w-4" />
      case ActivityCategory.ORGANIZATION:
        return <Building className="h-4 w-4" />
      case ActivityCategory.WEBHOOK:
        return <Webhook className="h-4 w-4" />
      default:
        return <Activity className="h-4 w-4" />
    }
  }

  // Format action string for display
  const formatAction = (action: string) => {
    return action
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ")
  }

  // Render related entity information
  const renderRelatedEntity = (log: EnhancedActivityLog) => {
    const relatedEntities = []

    if (log.event) {
      relatedEntities.push(
        <div key="event" className="flex items-center gap-1 text-xs text-blue-600">
          <Calendar className="h-3 w-3" />
          <span className="font-medium">{log.event.title}</span>
        </div>
      )
    }

    if (log.registration) {
      relatedEntities.push(
        <div key="registration" className="flex items-center gap-1 text-xs text-green-600">
          <User className="h-3 w-3" />
          <span className="font-medium">{log.registration.attendee_name}</span>
          <span className="text-muted-foreground">({log.registration.attendee_email})</span>
        </div>
      )
    }

    if (log.organization) {
      relatedEntities.push(
        <div key="organization" className="flex items-center gap-1 text-xs text-purple-600">
          <Building className="h-3 w-3" />
          <span className="font-medium">{log.organization.name}</span>
        </div>
      )
    }

    if (log.target_user) {
      relatedEntities.push(
        <div key="target_user" className="flex items-center gap-1 text-xs text-orange-600">
          <User className="h-3 w-3" />
          <span className="font-medium">Target: {log.target_user.full_name}</span>
          <span className="text-muted-foreground">({log.target_user.email})</span>
        </div>
      )
    }

    if (log.payment_id) {
      relatedEntities.push(
        <div key="payment" className="flex items-center gap-1 text-xs text-emerald-600">
          <CreditCard className="h-3 w-3" />
          <span className="font-medium">Payment: {log.payment_id}</span>
        </div>
      )
    }

    return relatedEntities.length > 0 ? (
      <div className="space-y-1">
        {relatedEntities}
      </div>
    ) : null
  }

  // Render action buttons for related entities
  const renderActionButtons = (log: EnhancedActivityLog) => {
    const buttons = []

    if (log.event_id) {
      buttons.push(
        <Button
          key="view-event"
          variant="ghost"
          size="sm"
          className="h-6 px-2 text-xs"
          onClick={() => window.open(`/events/${log.event?.slug || log.event_id}`, '_blank')}
        >
          <ExternalLink className="h-3 w-3 mr-1" />
          Event
        </Button>
      )
    }

    if (log.registration_id) {
      buttons.push(
        <Button
          key="view-registration"
          variant="ghost"
          size="sm"
          className="h-6 px-2 text-xs"
          onClick={() => {
            toast({
              title: "Registration Details",
              description: `Registration ID: ${log.registration_id}`,
            })
          }}
        >
          <Eye className="h-3 w-3 mr-1" />
          Registration
        </Button>
      )
    }

    return buttons.length > 0 ? (
      <div className="flex gap-1 flex-wrap">
        {buttons}
      </div>
    ) : null
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Enhanced Activity Logs</CardTitle>
            <CardDescription>
              Activity logs with direct entity relationships and quick actions
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={onRefresh} disabled={loading}>
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Timestamp</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Action</TableHead>
                <TableHead>Related Entities</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    Loading enhanced activity logs...
                  </TableCell>
                </TableRow>
              ) : logs.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    No activity logs found
                  </TableCell>
                </TableRow>
              ) : (
                logs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell className="whitespace-nowrap">
                      {format(new Date(log.created_at), "MMM dd, yyyy HH:mm:ss")}
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col">
                        <span className="font-medium">{log.user_name || 'Unknown User'}</span>
                        {log.ip_address && (
                          <span className="text-xs text-muted-foreground">
                            IP: {log.ip_address}
                          </span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="flex items-center gap-1">
                          {getActionIcon(log.category)}
                          <span>{formatAction(log.action)}</span>
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      {renderRelatedEntity(log)}
                    </TableCell>
                    <TableCell>
                      {renderActionButtons(log)}
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
