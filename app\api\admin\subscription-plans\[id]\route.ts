import { NextRequest, NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { getUserFromToken } from "@/lib/auth/token"
import { logActivity, ActivityCategory } from "@/lib/activity-logger"

// GET: Get single subscription plan
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Get user from token
    const userToken = await getUserFromToken(request)
    if (!userToken) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin()

    // Get user details to check admin role
    const { data: user, error: userError } = await supabaseAdmin
      .from("users")
      .select(`
        *,
        role_info:role_id(id, role_name, description, permissions)
      `)
      .eq("id", userToken.userId)
      .single()

    if (userError || !user) {
      console.error("Error fetching user:", userError)
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Check if user has admin role
    const isAdmin = user.role === "admin" || user.role_info?.role_name === "admin"
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 403 }
      )
    }

    // Fetch the subscription plan
    const { data: plan, error } = await supabaseAdmin
      .from('subscription_plans')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error("Error fetching subscription plan:", error)
      return NextResponse.json(
        { error: "Subscription plan not found" },
        { status: 404 }
      )
    }

    if (!plan) {
      return NextResponse.json(
        { error: "Subscription plan not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      plan: {
        id: plan.id,
        name: plan.name,
        price: parseFloat(plan.price.toString()),
        description: plan.description,
        features: Array.isArray(plan.features) ? plan.features : [],
        max_events: plan.max_events,
        max_attendees_per_event: plan.max_attendees_per_event,
        is_popular: plan.is_popular,
        is_active: plan.is_active,
        certificates_enabled: plan.certificates_enabled,
        attendance_enabled: plan.attendance_enabled,
        webhooks_enabled: plan.webhooks_enabled,
        analytics_enabled: plan.analytics_enabled,
        reports_enabled: plan.reports_enabled,
        created_at: plan.created_at,
        updated_at: plan.updated_at,
      }
    })
  } catch (error: any) {
    console.error("Error in subscription plan GET API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

// PUT: Update subscription plan
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Get user from token
    const userToken = await getUserFromToken(request)
    if (!userToken) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin()

    // Get user details to check admin role
    const { data: user, error: userError } = await supabaseAdmin
      .from("users")
      .select(`
        *,
        role_info:role_id(id, role_name, description, permissions)
      `)
      .eq("id", userToken.userId)
      .single()

    if (userError || !user) {
      console.error("Error fetching user:", userError)
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Check if user has admin role
    const isAdmin = user.role === "admin" || user.role_info?.role_name === "admin"
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      name,
      price,
      description,
      features,
      max_events,
      max_attendees_per_event,
      is_popular,
      is_active,
      certificates_enabled,
      attendance_enabled,
      webhooks_enabled,
      analytics_enabled,
      reports_enabled
    } = body

    // Validate required fields
    if (!name || price === undefined) {
      return NextResponse.json(
        { error: "Missing required fields: name, price" },
        { status: 400 }
      )
    }

    // Validate features array
    if (!Array.isArray(features)) {
      return NextResponse.json(
        { error: "Features must be an array" },
        { status: 400 }
      )
    }

    // Update the subscription plan
    const { data: plan, error } = await supabaseAdmin
      .from('subscription_plans')
      .update({
        name,
        price: parseFloat(price.toString()),
        description: description || '',
        features: features.filter(f => f.trim() !== ''), // Remove empty features
        max_events: max_events === '' || max_events === null ? null : parseInt(max_events.toString()),
        max_attendees_per_event: max_attendees_per_event === '' || max_attendees_per_event === null ? null : parseInt(max_attendees_per_event.toString()),
        is_popular: Boolean(is_popular),
        is_active: Boolean(is_active),
        certificates_enabled: Boolean(certificates_enabled),
        attendance_enabled: Boolean(attendance_enabled),
        webhooks_enabled: Boolean(webhooks_enabled),
        analytics_enabled: Boolean(analytics_enabled),
        reports_enabled: Boolean(reports_enabled),
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error("Error updating subscription plan:", error)
      return NextResponse.json(
        { error: error.message || "Failed to update subscription plan" },
        { status: 500 }
      )
    }

    if (!plan) {
      return NextResponse.json(
        { error: "Subscription plan not found" },
        { status: 404 }
      )
    }

    // Log activity
    try {
      await logActivity({
        userId: user.id,
        action: "update_subscription_plan",
        entityType: "subscription_plan",
        entityId: plan.id,
        category: ActivityCategory.ADMIN,
        details: {
          plan_name: plan.name,
          price: plan.price,
        },
      })
    } catch (logError) {
      console.error("Error logging activity:", logError)
    }

    return NextResponse.json({
      success: true,
      plan: {
        id: plan.id,
        name: plan.name,
        price: parseFloat(plan.price.toString()),
        description: plan.description,
        features: Array.isArray(plan.features) ? plan.features : [],
        max_events: plan.max_events,
        max_attendees_per_event: plan.max_attendees_per_event,
        is_popular: plan.is_popular,
        is_active: plan.is_active,
        created_at: plan.created_at,
        updated_at: plan.updated_at,
      }
    })
  } catch (error: any) {
    console.error("Error in update subscription plan API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}

// DELETE: Delete subscription plan
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // Get user from token
    const userToken = await getUserFromToken(request)
    if (!userToken) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin()

    // Get user details to check admin role
    const { data: user, error: userError } = await supabaseAdmin
      .from("users")
      .select(`
        *,
        role_info:role_id(id, role_name, description, permissions)
      `)
      .eq("id", userToken.userId)
      .single()

    if (userError || !user) {
      console.error("Error fetching user:", userError)
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Check if user has admin role
    const isAdmin = user.role === "admin" || user.role_info?.role_name === "admin"
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 403 }
      )
    }

    // Get the plan details before deletion for logging
    const { data: planToDelete, error: fetchError } = await supabaseAdmin
      .from('subscription_plans')
      .select('*')
      .eq('id', id)
      .single()

    if (fetchError || !planToDelete) {
      return NextResponse.json(
        { error: "Subscription plan not found" },
        { status: 404 }
      )
    }

    // Check if any users are currently subscribed to this plan
    const { data: activeSubscriptions, error: subError } = await supabaseAdmin
      .from('user_subscription')
      .select('id')
      .eq('subscription_type', planToDelete.name.toLowerCase())
      .eq('is_active', true)

    if (subError) {
      console.error("Error checking active subscriptions:", subError)
      return NextResponse.json(
        { error: "Failed to check active subscriptions" },
        { status: 500 }
      )
    }

    if (activeSubscriptions && activeSubscriptions.length > 0) {
      return NextResponse.json(
        { error: "Cannot delete plan with active subscriptions. Please deactivate the plan instead." },
        { status: 400 }
      )
    }

    // Delete the subscription plan
    const { error } = await supabaseAdmin
      .from('subscription_plans')
      .delete()
      .eq('id', id)

    if (error) {
      console.error("Error deleting subscription plan:", error)
      return NextResponse.json(
        { error: error.message || "Failed to delete subscription plan" },
        { status: 500 }
      )
    }

    // Log activity
    try {
      await logActivity({
        userId: user.id,
        action: "delete_subscription_plan",
        entityType: "subscription_plan",
        entityId: id,
        category: ActivityCategory.ADMIN,
        details: {
          plan_name: planToDelete.name,
          price: planToDelete.price,
        },
      })
    } catch (logError) {
      console.error("Error logging activity:", logError)
    }

    return NextResponse.json({
      success: true,
      message: "Subscription plan deleted successfully"
    })
  } catch (error: any) {
    console.error("Error in delete subscription plan API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
