/**
 * Client-side crypto polyfill for Next.js 15
 *
 * This file provides a polyfill for the crypto module in Next.js 15
 * which dropped automatic polyfills for Node-core modules
 */

// Import the crypto-browserify module
import cryptoBrowserify from 'crypto-browserify';

// Make crypto available globally
if (typeof global !== 'undefined' && !global.crypto) {
  global.crypto = cryptoBrowserify;
}

// Only run this code on the client side
if (typeof window !== 'undefined') {
  // Check if crypto is already defined
  if (!window.crypto || !window.crypto.subtle) {

    // Use crypto-browserify as a polyfill
    window.crypto = {
      ...window.crypto,
      ...cryptoBrowserify,
      // Ensure getRandomValues is available
      getRandomValues: window.crypto?.getRandomValues || function(buffer) {
        const bytes = cryptoBrowserify.randomBytes(buffer.length);
        buffer.set(new Uint8Array(bytes));
        return buffer;
      }
    };
  }

  // Make sure subtle crypto is available
  if (!window.crypto.subtle) {
    window.crypto.subtle = cryptoBrowserify.subtle || {};
  }

  // Make crypto available globally
  window.cryptoAPI = window.crypto;

  // Ensure crypto is available on the global object
  if (typeof global !== 'undefined' && !global.crypto) {
    global.crypto = window.crypto;
  }
}

// Export the crypto object for direct imports
export const crypto = typeof window !== 'undefined' ? window.crypto : cryptoBrowserify;
export default typeof window !== 'undefined' ? window.crypto : cryptoBrowserify;
