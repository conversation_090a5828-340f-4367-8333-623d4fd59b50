import type { UserRole, RolePermissions } from './types'

/**
 * Role hierarchy and permissions management
 */

export const USER_ROLES: Record<UserRole, string> = {
  admin: 'Administrator',
  user: 'User',
  manager: 'Manager', 
  supermanager: 'Super Manager',
  event_admin: 'Event Administrator'
} as const

/**
 * Get role permissions based on role name
 */
export function getRolePermissions(role: string): RolePermissions {
  switch (role) {
    case 'admin':
      return {
        canAccessAdmin: true,
        canManageUsers: true,
        canManageEvents: true,
        canManagePayments: true,
        canViewAnalytics: true,
        canManageOrganizations: true
      }
    
    case 'event_admin':
      return {
        canAccessAdmin: false,
        canManageUsers: false,
        canManageEvents: true, // Can manage ALL events
        canManagePayments: false,
        canViewAnalytics: true,
        canManageOrganizations: false
      }
    
    case 'manager':
    case 'supermanager':
      return {
        canAccessAdmin: false,
        canManageUsers: false,
        canManageEvents: true, // Can manage own events only
        canManagePayments: true, // Can manage own payments
        canViewAnalytics: true,
        canManageOrganizations: true // Can manage own organizations
      }
    
    case 'user':
    default:
      return {
        canAccessAdmin: false,
        canManageUsers: false,
        canManageEvents: false,
        canManagePayments: false,
        canViewAnalytics: false,
        canManageOrganizations: false
      }
  }
}

/**
 * Check if user has admin privileges
 */
export function isAdmin(role: string): boolean {
  return role === 'admin'
}

/**
 * Check if user has manager privileges
 */
export function isManager(role: string): boolean {
  return ['manager', 'supermanager'].includes(role)
}

/**
 * Check if user has elevated privileges (admin, manager, or event_admin)
 */
export function isElevated(role: string): boolean {
  return ['admin', 'manager', 'supermanager', 'event_admin'].includes(role)
}

/**
 * Check if user can access admin panel
 */
export function canAccessAdmin(role: string): boolean {
  return isAdmin(role)
}

/**
 * Check if user can manage all events (admin or event_admin)
 */
export function canManageAllEvents(role: string): boolean {
  return ['admin', 'event_admin'].includes(role)
}

/**
 * Check if user can manage own events (manager roles)
 */
export function canManageOwnEvents(role: string): boolean {
  return isManager(role) || canManageAllEvents(role)
}

/**
 * Check if user can manage users
 */
export function canManageUsers(role: string): boolean {
  return isAdmin(role)
}

/**
 * Check if user can manage payment gateways
 */
export function canManagePaymentGateways(role: string): boolean {
  return isAdmin(role)
}

/**
 * Check if user can view analytics
 */
export function canViewAnalytics(role: string): boolean {
  return isElevated(role)
}

/**
 * Check if user can manage organizations
 */
export function canManageOrganizations(role: string): boolean {
  return isAdmin(role) || isManager(role)
}

/**
 * Get user's default landing page based on role
 */
export function getDefaultLandingPage(role: string): string {
  switch (role) {
    case 'admin':
      return '/dashboard'
    case 'manager':
    case 'supermanager':
    case 'event_admin':
      return '/dashboard/events'
    case 'user':
    default:
      return '/dashboard/my-tickets'
  }
}

/**
 * Get available menu items based on user role
 */
export function getMenuItems(role: string): Array<{
  label: string
  href: string
  icon?: string
  adminOnly?: boolean
  managerOnly?: boolean
  elevatedOnly?: boolean
}> {
  const baseItems = [
    { label: 'My Tickets', href: '/dashboard/my-tickets' }
  ]

  if (isElevated(role)) {
    baseItems.push(
      { label: 'Events', href: '/dashboard/events', elevatedOnly: true },
      { label: 'Analytics', href: '/dashboard/analytics', elevatedOnly: true }
    )
  }

  if (isManager(role)) {
    baseItems.push(
      { label: 'Organizations', href: '/dashboard/organization', managerOnly: true },
      { label: 'Wallet', href: '/dashboard/wallet', managerOnly: true }
    )
  }

  if (isAdmin(role)) {
    baseItems.push(
      { label: 'Users', href: '/dashboard/users', adminOnly: true },
      { label: 'Settings', href: '/dashboard/settings', adminOnly: true },
      { label: 'Activity Logs', href: '/dashboard/activity-logs', adminOnly: true }
    )
  }

  return baseItems
}

/**
 * Validate role transition (for role updates)
 */
export function canChangeRole(currentRole: string, newRole: string, changerRole: string): boolean {
  // Only admins can change roles
  if (!isAdmin(changerRole)) {
    return false
  }

  // Admins can change any role except their own to admin
  if (newRole === 'admin' && currentRole !== 'admin') {
    return true
  }

  // Can change any non-admin role
  return currentRole !== 'admin'
}
