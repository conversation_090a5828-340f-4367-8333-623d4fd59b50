import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"

interface RouteParams {
  params: { organizerId: string }
}

/**
 * GET /api/events/organizer/[organizerId]
 * Fetches all published events by a specific organizer
 * This is a public endpoint - no authentication required
 */
export async function GET(request: Request, { params }: RouteParams) {
  try {
    const { organizerId } = await params

    if (!organizerId) {
      return NextResponse.json(
        { error: "Organizer ID is required" },
        { status: 400 }
      )
    }

    console.log("Events by Organizer API: Fetching events for organizer:", organizerId)

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin()

    // Fetch all published events by the organizer with organizer and category information
    const { data, error } = await supabaseAdmin
      .from("events")
      .select(`
        *,
        organizations:organization_id(id, name),
        event_category:category_id(id, name, color, icon)
      `)
      .eq("organization_id", organizerId)
      .eq("is_published", true)

    if (error) {
      console.error("Error fetching events by organizer:", error)
      return NextResponse.json(
        { error: "Failed to fetch events" },
        { status: 500 }
      )
    }

    // Sort events: available events first (by start_date), then ended events last
    const sortedEvents = (data || []).sort((a, b) => {
      const now = new Date();
      const aEnded = new Date(a.end_date) < now;
      const bEnded = new Date(b.end_date) < now;

      // If one is ended and the other isn't, prioritize the non-ended one
      if (aEnded && !bEnded) return 1;
      if (!aEnded && bEnded) return -1;

      // If both have the same status (both ended or both available), sort by start_date
      return new Date(a.start_date).getTime() - new Date(b.start_date).getTime();
    });

    console.log(`Events by Organizer API: Successfully fetched ${sortedEvents.length} events`)

    return NextResponse.json({
      events: sortedEvents,
      count: sortedEvents.length,
      organizer: sortedEvents?.[0]?.organizations || null
    })
  } catch (error: any) {
    console.error("Error in events by organizer API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
