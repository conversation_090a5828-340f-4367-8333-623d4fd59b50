-- Create a function to update user password with proper permissions
CREATE OR REPLACE FUNCTION update_user_password(user_id UUID, new_password_hash TEXT)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER -- This runs with the permissions of the function creator
AS $$
BEGIN
  -- Update the password_hash in the users table
  UPDATE users
  SET password_hash = new_password_hash
  WHERE id = user_id;
  
  -- Return true if the update was successful
  RETURN FOUND;
END;
$$;
