// Migration script to move certificate images to new certificates storage bucket
// Structure: certificates/bg/ for backgrounds, certificates/img/ for field images

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://bivslxeghhebmkelieue.supabase.co';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseServiceKey) {
  console.error('❌ SUPABASE_SERVICE_ROLE_KEY environment variable is required');
  console.error('   Set it with: export SUPABASE_SERVICE_ROLE_KEY="your-service-role-key"');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function migrateCertificateImages() {
  try {
    console.log('🔄 Starting certificate images migration to new storage structure...\n');
    console.log('📁 New structure:');
    console.log('   certificates/bg/   - Background images');
    console.log('   certificates/img/  - Field images (logos, etc.)\n');

    // 1. Get all templates with background images
    const { data: templates, error: fetchError } = await supabase
      .from('certificate_templates')
      .select('id, name, background_image_url')
      .not('background_image_url', 'is', null)
      .neq('background_image_url', '');

    if (fetchError) {
      throw new Error(`Failed to fetch templates: ${fetchError.message}`);
    }

    console.log(`📋 Found ${templates.length} templates with background images\n`);

    let migratedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const template of templates) {
      const { id, name, background_image_url } = template;
      
      console.log(`🔄 Processing: ${name}`);
      console.log(`   Current URL: ${background_image_url}`);

      // Skip if already in certificates bucket
      if (background_image_url.includes('/certificates/bg/')) {
        console.log(`   ⏭️  Already in certificates/bg/ - skipping\n`);
        skippedCount++;
        continue;
      }

      // Extract filename from URL
      let fileName;
      let oldBucket;
      let oldPath;

      if (background_image_url.includes('/avatars/certificate-backgrounds/')) {
        // From avatars bucket
        oldBucket = 'avatars';
        const urlParts = background_image_url.split('/');
        fileName = urlParts[urlParts.length - 1];
        oldPath = `certificate-backgrounds/${fileName}`;
      } else if (background_image_url.includes('/certificate-backgrounds/')) {
        // From certificate-backgrounds bucket
        oldBucket = 'certificate-backgrounds';
        const urlParts = background_image_url.split('/');
        fileName = urlParts[urlParts.length - 1];
        oldPath = fileName;
      } else {
        console.log(`   ⚠️  Unknown URL format - skipping\n`);
        skippedCount++;
        continue;
      }

      console.log(`   📁 Source: ${oldBucket}/${oldPath}`);
      console.log(`   📁 Target: certificates/bg/${fileName}`);

      try {
        // 1. Download the file from old location
        console.log('   📥 Downloading...');
        const { data: fileData, error: downloadError } = await supabase.storage
          .from(oldBucket)
          .download(oldPath);

        if (downloadError) {
          console.log(`   ❌ Download failed: ${downloadError.message}\n`);
          errorCount++;
          continue;
        }

        // 2. Upload to new location
        console.log('   📤 Uploading to certificates/bg/...');
        const { error: uploadError } = await supabase.storage
          .from('certificates')
          .upload(`bg/${fileName}`, fileData, {
            contentType: fileData.type || 'image/jpeg',
            upsert: true
          });

        if (uploadError) {
          console.log(`   ❌ Upload failed: ${uploadError.message}\n`);
          errorCount++;
          continue;
        }

        // 3. Get new public URL
        const { data: urlData } = supabase.storage
          .from('certificates')
          .getPublicUrl(`bg/${fileName}`);

        const newUrl = urlData.publicUrl;
        console.log(`   🔗 New URL: ${newUrl}`);

        // 4. Update template with new URL
        console.log('   💾 Updating template...');
        const { error: updateError } = await supabase
          .from('certificate_templates')
          .update({ background_image_url: newUrl })
          .eq('id', id);

        if (updateError) {
          console.log(`   ❌ Update failed: ${updateError.message}\n`);
          errorCount++;
          continue;
        }

        // 5. Optionally delete old file (commented out for safety)
        // console.log('   🗑️  Removing old file...');
        // const { error: deleteError } = await supabase.storage
        //   .from(oldBucket)
        //   .remove([oldPath]);
        // 
        // if (deleteError) {
        //   console.log(`   ⚠️  Warning: Could not delete old file: ${deleteError.message}`);
        // }

        console.log(`   ✅ Successfully migrated!\n`);
        migratedCount++;

      } catch (error) {
        console.log(`   ❌ Error: ${error.message}\n`);
        errorCount++;
      }
    }

    console.log('📊 Migration Summary:');
    console.log(`   ✅ Migrated: ${migratedCount}`);
    console.log(`   ⏭️  Skipped: ${skippedCount}`);
    console.log(`   ❌ Errors: ${errorCount}`);
    console.log(`   📋 Total: ${templates.length}\n`);

    if (migratedCount > 0) {
      console.log('🎉 Migration completed successfully!');
      console.log('💡 Note: Old files were not deleted for safety.');
      console.log('   You can manually delete them after verifying the migration.');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

// Run migration
migrateCertificateImages();
