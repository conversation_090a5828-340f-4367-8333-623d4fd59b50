"use client"

import { createContext, useContext, useState, type ReactNode } from "react"
import { v4 as uuidv4 } from "uuid"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { useSettings } from "@/contexts/settings-context"
import { supabase } from "@/lib/supabase"

// Types
export type Transaction = {
  id: string
  user_id: string
  event_id: string | null
  registration_id: string | null
  amount: number
  fee_amount: number
  net_amount: number
  transaction_type: "registration_payment" | "withdrawal" | "refund" | "system_fee"
  status: "pending" | "completed" | "failed" | "cancelled"
  payment_method: string | null
  payment_gateway_id: string | null
  transaction_date: string
  description: string
  reference_id: string | null
}

export type WithdrawalRequest = {
  id: string
  user_id: string
  amount: number
  fee_amount: number
  net_amount: number
  status: "pending" | "approved" | "rejected" | "completed"
  bank_name: string
  account_number: string
  account_holder: string
  request_date: string
  processed_date: string | null
  notes: string | null
}

type FinancialContextType = {
  loading: boolean
  error: string | null
  recordPayment: (
    eventId: string,
    registrationId: string,
    amount: number,
    paymentMethod: string,
    paymentGatewayId?: string,
  ) => Promise<Transaction | null>
  requestWithdrawal: (
    amount: number,
    bankDetails: { bank_name: string; account_number: string; account_holder: string },
  ) => Promise<WithdrawalRequest | null>
  getTransactions: (userId: string) => Promise<Transaction[]>
  getWithdrawalRequests: (userId: string) => Promise<WithdrawalRequest[]>
  getUserEarnings: (userId: string) => Promise<{ total: number; available: number; pending: number }>
  approveWithdrawal: (withdrawalId: string) => Promise<boolean>
  rejectWithdrawal: (withdrawalId: string, reason: string) => Promise<boolean>
  completeWithdrawal: (withdrawalId: string, referenceId: string) => Promise<boolean>
}

const FinancialContext = createContext<FinancialContextType | undefined>(undefined)

export function FinancialProvider({ children }: { children: ReactNode }) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()
  const { user, isAdmin } = useAuth()
  const { calculateEventFee, calculateWithdrawalFee, settings } = useSettings()

  // Record payment
  const recordPayment = async (
    eventId: string,
    registrationId: string,
    amount: number,
    paymentMethod: string,
    paymentGatewayId?: string,
  ): Promise<Transaction | null> => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to record a payment",
        variant: "destructive",
      })
      return null
    }

    setLoading(true)
    try {
      // Calculate system fee
      const feeAmount = calculateEventFee(amount)
      const netAmount = amount - feeAmount

      // Get event organizer ID
      const { data: eventData, error: eventError } = await supabase
        .from("events")
        .select("created_by")
        .eq("id", eventId)
        .single()

      if (eventError) throw eventError

      const organizerId = eventData.created_by

      // Create payment transaction
      const paymentTransaction = {
        id: uuidv4(),
        user_id: organizerId,
        event_id: eventId,
        registration_id: registrationId,
        amount,
        fee_amount: feeAmount,
        net_amount: netAmount,
        transaction_type: "registration_payment",
        status: "completed",
        payment_method: paymentMethod,
        payment_gateway_id: paymentGatewayId || null,
        transaction_date: new Date().toISOString(),
        description: `Payment for event registration`,
        reference_id: `REG-${registrationId}`,
      } as Transaction

      // Create system fee transaction
      const feeTransaction = {
        id: uuidv4(),
        user_id: organizerId,
        event_id: eventId,
        registration_id: registrationId,
        amount: feeAmount,
        fee_amount: 0,
        net_amount: feeAmount,
        transaction_type: "system_fee",
        status: "completed",
        payment_method: null,
        payment_gateway_id: null,
        transaction_date: new Date().toISOString(),
        description: `System fee (${settings?.event_fee_percentage}%) for event registration`,
        reference_id: `FEE-${registrationId}`,
      } as Transaction

      // Insert transactions
      const { error: transactionError } = await supabase
        .from("transactions")
        .insert([paymentTransaction, feeTransaction])

      if (transactionError) throw transactionError

      // Update user earnings
      const { error: userError } = await supabase
        .from("users")
        .update({
          total_earnings: supabase.rpc("increment", { x: netAmount }),
          available_balance: supabase.rpc("increment", { x: netAmount }),
        })
        .eq("id", organizerId)

      if (userError) throw userError

      toast({
        title: "Success",
        description: "Payment recorded successfully",
      })

      return paymentTransaction
    } catch (err) {
      console.error("Error recording payment:", err)
      setError("Failed to record payment")
      toast({
        title: "Error",
        description: "Failed to record payment",
        variant: "destructive",
      })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Request withdrawal
  const requestWithdrawal = async (
    amount: number,
    bankDetails: { bank_name: string; account_number: string; account_holder: string },
  ): Promise<WithdrawalRequest | null> => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to request a withdrawal",
        variant: "destructive",
      })
      return null
    }

    setLoading(true)
    try {
      // Check if user has enough balance
      if (user.available_balance < amount) {
        toast({
          title: "Error",
          description: "Insufficient balance",
          variant: "destructive",
        })
        return null
      }

      // Check minimum withdrawal amount
      if (amount < (settings?.min_withdrawal_amount || 0)) {
        toast({
          title: "Error",
          description: `Minimum withdrawal amount is RM ${settings?.min_withdrawal_amount}`,
          variant: "destructive",
        })
        return null
      }

      // Calculate withdrawal fee
      const feeAmount = calculateWithdrawalFee(amount)
      const netAmount = amount - feeAmount

      // Create withdrawal request
      const withdrawalRequest = {
        id: uuidv4(),
        user_id: user.id,
        amount,
        fee_amount: feeAmount,
        net_amount: netAmount,
        status: "pending",
        bank_name: bankDetails.bank_name,
        account_number: bankDetails.account_number,
        account_holder: bankDetails.account_holder,
        request_date: new Date().toISOString(),
        processed_date: null,
        notes: null,
      } as WithdrawalRequest

      const { data, error } = await supabase.from("withdrawal_requests").insert([withdrawalRequest]).select()

      if (error) throw error

      // Update user available balance
      const { error: userError } = await supabase
        .from("users")
        .update({
          available_balance: supabase.rpc("decrement", { x: amount }),
        })
        .eq("id", user.id)

      if (userError) throw userError

      toast({
        title: "Success",
        description: "Withdrawal request submitted successfully",
      })

      return data[0] as WithdrawalRequest
    } catch (err) {
      console.error("Error requesting withdrawal:", err)
      setError("Failed to request withdrawal")
      toast({
        title: "Error",
        description: "Failed to request withdrawal",
        variant: "destructive",
      })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Get transactions
  const getTransactions = async (userId: string): Promise<Transaction[]> => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from("transactions")
        .select("*")
        .eq("user_id", userId)
        .order("transaction_date", { ascending: false })

      if (error) throw error

      return data as Transaction[]
    } catch (err) {
      console.error("Error fetching transactions:", err)
      setError("Failed to fetch transactions")
      return []
    } finally {
      setLoading(false)
    }
  }

  // Get withdrawal requests
  const getWithdrawalRequests = async (userId: string): Promise<WithdrawalRequest[]> => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from("withdrawal_requests")
        .select("*")
        .eq("user_id", userId)
        .order("request_date", { ascending: false })

      if (error) throw error

      return data as WithdrawalRequest[]
    } catch (err) {
      console.error("Error fetching withdrawal requests:", err)
      setError("Failed to fetch withdrawal requests")
      return []
    } finally {
      setLoading(false)
    }
  }

  // Get user earnings
  const getUserEarnings = async (userId: string): Promise<{ total: number; available: number; pending: number }> => {
    setLoading(true)
    try {
      // Get user data
      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("total_earnings, available_balance")
        .eq("id", userId)
        .single()

      if (userError) throw userError

      // Calculate pending withdrawals
      const { data: withdrawalData, error: withdrawalError } = await supabase
        .from("withdrawal_requests")
        .select("amount")
        .eq("user_id", userId)
        .eq("status", "pending")

      if (withdrawalError) throw withdrawalError

      const pendingWithdrawals = withdrawalData.reduce((sum, item) => sum + item.amount, 0)

      return {
        total: userData.total_earnings || 0,
        available: userData.available_balance || 0,
        pending: pendingWithdrawals || 0,
      }
    } catch (err) {
      console.error("Error fetching user earnings:", err)
      setError("Failed to fetch earnings")
      return { total: 0, available: 0, pending: 0 }
    } finally {
      setLoading(false)
    }
  }

  // Approve withdrawal (admin only)
  const approveWithdrawal = async (withdrawalId: string): Promise<boolean> => {
    if (!user || !isAdmin()) {
      toast({
        title: "Error",
        description: "You don't have permission to approve withdrawals",
        variant: "destructive",
      })
      return false
    }

    setLoading(true)
    try {
      const { error } = await supabase
        .from("withdrawal_requests")
        .update({
          status: "approved",
          processed_date: new Date().toISOString(),
        })
        .eq("id", withdrawalId)

      if (error) throw error

      toast({
        title: "Success",
        description: "Withdrawal request approved",
      })

      return true
    } catch (err) {
      console.error("Error approving withdrawal:", err)
      setError("Failed to approve withdrawal")
      toast({
        title: "Error",
        description: "Failed to approve withdrawal",
        variant: "destructive",
      })
      return false
    } finally {
      setLoading(false)
    }
  }

  // Reject withdrawal (admin only)
  const rejectWithdrawal = async (withdrawalId: string, reason: string): Promise<boolean> => {
    if (!user || !isAdmin()) {
      toast({
        title: "Error",
        description: "You don't have permission to reject withdrawals",
        variant: "destructive",
      })
      return false
    }

    setLoading(true)
    try {
      // Get withdrawal request
      const { data: withdrawalData, error: withdrawalError } = await supabase
        .from("withdrawal_requests")
        .select("*")
        .eq("id", withdrawalId)
        .single()

      if (withdrawalError) throw withdrawalError

      const withdrawal = withdrawalData as WithdrawalRequest

      // Update withdrawal status
      const { error: updateError } = await supabase
        .from("withdrawal_requests")
        .update({
          status: "rejected",
          processed_date: new Date().toISOString(),
          notes: reason,
        })
        .eq("id", withdrawalId)

      if (updateError) throw updateError

      // Refund user's available balance
      const { error: userError } = await supabase
        .from("users")
        .update({
          available_balance: supabase.rpc("increment", { x: withdrawal.amount }),
        })
        .eq("id", withdrawal.user_id)

      if (userError) throw userError

      toast({
        title: "Success",
        description: "Withdrawal request rejected",
      })

      return true
    } catch (err) {
      console.error("Error rejecting withdrawal:", err)
      setError("Failed to reject withdrawal")
      toast({
        title: "Error",
        description: "Failed to reject withdrawal",
        variant: "destructive",
      })
      return false
    } finally {
      setLoading(false)
    }
  }

  // Complete withdrawal (admin only)
  const completeWithdrawal = async (withdrawalId: string, referenceId: string): Promise<boolean> => {
    if (!user || !isAdmin()) {
      toast({
        title: "Error",
        description: "You don't have permission to complete withdrawals",
        variant: "destructive",
      })
      return false
    }

    setLoading(true)
    try {
      // Get withdrawal request
      const { data: withdrawalData, error: withdrawalError } = await supabase
        .from("withdrawal_requests")
        .select("*")
        .eq("id", withdrawalId)
        .single()

      if (withdrawalError) throw withdrawalError

      const withdrawal = withdrawalData as WithdrawalRequest

      // Update withdrawal status
      const { error: updateError } = await supabase
        .from("withdrawal_requests")
        .update({
          status: "completed",
          processed_date: new Date().toISOString(),
          notes: `Completed with reference: ${referenceId}`,
        })
        .eq("id", withdrawalId)

      if (updateError) throw updateError

      // Create withdrawal transaction
      const transaction = {
        id: uuidv4(),
        user_id: withdrawal.user_id,
        event_id: null,
        registration_id: null,
        amount: withdrawal.amount,
        fee_amount: withdrawal.fee_amount,
        net_amount: withdrawal.net_amount,
        transaction_type: "withdrawal",
        status: "completed",
        payment_method: null,
        payment_gateway_id: null,
        transaction_date: new Date().toISOString(),
        description: `Withdrawal to ${withdrawal.bank_name} account ending in ${withdrawal.account_number.slice(-4)}`,
        reference_id: referenceId,
      } as Transaction

      const { error: transactionError } = await supabase.from("transactions").insert([transaction])

      if (transactionError) throw transactionError

      toast({
        title: "Success",
        description: "Withdrawal marked as completed",
      })

      return true
    } catch (err) {
      console.error("Error completing withdrawal:", err)
      setError("Failed to complete withdrawal")
      toast({
        title: "Error",
        description: "Failed to complete withdrawal",
        variant: "destructive",
      })
      return false
    } finally {
      setLoading(false)
    }
  }

  const value = {
    loading,
    error,
    recordPayment,
    requestWithdrawal,
    getTransactions,
    getWithdrawalRequests,
    getUserEarnings,
    approveWithdrawal,
    rejectWithdrawal,
    completeWithdrawal,
  }

  return <FinancialContext.Provider value={value}>{children}</FinancialContext.Provider>
}

export function useFinancial() {
  const context = useContext(FinancialContext)
  if (context === undefined) {
    throw new Error("useFinancial must be used within a FinancialProvider")
  }
  return context
}
