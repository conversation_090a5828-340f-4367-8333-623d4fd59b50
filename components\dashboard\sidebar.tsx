"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import {
  BarChart3,
  CalendarDays,
  ChevronLeft,
  CreditCard,
  FileText,
  KeyRound,
  LayoutDashboard,
  LogOut,
  Shield,
  Ticket,
  User,
  Users,
  Activity,
  BookOpen,
  Wallet,
  Settings,
  Award,
} from "lucide-react"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useAuth } from "@/contexts/auth-context"

interface SidebarProps {
  className?: string
  isCollapsed: boolean
  toggleSidebar: () => void
  isMobile: boolean
}

export function DashboardSidebar({
  className,
  isCollapsed,
  toggleSidebar,
  isMobile
}: SidebarProps) {
  const pathname = usePathname()
  const { user, loading, logout, isAdmin: checkIsAdmin, isManager } = useAuth()

  // Use the auth context's admin check functions for consistency
  const isAdmin = checkIsAdmin()
  const isManagerUser = isManager()

  // Log user role information for debugging
  console.log("Sidebar - User role_name:", user?.role_name)
  console.log("Sidebar - User role_id:", user?.role_id)
  console.log("Sidebar - Is admin:", isAdmin)
  console.log("Sidebar - Is manager:", isManagerUser)
  console.log("Sidebar - Loading state:", loading)
  console.log("Sidebar - Full user object:", user)

  // Check if user is regular user (role: "user")
  const isRegularUser = user?.role_name === "user"

  // General navigation items - filtered based on user role
  const generalNavItems = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      title: "My Tickets",
      href: "/dashboard/my-tickets",
      icon: Ticket,
    },
    // Only show these items for non-regular users (managers, admins, etc.)
    ...(isRegularUser ? [] : [
      {
        title: "Wallet",
        href: "/dashboard/wallet",
        icon: Wallet,
      },
      {
        title: "Analytics",
        href: "/dashboard/analytics",
        icon: BarChart3,
      },
      {
        title: "Reports",
        href: "/dashboard/reports",
        icon: FileText,
      },
    ]),
  ]

  // Manager navigation items (accessible to manager, supermanager, event_admin, and admin)
  const managerNavItems = [
    {
      title: "Manage Events",
      href: "/dashboard/events",
      icon: CalendarDays,
    },
    {
      title: "Certificate Editor",
      href: "/dashboard/certificates/editor",
      icon: Award,
    },
  ]

  // Admin-only navigation items (only accessible to admin role)
  const adminOnlyNavItems = [
    {
      title: "Manage Users",
      href: "/dashboard/users",
      icon: Users,
    },
    {
      title: "Manage Roles",
      href: "/dashboard/admin/roles",
      icon: Shield,
    },
    {
      title: "Payment Gateways",
      href: "/dashboard/settings/payment-gateways",
      icon: CreditCard,
    },
    {
      title: "Subscription Plans",
      href: "/dashboard/settings/subscriptions",
      icon: BookOpen,
    },
    {
      title: "Subscription Management",
      href: "/dashboard/subscriptions",
      icon: Settings,
    },
    {
      title: "Admin Settings",
      href: "/dashboard/admin/settings",
      icon: Settings,
    },
    {
      title: "Activity Logs",
      href: "/dashboard/activity-logs",
      icon: Activity,
    },
  ]

  // Settings navigation items
  const settingsNavItems = [
    {
      title: "Profile",
      href: "/dashboard/profile",
      icon: User,
    },
    {
      title: "Change Password",
      href: "/dashboard/settings/password",
      icon: KeyRound,
    },
  ]

  const handleLogout = async () => {
    await logout()
    window.location.href = "/"
  }

  return (
    <aside
      className={cn(
        "flex h-screen flex-col border-r bg-white transition-all duration-300 overflow-hidden",
        isCollapsed ? 'w-16' : 'w-64',
        isMobile && 'shadow-lg',
        className
      )}
    >
      {/* Header - Fixed height */}
      <div className="flex h-16 shrink-0 items-center justify-between border-b px-4">
        <Link href="/dashboard" className={cn("flex items-center", isCollapsed && "justify-center")}>
          {!isCollapsed ? (
            <>
              <Image
                src="https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//logo.png"
                alt="mTicket.my logo"
                width={142}
                height={42}
                className="h-8 w-auto object-contain"
                priority
              />
              <span className="sr-only">mTicket.my</span>
            </>
          ) : (
            <span className="text-xl font-bold text-purple-600">m</span>
          )}
        </Link>
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={toggleSidebar}
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          <ChevronLeft className={cn("h-4 w-4 transition-transform", isCollapsed && "rotate-180")} />
        </Button>
      </div>
      {/* Navigation - Scrollable area that takes remaining space but ensures footer visibility */}
      <ScrollArea className="flex-1 py-4">
        <nav className="grid gap-1 px-2">
          <TooltipProvider delayDuration={0}>
            {/* GENERAL Section */}
            {!isCollapsed ? (
              <div className="px-3 py-2 text-xs font-semibold text-muted-foreground">GENERAL</div>
            ) : (
              <div className="h-8"></div>
            )}
            {generalNavItems.map((item) => (
              <Tooltip key={item.href} delayDuration={0}>
                <TooltipTrigger asChild>
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors",
                      pathname === item.href
                        ? "bg-accent text-accent-foreground"
                        : "text-muted-foreground hover:bg-accent hover:text-accent-foreground",
                      isCollapsed && "justify-center",
                    )}
                  >
                    <item.icon className="h-4 w-4" />
                    {!isCollapsed && <span>{item.title}</span>}
                  </Link>
                </TooltipTrigger>
                {isCollapsed && <TooltipContent side="right">{item.title}</TooltipContent>}
              </Tooltip>
            ))}

            {/* MANAGEMENT Section - Shown to manager/admin users, hidden for regular users */}
            {isManagerUser && !isRegularUser && (
              <>
                {!isCollapsed ? (
                  <div className="mt-6 px-3 py-2 text-xs font-semibold text-muted-foreground">MANAGEMENT</div>
                ) : (
                  <div className="h-8 mt-6"></div>
                )}
                {managerNavItems.map((item) => (
                  <Tooltip key={item.href} delayDuration={0}>
                    <TooltipTrigger asChild>
                      <Link
                        href={item.href}
                        className={cn(
                          "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors",
                          pathname === item.href
                            ? "bg-accent text-accent-foreground"
                            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground",
                          isCollapsed && "justify-center",
                        )}
                      >
                        <item.icon className="h-4 w-4" />
                        {!isCollapsed && <span>{item.title}</span>}
                      </Link>
                    </TooltipTrigger>
                    {isCollapsed && <TooltipContent side="right">{item.title}</TooltipContent>}
                  </Tooltip>
                ))}
              </>
            )}

            {/* ADMIN Section - Only shown to admin users */}
            {isAdmin && (
              <>
                {!isCollapsed ? (
                  <div className="mt-6 px-3 py-2 text-xs font-semibold text-muted-foreground">ADMIN</div>
                ) : (
                  <div className="h-8 mt-6"></div>
                )}
                {adminOnlyNavItems.map((item) => (
                  <Tooltip key={item.href} delayDuration={0}>
                    <TooltipTrigger asChild>
                      <Link
                        href={item.href}
                        className={cn(
                          "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors",
                          pathname === item.href
                            ? "bg-accent text-accent-foreground"
                            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground",
                          isCollapsed && "justify-center",
                        )}
                      >
                        <item.icon className="h-4 w-4" />
                        {!isCollapsed && <span>{item.title}</span>}
                      </Link>
                    </TooltipTrigger>
                    {isCollapsed && <TooltipContent side="right">{item.title}</TooltipContent>}
                  </Tooltip>
                ))}
              </>
            )}

            {/* SETTINGS Section */}
            {!isCollapsed ? (
              <div className="mt-6 px-3 py-2 text-xs font-semibold text-muted-foreground">SETTINGS</div>
            ) : (
              <div className="h-8 mt-6"></div>
            )}
            {settingsNavItems.map((item) => (
              <Tooltip key={item.href} delayDuration={0}>
                <TooltipTrigger asChild>
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors",
                      pathname === item.href
                        ? "bg-accent text-accent-foreground"
                        : "text-muted-foreground hover:bg-accent hover:text-accent-foreground",
                      isCollapsed && "justify-center",
                    )}
                  >
                    <item.icon className="h-4 w-4" />
                    {!isCollapsed && <span>{item.title}</span>}
                  </Link>
                </TooltipTrigger>
                {isCollapsed && <TooltipContent side="right">{item.title}</TooltipContent>}
              </Tooltip>
            ))}
          </TooltipProvider>
        </nav>
      </ScrollArea>
      {/* Footer - Fixed height, always visible at bottom */}
      <div className="mt-auto shrink-0 border-t">
        {/* User profile section */}
        <div className={cn(
          "flex items-center gap-3 p-4",
          isCollapsed && "justify-center"
        )}>
          <Avatar className="h-8 w-8" key={`${user?.profile_image_url}-${user?.profile_updated_at || user?.id}`}>
            <AvatarImage
              src={user?.profile_image_url || "/placeholder.svg?height=32&width=32"}
              alt={user?.full_name || "User"}
              onLoad={() => console.log("Sidebar avatar updated:", user?.profile_image_url)}
            />
            <AvatarFallback>{user?.full_name?.substring(0, 2) || "U"}</AvatarFallback>
          </Avatar>
          {!isCollapsed && (
            <div className="grid gap-0.5 text-sm">
              <div className="font-medium">{user?.full_name || "User"}</div>
              <div className="text-xs text-muted-foreground">{user?.email || "<EMAIL>"}</div>
            </div>
          )}
          {!isCollapsed && (
            <Button variant="ghost" size="icon" className="ml-auto h-8 w-8" onClick={handleLogout}>
              <LogOut className="h-4 w-4" />
              <span className="sr-only">Log out</span>
            </Button>
          )}
        </div>

        {/* Logout button for collapsed state */}
        {isCollapsed && (
          <div className="px-4 pb-4">
            <TooltipProvider delayDuration={0}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 mx-auto flex"
                    onClick={handleLogout}
                  >
                    <LogOut className="h-4 w-4" />
                    <span className="sr-only">Log out</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="right">Log out</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        )}
      </div>
    </aside>
  )
}
