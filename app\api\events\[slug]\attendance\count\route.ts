import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"

const supabaseAdmin = getSupabaseAdmin()

interface RouteParams {
  params: { slug: string }
}

/**
 * GET /api/events/[slug]/attendance/count
 * Get attendance count for an event (public endpoint for team QR scanner)
 */
export async function GET(request: Request, { params }: RouteParams) {
  try {
    const { slug } = await params

    // Get event by slug
    const { data: event, error: eventError } = await supabaseAdmin
      .from("events")
      .select("id, title")
      .eq("slug", slug)
      .single()

    if (eventError || !event) {
      return NextResponse.json(
        { error: "Event not found" },
        { status: 404 }
      )
    }

    // Count checked-in attendees
    const { count, error: countError } = await supabaseAdmin
      .from("registrations")
      .select("*", { count: "exact", head: true })
      .eq("event_id", event.id)
      .eq("checked_in", true)

    if (countError) {
      console.error("Error counting attendance:", countError)
      return NextResponse.json(
        { error: "Failed to count attendance" },
        { status: 500 }
      )
    }

    // Count total paid registrations
    const { count: totalCount, error: totalError } = await supabaseAdmin
      .from("registrations")
      .select("*", { count: "exact", head: true })
      .eq("event_id", event.id)
      .in("status", ["registered", "confirmed"])

    if (totalError) {
      console.error("Error counting total registrations:", totalError)
      return NextResponse.json(
        { error: "Failed to count total registrations" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      count: count || 0,
      total: totalCount || 0,
      event: {
        id: event.id,
        title: event.title
      }
    })

  } catch (error: any) {
    console.error("Error in attendance count:", error)
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    )
  }
}
