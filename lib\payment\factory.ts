import type { PaymentRequest, PaymentResponse } from "./types"
import { getPaymentGateway } from "./config"
import { BillplzGateway } from "./gateways/billplz"
import { ToyyibPayGateway } from "./gateways/toyyibpay"
import { ChipGateway } from "./gateways/chip"
import { StripeGateway } from "./gateways/stripe"

// Payment gateway factory
export class PaymentGatewayFactory {
  private static gateways = {
    billplz: new BillplzGateway(),
    toyyibpay: new ToyyibPayGateway(),
    chip: new ChipGateway(),
    stripe: new StripeGateway(),
  }

  static getGateway(type: string) {
    return this.gateways[type as keyof typeof this.gateways]
  }
}

// Create payment with any supported gateway
export async function createPayment(gatewayId: string, paymentRequest: PaymentRequest): Promise<PaymentResponse> {
  try {
    console.log("createPayment: Looking for gateway with ID:", gatewayId)

    // Get payment gateway
    const gateway = await getPaymentGateway(gatewayId)
    console.log("createPayment: Found gateway:", gateway)

    if (!gateway || !gateway.enabled) {
      console.error("createPayment: Gateway not found or not enabled:", { gateway, gatewayId })
      return {
        success: false,
        error: "Payment gateway not found or not enabled",
      }
    }

    // Add the payment gateway ID to the request
    const requestWithGatewayId = {
      ...paymentRequest,
      payment_gateway_id: gatewayId,
    }

    // Get the appropriate gateway implementation
    const gatewayImpl = PaymentGatewayFactory.getGateway(gateway.type)
    if (!gatewayImpl) {
      return {
        success: false,
        error: `Unsupported payment gateway type: ${gateway.type}`,
      }
    }

    // Process payment using the gateway implementation
    return await gatewayImpl.createPayment(gateway, requestWithGatewayId)
  } catch (error: any) {
    console.error("Error creating payment:", error)
    return {
      success: false,
      error: error.message || "Failed to create payment",
    }
  }
}

// Verify payment status
export async function verifyPayment(transactionId: string, gatewayType?: string): Promise<boolean> {
  try {
    if (!gatewayType) {
      // If no gateway type provided, try to determine from transaction ID
      if (transactionId.startsWith('billplz_')) gatewayType = 'billplz'
      else if (transactionId.startsWith('toyyib_')) gatewayType = 'toyyibpay'
      else if (transactionId.startsWith('chip_')) gatewayType = 'chip'
      else if (transactionId.startsWith('stripe_')) gatewayType = 'stripe'
      else {
        console.warn("Cannot determine gateway type from transaction ID:", transactionId)
        return false
      }
    }

    const gatewayImpl = PaymentGatewayFactory.getGateway(gatewayType)
    if (!gatewayImpl || !gatewayImpl.verifyPayment) {
      console.warn("Gateway implementation not found or doesn't support verification:", gatewayType)
      return false
    }

    return await gatewayImpl.verifyPayment(transactionId)
  } catch (error) {
    console.error("Error verifying payment:", error)
    return false
  }
}
