"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, CreditCard, Wallet } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"

export default function WithdrawalPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { user } = useAuth()
  const [amount, setAmount] = useState("")
  const [paymentMethod, setPaymentMethod] = useState("bank")
  const [bankName, setBankName] = useState("")
  const [accountNumber, setAccountNumber] = useState("")
  const [accountName, setAccountName] = useState("")
  const [isLoading, setIsLoading] = useState(false)

  // Calculate available balance
  const availableBalance = user?.available_balance || 9750.0

  // Calculate withdrawal fee (1.5%)
  const withdrawalFee = amount ? Number.parseFloat(amount) * 0.015 : 0

  // Calculate net amount
  const netAmount = amount ? Number.parseFloat(amount) - withdrawalFee : 0

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!amount || Number.parseFloat(amount) <= 0) {
      toast({
        title: "Invalid amount",
        description: "Please enter a valid withdrawal amount",
        variant: "destructive",
      })
      return
    }

    if (Number.parseFloat(amount) > availableBalance) {
      toast({
        title: "Insufficient balance",
        description: "Withdrawal amount exceeds your available balance",
        variant: "destructive",
      })
      return
    }

    if (paymentMethod === "bank" && (!bankName || !accountNumber || !accountName)) {
      toast({
        title: "Missing information",
        description: "Please fill in all bank account details",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    try {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1500))

      toast({
        title: "Withdrawal requested",
        description: `Your withdrawal of RM ${Number.parseFloat(amount).toFixed(2)} has been requested and will be processed within 1-3 business days.`,
      })

      router.push("/dashboard/wallet")
    } catch (error) {
      console.error("Withdrawal error:", error)
      toast({
        title: "Withdrawal failed",
        description: "There was an error processing your withdrawal. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="grid gap-6 p-4 md:p-6">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="icon" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4" />
          <span className="sr-only">Back</span>
        </Button>
        <h1 className="text-2xl font-bold">Withdraw Funds</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Withdrawal Details</CardTitle>
            <CardDescription>Request a withdrawal to your bank account or e-wallet</CardDescription>
          </CardHeader>
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="amount">Withdrawal Amount (RM)</Label>
                <Input
                  id="amount"
                  type="number"
                  placeholder="0.00"
                  min="50"
                  step="0.01"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  required
                />
                <p className="text-sm text-muted-foreground">Minimum withdrawal: RM 50.00</p>
              </div>

              <div className="space-y-2">
                <Label>Payment Method</Label>
                <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod} className="flex flex-col space-y-1">
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="bank" id="bank" />
                    <Label htmlFor="bank" className="flex items-center gap-2 cursor-pointer">
                      <CreditCard className="h-4 w-4" />
                      Bank Transfer
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="ewallet" id="ewallet" />
                    <Label htmlFor="ewallet" className="flex items-center gap-2 cursor-pointer">
                      <Wallet className="h-4 w-4" />
                      E-Wallet
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              {paymentMethod === "bank" && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="bank-name">Bank Name</Label>
                    <Select value={bankName} onValueChange={setBankName} required>
                      <SelectTrigger id="bank-name">
                        <SelectValue placeholder="Select your bank" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="maybank">Maybank</SelectItem>
                        <SelectItem value="cimb">CIMB Bank</SelectItem>
                        <SelectItem value="public">Public Bank</SelectItem>
                        <SelectItem value="rhb">RHB Bank</SelectItem>
                        <SelectItem value="hong-leong">Hong Leong Bank</SelectItem>
                        <SelectItem value="ambank">AmBank</SelectItem>
                        <SelectItem value="bsn">Bank Simpanan Nasional</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="account-number">Account Number</Label>
                    <Input
                      id="account-number"
                      placeholder="Enter your account number"
                      value={accountNumber}
                      onChange={(e) => setAccountNumber(e.target.value)}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="account-name">Account Holder Name</Label>
                    <Input
                      id="account-name"
                      placeholder="Enter account holder name"
                      value={accountName}
                      onChange={(e) => setAccountName(e.target.value)}
                      required
                    />
                  </div>
                </div>
              )}

              {paymentMethod === "ewallet" && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="ewallet-type">E-Wallet Type</Label>
                    <Select required>
                      <SelectTrigger id="ewallet-type">
                        <SelectValue placeholder="Select e-wallet type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="touch-n-go">Touch 'n Go eWallet</SelectItem>
                        <SelectItem value="boost">Boost</SelectItem>
                        <SelectItem value="grabpay">GrabPay</SelectItem>
                        <SelectItem value="shopeepay">ShopeePay</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone-number">Phone Number</Label>
                    <Input id="phone-number" placeholder="Enter your phone number" required />
                  </div>
                </div>
              )}
            </CardContent>
            <CardFooter className="flex flex-col items-start gap-4">
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? "Processing..." : "Request Withdrawal"}
              </Button>
            </CardFooter>
          </form>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Withdrawal Summary</CardTitle>
            <CardDescription>Review your withdrawal details</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Available Balance</span>
                <span className="font-medium">RM {availableBalance.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Withdrawal Amount</span>
                <span className="font-medium">RM {amount ? Number.parseFloat(amount).toFixed(2) : "0.00"}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">System Fee (1.5%)</span>
                <span className="font-medium text-destructive">-RM {withdrawalFee.toFixed(2)}</span>
              </div>
              <div className="border-t pt-2 mt-2">
                <div className="flex justify-between">
                  <span className="font-medium">Net Amount</span>
                  <span className="font-bold">RM {netAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>

            <div className="rounded-lg bg-muted p-4 text-sm">
              <h4 className="font-medium mb-2">Important Notes:</h4>
              <ul className="list-disc pl-4 space-y-1">
                <li>Withdrawals are processed within 1-3 business days.</li>
                <li>A 1.5% system fee is charged for all withdrawals.</li>
                <li>Minimum withdrawal amount is RM 50.00.</li>
                <li>Ensure your bank account details are correct to avoid delays.</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
