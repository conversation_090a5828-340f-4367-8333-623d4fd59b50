"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/hooks/use-toast"
import { ArrowLeft, Plus, Trash } from "lucide-react"

type SubscriptionPlan = {
  id: string
  name: string
  price: number
  description: string
  features: string[]
  is_popular?: boolean
  max_events?: number | null
  max_attendees_per_event?: number | null
  // Feature toggles
  certificates_enabled?: boolean
  attendance_enabled?: boolean
  webhooks_enabled?: boolean
  analytics_enabled?: boolean
  reports_enabled?: boolean
}

// Predefined features that can be selected
const AVAILABLE_FEATURES = [
  "Basic event page",
  "Custom event pages",
  "Custom event pages with branding",
  "Email notifications",
  "Email and SMS notifications",
  "Manual attendance tracking",
  "Secure QR code check-in",
  "Basic analytics",
  "Event analytics",
  "Event reporting",
  "Event certificates",
  "Custom certificates",
  "Priority support",
  "Dedicated support",
  "API & Webhook access",
  "White-label solution",
  "1 event at a time",
  "5 events at a time",
  "Unlimited events",
  "Up to 30 attendees per event",
  "Up to 100 attendees per event",
  "Up to 500 attendees per event",
  "Unlimited attendees",
]

interface SubscriptionPlanFormProps {
  plan?: SubscriptionPlan
  isEdit?: boolean
}

export default function SubscriptionPlanForm({ plan, isEdit = false }: SubscriptionPlanFormProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [formData, setFormData] = useState<SubscriptionPlan>(
    plan || {
      id: "",
      name: "",
      price: 0,
      description: "",
      features: [],
      max_events: null,
      max_attendees_per_event: null,
      certificates_enabled: false,
      attendance_enabled: false,
      webhooks_enabled: false,
      analytics_enabled: false,
      reports_enabled: false,
      is_popular: false,
    }
  )

  // Helper function to get cookie value
  const getCookie = (name: string): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  const handleSave = async () => {
    setIsSubmitting(true)
    try {
      const isUpdate = isEdit && formData.id && formData.id !== ""
      const url = isUpdate
        ? `/api/admin/subscription-plans/${formData.id}`
        : '/api/admin/subscription-plans/create'

      const method = isUpdate ? 'PUT' : 'POST'

      const planData = {
        ...formData,
        id: isUpdate ? formData.id : `plan-${formData.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`,
      }

      const token = getCookie('auth_token')
      if (!token) {
        throw new Error('No authentication token found')
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(planData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to save subscription plan')
      }

      toast({
        title: "Success",
        description: `Subscription plan ${isUpdate ? "updated" : "created"} successfully`,
      })

      router.push('/dashboard/settings/subscriptions')
    } catch (error: any) {
      console.error("Error saving subscription plan:", error)
      toast({
        title: "Error",
        description: error.message || `Failed to ${isEdit ? "update" : "create"} subscription plan`,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleFeatureChange = (index: number, value: string) => {
    const updatedFeatures = [...formData.features]
    updatedFeatures[index] = value
    setFormData({ ...formData, features: updatedFeatures })
  }

  const handleAddFeature = () => {
    setFormData({ ...formData, features: [...formData.features, ""] })
  }

  const handleRemoveFeature = (index: number) => {
    const updatedFeatures = [...formData.features]
    updatedFeatures.splice(index, 1)
    setFormData({ ...formData, features: updatedFeatures })
  }

  const handleFeatureToggle = (feature: string, checked: boolean) => {
    let updatedFeatures = [...formData.features]

    if (checked) {
      // Add feature if not already present
      if (!updatedFeatures.includes(feature)) {
        updatedFeatures.push(feature)
      }
    } else {
      // Remove feature
      updatedFeatures = updatedFeatures.filter(f => f !== feature)
    }

    setFormData({ ...formData, features: updatedFeatures })
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">
          {isEdit ? "Edit" : "Add"} Subscription Plan
        </h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Plan Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="plan-name">Plan Name</Label>
              <Input
                id="plan-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="e.g., Basic, Pro, Enterprise"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="plan-price">Price (RM/month)</Label>
              <Input
                id="plan-price"
                type="number"
                step="0.01"
                value={formData.price}
                onChange={(e) =>
                  setFormData({ ...formData, price: Number.parseFloat(e.target.value) || 0 })
                }
                placeholder="0.00"
              />
            </div>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="plan-description">Description</Label>
            <Textarea
              id="plan-description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Brief description of the plan"
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="max-events">Max Events</Label>
              <Input
                id="max-events"
                type="number"
                value={formData.max_events === null ? "" : formData.max_events}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    max_events: e.target.value === "" ? null : Number.parseInt(e.target.value) || 0,
                  })
                }
                placeholder="Leave empty for unlimited"
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="max-attendees">Max Attendees Per Event</Label>
              <Input
                id="max-attendees"
                type="number"
                value={formData.max_attendees_per_event === null ? "" : formData.max_attendees_per_event}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    max_attendees_per_event: e.target.value === "" ? null : Number.parseInt(e.target.value) || 0,
                  })
                }
                placeholder="Leave empty for unlimited"
              />
            </div>
          </div>

          <div className="grid gap-2">
            <Label>Features</Label>
            <div className="space-y-4">
              {/* Predefined Features */}
              <div>
                <Label className="text-sm font-medium">Common Features</Label>
                <div className="grid grid-cols-2 gap-2 mt-2 max-h-48 overflow-y-auto border rounded-md p-3">
                  {AVAILABLE_FEATURES.map((feature) => (
                    <div key={feature} className="flex items-center space-x-2">
                      <Checkbox
                        id={`feature-${feature}`}
                        checked={formData.features.includes(feature)}
                        onCheckedChange={(checked) => handleFeatureToggle(feature, checked as boolean)}
                      />
                      <Label
                        htmlFor={`feature-${feature}`}
                        className="text-sm font-normal cursor-pointer"
                      >
                        {feature}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Custom Features */}
              <div>
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Custom Features</Label>
                  <Button type="button" variant="outline" size="sm" onClick={handleAddFeature}>
                    <Plus className="h-4 w-4 mr-1" /> Add Custom
                  </Button>
                </div>
                <div className="space-y-2 mt-2">
                  {formData.features
                    .filter(feature => !AVAILABLE_FEATURES.includes(feature))
                    .map((feature) => {
                      const actualIndex = formData.features.indexOf(feature)
                      return (
                        <div key={actualIndex} className="flex items-center gap-2">
                          <Input
                            value={feature}
                            onChange={(e) => handleFeatureChange(actualIndex, e.target.value)}
                            placeholder="Custom feature"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => handleRemoveFeature(actualIndex)}
                          >
                            <Trash className="h-4 w-4" />
                          </Button>
                        </div>
                      )
                    })}
                </div>
              </div>
            </div>
          </div>

          {/* Feature Toggles */}
          <div className="grid gap-4">
            <Label className="text-sm font-medium">Feature Toggles</Label>
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="certificates-enabled"
                  checked={formData.certificates_enabled || false}
                  onCheckedChange={(checked) => setFormData({ ...formData, certificates_enabled: checked })}
                />
                <Label htmlFor="certificates-enabled">Certificates</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="attendance-enabled"
                  checked={formData.attendance_enabled || false}
                  onCheckedChange={(checked) => setFormData({ ...formData, attendance_enabled: checked })}
                />
                <Label htmlFor="attendance-enabled">Attendance Tracking</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="webhooks-enabled"
                  checked={formData.webhooks_enabled || false}
                  onCheckedChange={(checked) => setFormData({ ...formData, webhooks_enabled: checked })}
                />
                <Label htmlFor="webhooks-enabled">Webhook API</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="analytics-enabled"
                  checked={formData.analytics_enabled || false}
                  onCheckedChange={(checked) => setFormData({ ...formData, analytics_enabled: checked })}
                />
                <Label htmlFor="analytics-enabled">Analytics</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="reports-enabled"
                  checked={formData.reports_enabled || false}
                  onCheckedChange={(checked) => setFormData({ ...formData, reports_enabled: checked })}
                />
                <Label htmlFor="reports-enabled">Reports</Label>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="is-popular"
              checked={formData.is_popular || false}
              onCheckedChange={(checked) => setFormData({ ...formData, is_popular: checked })}
            />
            <Label htmlFor="is-popular">Mark as popular plan</Label>
          </div>

          <div className="flex items-center gap-4 pt-4">
            <Button onClick={handleSave} disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save Plan"}
            </Button>
            <Button variant="outline" onClick={() => router.back()}>
              Cancel
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
