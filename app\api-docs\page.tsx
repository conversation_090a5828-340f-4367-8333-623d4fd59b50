import { PageLayout } from "@/components/page-layout"
import { HeroSection } from "@/components/hero-section"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Code, Key, Webhook, Database } from "lucide-react"

export const metadata = {
  title: "API Documentation | mTicket.my",
  description: "Complete API reference and integration guide for mTicket.my platform",
}

export default function APIDocsPage() {
  return (
    <PageLayout>
      {/* Corporate Hero Section */}
      <section className="relative w-full py-20 md:py-32 lg:py-40 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}
          ></div>
        </div>

        <div className="container relative px-4 md:px-6">
          <div className="flex flex-col items-center space-y-8 text-center">
            {/* Main Heading */}
            <div className="space-y-4 max-w-4xl">
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl lg:text-7xl">
                API
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> Documentation</span>
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-gray-300 md:text-2xl leading-relaxed">
                Integrate mTicket.my into your applications with our comprehensive REST API.
              </p>
            </div>
          </div>
        </div>
      </section>

      <div className="container px-4 md:px-6 py-12">
        <div className="mx-auto max-w-7xl">
          <div className="max-w-6xl mx-auto">
            <div className="grid gap-6 md:grid-cols-2 mb-12">
              <Card>
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2">
                    <Key className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle>Authentication</CardTitle>
                  <CardDescription>
                    Secure API access with API keys and tokens
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="bg-muted p-3 rounded-md">
                      <code className="text-sm">Authorization: Bearer YOUR_API_KEY</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      All API requests require authentication using your API key.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2">
                    <Database className="h-6 w-6 text-primary" />
                  </div>
                  <CardTitle>Base URL</CardTitle>
                  <CardDescription>
                    API endpoint for all requests
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="bg-muted p-3 rounded-md">
                      <code className="text-sm">https://api.mticket.my/v1</code>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      All API endpoints are relative to this base URL.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="mb-12">
              <h3 className="text-2xl font-bold mb-6">Available Endpoints</h3>
              <div className="space-y-4">
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">Events</CardTitle>
                      <Badge variant="outline">REST</Badge>
                    </div>
                    <CardDescription>
                      Create, read, update, and delete events
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <Badge className="bg-green-100 text-green-800">GET</Badge>
                        <code>/events</code>
                        <span className="text-muted-foreground">- List all events</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className="bg-blue-100 text-blue-800">POST</Badge>
                        <code>/events</code>
                        <span className="text-muted-foreground">- Create new event</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className="bg-green-100 text-green-800">GET</Badge>
                        <code>/events/&#123;id&#125;</code>
                        <span className="text-muted-foreground">- Get event details</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className="bg-orange-100 text-orange-800">PUT</Badge>
                        <code>/events/&#123;id&#125;</code>
                        <span className="text-muted-foreground">- Update event</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg">Registrations</CardTitle>
                      <Badge variant="outline">REST</Badge>
                    </div>
                    <CardDescription>
                      Manage event registrations and attendees
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center gap-2">
                        <Badge className="bg-green-100 text-green-800">GET</Badge>
                        <code>/events/&#123;id&#125;/registrations</code>
                        <span className="text-muted-foreground">- List registrations</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className="bg-blue-100 text-blue-800">POST</Badge>
                        <code>/events/&#123;id&#125;/registrations</code>
                        <span className="text-muted-foreground">- Create registration</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            <Card className="mb-12">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-2">
                  <Webhook className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Webhooks</CardTitle>
                <CardDescription>
                  Real-time notifications for event updates
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Configure webhooks to receive real-time notifications when events occur in your account.
                </p>
                <div className="space-y-2 text-sm">
                  <div>• <code>event.created</code> - New event created</div>
                  <div>• <code>registration.completed</code> - New registration</div>
                  <div>• <code>payment.successful</code> - Payment completed</div>
                  <div>• <code>attendee.checked_in</code> - Attendee checked in</div>
                </div>
              </CardContent>
            </Card>

            <div className="text-center">
              <h3 className="text-2xl font-bold mb-4">Ready to Integrate?</h3>
              <p className="text-muted-foreground mb-6">
                Get your API key from your dashboard and start building amazing integrations.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="/dashboard"
                  className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90"
                >
                  Get API Key
                </a>
                <a
                  href="/contact"
                  className="inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground"
                >
                  Contact Support
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  )
}
