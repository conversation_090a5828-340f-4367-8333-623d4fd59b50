"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowDownRight, ArrowUpRight, DollarSign, Download, Search, Wallet, Copy, Check, X, Clock, Loader2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { useAuth } from "@/contexts/auth-context"
import { format } from "date-fns"

// Types for withdrawal status
const WITHDRAWAL_STATUS = {
  PENDING: { id: 1, label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
  PROCESSING: { id: 2, label: 'Processing', color: 'bg-blue-100 text-blue-800' },
  COMPLETED: { id: 3, label: 'Completed', color: 'bg-green-100 text-green-800' },
  FAILED: { id: 4, label: 'Failed', color: 'bg-red-100 text-red-800' },
  CANCELLED: { id: 5, label: 'Cancelled', color: 'bg-gray-100 text-gray-800' },
}

// Helper function to get status info
const getStatusInfo = (status: string) => {
  const statusUpper = status.toUpperCase()
  return WITHDRAWAL_STATUS[statusUpper as keyof typeof WITHDRAWAL_STATUS] ||
    { id: 0, label: status, color: 'bg-gray-100 text-gray-800' }
}

// Copy to clipboard function
const copyToClipboard = (text: string, setCopied: (value: boolean) => void) => {
  navigator.clipboard.writeText(text)
  setCopied(true)
  setTimeout(() => setCopied(false), 2000)
}

// Mock transaction data
const mockTransactions = [
  {
    id: "txn-001",
    date: new Date("2023-11-20").toISOString(),
    type: "registration_payment",
    event_id: "1",
    event_name: "Tech Conference 2023",
    amount: 150.0,
    fee_amount: 7.5, // 5% fee
    net_amount: 142.5,
    status: "completed",
    description: "Registration payment for Tech Conference 2023",
  },
  {
    id: "txn-002",
    date: new Date("2023-11-20").toISOString(),
    type: "system_fee",
    event_id: "1",
    event_name: "Tech Conference 2023",
    amount: 7.5,
    fee_amount: 0,
    net_amount: 7.5,
    status: "completed",
    description: "System fee (5%) for Tech Conference 2023 registration",
  },
  {
    id: "txn-003",
    date: new Date("2023-11-19").toISOString(),
    type: "registration_payment",
    event_id: "2",
    event_name: "Digital Marketing Workshop",
    amount: 75.0,
    fee_amount: 3.75, // 5% fee
    net_amount: 71.25,
    status: "completed",
    description: "Registration payment for Digital Marketing Workshop",
  },
  {
    id: "txn-004",
    date: new Date("2023-11-19").toISOString(),
    type: "system_fee",
    event_id: "2",
    event_name: "Digital Marketing Workshop",
    amount: 3.75,
    fee_amount: 0,
    net_amount: 3.75,
    status: "completed",
    description: "System fee (5%) for Digital Marketing Workshop registration",
  },
  {
    id: "txn-005",
    date: new Date("2023-11-18").toISOString(),
    type: "registration_payment",
    event_id: "4",
    event_name: "AI & Machine Learning Summit",
    amount: 200.0,
    fee_amount: 10.0, // 5% fee
    net_amount: 190.0,
    status: "completed",
    description: "Registration payment for AI & Machine Learning Summit",
  },
  {
    id: "txn-006",
    date: new Date("2023-11-18").toISOString(),
    type: "system_fee",
    event_id: "4",
    event_name: "AI & Machine Learning Summit",
    amount: 10.0,
    fee_amount: 0,
    net_amount: 10.0,
    status: "completed",
    description: "System fee (5%) for AI & Machine Learning Summit registration",
  },
  {
    id: "txn-007",
    withdrawal_id: "WDR-********-001",
    date: new Date("2023-11-15").toISOString(),
    type: "withdrawal",
    event_id: null,
    event_name: null,
    amount: 500.0,
    fee_amount: 7.5, // 1.5% fee
    net_amount: 492.5,
    status: "completed",
    status_id: WITHDRAWAL_STATUS.COMPLETED.id,
    description: "Withdrawal to bank account ending in 1234",
    payment_method: "bank",
    bank_name: "Maybank",
    account_number: "**********",
    account_name: "John Doe",
    reference_number: "WDR-REF-***********",
  },
  {
    id: "txn-008",
    date: new Date("2023-11-15").toISOString(),
    type: "system_fee",
    event_id: null,
    event_name: null,
    amount: 7.5,
    fee_amount: 0,
    net_amount: 7.5,
    status: "completed",
    description: "System fee (1.5%) for withdrawal",
  },
  {
    id: "txn-009",
    date: new Date("2023-11-10").toISOString(),
    type: "registration_payment",
    event_id: "1",
    event_name: "Tech Conference 2023",
    amount: 150.0,
    fee_amount: 7.5, // 5% fee
    net_amount: 142.5,
    status: "completed",
    description: "Registration payment for Tech Conference 2023",
  },
  {
    id: "txn-010",
    date: new Date("2023-11-10").toISOString(),
    type: "system_fee",
    event_id: "1",
    event_name: "Tech Conference 2023",
    amount: 7.5,
    fee_amount: 0,
    net_amount: 7.5,
    status: "completed",
    description: "System fee (5%) for Tech Conference 2023 registration",
  },
  {
    id: "txn-011",
    date: new Date("2023-11-05").toISOString(),
    type: "registration_payment",
    event_id: "4",
    event_name: "AI & Machine Learning Summit",
    amount: 200.0,
    fee_amount: 10.0, // 5% fee
    net_amount: 190.0,
    status: "completed",
    description: "Registration payment for AI & Machine Learning Summit",
  },
  {
    id: "txn-012",
    date: new Date("2023-11-05").toISOString(),
    type: "system_fee",
    event_id: "4",
    event_name: "AI & Machine Learning Summit",
    amount: 10.0,
    fee_amount: 0,
    net_amount: 10.0,
    status: "completed",
    description: "System fee (5%) for AI & Machine Learning Summit registration",
  },
  {
    id: "txn-013",
    withdrawal_id: "WDR-********-002",
    date: new Date("2023-11-01").toISOString(),
    type: "withdrawal",
    event_id: null,
    event_name: null,
    amount: 1000.0,
    fee_amount: 15.0, // 1.5% fee
    net_amount: 985.0,
    status: "processing",
    status_id: WITHDRAWAL_STATUS.PROCESSING.id,
    description: "Withdrawal to bank account ending in 5678",
    payment_method: "ewallet",
    ewallet_provider: "Touch 'n Go",
    ewallet_number: "**********",
    reference_number: "WDR-REF-***********",
  },
  {
    id: "txn-014",
    date: new Date("2023-11-01").toISOString(),
    type: "system_fee",
    event_id: null,
    event_name: null,
    amount: 15.0,
    fee_amount: 0,
    net_amount: 15.0,
    status: "completed",
    description: "System fee (1.5%) for withdrawal",
  },
]

// Mock event revenue data
const mockEventRevenue = [
  {
    id: "1",
    name: "Tech Conference 2023",
    total_registrations: 320,
    total_revenue: 48000.0,
    system_fees: 2400.0, // 5% of total revenue
    net_revenue: 45600.0,
  },
  {
    id: "2",
    name: "Digital Marketing Workshop",
    total_registrations: 42,
    total_revenue: 3150.0,
    system_fees: 157.5, // 5% of total revenue
    net_revenue: 2992.5,
  },
  {
    id: "4",
    name: "AI & Machine Learning Summit",
    total_registrations: 180,
    total_revenue: 36000.0,
    system_fees: 1800.0, // 5% of total revenue
    net_revenue: 34200.0,
  },
  {
    id: "6",
    name: "Photography Workshop",
    total_registrations: 20,
    total_revenue: 1000.0,
    system_fees: 50.0, // 5% of total revenue
    net_revenue: 950.0,
  },
  {
    id: "7",
    name: "Blockchain Conference",
    total_registrations: 180,
    total_revenue: 45000.0,
    system_fees: 2250.0, // 5% of total revenue
    net_revenue: 42750.0,
  },
  {
    id: "8",
    name: "UX/UI Design Masterclass",
    total_registrations: 38,
    total_revenue: 3800.0,
    system_fees: 190.0, // 5% of total revenue
    net_revenue: 3610.0,
  },
]

export default function WalletPage() {
  const { user } = useAuth()
  const [searchQuery, setSearchQuery] = useState("")
  const [transactionType, setTransactionType] = useState("all")
  const [dateRange, setDateRange] = useState("all")
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<any>(null)
  const [isWithdrawalModalOpen, setIsWithdrawalModalOpen] = useState(false)
  const [copied, setCopied] = useState<string | null>(null)

  // Calculate total revenue, system fees, and available balance
  const totalRevenue = mockEventRevenue.reduce((sum, event) => sum + event.total_revenue, 0)
  const totalSystemFees = mockEventRevenue.reduce((sum, event) => sum + event.system_fees, 0)
  const totalNetRevenue = mockEventRevenue.reduce((sum, event) => sum + event.net_revenue, 0)

  // Calculate total withdrawals
  const totalWithdrawals = mockTransactions
    .filter((txn) => txn.type === "withdrawal")
    .reduce((sum, txn) => sum + txn.amount, 0)

  // Calculate withdrawal fees
  const withdrawalFees = mockTransactions
    .filter((txn) => txn.type === "system_fee" && txn.description.includes("withdrawal"))
    .reduce((sum, txn) => sum + txn.amount, 0)

  // Calculate available balance
  const availableBalance = totalNetRevenue - totalWithdrawals

  // Filter transactions based on search query, transaction type, and date range
  const filteredTransactions = mockTransactions.filter((txn) => {
    // Filter by search query
    const matchesSearch =
      txn.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (txn.event_name && txn.event_name.toLowerCase().includes(searchQuery.toLowerCase())) ||
      txn.id.toLowerCase().includes(searchQuery.toLowerCase())

    // Filter by transaction type
    const matchesType =
      transactionType === "all" ||
      (transactionType === "income" && txn.type === "registration_payment") ||
      (transactionType === "withdrawal" && txn.type === "withdrawal") ||
      (transactionType === "fee" && txn.type === "system_fee")

    // Filter by date range
    let matchesDate = true
    const txnDate = new Date(txn.date)
    const now = new Date()

    if (dateRange === "today") {
      const today = new Date()
      matchesDate = txnDate.toDateString() === today.toDateString()
    } else if (dateRange === "week") {
      const weekAgo = new Date()
      weekAgo.setDate(now.getDate() - 7)
      matchesDate = txnDate >= weekAgo
    } else if (dateRange === "month") {
      const monthAgo = new Date()
      monthAgo.setMonth(now.getMonth() - 1)
      matchesDate = txnDate >= monthAgo
    } else if (dateRange === "year") {
      const yearAgo = new Date()
      yearAgo.setFullYear(now.getFullYear() - 1)
      matchesDate = txnDate >= yearAgo
    }

    return matchesSearch && matchesType && matchesDate
  })

  return (
    <div className="grid gap-6 p-4 md:p-6">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <h1 className="text-2xl font-bold">Wallet</h1>
        <Link href="/dashboard/wallet/withdrawal">
          <Button>
            <Wallet className="mr-2 h-4 w-4" />
            Withdraw Funds
          </Button>
        </Link>
      </div>

      <div className="grid gap-4 md:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">RM {totalRevenue.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">Before system fees</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">System Fees</CardTitle>
            <ArrowUpRight className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">RM {(totalSystemFees + withdrawalFees).toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">5% on registrations, 1.5% on withdrawals</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Withdrawals</CardTitle>
            <ArrowDownRight className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">RM {totalWithdrawals.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">Total amount withdrawn</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Available Balance</CardTitle>
            <Wallet className="h-4 w-4 text-primary" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">RM {availableBalance.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">Available for withdrawal</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="transactions">
        <TabsList className="grid w-full grid-cols-3 mb-4">
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
          <TabsTrigger value="withdrawals">Withdrawal Requests</TabsTrigger>
          <TabsTrigger value="events">Event Revenue</TabsTrigger>
        </TabsList>
        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Transaction History</CardTitle>
              <CardDescription>View all your financial transactions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between mb-6">
                <div className="relative w-full md:w-[300px]">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Search transactions..."
                    className="w-full pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Select value={transactionType} onValueChange={setTransactionType}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Transaction Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Transactions</SelectItem>
                      <SelectItem value="income">Income</SelectItem>
                      <SelectItem value="withdrawal">Withdrawals</SelectItem>
                      <SelectItem value="fee">System Fees</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={dateRange} onValueChange={setDateRange}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Date Range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Time</SelectItem>
                      <SelectItem value="today">Today</SelectItem>
                      <SelectItem value="week">Last 7 Days</SelectItem>
                      <SelectItem value="month">Last 30 Days</SelectItem>
                      <SelectItem value="year">Last Year</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button variant="outline" size="icon" className="hidden sm:flex">
                    <Download className="h-4 w-4" />
                    <span className="sr-only">Download</span>
                  </Button>
                </div>
              </div>

              <div className="rounded-md border overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead className="hidden md:table-cell">Description</TableHead>
                      <TableHead className="hidden sm:table-cell">Event</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead className="text-right">Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredTransactions.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="h-24 text-center">
                          No transactions found
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredTransactions.map((transaction) => (
                        <TableRow key={transaction.id}>
                          <TableCell>{new Date(transaction.date).toLocaleDateString()}</TableCell>
                          <TableCell className="hidden md:table-cell">{transaction.description}</TableCell>
                          <TableCell className="hidden sm:table-cell">{transaction.event_name || "-"}</TableCell>
                          <TableCell>
                            <TransactionTypeBadge type={transaction.type} />
                          </TableCell>
                          <TableCell className={`text-right font-medium ${getAmountColor(transaction)}`}>
                            {getAmountPrefix(transaction)}
                            RM {transaction.amount.toFixed(2)}
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Event Revenue</CardTitle>
              <CardDescription>Revenue breakdown by event</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Event</TableHead>
                      <TableHead className="hidden sm:table-cell">Registrations</TableHead>
                      <TableHead>Total Revenue</TableHead>
                      <TableHead className="hidden md:table-cell">System Fees (5%)</TableHead>
                      <TableHead className="text-right">Net Revenue</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockEventRevenue.map((event) => (
                      <TableRow key={event.id}>
                        <TableCell className="font-medium">{event.name}</TableCell>
                        <TableCell className="hidden sm:table-cell">{event.total_registrations}</TableCell>
                        <TableCell>RM {event.total_revenue.toFixed(2)}</TableCell>
                        <TableCell className="hidden md:table-cell text-destructive">
                          -RM {event.system_fees.toFixed(2)}
                        </TableCell>
                        <TableCell className="text-right font-medium">RM {event.net_revenue.toFixed(2)}</TableCell>
                      </TableRow>
                    ))}
                    <TableRow className="bg-muted/50">
                      <TableCell className="font-bold">Total</TableCell>
                      <TableCell className="hidden sm:table-cell font-bold">
                        {mockEventRevenue.reduce((sum, event) => sum + event.total_registrations, 0)}
                      </TableCell>
                      <TableCell className="font-bold">RM {totalRevenue.toFixed(2)}</TableCell>
                      <TableCell className="hidden md:table-cell font-bold text-destructive">
                        -RM {totalSystemFees.toFixed(2)}
                      </TableCell>
                      <TableCell className="text-right font-bold">RM {totalNetRevenue.toFixed(2)}</TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="withdrawals" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div>
                  <CardTitle>Withdrawal Requests</CardTitle>
                  <CardDescription>Track your withdrawal request status</CardDescription>
                </div>
                <Link href="/dashboard/wallet/withdrawal">
                  <Button>
                    <Wallet className="mr-2 h-4 w-4" />
                    New Withdrawal
                  </Button>
                </Link>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Withdrawal ID</TableHead>
                      <TableHead>Date Requested</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead className="hidden md:table-cell">Payment Method</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="hidden lg:table-cell">Status ID</TableHead>
                      <TableHead className="text-right">Net Amount</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {mockTransactions
                      .filter(txn => txn.withdrawal_id || (txn.type === 'system_fee' && txn.description.includes('withdrawal')))
                      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                      .map((transaction) => {
                        const isWithdrawal = transaction.type === 'withdrawal';
                        const withdrawalId = isWithdrawal ? transaction.withdrawal_id :
                          mockTransactions.find(t => t.reference_number === transaction.reference_number)?.withdrawal_id;

                        // Skip system fee rows that don't have a matching withdrawal
                        if (!isWithdrawal && !withdrawalId) return null;

                        const statusInfo = getStatusInfo(transaction.status);

                        return (
                          <TableRow
                            key={transaction.id}
                            className="cursor-pointer hover:bg-muted/50"
                            onClick={() => {
                              if (isWithdrawal) {
                                setSelectedWithdrawal(transaction);
                                setIsWithdrawalModalOpen(true);
                              }
                            }}
                          >
                            <TableCell className="font-medium">
                              {withdrawalId || '-'}
                            </TableCell>
                            <TableCell>{new Date(transaction.date).toLocaleDateString()}</TableCell>
                            <TableCell className="font-medium">
                              RM {transaction.amount.toFixed(2)}
                            </TableCell>
                            <TableCell className="hidden md:table-cell">
                              {transaction.payment_method === 'bank' ? 'Bank Transfer' :
                               transaction.payment_method === 'ewallet' ? 'E-Wallet' : 'N/A'}
                            </TableCell>
                            <TableCell>
                              <Badge className={statusInfo.color}>
                                {statusInfo.label}
                              </Badge>
                            </TableCell>
                            <TableCell className="hidden lg:table-cell">
                              {statusInfo.id}
                            </TableCell>
                            <TableCell className="text-right font-medium">
                              {transaction.type === 'system_fee' ? (
                                <span className="text-destructive">-RM {transaction.amount.toFixed(2)}</span>
                              ) : (
                                <span>RM {transaction.net_amount.toFixed(2)}</span>
                              )}
                            </TableCell>
                          </TableRow>
                        );
                      })}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Withdrawal Details Modal */}
        <Dialog open={isWithdrawalModalOpen} onOpenChange={setIsWithdrawalModalOpen}>
          <DialogContent className="sm:max-w-[600px]">
            {selectedWithdrawal && (
              <>
                <DialogHeader>
                  <DialogTitle>Withdrawal Details</DialogTitle>
                  <DialogDescription>
                    Withdrawal ID: {selectedWithdrawal.withdrawal_id}
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Date Requested</p>
                      <p>{format(new Date(selectedWithdrawal.date), 'PPpp')}</p>
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground">Status</p>
                      <div className="mt-1">
                        <Badge className={getStatusInfo(selectedWithdrawal.status).color}>
                          {getStatusInfo(selectedWithdrawal.status).label}
                        </Badge>
                        <span className="ml-2 text-sm text-muted-foreground">
                          (ID: {getStatusInfo(selectedWithdrawal.status).id})
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <h4 className="font-medium mb-2">Transaction Details</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Amount</p>
                        <p>RM {selectedWithdrawal.amount.toFixed(2)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">Fee</p>
                        <p className="text-destructive">-RM {selectedWithdrawal.fee_amount.toFixed(2)}</p>
                      </div>
                      <div className="col-span-2 pt-2 border-t">
                        <p className="text-sm text-muted-foreground">Net Amount</p>
                        <p className="text-lg font-medium">RM {selectedWithdrawal.net_amount.toFixed(2)}</p>
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <h4 className="font-medium mb-2">Payment Details</h4>
                    {selectedWithdrawal.payment_method === 'bank' ? (
                      <div className="space-y-3">
                        <div>
                          <p className="text-sm text-muted-foreground">Payment Method</p>
                          <p>Bank Transfer</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Bank Name</p>
                          <p>{selectedWithdrawal.bank_name}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Account Number</p>
                          <div className="flex items-center gap-2">
                            <span>{selectedWithdrawal.account_number}</span>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => copyToClipboard(selectedWithdrawal.account_number, () => setCopied('account'))}
                            >
                              {copied === 'account' ? (
                                <Check className="h-3.5 w-3.5" />
                              ) : (
                                <Copy className="h-3.5 w-3.5" />
                              )}
                            </Button>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Account Name</p>
                          <p>{selectedWithdrawal.account_name}</p>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-3">
                        <div>
                          <p className="text-sm text-muted-foreground">Payment Method</p>
                          <p>E-Wallet ({selectedWithdrawal.ewallet_provider})</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">E-Wallet Number</p>
                          <div className="flex items-center gap-2">
                            <span>{selectedWithdrawal.ewallet_number}</span>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6"
                              onClick={() => copyToClipboard(selectedWithdrawal.ewallet_number, () => setCopied('ewallet'))}
                            >
                              {copied === 'ewallet' ? (
                                <Check className="h-3.5 w-3.5" />
                              ) : (
                                <Copy className="h-3.5 w-3.5" />
                              )}
                            </Button>
                          </div>
                        </div>
                      </div>
                    )}
                    {selectedWithdrawal.reference_number && (
                      <div className="mt-4 pt-4 border-t">
                        <p className="text-sm text-muted-foreground">Reference Number</p>
                        <div className="flex items-center gap-2">
                          <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold">
                            {selectedWithdrawal.reference_number}
                          </code>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6"
                            onClick={() => copyToClipboard(selectedWithdrawal.reference_number, () => setCopied('reference'))}
                          >
                            {copied === 'reference' ? (
                              <Check className="h-3.5 w-3.5" />
                            ) : (
                              <Copy className="h-3.5 w-3.5" />
                            )}
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>

                  {selectedWithdrawal.status === 'processing' && (
                    <div className="mt-4 p-4 bg-blue-50 rounded-md border border-blue-100">
                      <div className="flex items-center gap-2 text-blue-800">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <p className="font-medium">Your withdrawal is being processed</p>
                      </div>
                      <p className="mt-2 text-sm text-blue-700">
                        This usually takes 1-3 business days. You'll receive a notification once it's completed.
                      </p>
                    </div>
                  )}
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => {
                      // Add cancel withdrawal logic here
                      setIsWithdrawalModalOpen(false);
                    }}
                    disabled={!['pending', 'processing'].includes(selectedWithdrawal.status)}
                  >
                    <X className="mr-2 h-4 w-4" />
                    {selectedWithdrawal.status === 'processing' ? 'Cancel Request' : 'Cancel Withdrawal'}
                  </Button>
                </DialogFooter>
              </>
            )}
          </DialogContent>
        </Dialog>
      </Tabs>
    </div>
  )
}

function TransactionTypeBadge({ type }: { type: string }) {
  switch (type) {
    case "registration_payment":
      return <Badge className="bg-green-100 text-green-800">Income</Badge>
    case "withdrawal":
      return <Badge variant="outline">Withdrawal</Badge>
    case "system_fee":
      return <Badge variant="destructive">System Fee</Badge>
    default:
      return <Badge variant="secondary">{type}</Badge>
  }
}

function getAmountColor(transaction: any) {
  if (transaction.type === "registration_payment") {
    return "text-green-600"
  } else if (transaction.type === "withdrawal" || transaction.type === "system_fee") {
    return "text-red-600"
  }
  return ""
}

function getAmountPrefix(transaction: any) {
  if (transaction.type === "registration_payment") {
    return "+"
  } else if (transaction.type === "withdrawal" || transaction.type === "system_fee") {
    return "-"
  }
  return ""
}
