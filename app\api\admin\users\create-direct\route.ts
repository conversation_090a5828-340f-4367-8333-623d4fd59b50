import { NextResponse } from "next/server";
import { hashPassword, verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth";
import { createClient } from "@supabase/supabase-js";
import crypto from "crypto";

// This endpoint uses direct SQL to insert a user, bypassing RLS policies
export async function POST(request: Request) {
  try {
    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === "development";
    console.log("API users/create-direct - Is development mode:", isDevelopment);

    // Get JWT token from request
    const token = getJWTTokenFromRequest(request);
    console.log("API users/create-direct - Token present:", !!token);

    // In production, verify authentication and admin role
    // In development, allow access without authentication for easier testing
    if (!isDevelopment) {
      if (!token) {
        console.log("API users/create-direct - Access denied: No token provided");
        return NextResponse.json(
          { error: "Unauthorized. Admin access required." },
          { status: 403 }
        );
      }

      // Verify the JWT token and get user data
      const authResult = await verifyJWTToken(token);
      if (!authResult?.user) {
        console.log("API users/create-direct - Access denied: Invalid token");
        return NextResponse.json(
          { error: "Unauthorized. Invalid token." },
          { status: 403 }
        );
      }

      // Check if user has admin role (only "admin" role has full access)
      const userRole = authResult.user.role_name || authResult.user.role;

      if (userRole !== "admin") {
        console.log("API users/create-direct - Access denied: Not admin role. User role:", userRole);
        return NextResponse.json(
          { error: "Unauthorized. Admin access required." },
          { status: 403 }
        );
      }

      console.log("API users/create-direct - Access granted to admin user:", authResult.user.email);
    } else {
      console.log("API users/create-direct - Development mode: Allowing access without authentication");
    }

    const { email, fullName, role, role_id, sendPasswordEmail } = await request.json();

    // Validate input
    if (!email || !fullName) {
      return NextResponse.json(
        { error: "Email and full name are required" },
        { status: 400 }
      );
    }

    // Generate a unique ID for the new user
    const userId = crypto.randomUUID();

    // Generate a temporary random password
    const tempPassword = crypto.randomBytes(12).toString("hex");

    // Hash the password
    const passwordHash = await hashPassword(tempPassword);

    // Create a Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || "";
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    try {
      // Use direct SQL to insert the user
      // This can bypass RLS in some cases
      const { data, error } = await supabase.rpc('execute_sql', {
        sql: `
          INSERT INTO users (
            id, email, password_hash, full_name, role, role_id,
            subscription_status, created_at, events_created,
            total_earnings, available_balance
          ) VALUES (
            '${userId}', '${email}', '${passwordHash}', '${fullName}',
            '${role || "free"}', ${role_id ? `'${role_id}'` : 'NULL'},
            'none', '${new Date().toISOString()}',
            0, 0, 0
          ) RETURNING id, email, full_name, role, role_id;
        `
      });

      if (error) {
        console.error("Error executing SQL:", error);
        return NextResponse.json(
          { error: `Database error: ${error.message}` },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        user: {
          id: userId,
          email,
          full_name: fullName,
          role: role || "free",
          role_id: role_id || null,
        },
        tempPassword: sendPasswordEmail ? null : tempPassword,
        message: sendPasswordEmail
          ? "User created successfully. Password reset email will be sent."
          : "User created successfully with temporary password.",
      });
    } catch (dbError: any) {
      console.error("Database error:", dbError);
      return NextResponse.json(
        { error: dbError.message || "Failed to create user in database" },
        { status: 500 }
      );
    }
  } catch (error: any) {
    console.error("Error in direct user creation:", error);
    return NextResponse.json(
      { error: error.message || "Failed to create user" },
      { status: 500 }
    );
  }
}
