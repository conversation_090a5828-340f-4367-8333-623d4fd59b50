# Quick Start Guide

Get up and running with mTicket.my in just 5 minutes!

## 🎯 Goal
By the end of this guide, you'll have:
- Created your account
- Set up your first event
- Configured basic payment settings
- Published your event for registrations

## Step 1: Create Your Account (2 minutes)

1. **Visit the Registration Page**
   - Go to `/auth/register`
   - Choose your subscription plan (Free plan available)

2. **Fill in Your Details**
   - Email address
   - Full name
   - Password (minimum 8 characters)
   - Phone number (optional)

3. **Verify Your Email**
   - Check your email for verification link
   - Click to activate your account

## Step 2: Set Up Your Organization (1 minute)

1. **Go to Profile Settings**
   - Navigate to Dashboard → Profile
   - Click on "Organization" tab

2. **Create or Link Organization**
   - Create new organization with your company details
   - Or search and link to existing organization

## Step 3: Create Your First Event (2 minutes)

1. **Navigate to Events**
   - Go to Dashboard → Events
   - Click "Create Event"

2. **Basic Event Information**
   ```
   Title: Your Event Name
   Description: Brief description of your event
   Date & Time: When your event will happen
   Location: Where it will be held
   ```

3. **Set Ticket Types**
   - Add ticket types (Free, Early Bird, Standard, VIP)
   - Set prices and quantities
   - Configure sale periods

4. **Upload Event Image**
   - Add an attractive event banner
   - Recommended size: 1200x600px

## Step 4: Configure Payment (Optional)

If you have paid tickets:

1. **Go to Payment Settings**
   - Dashboard → Settings → Payment Gateways

2. **Enable Payment Gateway**
   - Choose from ToyyibPay, Billplz, Chip, or Stripe
   - Enter your API credentials
   - Test the connection

## Step 5: Publish Your Event

1. **Review Event Details**
   - Check all information is correct
   - Preview how it will look to attendees

2. **Publish**
   - Toggle "Published" status to ON
   - Your event is now live!

## 🎉 Congratulations!

Your event is now live and ready for registrations. Here's what happens next:

### For Attendees
- They can find your event on the homepage
- Register and make payments
- Receive digital tickets with QR codes

### For You
- Monitor registrations in real-time
- Track payments and revenue
- Generate certificates for attendees
- Use QR scanner for check-ins

## Next Steps

### Immediate Actions
- [ ] Share your event link with potential attendees
- [ ] Set up certificate templates (if needed)
- [ ] Configure team access for check-ins
- [ ] Test the registration flow yourself

### Learn More
- [Complete Event Management Guide](../features/event-management.md)
- [Payment System Setup](../architecture/payment-system.md)
- [Certificate Configuration](../features/certificate-system.md)
- [Team QR Scanner Setup](../features/team-qr-scanner.md)

## Need Help?

- **User Guide**: [Complete documentation](../guides/user-guide.md)
- **API Reference**: [For developers](../api/)
- **Support**: Contact our team for assistance

---

**🚀 Ready to create amazing events? Start with Step 1 above!**
