"use client"

import { useState } from "react"
import { Check, CreditCard } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"

export function SubscriptionUpgrade() {
  const { user, upgradeSubscription } = useAuth()
  const [isProcessing, setIsProcessing] = useState(false)
  const { toast } = useToast()

  const handleUpgrade = async () => {
    setIsProcessing(true)
    try {
      // In a real app, this would redirect to a payment page
      // For demo purposes, we'll just call the upgradeSubscription function
      const result = await upgradeSubscription()

      if (result) {
        toast({
          title: "Success",
          description: "Subscription upgraded successfully",
        })
      }
    } catch (error) {
      console.error("Error upgrading subscription:", error)
      toast({
        title: "Error",
        description: "Failed to upgrade subscription",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  const isFreeUser = user?.role === "free"
  const isPaidUser = user?.role === "paid" && user?.subscription_status === "active"

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Upgrade to Premium</CardTitle>
        <CardDescription>
          {isFreeUser
            ? "Unlock unlimited events and participants"
            : isPaidUser
              ? "You are currently on the Premium plan"
              : "Renew your Premium subscription"}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="rounded-lg bg-muted p-4">
          <div className="flex items-center justify-between">
            <span className="text-lg font-bold">Premium Plan</span>
            <span className="text-xl font-bold">RM 25.00/month</span>
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-start">
            <Check className="mr-2 h-5 w-5 text-green-500" />
            <span>Create unlimited events</span>
          </div>
          <div className="flex items-start">
            <Check className="mr-2 h-5 w-5 text-green-500" />
            <span>No participant limit</span>
          </div>
          <div className="flex items-start">
            <Check className="mr-2 h-5 w-5 text-green-500" />
            <span>Access to premium certificate templates</span>
          </div>
          <div className="flex items-start">
            <Check className="mr-2 h-5 w-5 text-green-500" />
            <span>Advanced analytics and reporting</span>
          </div>
          <div className="flex items-start">
            <Check className="mr-2 h-5 w-5 text-green-500" />
            <span>Priority support</span>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button className="w-full" disabled={isProcessing || isPaidUser} onClick={handleUpgrade}>
          {isProcessing ? (
            "Processing..."
          ) : isPaidUser ? (
            "Current Plan"
          ) : (
            <>
              <CreditCard className="mr-2 h-4 w-4" />
              {isFreeUser ? "Upgrade Now" : "Renew Subscription"}
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  )
}
