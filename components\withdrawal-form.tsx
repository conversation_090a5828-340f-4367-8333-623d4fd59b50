"use client"

import { useState } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { CreditCard, DollarSign } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { useToast } from "@/hooks/use-toast"
import { useFinancial } from "@/contexts/financial-context"
import { useSettings } from "@/contexts/settings-context"
import { useAuth } from "@/contexts/auth-context"

// Form schema
const formSchema = z.object({
  amount: z.coerce.number().min(1, { message: "Amount must be greater than 0" }),
  bank_name: z.string().min(2, { message: "Bank name is required" }),
  account_number: z.string().min(5, { message: "Valid account number is required" }),
  account_holder: z.string().min(3, { message: "Account holder name is required" }),
})

type FormValues = z.infer<typeof formSchema>

export function WithdrawalForm() {
  const { requestWithdrawal } = useFinancial()
  const { settings, calculateWithdrawalFee } = useSettings()
  const { user } = useAuth()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      amount: 0,
      bank_name: "",
      account_number: "",
      account_holder: "",
    },
  })

  // Watch amount field to calculate fee
  const amount = form.watch("amount") || 0
  const feeAmount = calculateWithdrawalFee(amount)
  const netAmount = amount - feeAmount

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to request a withdrawal",
        variant: "destructive",
      })
      return
    }

    // Check if amount is greater than available balance
    if (values.amount > user.available_balance) {
      toast({
        title: "Error",
        description: "Withdrawal amount exceeds available balance",
        variant: "destructive",
      })
      return
    }

    // Check minimum withdrawal amount
    if (values.amount < (settings?.min_withdrawal_amount || 0)) {
      toast({
        title: "Error",
        description: `Minimum withdrawal amount is RM ${settings?.min_withdrawal_amount}`,
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)
    try {
      const result = await requestWithdrawal(values.amount, {
        bank_name: values.bank_name,
        account_number: values.account_number,
        account_holder: values.account_holder,
      })

      if (result) {
        form.reset()
        toast({
          title: "Success",
          description: "Withdrawal request submitted successfully",
        })
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Request Withdrawal</CardTitle>
        <CardDescription>Withdraw your earnings to your bank account</CardDescription>
      </CardHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <CardContent className="space-y-6">
            <div className="rounded-lg bg-muted p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Available Balance</span>
                <span className="text-lg font-bold">RM {user?.available_balance.toFixed(2) || "0.00"}</span>
              </div>
              <div className="mt-2 text-xs text-muted-foreground">
                Minimum withdrawal: RM {settings?.min_withdrawal_amount || "50.00"}
              </div>
            </div>

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Withdrawal Amount (MYR)</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <DollarSign className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                      <Input type="number" min="0" step="0.01" className="pl-10" {...field} />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Fee: RM {feeAmount.toFixed(2)} ({settings?.withdrawal_fee_percentage || 1.5}%)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="rounded-lg bg-muted p-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">You will receive</span>
                <span className="text-lg font-bold">RM {netAmount.toFixed(2)}</span>
              </div>
            </div>

            <FormField
              control={form.control}
              name="bank_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bank Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter bank name" {...field} value={field.value || ""} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="account_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Account Number</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <CreditCard className="absolute left-3 top-2.5 h-5 w-5 text-muted-foreground" />
                      <Input placeholder="Enter account number" className="pl-10" {...field} value={field.value || ""} />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="account_holder"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Account Holder Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter account holder name" {...field} value={field.value || ""} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter>
            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? "Processing..." : "Request Withdrawal"}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  )
}
