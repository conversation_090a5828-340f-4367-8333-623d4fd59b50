import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";

/**
 * GET /api/payment-gateways/public
 * Fetches enabled payment gateways for public use (event registration)
 * This is a public endpoint - no authentication required
 */
export async function GET() {
  try {
    console.log("Public Payment Gateways API: Fetching enabled payment gateways");

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin();

    // Fetch only enabled payment gateways, ordered by display_order
    const { data, error } = await supabaseAdmin
      .from("payment_gateway_settings")
      .select("id, gateway_name, is_enabled, configuration, display_order")
      .eq("is_enabled", true)
      .order("display_order", { ascending: true });

    if (error) {
      console.error("Error fetching payment gateways:", error);
      return NextResponse.json(
        { error: "Failed to fetch payment gateways" },
        { status: 500 }
      );
    }

    // Transform the data to match the expected format for the frontend
    const transformedGateways = data.map((gateway) => ({
      id: gateway.id,
      name: gateway.gateway_name,
      type: gateway.gateway_name.toLowerCase().replace(/\s+/g, ''), // Generate type from name
      description: gateway.configuration?.description || `Pay with ${gateway.gateway_name}`,
      logo_url: gateway.configuration?.logo_url || null,
      enabled: gateway.is_enabled,
    }));

    console.log(`Public Payment Gateways API: Found ${transformedGateways.length} enabled gateways`);

    return NextResponse.json({
      success: true,
      gateways: transformedGateways,
    });
  } catch (error) {
    console.error("Error in public payment gateways API:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
