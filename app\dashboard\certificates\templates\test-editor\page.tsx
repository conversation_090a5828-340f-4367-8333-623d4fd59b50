"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CertificateTemplateEditor } from "@/components/certificate-template-editor"
import { useToast } from "@/hooks/use-toast"

export default function TestEditorPage() {
  const [activeEditor, setActiveEditor] = useState("v1")
  const { toast } = useToast()

  const sampleTemplate = {
    id: "test-template",
    name: "Test Template",
    description: "A test template for comparison",
    orientation: "landscape" as const,
    background_color: "#f8fafc",
    background_image_url: "",
    show_frame: true,
    fields: [
      {
        id: "field-1",
        type: "text" as const,
        label: "Title",
        x: 100,
        y: 50,
        width: 300,
        height: 40,
        fontSize: 24,
        fontWeight: "bold",
        color: "#2b6cb0",
        textAlign: "center",
        content: "Certificate of Achievement"
      },
      {
        id: "field-2",
        type: "text" as const,
        label: "Participant Name",
        x: 150,
        y: 200,
        width: 400,
        height: 50,
        fontSize: 32,
        fontWeight: "bold",
        color: "#2d3748",
        textAlign: "center",
        content: "{{participant_name}}"
      },
      {
        id: "field-3",
        type: "text" as const,
        label: "Event Name",
        x: 200,
        y: 300,
        width: 300,
        height: 30,
        fontSize: 18,
        fontWeight: "normal",
        color: "#4a5568",
        textAlign: "center",
        content: "{{event_name}}"
      },
      {
        id: "qr-verification",
        type: "qr" as const,
        label: "Verification QR Code",
        x: 650,
        y: 450,
        width: 100,
        height: 100,
        content: "{{verification_url}}"
      }
    ]
  }

  const handleSave = (template: any) => {
    console.log("Saving template:", template)
    toast({
      title: "Template Saved",
      description: `Template "${template.name}" has been saved successfully.`,
    })
  }

  const handleCancel = () => {
    toast({
      title: "Cancelled",
      description: "Template editing was cancelled.",
    })
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Certificate Template Editor Comparison</h1>
        <p className="text-muted-foreground">
          Compare the original react-dnd implementation with the improved @dnd-kit version
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>New Features & Improvements</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="space-y-3">
              <h3 className="font-semibold text-lg">@dnd-kit Implementation</h3>
              <ul className="space-y-1 text-sm">
                <li>✅ Excellent touch device support with configurable activation</li>
                <li>✅ Better performance with optimized renders</li>
                <li>✅ Responsive canvas scaling</li>
                <li>✅ Built-in accessibility features</li>
                <li>✅ Drag overlay for smooth visual feedback</li>
                <li>✅ HTML/Visual editor synchronization</li>
                <li>✅ Real-time bidirectional editing</li>
                <li>✅ Automatic HTML generation from visual fields</li>
                <li>✅ Enhanced HTML parsing (any positioned elements)</li>
                <li>✅ Raw HTML Mode for pure HTML editing</li>
                <li>✅ Auto-formatting and syntax highlighting</li>
                <li>✅ Sample HTML templates and utility buttons</li>
              </ul>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-900 mb-2">Key Feature: Enhanced HTML Editing</h4>
              <p className="text-sm text-blue-800">
                Full HTML editing capabilities with automatic field parsing. Paste any HTML with positioning styles
                and it will be converted to draggable fields. Raw HTML Mode allows working with pure HTML without field conversion.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeEditor} onValueChange={setActiveEditor}>
        <TabsContent value="v1" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>New @dnd-kit Implementation</CardTitle>
              <p className="text-sm text-muted-foreground">
                Using @dnd-kit with HTML/Visual synchronization and better performance
              </p>
            </CardHeader>
            <CardContent>
              <CertificateTemplateEditor
                template={sampleTemplate}
                onSave={handleSave}
                onCancel={handleCancel}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-semibold">Visual Editor Testing:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Drag fields around the canvas and observe smooth movement</li>
              <li>Add new fields from the toolbar by dragging to canvas</li>
              <li>Select fields and edit properties in the right panel</li>
              <li>Test background color picker and image upload</li>
              <li>Try to delete QR code (should be prevented)</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h4 className="font-semibold">Image Field Testing:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Drag an "Image/Logo" field from the toolkit to the canvas</li>
              <li>Select the image field and observe the upload interface in properties panel</li>
              <li>Upload an image file (JPG, PNG, GIF) and watch compression/upload process</li>
              <li>Verify upload progress indicators and success messages</li>
              <li>Test changing the image by clicking "Change Image" button</li>
              <li>Test removing the image using the trash button</li>
              <li>Verify uploaded images appear in both visual and HTML editors</li>
              <li>Test resizing image fields and observe how images scale</li>
              <li>Check that images are properly stored and accessible after save</li>
            </ul>

            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h5 className="font-medium text-blue-900 mb-2">Quick Upload Test</h5>
              <p className="text-sm text-blue-700 mb-3">
                Test the upload functionality with a sample image:
              </p>
              <button
                onClick={async () => {
                  try {
                    // Create a simple test image (1x1 pixel PNG)
                    const canvas = document.createElement('canvas');
                    canvas.width = 100;
                    canvas.height = 100;
                    const ctx = canvas.getContext('2d');
                    if (ctx) {
                      ctx.fillStyle = '#4F46E5';
                      ctx.fillRect(0, 0, 100, 100);
                      ctx.fillStyle = 'white';
                      ctx.font = '12px Arial';
                      ctx.fillText('TEST', 35, 55);
                    }

                    canvas.toBlob(async (blob) => {
                      if (!blob) return;

                      const file = new File([blob], 'test-image.png', { type: 'image/png' });
                      console.log('Created test file:', file);

                      // Test the upload function
                      const { compressCertificateImage, isValidImageFile } = await import("@/lib/image-compression");

                      if (!isValidImageFile(file)) {
                        alert('Invalid file type');
                        return;
                      }

                      console.log('Compressing test image...');
                      const compressedFile = await compressCertificateImage(file);
                      console.log('Compressed:', { original: file.size, compressed: compressedFile.size });

                      // Get auth token
                      const getCookie = (name: string): string | null => {
                        const value = `; ${document.cookie}`;
                        const parts = value.split(`; ${name}=`);
                        if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
                        return null;
                      };

                      const token = getCookie('auth_token');
                      if (!token) {
                        alert('No auth token found');
                        return;
                      }

                      console.log('Uploading test image...');
                      const formData = new FormData();
                      formData.append('file', compressedFile);
                      formData.append('type', 'background');

                      const response = await fetch('/api/upload/certificate-image', {
                        method: 'POST',
                        headers: {
                          'Authorization': `Bearer ${token}`,
                        },
                        body: formData,
                      });

                      console.log('Upload response status:', response.status);

                      if (response.ok) {
                        const result = await response.json();
                        console.log('Upload successful:', result);
                        alert(`Upload successful! URL: ${result.url}`);
                      } else {
                        const errorText = await response.text();
                        console.error('Upload failed:', errorText);
                        alert(`Upload failed: ${errorText}`);
                      }
                    }, 'image/png');
                  } catch (error) {
                    console.error('Test failed:', error);
                    alert(`Test failed: ${error}`);
                  }
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
              >
                Test Upload API
              </button>
            </div>
          </div>
          <div className="space-y-2">
            <h4 className="font-semibold">Text Field Content Testing:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Select text fields and use content dropdown (participant, IC, event, etc.)</li>
              <li>Choose "Custom Text" option and enter custom content</li>
              <li>Test different placeholder variables like {`{{participant_name}}`}</li>
              <li>Verify content changes reflect in both visual and HTML editors</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h4 className="font-semibold">Background Testing:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Change background color using color picker</li>
              <li>Upload background image and watch compression/upload process</li>
              <li>Verify upload progress indicators and success messages</li>
              <li>Test combination of background color and image</li>
              <li>Verify background changes sync to HTML editor</li>
              <li>Check that background images are properly stored after save</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h4 className="font-semibold">Frame Toggle Testing:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Toggle the "White Frame Border" checkbox in background settings</li>
              <li>Observe the white decorative frame appearing/disappearing around certificate content</li>
              <li>Test with different background colors and images</li>
              <li>Verify frame toggle state is preserved when switching between tabs</li>
              <li>Check that frame setting is included in saved template data</li>
              <li>Test frame toggle with both landscape and portrait orientations</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h4 className="font-semibold">QR Code Testing:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Verify QR code is automatically added to new templates</li>
              <li>Try to delete QR code (should show warning and prevent deletion)</li>
              <li>Drag QR code to different positions (should work)</li>
              <li>Verify QR code appears in HTML with verification URL placeholder</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h4 className="font-semibold">HTML Editor Testing:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Switch to HTML Editor tab and observe generated HTML</li>
              <li>Click "Load Sample" to see sample HTML with positioned elements</li>
              <li>Paste custom HTML with position:absolute styles and see fields created</li>
              <li>Enable "Raw HTML Mode" and paste complex HTML without field conversion</li>
              <li>Test auto-formatting and syntax highlighting in Monaco Editor</li>
              <li>Verify changes in HTML editor sync back to visual editor (when not in raw mode)</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h4 className="font-semibold">Raw HTML Mode Testing:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Enable Raw HTML Mode checkbox and observe UI changes</li>
              <li>Paste complex HTML with CSS classes and nested elements</li>
              <li>Verify visual preview shows rendered HTML instead of draggable fields</li>
              <li>Test "Convert to Fields" button (should create fields and switch to Visual Editor)</li>
              <li>Test saving in Raw HTML Mode (should allow without strict QR validation)</li>
              <li>Disable Raw HTML Mode and see HTML automatically parsed back to fields</li>
              <li>Verify conversion feedback messages appear correctly</li>
            </ul>
          </div>
          <div className="space-y-2">
            <h4 className="font-semibold">Save Functionality Testing:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Test save button with empty template name (should show error)</li>
              <li>Test save with valid template name (should show success)</li>
              <li>Verify all template data is included in save (fields, background, etc.)</li>
              <li>Check console for saved template data structure</li>
              <li>Test saving in both normal and Raw HTML modes</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
