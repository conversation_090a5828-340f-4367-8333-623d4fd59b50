"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { useParams } from "next/navigation"
import { ArrowLeft, Users, CheckCircle2, Clock, RefreshCw, QrCode } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"
import { QrScanner } from "@/components/qr-scanner"
import { TeamManagement } from "@/components/team-management"

interface AttendeeData {
  id: string
  attendee_name: string
  attendee_email: string
  attendee_phone?: string
  ticket_type?: string
  checked_in: boolean
  checked_in_at?: string
  payment_status: string
  created_at: string
}

export default function QRAttendancePage() {
  const params = useParams()
  const { toast } = useToast()
  const [event, setEvent] = useState<any>(null)
  const [attendees, setAttendees] = useState<AttendeeData[]>([])
  const [checkedInAttendees, setCheckedInAttendees] = useState<AttendeeData[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const eventSlug = params.slug as string

  // Fetch event and attendees data
  const fetchData = async () => {
    try {
      // Fetch event details
      const { data: eventData, error: eventError } = await supabase
        .from("events")
        .select("*")
        .eq("slug", eventSlug)
        .single()

      if (eventError) {
        console.error("Error fetching event:", eventError)
        toast({
          title: "Error",
          description: "Failed to fetch event details",
          variant: "destructive",
        })
        return
      }

      setEvent(eventData)

      // Fetch attendees
      const { data: attendeesData, error: attendeesError } = await supabase
        .from("registrations")
        .select("*")
        .eq("event_id", eventData.id)
        .eq("payment_status", "paid")
        .order("created_at", { ascending: false })

      if (attendeesError) {
        console.error("Error fetching attendees:", attendeesError)
        toast({
          title: "Error",
          description: "Failed to fetch attendees data",
          variant: "destructive",
        })
        return
      }

      setAttendees(attendeesData || [])
      setCheckedInAttendees((attendeesData || []).filter(a => a.checked_in))
    } catch (error) {
      console.error("Error fetching data:", error)
      toast({
        title: "Error",
        description: "Failed to fetch data",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  useEffect(() => {
    if (eventSlug) {
      fetchData()
    }
  }, [eventSlug])

  // Handle attendance marked from QR scanner
  const handleAttendanceMarked = (attendeeData: any) => {
    // Refresh the data to show updated attendance
    setRefreshing(true)
    fetchData()
  }

  // Manual refresh function
  const handleRefresh = () => {
    setRefreshing(true)
    fetchData()
  }

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6 px-4">
        <div className="mb-6">
          <Skeleton className="h-8 w-40" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Skeleton className="h-96" />
          <Skeleton className="h-96" />
        </div>
      </div>
    )
  }

  if (!event) {
    return (
      <div className="container mx-auto py-6 px-4">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-2">Event Not Found</h2>
          <p className="text-muted-foreground mb-6">The event you're looking for doesn't exist.</p>
          <Link href="/dashboard/events">
            <Button>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Events
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 px-4">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between">
        <Link href={`/dashboard/events/${eventSlug}`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Event
          </Button>
        </Link>
        <Button variant="outline" size="sm" onClick={handleRefresh} disabled={refreshing}>
          <RefreshCw className={`mr-2 h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Page Title */}
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">QR Attendance Scanner</h1>
        <p className="text-muted-foreground">
          Scan attendee QR codes to mark attendance for <span className="font-medium">{event.title}</span>
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Attendees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{attendees.length}</div>
            <p className="text-xs text-muted-foreground">Paid registrations</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Checked In</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{checkedInAttendees.length}</div>
            <p className="text-xs text-muted-foreground">
              {attendees.length > 0 ? Math.round((checkedInAttendees.length / attendees.length) * 100) : 0}% attendance rate
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{attendees.length - checkedInAttendees.length}</div>
            <p className="text-xs text-muted-foreground">Not yet checked in</p>
          </CardContent>
        </Card>
      </div>

      {/* Team Management Section */}
      <div className="mb-6">
        <TeamManagement
          eventSlug={eventSlug}
          eventId={event.id}
          eventTitle={event.title}
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* QR Scanner Section */}
        <div className="space-y-4">
          <Card className="border-2 border-primary/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <div className="p-2 rounded-lg bg-primary/10">
                  <QrCode className="h-5 w-5 text-primary" />
                </div>
                QR Code Scanner
              </CardTitle>
              <CardDescription>
                Position the QR code within the camera frame to scan and mark attendance
              </CardDescription>
            </CardHeader>
            <CardContent>
              {event?.id && (
                <QrScanner
                  eventId={event.id}
                  onAttendanceMarked={handleAttendanceMarked}
                />
              )}
            </CardContent>
          </Card>
        </div>

        {/* Checked-in Attendees Table */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Check-ins</CardTitle>
              <CardDescription>
                Latest attendees who have been checked in
              </CardDescription>
            </CardHeader>
            <CardContent>
              {checkedInAttendees.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <CheckCircle2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No attendees checked in yet</p>
                  <p className="text-sm">Scan QR codes to mark attendance</p>
                </div>
              ) : (
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {checkedInAttendees
                    .sort((a, b) => new Date(b.checked_in_at || '').getTime() - new Date(a.checked_in_at || '').getTime())
                    .slice(0, 10)
                    .map((attendee) => (
                      <div key={attendee.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex-1">
                          <div className="font-medium">{attendee.attendee_name}</div>
                          <div className="text-sm text-muted-foreground">{attendee.attendee_email}</div>
                          {attendee.ticket_type && (
                            <Badge variant="outline" className="mt-1 text-xs">
                              {attendee.ticket_type}
                            </Badge>
                          )}
                        </div>
                        <div className="text-right">
                          <Badge className="bg-green-100 text-green-800 border-green-200">
                            <CheckCircle2 className="h-3 w-3 mr-1" />
                            Checked In
                          </Badge>
                          {attendee.checked_in_at && (
                            <div className="text-xs text-muted-foreground mt-1">
                              {formatDateTime(attendee.checked_in_at)}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
