import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth";
import { logActivity, ActivityCategory } from "@/lib/activity-logger";

export async function POST(request: Request) {
  try {
    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === "development";
    console.log("API users/update-role - Is development mode:", isDevelopment);

    // Get JWT token from request
    const token = getJWTTokenFromRequest(request);
    console.log("API users/update-role - Token present:", !!token);

    // Initialize session variable
    let session: any = null;

    // In production, verify authentication and admin role
    // In development, allow access without authentication for easier testing
    if (!isDevelopment) {
      if (!token) {
        console.log("API users/update-role - Access denied: No token provided");
        return NextResponse.json(
          { error: "Unauthorized. Admin access required." },
          { status: 403 }
        );
      }

      // Verify the JW<PERSON> token and get user data
      const authResult = await verifyJWTToken(token);
      if (!authResult?.user) {
        console.log("API users/update-role - Access denied: Invalid token");
        return NextResponse.json(
          { error: "Unauthorized. Invalid token." },
          { status: 403 }
        );
      }

      // Check if user has admin role (only "admin" role has full access)
      const userRole = authResult.user.role_name || authResult.user.role;

      if (userRole !== "admin") {
        console.log("API users/update-role - Access denied: Not admin role. User role:", userRole);
        return NextResponse.json(
          { error: "Unauthorized. Admin access required." },
          { status: 403 }
        );
      }

      // Set session for authenticated user
      session = { user: authResult.user };
      console.log("API users/update-role - Access granted to admin user:", authResult.user.email);
    } else {
      console.log("API users/update-role - Development mode: Allowing access without authentication");
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin();
    const body = await request.json();

    // Validate required fields
    if (!body.userId || !body.roleId) {
      return NextResponse.json(
        { error: "User ID and role ID are required" },
        { status: 400 }
      );
    }

    // First, check if the user exists
    const { data: userData, error: userError } = await supabaseAdmin
      .from("users")
      .select("id, email, full_name, role, role_id")
      .eq("id", body.userId)
      .single();

    if (userError || !userData) {
      console.error("User not found:", userError);
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Check if the role exists
    const { data: roleData, error: roleError } = await supabaseAdmin
      .from("user_roles")
      .select("id, role_name, description")
      .eq("id", body.roleId)
      .single();

    if (roleError || !roleData) {
      console.error("Role not found:", roleError);
      return NextResponse.json(
        { error: "Role not found" },
        { status: 404 }
      );
    }

    // Get the role name from the role data
    const roleName = roleData.role_name.toLowerCase();

    // Map the role name to one of the allowed values in the check constraint
    // The constraint only allows 'free', 'paid', 'manager', 'admin'
    let mappedRole = roleName;
    if (!['free', 'paid', 'manager', 'admin'].includes(roleName)) {
      // Default to 'admin' for any role that doesn't match the constraint
      // This is a fallback to ensure the update succeeds
      mappedRole = 'admin';

      // More specific mappings for common roles
      if (roleName.includes('admin')) {
        mappedRole = 'admin';
      } else if (roleName.includes('manager')) {
        mappedRole = 'manager';
      } else if (roleName.includes('paid')) {
        mappedRole = 'paid';
      } else {
        mappedRole = 'free';
      }
    }

    // Update both role_id and role fields
    const { data, error } = await supabaseAdmin
      .from("users")
      .update({
        role_id: body.roleId,
        role: mappedRole // Use the mapped role that satisfies the check constraint
      })
      .eq("id", body.userId)
      .select();

    if (error) {
      console.error("Error updating user role:", error);
      return NextResponse.json(
        { error: "Failed to update user role: " + error.message },
        { status: 500 }
      );
    }

    // Log the activity
    try {
      await logActivity({
        user_id: session?.user?.id || "system",
        action: "update_user_role",
        entity_type: "user",
        entity_id: body.userId,
        category: ActivityCategory.USER,
        details: {
          previous_role_id: userData.role_id,
          new_role_id: body.roleId,
          previous_role: userData.role,
          new_role: mappedRole, // Use the mapped role that was actually set
          original_role_name: roleData.role_name, // Also log the original role name
          user_email: userData.email,
          user_name: userData.full_name
        },
      });
    } catch (logError) {
      // Don't fail the role update if logging fails
      console.error("Error logging role update activity:", logError);
    }

    return NextResponse.json({
      success: true,
      user: data?.[0] || null,
      role: roleData,
      mappedRole: mappedRole // Include the mapped role in the response
    });
  } catch (error: any) {
    console.error("Role update error:", error);
    return NextResponse.json(
      { error: error.message || "Role update failed" },
      { status: 500 }
    );
  }
}
