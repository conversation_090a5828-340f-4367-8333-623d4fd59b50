"use client"

import { useState, useEffect } from "react"
import * as React from "react"
import Image from "next/image"
import { X, ChevronLeft, ChevronRight } from "lucide-react"
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel"
import {
  <PERSON>alog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { getEventInitials, generateEventGradient } from "@/lib/utils"

interface EventImage {
  url: string
  alt_text?: string
  order?: number
  is_primary?: boolean
}

interface EventImageCarouselProps {
  images: EventImage[]
  eventTitle: string
  className?: string
  fixedHeight?: boolean // New prop to control height behavior
}

export function EventImageCarousel({
  images = [],
  eventTitle,
  className,
  fixedHeight = false
}: EventImageCarouselProps) {
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set())
  // Start with first image (index 0) to show primary image
  const [currentIndex, setCurrentIndex] = useState(0)
  const [api, setApi] = useState<CarouselApi>()
  const [isPopupOpen, setIsPopupOpen] = useState(false)
  const [popupImageIndex, setPopupImageIndex] = useState(0)

  // Sort images by order
  const sortedImages = images.sort((a, b) => (a.order || 0) - (b.order || 0))

  const handleImageError = (imageUrl: string) => {
    setImageErrors(prev => new Set([...prev, imageUrl]))
  }

  // Popup navigation functions
  const openPopup = (index: number) => {
    setPopupImageIndex(index)
    setIsPopupOpen(true)
  }

  const navigatePopup = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setPopupImageIndex(prev => prev === 0 ? sortedImages.length - 1 : prev - 1)
    } else {
      setPopupImageIndex(prev => prev === sortedImages.length - 1 ? 0 : prev + 1)
    }
  }

  // Handle keyboard navigation in popup
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!isPopupOpen) return

      if (event.key === 'ArrowLeft') {
        navigatePopup('prev')
      } else if (event.key === 'ArrowRight') {
        navigatePopup('next')
      } else if (event.key === 'Escape') {
        setIsPopupOpen(false)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [isPopupOpen, sortedImages.length])

  // Ensure currentIndex is valid - reset to 0 if current index is out of bounds
  React.useEffect(() => {
    if (currentIndex >= sortedImages.length) {
      setCurrentIndex(0)
    }
  }, [sortedImages.length, currentIndex])

  useEffect(() => {
    if (!api) {
      return
    }

    const onSelect = () => {
      setCurrentIndex(api.selectedScrollSnap())
    }

    api.on("select", onSelect)

    // Set initial position to first image (index 0)
    api.scrollTo(0, false) // false = no animation for initial position

    onSelect()

    return () => {
      api.off("select", onSelect)
    }
  }, [api, sortedImages.length])

  const renderFallbackImage = () => {
    const gradient = generateEventGradient(eventTitle)
    return (
      <div
        className={`w-full h-full ${gradient.className} flex items-center justify-center`}
        style={gradient.style}
      >
        <span className="text-white font-bold text-2xl md:text-3xl">
          {getEventInitials(eventTitle)}
        </span>
      </div>
    )
  }

  const renderImage = (image: EventImage, index: number) => {
    const hasError = imageErrors.has(image.url)

    if (hasError) {
      return renderFallbackImage()
    }

    return (
      <Image
        src={image.url}
        alt={image.alt_text || `${eventTitle} - Image ${index + 1}`}
        fill
        className="object-cover rounded-lg"
        onError={() => handleImageError(image.url)}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      />
    )
  }

  const renderThumbnailImage = (image: EventImage, index: number) => {
    const hasError = imageErrors.has(image.url)

    if (hasError) {
      return renderFallbackImage()
    }

    return (
      <Image
        src={image.url}
        alt={image.alt_text || `${eventTitle} - Thumbnail ${index + 1}`}
        fill
        className="object-cover"
        onError={() => handleImageError(image.url)}
        sizes="(max-width: 768px) 25vw, (max-width: 1200px) 16vw, 12vw"
      />
    )
  }

  // Don't render if no images
  if (sortedImages.length === 0) {
    return null
  }

  const currentImage = sortedImages[currentIndex]

  return (
    <div className={`${fixedHeight ? 'h-full flex flex-col' : 'space-y-4'} ${className}`}>
      {/* Main Large Image */}
      <div
        className={`relative w-full max-w-[1200px] max-h-[1200px] bg-gray-200 rounded-lg overflow-hidden mx-auto cursor-pointer group ${
          fixedHeight ? 'flex-1 min-h-0' : 'h-96 md:h-[500px] lg:h-[600px]'
        }`}
        onClick={() => openPopup(currentIndex)}
      >
        {currentImage && (
          <>
            {renderImage(currentImage, currentIndex)}

            {/* Hover overlay */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-200 flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-white/90 rounded-full p-3">
                <svg className="w-6 h-6 text-gray-800" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                </svg>
              </div>
            </div>

            {/* Primary badge */}
            {currentImage.is_primary && (
              <div className="absolute top-3 left-3 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                Primary
              </div>
            )}

            {/* Image counter */}
            <div className="absolute top-3 right-3 bg-black/50 text-white px-2 py-1 rounded text-sm">
              {currentIndex + 1} / {sortedImages.length}
            </div>

            {/* Navigation arrows on main image */}
            {sortedImages.length > 1 && (
              <>
                <button
                  onClick={() => {
                    const prevIndex = currentIndex === 0 ? sortedImages.length - 1 : currentIndex - 1
                    setCurrentIndex(prevIndex)
                    api?.scrollTo(prevIndex)
                  }}
                  className="absolute left-3 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all"
                  aria-label="Previous image"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button
                  onClick={() => {
                    const nextIndex = currentIndex === sortedImages.length - 1 ? 0 : currentIndex + 1
                    setCurrentIndex(nextIndex)
                    api?.scrollTo(nextIndex)
                  }}
                  className="absolute right-3 top-1/2 -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all"
                  aria-label="Next image"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </>
            )}
          </>
        )}
      </div>

      {/* Thumbnail Carousel */}
      {sortedImages.length > 1 && (
        <div className={`relative max-w-[1200px] mx-auto ${fixedHeight ? 'mt-2 flex-shrink-0' : 'mt-4'}`}>
          <Carousel className="w-full" setApi={setApi}>
            <CarouselContent className="-ml-2">
              {sortedImages.map((image, index) => (
                <CarouselItem key={index} className="pl-2 basis-1/5 md:basis-1/6 lg:basis-1/8">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      if (e.detail === 2) { // Double click
                        openPopup(index)
                      } else { // Single click
                        setCurrentIndex(index)
                      }
                    }}
                    className={`relative w-full bg-gray-200 rounded-md overflow-hidden transition-all cursor-pointer ${
                      fixedHeight ? 'h-16' : 'h-16 md:h-20 lg:h-24'
                    } ${
                      index === currentIndex
                        ? 'ring-2 ring-primary ring-offset-2'
                        : 'hover:ring-2 hover:ring-gray-300 hover:ring-offset-1'
                    }`}
                  >
                    {renderThumbnailImage(image, index)}

                    {/* Overlay for non-active thumbnails */}
                    {index !== currentIndex && (
                      <div className="absolute inset-0 bg-black/20" />
                    )}
                  </button>
                </CarouselItem>
              ))}
            </CarouselContent>

            {/* Thumbnail navigation */}
            {sortedImages.length > 8 && (
              <>
                <CarouselPrevious className="absolute -left-4 top-1/2 -translate-y-1/2 h-8 w-8 bg-white/80 hover:bg-white text-gray-800 border-gray-300" />
                <CarouselNext className="absolute -right-4 top-1/2 -translate-y-1/2 h-8 w-8 bg-white/80 hover:bg-white text-gray-800 border-gray-300" />
              </>
            )}
          </Carousel>
        </div>
      )}

      {/* Image info - Hidden in fixed height mode */}
      {!fixedHeight && currentImage?.alt_text && (
        <div className="text-center text-sm text-muted-foreground max-w-[1200px] mx-auto">
          {currentImage.alt_text}
        </div>
      )}

      {/* Full Screen Popup Modal */}
      <Dialog open={isPopupOpen} onOpenChange={setIsPopupOpen}>
        <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full p-0 bg-black/95 border-none">
          <DialogTitle className="sr-only">
            {eventTitle} - Image Gallery
          </DialogTitle>
          <div className="relative w-full h-full flex items-center justify-center">
            {/* Close button */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-4 right-4 z-50 bg-black/50 hover:bg-black/70 text-white border-none"
              onClick={() => setIsPopupOpen(false)}
            >
              <X className="h-6 w-6" />
            </Button>

            {/* Navigation buttons */}
            {sortedImages.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute left-4 top-1/2 -translate-y-1/2 z-50 bg-black/50 hover:bg-black/70 text-white border-none"
                  onClick={() => navigatePopup('prev')}
                >
                  <ChevronLeft className="h-8 w-8" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-4 top-1/2 -translate-y-1/2 z-50 bg-black/50 hover:bg-black/70 text-white border-none"
                  onClick={() => navigatePopup('next')}
                >
                  <ChevronRight className="h-8 w-8" />
                </Button>
              </>
            )}

            {/* Main popup image */}
            <div className="relative w-full h-full max-w-[90vw] max-h-[90vh]">
              {sortedImages[popupImageIndex] && (
                <>
                  {imageErrors.has(sortedImages[popupImageIndex].url) ? (
                    <div className="w-full h-full flex items-center justify-center">
                      {renderFallbackImage()}
                    </div>
                  ) : (
                    <Image
                      src={sortedImages[popupImageIndex].url}
                      alt={sortedImages[popupImageIndex].alt_text || `${eventTitle} - Image ${popupImageIndex + 1}`}
                      fill
                      className="object-contain"
                      onError={() => handleImageError(sortedImages[popupImageIndex].url)}
                      sizes="95vw"
                      priority
                    />
                  )}
                </>
              )}
            </div>

            {/* Image counter and info */}
            <div className="absolute bottom-4 left-1/2 -translate-x-1/2 z-50 bg-black/70 text-white px-4 py-2 rounded-lg">
              <div className="text-center">
                <div className="text-sm font-medium">
                  {popupImageIndex + 1} / {sortedImages.length}
                </div>
                {sortedImages[popupImageIndex]?.alt_text && (
                  <div className="text-xs text-gray-300 mt-1">
                    {sortedImages[popupImageIndex].alt_text}
                  </div>
                )}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
