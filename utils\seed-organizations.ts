import { getSupabaseAdmin } from "@/lib/supabase"
import { v4 as uuidv4 } from "uuid"

/**
 * Seed sample organizations for testing
 */
export async function seedOrganizations() {
  const supabaseAdmin = getSupabaseAdmin()

  // Sample organizations data
  const organizations = [
    {
      id: uuidv4(),
      name: "Tech Innovators Sdn Bhd",
      ssm_number: "************",
      pic_name: "<PERSON>",
      pic_phone: "+60123456789",
      pic_email: "<EMAIL>",
      address: "Level 10, Menara TM, Jalan Pantai Baharu, 50672 Kuala Lumpur",
      website: "https://techinnovators.com.my",
      created_by: "system",
      created_at: new Date().toISOString(),
    },
    {
      id: uuidv4(),
      name: "Digital Solutions Malaysia",
      ssm_number: "************",
      pic_name: "<PERSON><PERSON> Nurhaliza",
      pic_phone: "+60198765432",
      pic_email: "<EMAIL>",
      address: "Suite 15-3, Level 15, Wisma UOA Damansara, 50490 Kuala Lumpur",
      website: "https://digitalsolutions.my",
      created_by: "system",
      created_at: new Date().toISOString(),
    },
    {
      id: uuidv4(),
      name: "Startup Hub KL",
      ssm_number: "************",
      pic_name: "David Lim",
      pic_phone: "+60187654321",
      pic_email: "<EMAIL>",
      address: "Level 5, MaGIC Cyberjaya, 63000 Cyberjaya, Selangor",
      website: "https://startuphubkl.com",
      created_by: "system",
      created_at: new Date().toISOString(),
    },
    {
      id: uuidv4(),
      name: "Education Excellence Centre",
      ssm_number: "************",
      pic_name: "Dr. Fatimah Abdullah",
      pic_phone: "+60176543210",
      pic_email: "<EMAIL>",
      address: "Block A, Level 3, Sunway University, 47500 Bandar Sunway, Selangor",
      website: "https://eduexcellence.edu.my",
      created_by: "system",
      created_at: new Date().toISOString(),
    },
    {
      id: uuidv4(),
      name: "Green Energy Solutions",
      ssm_number: "************",
      pic_name: "Raj Kumar",
      pic_phone: "+60165432109",
      pic_email: "<EMAIL>",
      address: "Level 8, Menara Axiata, 9 Jalan Stesen Sentral 5, 50470 Kuala Lumpur",
      website: "https://greenenergy.com.my",
      created_by: "system",
      created_at: new Date().toISOString(),
    },
  ]

  try {
    console.log("Seeding organizations...")

    // Insert organizations
    const { data, error } = await supabaseAdmin
      .from("organizations")
      .insert(organizations)
      .select()

    if (error) {
      console.error("Error seeding organizations:", error)
      throw error
    }

    console.log(`Successfully seeded ${data.length} organizations`)
    return data
  } catch (error) {
    console.error("Failed to seed organizations:", error)
    throw error
  }
}

// Run if called directly
if (require.main === module) {
  seedOrganizations()
    .then(() => {
      console.log("Organization seeding completed")
      process.exit(0)
    })
    .catch((error) => {
      console.error("Organization seeding failed:", error)
      process.exit(1)
    })
}
