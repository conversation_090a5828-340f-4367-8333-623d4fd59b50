-- Create a function to insert a user with admin privileges
-- This function bypasses RLS policies
CREATE OR REPLACE FUNCTION insert_user_admin(user_data JSONB)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER -- This runs with the permissions of the function creator
AS $$
DECLARE
  inserted_user JSONB;
BEGIN
  -- Insert the user into the users table
  INSERT INTO users (
    id,
    email,
    password_hash,
    full_name,
    role,
    subscription_status,
    subscription_end_date,
    created_at,
    organization,
    profile_image_url,
    events_created,
    total_earnings,
    available_balance
  ) VALUES (
    (user_data->>'id')::UUID,
    user_data->>'email',
    user_data->>'password_hash',
    user_data->>'full_name',
    user_data->>'role',
    user_data->>'subscription_status',
    (user_data->>'subscription_end_date')::TIMESTAMP WITH TIME ZONE,
    (user_data->>'created_at')::TIMES<PERSON>MP WITH TIME ZONE,
    user_data->>'organization',
    user_data->>'profile_image_url',
    (user_data->>'events_created')::INTEGER,
    (user_data->>'total_earnings')::NUMERIC,
    (user_data->>'available_balance')::NUMERIC
  )
  RETURNING to_jsonb(users.*) INTO inserted_user;
  
  -- Return the inserted user
  RETURN inserted_user;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION insert_user_admin TO authenticated;
GRANT EXECUTE ON FUNCTION insert_user_admin TO anon;
