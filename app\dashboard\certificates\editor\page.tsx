"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import {
  Award,
  Plus,
  Edit,
  Trash,
  Copy,
  Share,
  Eye,
  Users,
  Settings,
  Globe,
  Lock,
  Crown,
  User
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { CertificateTemplateEditor, CertificateField } from "@/components/certificate-template-editor"

// Template Card Component
interface TemplateCardProps {
  template: CertificateTemplate
  onEdit: (template: CertificateTemplate) => void
  onCopy: (template: CertificateTemplate) => void
  onShare: (template: CertificateTemplate) => void
  onDelete: (template: CertificateTemplate) => void
  isReadOnly?: boolean
  isOwner?: boolean
  isShared?: boolean
  showCopyOnly?: boolean
}

function TemplateCard({
  template,
  onEdit,
  onCopy,
  onShare,
  onDelete,
  isReadOnly = false,
  isOwner = false,
  isShared = false,
  showCopyOnly = false
}: TemplateCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle className="text-base">{template.name}</CardTitle>
            {template.is_default && <Crown className="h-4 w-4 text-yellow-500" />}
            {template.is_shared && <Globe className="h-4 w-4 text-blue-500" />}
            {template.is_premium && <Badge variant="secondary">Premium</Badge>}
          </div>
          <div className="flex items-center gap-1">
            {!showCopyOnly && !isReadOnly && isOwner && (
              <>
                <Button variant="ghost" size="icon" onClick={() => onEdit(template)}>
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => onShare(template)}
                  title={template.is_shared ? "Remove from marketplace" : "Share to marketplace"}
                >
                  {template.is_shared ? <Lock className="h-4 w-4" /> : <Share className="h-4 w-4" />}
                </Button>
                <Button variant="ghost" size="icon" onClick={() => onDelete(template)}>
                  <Trash className="h-4 w-4" />
                </Button>
              </>
            )}
            <Button variant="ghost" size="icon" onClick={() => onCopy(template)}>
              <Copy className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" onClick={() => onEdit(template)}>
              <Eye className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <CardDescription className="line-clamp-2">
          {template.description || "No description available"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>Orientation</span>
            <Badge variant="outline">{template.orientation || 'landscape'}</Badge>
          </div>
          {template.created_at && (
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>Created</span>
              <span>{new Date(template.created_at).toLocaleDateString()}</span>
            </div>
          )}
          {isShared && (
            <div className="flex items-center gap-2 text-sm text-blue-600">
              <Users className="h-4 w-4" />
              <span>Shared by community</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

type CertificateTemplate = {
  id: string
  name: string
  description?: string
  thumbnail_url?: string
  is_premium: boolean
  is_default?: boolean
  is_shared?: boolean
  html_template?: string
  css_styles?: string
  fields?: CertificateField[]
  orientation?: "landscape" | "portrait"
  is_active?: boolean
  created_by?: string
  created_at?: string
  updated_at?: string
  background_image_url?: string
  template_data?: any
}

export default function CertificateEditorPage() {
  const [templates, setTemplates] = useState<CertificateTemplate[]>([])
  const [defaultTemplates, setDefaultTemplates] = useState<CertificateTemplate[]>([])
  const [sharedTemplates, setSharedTemplates] = useState<CertificateTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [showEditor, setShowEditor] = useState(false)
  const [showDeleteDialog, setShowDeleteDialog] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<CertificateTemplate | null>(null)
  const [activeTab, setActiveTab] = useState("available")

  const { toast } = useToast()
  const { user: currentUser } = useAuth()

  const isAdmin = currentUser?.role_name === "admin"

  useEffect(() => {
    fetchTemplates()
  }, [currentUser])

  const fetchTemplates = async () => {
    if (!currentUser?.id) {
      console.log("No authenticated user found, skipping template fetch")
      return
    }

    setLoading(true)
    try {
      // Fetch default templates
      const defaultResponse = await fetch('/api/certificate-templates?type=default')
      if (!defaultResponse.ok) throw new Error('Failed to fetch default templates')
      const defaultResult = await defaultResponse.json()

      // Fetch user's own templates
      const userResponse = await fetch('/api/certificate-templates?type=user')
      if (!userResponse.ok) throw new Error('Failed to fetch user templates')
      const userResult = await userResponse.json()

      // Fetch shared templates (marketplace)
      const sharedResponse = await fetch('/api/certificate-templates?type=shared')
      if (!sharedResponse.ok) throw new Error('Failed to fetch shared templates')
      const sharedResult = await sharedResponse.json()

      setDefaultTemplates(defaultResult.data || [])
      setTemplates(userResult.data || [])
      setSharedTemplates(sharedResult.data || [])

    } catch (error) {
      console.error("Error fetching templates:", error)
      toast({
        title: "Error",
        description: `Failed to fetch certificate templates: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateTemplate = () => {
    setSelectedTemplate(null)
    setShowEditor(true)
  }

  const handleEditTemplate = (template: CertificateTemplate) => {
    setSelectedTemplate(template)
    setShowEditor(true)
  }

  const handleCopyTemplate = async (template: CertificateTemplate) => {
    try {
      // Check if user is authenticated
      if (!currentUser?.id) {
        toast({
          title: "Error",
          description: "You must be logged in to copy templates",
          variant: "destructive",
        })
        return
      }

      console.log("Copying template:", template.id)

      const response = await fetch(`/api/certificate-templates/${template.id}/copy`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to copy template')
      }

      const result = await response.json()
      setTemplates(prev => [result.data, ...prev])

      toast({
        title: "Success",
        description: "Template copied successfully",
      })
    } catch (error) {
      console.error("Error copying template:", error)
      toast({
        title: "Error",
        description: `Failed to copy template: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      })
    }
  }

  const handleShareTemplate = async (template: CertificateTemplate) => {
    try {
      const response = await fetch(`/api/certificate-templates/${template.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...template,
          is_shared: !template.is_shared
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update template')
      }

      const result = await response.json()
      setTemplates(prev => prev.map(t =>
        t.id === template.id ? result.data : t
      ))

      toast({
        title: "Success",
        description: template.is_shared ? "Template removed from marketplace" : "Template shared to marketplace",
      })
    } catch (error) {
      console.error("Error sharing template:", error)
      toast({
        title: "Error",
        description: "Failed to update template sharing",
        variant: "destructive",
      })
    }
  }

  const handleDeleteTemplate = (template: CertificateTemplate) => {
    setSelectedTemplate(template)
    setShowDeleteDialog(true)
  }

  const confirmDeleteTemplate = async () => {
    if (!selectedTemplate) return

    try {
      const response = await fetch(`/api/certificate-templates/${selectedTemplate.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete template')
      }

      setTemplates(prev => prev.filter(t => t.id !== selectedTemplate.id))
      setShowDeleteDialog(false)
      setSelectedTemplate(null)

      toast({
        title: "Success",
        description: "Template deleted successfully",
      })
    } catch (error) {
      console.error("Error deleting template:", error)
      toast({
        title: "Error",
        description: "Failed to delete template",
        variant: "destructive",
      })
    }
  }

  const handleSaveTemplate = async (templateData: any) => {
    try {
      // Check if user is authenticated
      if (!currentUser?.id) {
        toast({
          title: "Error",
          description: "You must be logged in to save templates",
          variant: "destructive",
        })
        return
      }

      console.log("Saving template with user ID:", currentUser.id)
      console.log("Template data:", templateData)

      if (templateData.id) {
        // Update existing template
        const response = await fetch(`/api/certificate-templates/${templateData.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(templateData),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to update template')
        }

        const result = await response.json()
        setTemplates(prev => prev.map(t =>
          t.id === templateData.id ? result.data : t
        ))

        toast({
          title: "Success",
          description: "Template updated successfully",
        })
      } else {
        // Create new template
        const response = await fetch('/api/certificate-templates', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(templateData),
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.error || 'Failed to create template')
        }

        const result = await response.json()
        setTemplates(prev => [result.data, ...prev])

        toast({
          title: "Success",
          description: "Template created successfully",
        })
      }

      setShowEditor(false)
      setSelectedTemplate(null)
    } catch (error) {
      console.error("Error saving template:", error)
      toast({
        title: "Error",
        description: `Failed to save template: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: "destructive",
      })
    }
  }

  if (loading || !currentUser) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">
            {!currentUser ? "Authenticating..." : "Loading templates..."}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Certificate Editor</h1>
          <p className="text-muted-foreground">Create, edit, and manage certificate templates</p>
        </div>
        <Button onClick={handleCreateTemplate}>
          <Plus className="mr-2 h-4 w-4" />
          Create Template
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3 lg:grid-cols-4">
          <TabsTrigger value="available">Available</TabsTrigger>
          <TabsTrigger value="my-templates">My Templates</TabsTrigger>
          <TabsTrigger value="marketplace">Marketplace</TabsTrigger>
          {isAdmin && <TabsTrigger value="admin">Admin</TabsTrigger>}
        </TabsList>

        {/* Available Templates Tab */}
        <TabsContent value="available" className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <Crown className="h-5 w-5 text-yellow-500" />
            <h2 className="text-lg font-semibold">Default Templates</h2>
            <Badge variant="secondary">Read Only</Badge>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {defaultTemplates.map((template) => (
              <TemplateCard
                key={template.id}
                template={template}
                onEdit={handleEditTemplate}
                onCopy={handleCopyTemplate}
                onShare={handleShareTemplate}
                onDelete={handleDeleteTemplate}
                isReadOnly={true}
                showCopyOnly={true}
              />
            ))}
          </div>
        </TabsContent>

        {/* My Templates Tab */}
        <TabsContent value="my-templates" className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <User className="h-5 w-5" />
            <h2 className="text-lg font-semibold">My Templates</h2>
            <Badge variant="outline">{templates.length}</Badge>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {templates.map((template) => (
              <TemplateCard
                key={template.id}
                template={template}
                onEdit={handleEditTemplate}
                onCopy={handleCopyTemplate}
                onShare={handleShareTemplate}
                onDelete={handleDeleteTemplate}
                isOwner={true}
              />
            ))}
          </div>
        </TabsContent>

        {/* Marketplace Tab */}
        <TabsContent value="marketplace" className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <Globe className="h-5 w-5 text-blue-500" />
            <h2 className="text-lg font-semibold">Marketplace</h2>
            <Badge variant="outline">{sharedTemplates.length}</Badge>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {sharedTemplates.map((template) => (
              <TemplateCard
                key={template.id}
                template={template}
                onEdit={handleEditTemplate}
                onCopy={handleCopyTemplate}
                onShare={handleShareTemplate}
                onDelete={handleDeleteTemplate}
                isShared={true}
                showCopyOnly={true}
              />
            ))}
          </div>
        </TabsContent>

        {/* Admin Tab */}
        {isAdmin && (
          <TabsContent value="admin" className="space-y-6">
            <div className="flex items-center gap-2 mb-4">
              <Settings className="h-5 w-5 text-red-500" />
              <h2 className="text-lg font-semibold">Admin Management</h2>
              <Badge variant="destructive">Admin Only</Badge>
            </div>

            {/* Default Templates Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Crown className="h-5 w-5 text-yellow-500" />
                  <h3 className="text-md font-semibold">Default Templates</h3>
                  <Badge variant="secondary">{defaultTemplates.length}</Badge>
                </div>
                <Button onClick={handleCreateTemplate} variant="outline" size="sm">
                  <Plus className="mr-2 h-4 w-4" />
                  Add Default Template
                </Button>
              </div>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {defaultTemplates.map((template) => (
                  <TemplateCard
                    key={template.id}
                    template={template}
                    onEdit={handleEditTemplate}
                    onCopy={handleCopyTemplate}
                    onShare={handleShareTemplate}
                    onDelete={handleDeleteTemplate}
                    isOwner={true}
                  />
                ))}
              </div>
            </div>

            {/* All Templates Section */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Eye className="h-5 w-5 text-blue-500" />
                <h3 className="text-md font-semibold">All User Templates</h3>
                <Badge variant="outline">{templates.length + sharedTemplates.length}</Badge>
              </div>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {[...templates, ...sharedTemplates].map((template) => (
                  <TemplateCard
                    key={template.id}
                    template={template}
                    onEdit={handleEditTemplate}
                    onCopy={handleCopyTemplate}
                    onShare={handleShareTemplate}
                    onDelete={handleDeleteTemplate}
                    isOwner={true}
                  />
                ))}
              </div>
            </div>

            {/* Marketplace Management */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Globe className="h-5 w-5 text-green-500" />
                <h3 className="text-md font-semibold">Marketplace Templates</h3>
                <Badge variant="outline">{sharedTemplates.length}</Badge>
              </div>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {sharedTemplates.map((template) => (
                  <TemplateCard
                    key={template.id}
                    template={template}
                    onEdit={handleEditTemplate}
                    onCopy={handleCopyTemplate}
                    onShare={handleShareTemplate}
                    onDelete={handleDeleteTemplate}
                    isOwner={true}
                    isShared={true}
                  />
                ))}
              </div>
            </div>
          </TabsContent>
        )}
      </Tabs>

      {/* Template Editor Dialog */}
      {showEditor && (
        <Dialog open={showEditor} onOpenChange={setShowEditor}>
          <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {selectedTemplate ? "Edit Template" : "Create New Template"}
              </DialogTitle>
              <DialogDescription>
                {selectedTemplate
                  ? "Modify your certificate template design and settings"
                  : "Create a new certificate template with custom design"
                }
              </DialogDescription>
            </DialogHeader>
            <CertificateTemplateEditor
              template={selectedTemplate || undefined}
              onSave={handleSaveTemplate}
              onCancel={() => {
                setShowEditor(false)
                setSelectedTemplate(null)
              }}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Template</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedTemplate?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={confirmDeleteTemplate}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
