import { NextResponse } from "next/server";
import { NextRequest } from "next/server";
import { validateApi<PERSON>ey } from "@/lib/api-key-validator";

/**
 * Middleware for API routes that require API key authentication
 * @param request The Next.js request object
 * @returns A response if the API key is invalid, or null to continue
 */
export async function validateApiKeyMiddleware(request: NextRequest): Promise<NextResponse | null> {
  // Skip API key validation for certain paths
  const skipValidation = [
    "/api/auth",
    "/api/webhooks/receive", // Public webhook receiver endpoint
  ];

  // Check if the current path should skip validation
  const shouldSkipValidation = skipValidation.some(path => request.nextUrl.pathname.startsWith(path));
  if (shouldSkipValidation) {
    return null;
  }

  // Extract the API key from the Authorization header
  const authHeader = request.headers.get("Authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return NextResponse.json(
      { error: "Missing or invalid Authorization header" },
      { status: 401 }
    );
  }

  const apiKey = authHeader.substring(7); // Remove "Bearer " prefix
  
  // Validate the API key
  const isValid = await validateApiKey(apiKey);
  
  if (!isValid) {
    return NextResponse.json(
      { error: "Invalid API key" },
      { status: 401 }
    );
  }
  
  // API key is valid, continue with the request
  return null;
}
