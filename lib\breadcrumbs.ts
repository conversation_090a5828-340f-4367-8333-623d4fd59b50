import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"
import { supabase } from "@/lib/supabase"

type Breadcrumb = {
  label: string
  href: string
  isCurrent?: boolean
}

export function useBreadcrumbs() {
  const pathname = usePathname()
  const [eventTitle, setEventTitle] = useState<string | null>(null)

  // Calculate path segments and event info first
  const pathSegments = pathname.split('/').filter(Boolean)
  const isEventPage = pathSegments[1] === 'events' && pathSegments.length >= 3
  const eventSlug = isEventPage ? pathSegments[2] : null

  // Fetch event title if we're on an event page
  useEffect(() => {
    if (eventSlug && !eventTitle) {
      const fetchEventTitle = async () => {
        try {
          const { data, error } = await supabase
            .from("events")
            .select("title")
            .eq("slug", eventSlug)
            .single()

          if (data && !error) {
            setEventTitle(data.title)
          }
        } catch (error) {
          console.error("Error fetching event title for breadcrumbs:", error)
        }
      }

      fetchEventTitle()
    }
  }, [eventSlug, eventTitle])

  // Skip if not in dashboard
  if (!pathname.startsWith('/dashboard')) {
    return []
  }

  // Special case for dashboard home
  if (pathSegments.length === 1 && pathSegments[0] === 'dashboard') {
    return []
  }

  const breadcrumbs: Breadcrumb[] = []
  let currentPath = '/dashboard'

  // Add dashboard as first breadcrumb (but not for dashboard home)
  breadcrumbs.push({
    label: 'Dashboard',
    href: '/dashboard'
  })

  // Generate breadcrumbs from path segments (skip the first 'dashboard' segment)
  for (let i = 1; i < pathSegments.length; i++) {
    const segment = pathSegments[i]
    currentPath = `${currentPath}/${segment}`
    const isLast = i === pathSegments.length - 1

    let label: string

    // Special handling for event pages
    if (pathSegments[1] === 'events' && i === 2 && eventTitle) {
      // This is the event slug segment, use the event title
      label = eventTitle
    } else if (pathSegments[1] === 'events' && i === 2 && !eventTitle) {
      // Event title not loaded yet, show loading or slug
      label = segment.length <= 5 ? segment : 'Event'
    } else {
      // Format label (capitalize and replace dashes with spaces)
      label = segment
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
    }

    if (isLast) {
      breadcrumbs.push({
        label,
        href: currentPath,
        isCurrent: true
      })
    } else {
      breadcrumbs.push({
        label,
        href: currentPath
      })
    }
  }

  return breadcrumbs
}
