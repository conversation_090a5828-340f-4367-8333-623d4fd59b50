// Test script to set up 3 tickets and navigate to registration
console.log('🧪 Testing 3 Tickets Registration Issue');

// Simulate the ticket selection that should generate 3 participants
const testTickets = [
  {
    ticketType: {
      id: 'general',
      name: 'General Admission',
      price: 30,
      description: 'Standard ticket for the fun run'
    },
    quantity: 3  // This should generate 3 participants
  }
];

console.log('📊 Test Configuration:');
console.log('- Event: 5nEh (Ipoh Fun Run Day 2025)');
console.log('- Tickets: 3x General Admission');
console.log('- Expected Participants: 3');
console.log('- Expected Required Fields: 6 (4 standard + 2 custom)');
console.log('');

// Calculate expected participants (same logic as generateParticipants)
let expectedParticipants = [];
testTickets.forEach((selectedTicket) => {
  for (let i = 0; i < selectedTicket.quantity; i++) {
    expectedParticipants.push({
      name: "",
      ic: "",
      phone: "",
      email: "",
      ticketTypeId: selectedTicket.ticketType.id,
      ticketTypeName: selectedTicket.ticketType.name,
      custom_field_responses: {}
    });
  }
});

console.log('✅ Expected Participants Generated:');
console.log(`   Count: ${expectedParticipants.length}`);
console.log(`   Structure:`, expectedParticipants);
console.log('');

console.log('🔍 Debug Steps:');
console.log('1. Open debug-participants.html');
console.log('2. Set quantity to 3');
console.log('3. Click "Test Registration"');
console.log('4. Check browser console for logs');
console.log('5. Verify 3 participant forms are displayed');
console.log('');

console.log('🎯 Expected Console Logs:');
console.log('- 🔧 generateParticipants called with selectedTickets');
console.log('- 🎫 Processing ticket: General Admission x 3');
console.log('- ✅ Generated 3 participants');
console.log('- 🔄 useEffect triggered - updating participants array');
console.log('- 🔄 Replacing field array with participants');
console.log('- 🎭 Rendering participant forms - fields array length: 3');
console.log('');

console.log('🚨 If Issue Persists:');
console.log('- Check if selectedTickets are properly loaded from session storage');
console.log('- Verify useEffect dependencies are correct');
console.log('- Check if replace() function is working');
console.log('- Look for any React Strict Mode double execution issues');
console.log('');

console.log('✅ Test script complete. Use debug-participants.html to test.');
