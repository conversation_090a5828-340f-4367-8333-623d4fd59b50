/**
 * Only the "admin" role has access to all features regardless of subscription
 * Other admin-type roles have elevated permissions but not full admin access
 */
export const ADMIN_ROLE = "admin"
export const ELEVATED_ROLES = ["super_admin", "supermanager", "event_admin"]

/**
 * Check if a user has full admin privileges
 * Only users with "admin" role have access to all functions
 * This function is client-safe and doesn't require server-side dependencies
 */
export function isAdminUser(userRoleName?: string): boolean {
  return userRoleName === ADMIN_ROLE
}

/**
 * Check if a role is a manager role
 * Includes manager roles, elevated admin roles, and the full admin role
 */
export function isManagerUser(userRoleName?: string): boolean {
  const managerRoles = ["manager", ...ELEVATED_ROLES, ADMIN_ROLE]
  return managerRoles.includes(userRoleName || "")
}

/**
 * Get role display name with proper formatting
 */
export function getRoleDisplayName(roleName?: string): string {
  if (!roleName) return "User"

  return roleName
    .split("_")
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ")
}

/**
 * Get role badge color based on role type
 */
export function getRoleBadgeColor(roleName?: string): string {
  if (!roleName) return "default"

  if (ADMIN_ROLES.includes(roleName)) {
    return "destructive" // Red for admin roles
  }

  if (isManagerUser(roleName)) {
    return "secondary" // Gray for manager roles
  }

  return "default" // Default for regular users
}
