"use client"

import React from "react"
import { useRouter } from "next/navigation"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { type EventType } from "@/contexts/event-context"
import { type ParticipantType } from "@/components/registration-form"
import {
  CheckCircle,
  Download,
  Ticket,
  Calendar,
  MapPin,
  Users,
  Mail,
  Home,
  LayoutDashboard,
  Share2,
  Clock,
  LogIn
} from "lucide-react"

interface SuccessStepProps {
  event: EventType
  registrationId: string | null
  participants: ParticipantType[]
  mainContact: any
  paymentDetails: any
  totalTickets: number
  receiptToken: string | null
  ticketToken: string | null
  onViewReceipt: () => void
  onViewTickets: () => void
  user: any | null // Add user prop to check authentication status
}

export function SuccessStep({
  event,
  registrationId,
  participants,
  mainContact,
  paymentDetails,
  totalTickets,
  receiptToken,
  ticketToken,
  onViewReceipt,
  onViewTickets,
  user
}: SuccessStepProps) {
  const router = useRouter()

  return (
    <div className="space-y-6">
      {/* Success Header */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-600 text-white rounded-full mb-6 animate-pulse">
          <CheckCircle className="h-10 w-10" />
        </div>
        <h2 className="text-3xl font-bold text-gray-900 mb-3">Registration Successful!</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Congratulations! Your registration for <strong>{event.title}</strong> has been confirmed.
          You will receive a confirmation email shortly.
        </p>
      </div>

      {/* Success Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card className="border-green-200 bg-gradient-to-br from-green-50 to-green-100">
          <CardContent className="p-4 text-center">
            <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-green-900">Status</p>
            <p className="text-lg font-bold text-green-700">Confirmed</p>
          </CardContent>
        </Card>

        <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100">
          <CardContent className="p-4 text-center">
            <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-blue-900">Participants</p>
            <p className="text-lg font-bold text-blue-700">{participants.length}</p>
          </CardContent>
        </Card>

        <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100">
          <CardContent className="p-4 text-center">
            <Ticket className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-purple-900">Tickets</p>
            <p className="text-lg font-bold text-purple-700">{totalTickets}</p>
          </CardContent>
        </Card>

        <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100">
          <CardContent className="p-4 text-center">
            <Clock className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-orange-900">Check-in</p>
            <p className="text-lg font-bold text-orange-700">Ready</p>
          </CardContent>
        </Card>
      </div>

      {/* Registration Details */}
      <Card className="shadow-lg border-0 bg-white">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <CardTitle className="flex items-center gap-2 text-xl text-gray-900">
            <Calendar className="h-5 w-5 text-blue-600" />
            Registration Details
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-700">Registration ID</p>
                <p className="text-gray-900 font-mono text-lg">{registrationId}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">Event</p>
                <p className="text-gray-900 font-semibold">{event.title}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">Main Contact</p>
                <p className="text-gray-900">{mainContact?.name}</p>
                <p className="text-gray-600 text-sm flex items-center gap-1">
                  <Mail className="h-3 w-3" />
                  {mainContact?.email}
                </p>
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-700">Event Date</p>
                <p className="text-gray-900">
                  {new Date(event.start_date).toLocaleDateString()} {new Date(event.start_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} -{" "}
                  {new Date(event.end_date).toLocaleDateString()} {new Date(event.end_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">Location</p>
                <p className="text-gray-900 flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
                  {event.location}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-700">Participants</p>
                <p className="text-gray-900">{participants.length} registered</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Payment Details */}
      {paymentDetails && (
        <Card className="shadow-lg border-0 bg-white">
          <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 border-b">
            <CardTitle className="flex items-center gap-2 text-xl text-gray-900">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Payment Confirmation
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-700">Transaction ID</p>
                  <p className="text-gray-900 font-mono">{paymentDetails.transaction_id}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Amount Paid</p>
                  <p className="text-gray-900 font-bold text-lg">RM {paymentDetails.amount?.toFixed(2)}</p>
                </div>
              </div>
              <div className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-700">Payment Method</p>
                  <p className="text-gray-900">{paymentDetails.payment_method || paymentDetails.gateway}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Status</p>
                  <Badge className="bg-green-100 text-green-800 border-green-300">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    {paymentDetails.status || 'Paid'}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <Card className="shadow-lg border-0 bg-white">
        <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 border-b">
          <CardTitle className="text-xl text-gray-900">Next Steps</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Download Actions */}
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900 mb-3">Download Documents</h4>
              {receiptToken && (
                <Button
                  onClick={onViewReceipt}
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
                  size="lg"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Receipt
                </Button>
              )}
              {ticketToken && (
                <Button
                  onClick={onViewTickets}
                  variant="outline"
                  className="w-full border-green-300 text-green-700 hover:bg-green-50"
                  size="lg"
                >
                  <Ticket className="h-4 w-4 mr-2" />
                  View My Tickets
                </Button>
              )}
            </div>

            {/* Navigation Actions */}
            <div className="space-y-3">
              <h4 className="font-semibold text-gray-900 mb-3">Continue</h4>
              {user ? (
                // User is logged in - show dashboard button
                <Button
                  onClick={() => router.push("/dashboard/my-tickets")}
                  variant="secondary"
                  className="w-full"
                  size="lg"
                >
                  <LayoutDashboard className="h-4 w-4 mr-2" />
                  Go to Dashboard
                </Button>
              ) : (
                // User is not logged in - show login button with return URL
                <Button
                  onClick={() => router.push("/auth/login?redirectTo=/dashboard/my-tickets")}
                  variant="secondary"
                  className="w-full"
                  size="lg"
                >
                  <LogIn className="h-4 w-4 mr-2" />
                  Login to View Dashboard
                </Button>
              )}
              <Button
                onClick={() => router.push("/")}
                variant="ghost"
                className="w-full"
                size="lg"
              >
                <Home className="h-4 w-4 mr-2" />
                Return to Home
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Important Information */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h4 className="font-semibold text-blue-900 mb-3 flex items-center gap-2">
          <Mail className="h-4 w-4" />
          Important Information
        </h4>
        <ul className="text-sm text-blue-800 space-y-2">
          <li>• A confirmation email has been sent to <strong>{mainContact?.email}</strong></li>
          <li>• Please save your registration ID: <strong>{registrationId}</strong></li>
          <li>• Bring a valid ID that matches your registration details</li>
          <li>• Check-in opens 30 minutes before the event starts</li>
          <li>• Contact support if you need to make any changes to your registration</li>
        </ul>
      </div>
    </div>
  )
}
