import { sendWebhookNotifications } from "@/lib/webhook-service";

/**
 * Triggers a webhook event
 * @param event The event type (e.g., "registration.created")
 * @param data The data to send with the webhook
 * @returns A promise that resolves when the webhook has been triggered
 */
export async function triggerWebhookEvent(event: string, data: any): Promise<void> {
  try {
    await sendWebhookNotifications(event, data);
  } catch (error) {
    console.error(`Error triggering webhook event ${event}:`, error);
  }
}

/**
 * Event types for webhooks
 */
export enum WebhookEventType {
  REGISTRATION_CREATED = "registration.created",
  REGISTRATION_UPDATED = "registration.updated",
  REGISTRATION_CANCELLED = "registration.cancelled",
  PAYMENT_SUCCEEDED = "payment.succeeded",
  PAYMENT_FAILED = "payment.failed",
  PAYMENT_REFUNDED = "payment.refunded",
  EVENT_PUBLISHED = "event.published",
  EVENT_UPDATED = "event.updated",
  EVENT_CANCELLED = "event.cancelled",
  CERTIFICATE_ISSUED = "certificate.issued",
  ATTENDANCE_MARKED = "attendance.marked",
}

/**
 * Triggers a registration created webhook event
 * @param registrationData The registration data
 */
export async function triggerRegistrationCreated(registrationData: any): Promise<void> {
  await triggerWebhookEvent(WebhookEventType.REGISTRATION_CREATED, registrationData);
}

/**
 * Triggers a registration updated webhook event
 * @param registrationData The registration data
 */
export async function triggerRegistrationUpdated(registrationData: any): Promise<void> {
  await triggerWebhookEvent(WebhookEventType.REGISTRATION_UPDATED, registrationData);
}

/**
 * Triggers a payment succeeded webhook event
 * @param paymentData The payment data
 */
export async function triggerPaymentSucceeded(paymentData: any): Promise<void> {
  await triggerWebhookEvent(WebhookEventType.PAYMENT_SUCCEEDED, paymentData);
}

/**
 * Triggers a certificate issued webhook event
 * @param certificateData The certificate data
 */
export async function triggerCertificateIssued(certificateData: any): Promise<void> {
  await triggerWebhookEvent(WebhookEventType.CERTIFICATE_ISSUED, certificateData);
}

/**
 * Triggers an attendance marked webhook event
 * @param attendanceData The attendance data
 */
export async function triggerAttendanceMarked(attendanceData: any): Promise<void> {
  await triggerWebhookEvent(WebhookEventType.ATTENDANCE_MARKED, attendanceData);
}
