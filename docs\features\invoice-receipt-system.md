# Invoice/Receipt System Documentation

## Overview

The mTicket.my invoice/receipt system has been completely redesigned to provide proper financial documentation with transaction tracking, auto-generated invoice/receipt numbers, and support for group registrations.

## Key Improvements

### ✅ **Fixed Issues**

1. **Proper Transaction Tracking**: Every payment now creates a proper transaction record
2. **Auto-generated Invoice Numbers**: Format: INV1000, INV1001, INV1002, etc.
3. **Auto-generated Receipt Numbers**: Format: RCP1000, RCP1001, RCP1002, etc. (only when paid)
4. **Group Registration Support**: Single invoice/receipt for multiple participants
5. **Receipt Logic**: Receipts only shown for confirmed payments with transaction data
6. **Gateway Integration**: Proper storage of payment gateway transaction IDs

### ✅ **Database Schema**

#### New Transactions Table
```sql
CREATE TABLE transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  registration_id UUID REFERENCES registrations(id) ON DELETE SET NULL,
  subscription_id UUID REFERENCES user_subscription(id) ON DELETE SET NULL,
  gateway_id UUID REFERENCES payment_gateway_settings(id) ON DELETE SET NULL,
  transaction_type VARCHAR(20) NOT NULL DEFAULT 'registration_payment',
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'MYR',
  status VARCHAR(20) DEFAULT 'pending',
  gateway_transaction_id VARCHAR(255),
  gateway_response JSONB DEFAULT '{}',
  invoice_number VARCHAR(50) UNIQUE,              -- Auto-generated
  receipt_number VARCHAR(50) UNIQUE,              -- Auto-generated when paid
  group_transaction_id UUID,                      -- For group registrations
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Enhanced Registrations Table
```sql
-- New columns added:
ALTER TABLE registrations ADD COLUMN transaction_id UUID REFERENCES transactions(id);
ALTER TABLE registrations ADD COLUMN group_registration_id UUID;
```

## Payment Flow

### 1. **Invoice Generation** (Payment Initiated)
- User initiates payment for event registration
- Transaction record created with status `pending`
- Auto-generated invoice number (INV1000, INV1001, etc.)
- Payment gateway URL provided to user

### 2. **Payment Processing**
- User completes payment at gateway
- Transaction status updated to `processing`
- Gateway transaction ID stored

### 3. **Payment Confirmation**
- Payment gateway confirms payment
- Transaction status updated to `paid`
- Auto-generated receipt number (RCP1000, RCP1001, etc.)
- `processed_at` timestamp set
- Registration status updated to `confirmed`

### 4. **Receipt Access**
- Receipt button only shown for paid transactions
- Receipt includes proper transaction details:
  - Receipt number
  - Invoice number
  - Gateway transaction ID
  - Payment date
  - Participant details

## Group Registration Logic

### Single Transaction for Multiple Participants
```javascript
// Example: 3 people registering for the same event
const groupTransactionId = crypto.randomUUID();

// Create one transaction for the group
const transaction = {
  user_id: createdBy,
  amount: totalAmount, // Sum of all participants
  status: 'pending',
  group_transaction_id: groupTransactionId
};

// Create multiple registrations linked to the same transaction
const registrations = participants.map(participant => ({
  user_id: participant.userId,
  created_by: createdBy,
  transaction_id: transaction.id,
  group_registration_id: groupTransactionId,
  // ... other fields
}));
```

### Group Receipt Features
- Single receipt for entire group
- Lists all participants
- Shows total amount paid
- Same transaction ID for all participants
- Accessible via `/api/tickets/group-receipt?transactionId=xxx`

## API Endpoints

### Payment APIs
- `POST /api/registrations/payment` - Initiate payment (creates transaction)
- `POST /api/registrations/verify-payment` - Confirm payment (updates transaction)

### Receipt APIs
- `GET /api/tickets/view-pdf?ticketId=xxx&type=receipt` - Individual receipt
- `GET /api/tickets/group-receipt?transactionId=xxx` - Group receipt

### Test APIs
- `POST /api/test-transaction` - Create test transaction
- `POST /api/test-transaction/confirm` - Confirm test payment

## UI Changes

### My Tickets Page
- Receipt button only shown for paid registrations
- Checks for `ticket.payment_status === 'paid'` AND transaction data exists
- Enhanced transaction data fetching in tickets API

### Receipt Display Logic
```javascript
// Only show receipt if payment is confirmed and has transaction data
{(ticket.payment_status === 'paid' && 
  (ticket.transaction?.receipt_number || ticket.payment_date)) && (
  <Button onClick={() => handleViewTicket(ticket, "receipt")}>
    <Receipt className="mr-2 h-4 w-4" />
    Receipt
  </Button>
)}
```

## Auto-Generated Numbers

### Invoice Numbers
- Format: `INV1000`, `INV1001`, `INV1002`, etc.
- Generated when transaction is created
- Sequential numbering starting from 1000

### Receipt Numbers
- Format: `RCP1000`, `RCP1001`, `RCP1002`, etc.
- Generated only when payment is confirmed (`status = 'paid'`)
- Sequential numbering starting from 1000

### Database Functions
```sql
-- Auto-generate invoice/receipt numbers
CREATE OR REPLACE FUNCTION auto_generate_invoice_receipt_numbers()
RETURNS TRIGGER AS $$
BEGIN
  -- Generate invoice number when transaction is created
  IF NEW.invoice_number IS NULL THEN
    NEW.invoice_number := generate_invoice_number();
  END IF;
  
  -- Generate receipt number when payment is confirmed
  IF NEW.status = 'paid' AND (OLD IS NULL OR OLD.status != 'paid') 
     AND NEW.receipt_number IS NULL THEN
    NEW.receipt_number := generate_receipt_number();
    NEW.processed_at := NOW();
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

## Testing

### Test Page
- Available at `/test-invoice-system`
- Create test transactions
- Confirm test payments
- Verify invoice/receipt number generation

### Manual Testing Steps
1. Create event registration
2. Initiate payment (invoice number generated)
3. Complete payment (receipt number generated)
4. Verify receipt is accessible
5. Check transaction data in database

## Migration Applied

The database migration `create_transactions_and_fix_invoice_system` has been successfully applied to the production database, including:

- ✅ Transactions table created
- ✅ Auto-generation functions installed
- ✅ Triggers configured
- ✅ Indexes created for performance
- ✅ Foreign key relationships established

## Next Steps

1. **Test the system** using the test page at `/test-invoice-system`
2. **Monitor transaction creation** in production
3. **Verify receipt generation** for real payments
4. **Implement group registration UI** if needed
5. **Add receipt email notifications** (future enhancement)

The invoice/receipt system is now properly implemented with transaction tracking, auto-generated numbers, and group registration support!
