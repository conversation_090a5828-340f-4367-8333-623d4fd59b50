-- Create transactions table and fix invoice/receipt system
-- This migration creates the missing transactions table and enhances the payment system

-- Create transactions table if it doesn't exist
CREATE TABLE IF NOT EXISTS transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  registration_id UUID REFERENCES registrations(id) ON DELETE SET NULL,
  subscription_id UUID REFERENCES user_subscription(id) ON DELETE SET NULL,
  gateway_id UUID REFERENCES payment_gateway_settings(id) ON DELETE SET NULL,
  transaction_type VARCHAR(20) NOT NULL DEFAULT 'registration_payment',
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'MYR',
  status VARCHAR(20) DEFAULT 'pending',
  gateway_transaction_id VARCHAR(255),
  gateway_response JSONB DEFAULT '{}',
  invoice_number VARCHAR(50) UNIQUE,
  receipt_number VARCHAR(50) UNIQUE,
  group_transaction_id UUID, -- For linking group registrations
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_transactions_user_id ON transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_registration_id ON transactions(registration_id);
CREATE INDEX IF NOT EXISTS idx_transactions_gateway_transaction_id ON transactions(gateway_transaction_id);
CREATE INDEX IF NOT EXISTS idx_transactions_group_transaction_id ON transactions(group_transaction_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);

-- Add transaction_id column to registrations table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'registrations' AND column_name = 'transaction_id') THEN
    ALTER TABLE registrations ADD COLUMN transaction_id UUID REFERENCES transactions(id) ON DELETE SET NULL;
  END IF;
END $$;

-- Create index for registrations.transaction_id
CREATE INDEX IF NOT EXISTS idx_registrations_transaction_id ON registrations(transaction_id);

-- Add group_registration_id to registrations for better group tracking
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'registrations' AND column_name = 'group_registration_id') THEN
    ALTER TABLE registrations ADD COLUMN group_registration_id UUID;
  END IF;
END $$;

-- Create index for group registrations
CREATE INDEX IF NOT EXISTS idx_registrations_group_registration_id ON registrations(group_registration_id);

-- Create function to generate invoice numbers
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS TEXT AS $$
DECLARE
  next_number INTEGER;
  invoice_number TEXT;
BEGIN
  -- Get the next invoice number (starting from 1000 for better formatting)
  SELECT COALESCE(MAX(CAST(SUBSTRING(invoice_number FROM 4) AS INTEGER)), 999) + 1
  INTO next_number
  FROM transactions
  WHERE invoice_number IS NOT NULL AND invoice_number ~ '^INV[0-9]+$';

  -- Format as INV1000, INV1001, etc.
  invoice_number := 'INV' || next_number::TEXT;

  RETURN invoice_number;
END;
$$ LANGUAGE plpgsql;

-- Create function to generate receipt numbers
CREATE OR REPLACE FUNCTION generate_receipt_number()
RETURNS TEXT AS $$
DECLARE
  next_number INTEGER;
  receipt_number TEXT;
BEGIN
  -- Get the next receipt number (starting from 1000 for better formatting)
  SELECT COALESCE(MAX(CAST(SUBSTRING(receipt_number FROM 4) AS INTEGER)), 999) + 1
  INTO next_number
  FROM transactions
  WHERE receipt_number IS NOT NULL AND receipt_number ~ '^RCP[0-9]+$';

  -- Format as RCP1000, RCP1001, etc.
  receipt_number := 'RCP' || next_number::TEXT;

  RETURN receipt_number;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-generate invoice numbers
CREATE OR REPLACE FUNCTION auto_generate_invoice_receipt_numbers()
RETURNS TRIGGER AS $$
BEGIN
  -- Generate invoice number when transaction is created
  IF NEW.invoice_number IS NULL THEN
    NEW.invoice_number := generate_invoice_number();
  END IF;

  -- Generate receipt number when payment is confirmed
  IF NEW.status = 'paid' AND (OLD IS NULL OR OLD.status != 'paid') AND NEW.receipt_number IS NULL THEN
    NEW.receipt_number := generate_receipt_number();
    NEW.processed_at := NOW();
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add trigger for auto-generating numbers
DROP TRIGGER IF EXISTS trigger_auto_generate_invoice_receipt_numbers ON transactions;
CREATE TRIGGER trigger_auto_generate_invoice_receipt_numbers
  BEFORE INSERT OR UPDATE ON transactions
  FOR EACH ROW
  EXECUTE FUNCTION auto_generate_invoice_receipt_numbers();

-- Add trigger for updated_at
DROP TRIGGER IF EXISTS update_transactions_updated_at ON transactions;
CREATE TRIGGER update_transactions_updated_at
  BEFORE UPDATE ON transactions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Add comments to document the purpose
COMMENT ON TABLE transactions IS 'Financial transactions for payments, refunds, and other monetary operations';
COMMENT ON COLUMN transactions.group_transaction_id IS 'Links multiple registrations in a group payment to the same transaction';
COMMENT ON COLUMN transactions.invoice_number IS 'Auto-generated invoice number (INV1000, INV1001, etc.)';
COMMENT ON COLUMN transactions.receipt_number IS 'Auto-generated receipt number when payment is confirmed (RCP1000, RCP1001, etc.)';
COMMENT ON COLUMN registrations.transaction_id IS 'Links registration to its payment transaction';
COMMENT ON COLUMN registrations.group_registration_id IS 'Groups multiple registrations created together';