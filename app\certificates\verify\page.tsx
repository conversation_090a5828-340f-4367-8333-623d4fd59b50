"use client"

import { useState, useEffect } from "react"
import Head from "next/head"
import { useSearchParams } from "next/navigation"
import { CheckCircle, Search, XCircle, Clock } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useToast } from "@/hooks/use-toast"

import { CertificateAuditTrail } from "@/components/certificate-audit-trail"
import MainNav from "@/components/main-nav"

export default function VerifyCertificatePage() {
  const searchParams = useSearchParams()
  const [verificationCode, setVerificationCode] = useState("")
  const [isVerifying, setIsVerifying] = useState(false)
  const [verificationResult, setVerificationResult] = useState<any | null>(null)
  const [verificationError, setVerificationError] = useState<string | null>(null)
  const { toast } = useToast()

  // Auto-populate code from URL parameter and verify automatically
  useEffect(() => {
    const codeFromUrl = searchParams.get('code')
    if (codeFromUrl) {
      setVerificationCode(codeFromUrl)
      // Auto-verify if code is provided in URL
      handleVerifyWithCode(codeFromUrl)
    }
  }, [searchParams])

  const handleVerifyWithCode = async (code: string) => {
    if (!code.trim()) {
      toast({
        title: "Error",
        description: "Please enter a verification code",
        variant: "destructive",
      })
      return
    }

    setIsVerifying(true)
    setVerificationResult(null)
    setVerificationError(null)

    try {
      const response = await fetch(`/api/certificates/verify?code=${encodeURIComponent(code)}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to verify certificate")
      }

      setVerificationResult(data)
    } catch (error: any) {
      console.error("Error verifying certificate:", error)
      setVerificationError(error.message || "Failed to verify certificate")
      toast({
        title: "Error",
        description: error.message || "Failed to verify certificate",
        variant: "destructive",
      })
    } finally {
      setIsVerifying(false)
    }
  }

  const handleVerify = async () => {
    await handleVerifyWithCode(verificationCode)
  }

  return (
    <>
      <Head>
        <title>Certificate Verification | mTicket.my - Event Management Platform</title>
        <meta name="description" content="Verify the authenticity of certificates issued through mTicket.my platform" />
      </Head>

      <div className="flex min-h-screen flex-col">
        <MainNav />

      {/* Purple background header section */}
      <div className="bg-gradient-to-r from-purple-600 to-purple-800 py-16">
        <div className="container mx-auto px-4">
          <div className="mx-auto max-w-4xl text-center">
            <h1 className="mb-4 text-4xl font-bold text-white">Certificate Verification</h1>
            <p className="text-lg text-purple-100">
              Verify the authenticity of a certificate by entering the verification code
            </p>
          </div>
        </div>
      </div>

      <main className="flex-1 bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="mx-auto max-w-4xl">

            <Card>
              <CardHeader>
                <CardTitle>Verify Certificate</CardTitle>
                <CardDescription>Enter the verification code found on the certificate</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center gap-2">
                  <Input
                    placeholder="Enter verification code"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value)}
                    className="flex-1"
                  />
                  <Button onClick={handleVerify} disabled={isVerifying}>
                    {isVerifying ? (
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                    ) : (
                      <Search className="h-4 w-4" />
                    )}
                    <span className="ml-2">{isVerifying ? "Verifying..." : "Verify"}</span>
                  </Button>
                </div>

                {verificationResult && (
                  <div className="mt-6 space-y-6">
                    <Alert variant="success">
                      <CheckCircle className="h-4 w-4" />
                      <AlertTitle>Certificate Verified</AlertTitle>
                      <AlertDescription>This certificate is authentic and valid.</AlertDescription>
                    </Alert>

                    {/* Certificate Details Card */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          🏆 Certificate Details
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="grid grid-cols-3 gap-2">
                            <div className="text-sm font-medium">Participant:</div>
                            <div className="col-span-2 text-sm">{verificationResult.certificate.participant_name}</div>
                          </div>
                          <div className="grid grid-cols-3 gap-2">
                            <div className="text-sm font-medium">Event:</div>
                            <div className="col-span-2 text-sm">{verificationResult.event.title}</div>
                          </div>
                          <div className="grid grid-cols-3 gap-2">
                            <div className="text-sm font-medium">Organizer:</div>
                            <div className="col-span-2 text-sm">{verificationResult.organizer}</div>
                          </div>
                          <div className="grid grid-cols-3 gap-2">
                            <div className="text-sm font-medium">Event Date:</div>
                            <div className="col-span-2 text-sm">
                              {new Date(verificationResult.event.start_date).toLocaleDateString()} -{" "}
                              {new Date(verificationResult.event.end_date).toLocaleDateString()}
                            </div>
                          </div>
                          <div className="grid grid-cols-3 gap-2">
                            <div className="text-sm font-medium">Location:</div>
                            <div className="col-span-2 text-sm">{verificationResult.event.location}</div>
                          </div>
                          <div className="grid grid-cols-3 gap-2">
                            <div className="text-sm font-medium">Certificate Issued:</div>
                            <div className="col-span-2 text-sm">
                              {new Date(verificationResult.certificate.issued_at).toLocaleDateString()}
                            </div>
                          </div>

                        </div>
                      </CardContent>
                    </Card>

                    {/* Enhanced Audit Trail */}
                    {verificationResult.certificate && (
                      <div className="space-y-6">
                        <Card>
                          <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                              <Clock className="h-5 w-5" />
                              Complete Audit Trail
                            </CardTitle>
                            <CardDescription>
                              Complete journey from registration to certificate verification - comprehensive audit trail with foreign key relationships
                            </CardDescription>
                          </CardHeader>
                          <CardContent>
                            <CertificateAuditTrail certificateId={verificationResult.certificate.id} />
                          </CardContent>
                        </Card>


                      </div>
                    )}
                  </div>
                )}

                {verificationError && (
                  <Alert variant="destructive" className="mt-6">
                    <XCircle className="h-4 w-4" />
                    <AlertTitle>Verification Failed</AlertTitle>
                    <AlertDescription>{verificationError}</AlertDescription>
                  </Alert>
                )}
              </CardContent>
              <CardFooter className="flex justify-center">
                <p className="text-center text-sm text-muted-foreground">
                  If you have any questions about certificate verification, <NAME_EMAIL>.
                </p>
              </CardFooter>
            </Card>
          </div>
        </div>
      </main>
    </div>
    </>
  )
}
