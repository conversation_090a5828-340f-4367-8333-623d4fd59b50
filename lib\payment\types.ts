// Payment system types

export type PaymentGatewayType = "billplz" | "toyyibpay" | "chip" | "stripe"

export type PaymentGatewayConfig = {
  id: string
  name: string
  type: PaymentGatewayType
  enabled: boolean
  description?: string
  fields: Record<string, string>
}

export type PaymentRequest = {
  amount: number
  currency: string
  description: string
  customer_email: string
  customer_name: string
  customer_phone?: string // Optional phone number
  reference_id: string
  success_url: string
  cancel_url: string
  payment_gateway_id?: string // Reference to the payment gateway used
}

export type PaymentResponse = {
  success: boolean
  payment_url?: string
  transaction_id?: string
  error?: string
  payment_gateway_id?: string // Reference to the payment gateway used
}

export interface PaymentGateway {
  createPayment(config: PaymentGatewayConfig, request: PaymentRequest): Promise<PaymentResponse>
  verifyPayment?(transactionId: string): Promise<boolean>
}
