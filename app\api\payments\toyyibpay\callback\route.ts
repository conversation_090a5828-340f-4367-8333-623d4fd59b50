import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"
import { logActivity } from "@/lib/activity-logger"
import { createUser } from "@/lib/auth"

// Initialize Supabase admin client
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    console.log("ToyyibPay callback received")

    // Parse the form data from ToyyibPay callback
    const formData = await request.formData()

    const refno = formData.get('refno') as string
    const status = formData.get('status') as string
    const reason = formData.get('reason') as string
    const billcode = formData.get('billcode') as string
    const order_id = formData.get('order_id') as string
    const amount = formData.get('amount') as string
    const transaction_time = formData.get('transaction_time') as string

    console.log("ToyyibPay callback data:", {
      refno,
      status,
      reason,
      billcode,
      order_id,
      amount,
      transaction_time
    })

    if (!order_id || !status) {
      console.error("Missing required callback data")
      return NextResponse.json({ error: "Missing required data" }, { status: 400 })
    }

    // Extract payment reference from order_id (format: payment_<timestamp>_<random>)
    const paymentReference = order_id

    // Get the transaction record to find the registration data
    const { data: transaction, error: transactionError } = await supabaseAdmin
      .from("transactions")
      .select("*")
      .eq("gateway_transaction_id", billcode)
      .or(`gateway_transaction_id.eq.${refno}`)
      .single()

    if (transactionError || !transaction) {
      console.error("Transaction not found:", transactionError)
      return NextResponse.json({ error: "Transaction not found" }, { status: 404 })
    }

    const registrationData = transaction.metadata
    if (!registrationData) {
      console.error("No registration data found in transaction metadata")
      return NextResponse.json({ error: "Registration data not found" }, { status: 400 })
    }

    // Determine payment status based on ToyyibPay status
    // 1 = success, 2 = pending, 3 = fail
    let paymentStatus: string
    let registrationStatus: string

    switch (status) {
      case '1':
        paymentStatus = 'paid'
        registrationStatus = 'confirmed'
        break
      case '2':
        paymentStatus = 'processing'
        registrationStatus = 'pending'
        break
      case '3':
        paymentStatus = 'failed'
        registrationStatus = 'cancelled'
        break
      default:
        paymentStatus = 'failed'
        registrationStatus = 'cancelled'
    }

    let registrationIds: string[] = []
    let userId: string | null = null

    // Only create registrations if payment is successful
    if (paymentStatus === 'paid') {
      console.log("Payment successful, creating registrations...")

      // Create registrations from the stored data
      const { event_id, participants, main_contact, is_public_registration } = registrationData

      // For public registrations, check if user exists or create new user
      if (is_public_registration) {
        console.log("Processing public registration, checking if user exists...")

        // Check if user with main contact email already exists
        const { data: existingUser, error: userCheckError } = await supabaseAdmin
          .from("users")
          .select("id")
          .eq("email", main_contact.email)
          .single()

        if (existingUser) {
          console.log("User already exists:", existingUser.id)
          userId = existingUser.id
        } else {
          console.log("Creating new user account for main contact...")
          try {
            // Generate a temporary password for the new user
            const tempPassword = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)

            // Create new user account
            const newUser = await createUser(main_contact.email, tempPassword, main_contact.name, "user")
            userId = newUser.id
            console.log("Created new user:", userId)

            // TODO: Send welcome email with password reset link
            // This would be implemented in a separate email service

          } catch (createUserError) {
            console.error("Error creating user:", createUserError)
            // Continue with registration even if user creation fails
            userId = null
          }
        }
      } else {
        // For authenticated registrations, use the existing user_id
        userId = registrationData.user_id
      }

      const registrations = participants.map((participant: any, index: number) => {
        const registration = {
          id: crypto.randomUUID(),
          event_id: event_id,
          user_id: userId,
          participant_name: participant.name,
          participant_email: participant.email,
          participant_phone: participant.phone,
          participant_ic: participant.ic,
          is_main_contact: index === registrationData.main_contact_index || participant.email === main_contact.email,
          main_contact_name: main_contact.name,
          main_contact_email: main_contact.email,
          main_contact_phone: main_contact.phone,
          status: registrationStatus,
          payment_status: paymentStatus,
          payment_amount: parseFloat(amount) / participants.length, // Distribute amount among participants
          ticket_type_id: participant.ticket_type_id || null,
          ticket_type_name: participant.ticket_type_name || null,
          custom_field_responses: participant.custom_field_responses || {},
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }

        return registration
      })

      // Insert the registrations
      const { data: insertedRegistrations, error: insertError } = await supabaseAdmin
        .from("registrations")
        .insert(registrations)
        .select("id")

      if (insertError) {
        console.error("Error creating registrations:", insertError)
        // Don't fail the callback, but log the error
      } else {
        registrationIds = insertedRegistrations.map(reg => reg.id)
        console.log("Created registrations:", registrationIds)
      }
    }

    // Update the transaction record
    const { error: updateTransactionError } = await supabaseAdmin
      .from("transactions")
      .update({
        status: paymentStatus,
        user_id: userId, // Update with the user ID (existing or newly created)
        registration_id: registrationIds.length > 0 ? registrationIds[0] : null, // Link to first registration
        gateway_response: JSON.stringify({
          refno,
          status,
          reason,
          billcode,
          order_id,
          amount,
          transaction_time
        }),
        completed_at: paymentStatus === 'paid' ? new Date().toISOString() : null,
        updated_at: new Date().toISOString()
      })
      .eq("id", transaction.id)

    if (updateTransactionError) {
      console.error("Error updating transaction:", updateTransactionError)
    }

    // Log activity
    await logActivity({
      action: paymentStatus === 'paid' ? "payment_completed" : "payment_failed",
      user_id: userId, // Use the actual user ID (existing or newly created)
      details: {
        registration_ids: registrationIds,
        payment_status: paymentStatus,
        transaction_id: refno || billcode,
        amount: amount,
        gateway: "ToyyibPay",
        reason: reason,
        participants_count: registrationData.participants?.length || 0,
        is_public_registration: registrationData.is_public_registration || false,
        main_contact_email: registrationData.main_contact?.email
      },
    })

    console.log(`Payment ${paymentStatus} for transaction ${transaction.id}`)

    return NextResponse.json({
      success: true,
      message: `Payment ${paymentStatus}`,
      registration_ids: registrationIds,
      status: paymentStatus
    })

  } catch (error: any) {
    console.error("ToyyibPay callback error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
