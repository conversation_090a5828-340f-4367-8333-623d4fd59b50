import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"
import { getUserFromToken } from "@/lib/auth/token"
import { requireSubscriptionFeature } from "@/lib/subscription-utils"

export async function GET(request: NextRequest) {
  try {
    // Get user from token
    const userToken = await getUserFromToken(request)
    if (!userToken) {
      return NextResponse.json(
        {
          success: false,
          error: "Authentication required",
        },
        { status: 401 }
      )
    }

    const supabase = createClient()

    // Get user details
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userToken.userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        {
          success: false,
          error: "User not found",
        },
        { status: 404 }
      )
    }

    // Check subscription feature access
    const featureCheck = await requireSubscriptionFeature(userToken.userId, 'webhooks_enabled', user.role_name)
    if (!featureCheck.hasAccess) {
      return NextResponse.json(
        {
          success: false,
          error: featureCheck.error,
        },
        { status: 403 }
      )
    }

    // Fetch user's webhooks
    const { data: webhooks, error: webhooksError } = await supabase
      .from('webhooks')
      .select('*')
      .eq('user_id', userToken.userId)
      .order('created_at', { ascending: false })

    if (webhooksError) {
      console.error("Error fetching webhooks:", webhooksError)
      return NextResponse.json(
        {
          success: false,
          error: "Failed to fetch webhooks",
        },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      webhooks: webhooks || [],
    })

  } catch (error) {
    console.error("Error in webhooks API:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get user from token
    const userToken = await getUserFromToken(request)
    if (!userToken) {
      return NextResponse.json(
        {
          success: false,
          error: "Authentication required",
        },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name, url, events, active = true } = body

    // Validate required fields
    if (!name || !url || !events || !Array.isArray(events)) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required fields: name, url, events",
        },
        { status: 400 }
      )
    }

    const supabase = createClient()

    // Get user details
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userToken.userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        {
          success: false,
          error: "User not found",
        },
        { status: 404 }
      )
    }

    // Check subscription feature access
    const featureCheck = await requireSubscriptionFeature(userToken.userId, 'webhooks_enabled', user.role_name)
    if (!featureCheck.hasAccess) {
      return NextResponse.json(
        {
          success: false,
          error: featureCheck.error,
        },
        { status: 403 }
      )
    }

    // Create webhook
    const { data: webhook, error: webhookError } = await supabase
      .from('webhooks')
      .insert({
        user_id: userToken.userId,
        name,
        url,
        events,
        active,
        success_count: 0,
        failure_count: 0,
      })
      .select()
      .single()

    if (webhookError) {
      console.error("Error creating webhook:", webhookError)
      return NextResponse.json(
        {
          success: false,
          error: "Failed to create webhook",
        },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      webhook,
    })

  } catch (error) {
    console.error("Error creating webhook:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    )
  }
}
