"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import {
  Plus,
  Filter,
  Eye,
  Edit,
  Users,
  FileText,
  Archive,
  Calendar,
  MapPin,
  Trash,
  CheckCircle,
  MoreHorizontal,
} from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"
import { useAuth } from "@/contexts/auth-context"
import { seedEvents } from "@/utils/seed-data"
import { getEventInitials, generateEventGradient, formatDateBadge, getEventDisplayImage } from "@/lib/utils"
import type { ToastProps } from "@/components/ui/toast"

type ToastFunction = (props: {
  title: string;
  description: string;
  variant?: 'default' | 'destructive' | 'success' | 'warning' | null | undefined;
}) => void;

export default function EventsPage() {
  const router = useRouter()
  const { toast } = useToast() as { toast: ToastFunction }
  const { user, loading: authLoading } = useAuth()
  const [events, setEvents] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [view, setView] = useState<"grid" | "table">("table")
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedEvent, setSelectedEvent] = useState<any>(null)
  const [isArchiveDialogOpen, setIsArchiveDialogOpen] = useState(false)
  const [needsSeeding, setNeedsSeeding] = useState(false)
  const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({})

  // Helper function to get auth token from cookies
  const getAuthToken = (): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; auth_token=`);
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
    return null;
  };

  useEffect(() => {
    const fetchEvents = async () => {
      setLoading(true);
      try {
        // Get auth token
        const token = getAuthToken();
        if (!token) {
          throw new Error("No authentication token found");
        }

        // Fetch data from API endpoint
        const response = await fetch('/api/dashboard/events', {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          if (response.status === 401) {
            throw new Error("Authentication failed. Please log in again.");
          }
          throw new Error(`Failed to fetch events: ${response.status}`);
        }

        const data = await response.json();

        if (data.error) {
          throw new Error(data.error);
        }

        const eventsData = data.events || [];
        setEvents(eventsData);

        // Show seeding option if no events exist
        setNeedsSeeding(eventsData.length === 0);

      } catch (error: any) {
        console.error("Error fetching events:", error);
        toast({
          title: "Error",
          description: error.message || "Failed to fetch events. Please try again later.",
          variant: "destructive",
        });

        // If authentication failed, clear events
        if (error.message.includes("Authentication")) {
          setEvents([]);
        }
      } finally {
        setLoading(false);
      }
    };

    // Only fetch events if user is authenticated and not loading
    if (!authLoading && user) {
      fetchEvents();
    } else if (!authLoading && !user) {
      setLoading(false);
      setEvents([]);
    }
  }, [user, authLoading, toast])

  const handleSeedEvents = async () => {
    setLoading(true);
    try {
      const success = await seedEvents();
      if (success) {
        toast({
          title: "Success",
          description: "Demo events created successfully!",
          variant: "default"
        });
        // Refresh the page to show the new events
        window.location.reload()
      } else {
        throw new Error("Failed to seed events")
      }
    } catch (error) {
      console.error("Error seeding events:", error)
      toast({
        title: "Error",
        description: "Failed to create demo events. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleArchive = async () => {
    if (!selectedEvent) return

    try {
      // Archive by unpublishing the event
      const { error } = await supabase
        .from("events")
        .update({ is_published: false })
        .eq("id", selectedEvent.id)

      if (error) throw error

      // Update local state - mark as cancelled/archived
      const updatedEvent = { ...selectedEvent, status: "cancelled", is_published: false }
      setEvents(
        events.map((event) => {
          if (event.id === selectedEvent.id) {
            return updatedEvent
          }
          return event
        }),
      )

      // Log the event archive activity
      try {
        await supabase.from("activity_logs").insert([
          {
            user_id: user?.id,
            action: "archive_event",
            entity_type: "event",
            entity_id: selectedEvent.id,
            category: "event",
            details: {
              event_name: selectedEvent.title,
              previous_status: selectedEvent.status,
              new_status: "cancelled",
            },
            created_at: new Date().toISOString(),
          },
        ])
      } catch (logError) {
        // Don't fail event archiving if logging fails
        console.error("Error logging event archive activity:", logError)
      }

      toast({
        title: "Success",
        description: "Event archived successfully",
      })
    } catch (error) {
      console.error("Error archiving event:", error)
      toast({
        title: "Error",
        description: "Failed to archive event",
        variant: "destructive",
      })
    } finally {
      setIsArchiveDialogOpen(false)
      setSelectedEvent(null)
    }
  }

  const filteredEvents = events.filter((event) => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();
    return (
      event.title?.toLowerCase().includes(query) ||
      event.description?.toLowerCase().includes(query) ||
      event.location?.toLowerCase().includes(query)
    );
  })

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString()
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "published":
        return <Badge variant="default">Published</Badge>
      case "draft":
        return <Badge variant="secondary">Draft</Badge>
      case "cancelled":
        return <Badge variant="outline">Cancelled</Badge>
      case "completed":
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const isPastEvent = (endDate: string) => {
    return new Date(endDate) < new Date()
  }

  const handleImageError = (eventId: string) => {
    setImageErrors(prev => ({ ...prev, [eventId]: true }))
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-4 p-4 md:p-8">
        <div className="flex h-40 items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Events</h2>
          <p className="text-muted-foreground">Manage your events and track registrations</p>
        </div>
        <div className="flex gap-2">
          {needsSeeding && (
            <Button onClick={handleSeedEvents} variant="outline">
              Create Demo Events
            </Button>
          )}
          <Link href="/dashboard/events/create">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Event
            </Button>
          </Link>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        <div className="flex w-full md:w-auto items-center gap-2">
          <Input
            placeholder="Search events..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full md:w-[300px]"
          />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>All Events</DropdownMenuItem>
              <DropdownMenuItem>Published</DropdownMenuItem>
              <DropdownMenuItem>Draft</DropdownMenuItem>
              <DropdownMenuItem>Cancelled</DropdownMenuItem>
              <DropdownMenuItem>Completed</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant={view === "grid" ? "default" : "outline"}
            size="sm"
            onClick={() => setView("grid")}
            className="hidden md:flex"
          >
            Grid View
          </Button>
          <Button
            variant={view === "table" ? "default" : "outline"}
            size="sm"
            onClick={() => setView("table")}
            className="hidden md:flex"
          >
            Table View
          </Button>
        </div>
      </div>

      <Tabs defaultValue="upcoming">
        <TabsList>
          <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
          <TabsTrigger value="past">Past</TabsTrigger>
          <TabsTrigger value="draft">Draft</TabsTrigger>
          <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
        </TabsList>

        {view === "table" ? (
          <>
            <TabsContent value="upcoming" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Upcoming Events</CardTitle>
                  <CardDescription>Manage your upcoming events</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Event Name</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Attendees</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredEvents
                        .filter(
                          (event) =>
                            !isPastEvent(event.end_date) && event.status !== "draft" && event.status !== "cancelled",
                        )
                        .map((event) => (
                          <TableRow
                            key={event.id}
                            className="hover:bg-muted/50 cursor-pointer"
                            onClick={(e) => {
                              // Don't navigate if clicking on action buttons
                              if (!(e.target instanceof HTMLElement) ||
                                  e.target.closest('button, a, [role="button"]')) {
                                return;
                              }
                              router.push(`/dashboard/events/${event.slug}`);
                            }}
                          >
                            <TableCell className="font-medium">{event.title}</TableCell>
                            <TableCell>{formatDate(event.start_date)}</TableCell>
                            <TableCell>{event.location}</TableCell>
                            <TableCell>{getStatusBadge(event.status)}</TableCell>
                            <TableCell>
                              {event.current_participants} / {event.max_participants || "∞"}
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}`)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}/edit`)}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=attendees`)}>
                                    <Users className="mr-2 h-4 w-4" />
                                    Attendees
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=certificates`)}>
                                    <FileText className="mr-2 h-4 w-4" />
                                    Certificates
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setSelectedEvent(event);
                                      setIsArchiveDialogOpen(true);
                                    }}
                                    className="text-red-600"
                                  >
                                    <Archive className="mr-2 h-4 w-4" />
                                    Archive
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="past" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Past Events</CardTitle>
                  <CardDescription>View your past events</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Event Name</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Attendees</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredEvents
                        .filter((event) => isPastEvent(event.end_date) && event.status !== "cancelled")
                        .map((event) => (
                          <TableRow
                            key={event.id}
                            className="hover:bg-muted/50 cursor-pointer"
                            onClick={(e) => {
                              // Don't navigate if clicking on action buttons
                              if (!(e.target instanceof HTMLElement) ||
                                  e.target.closest('button, a, [role="button"]')) {
                                return;
                              }
                              router.push(`/dashboard/events/${event.slug}`);
                            }}
                          >
                            <TableCell className="font-medium">{event.title}</TableCell>
                            <TableCell>{formatDate(event.start_date)}</TableCell>
                            <TableCell>{event.location}</TableCell>
                            <TableCell>{getStatusBadge(event.status)}</TableCell>
                            <TableCell>
                              {event.current_participants} / {event.max_participants || "∞"}
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}`)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}/edit`)}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=attendees`)}>
                                    <Users className="mr-2 h-4 w-4" />
                                    Attendees
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=certificates`)}>
                                    <FileText className="mr-2 h-4 w-4" />
                                    Certificates
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setSelectedEvent(event);
                                      setIsArchiveDialogOpen(true);
                                    }}
                                    className="text-red-600"
                                  >
                                    <Archive className="mr-2 h-4 w-4" />
                                    Archive
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="draft" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Draft Events</CardTitle>
                  <CardDescription>Manage your draft events</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Event Name</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredEvents
                        .filter((event) => event.status === "draft")
                        .map((event) => (
                          <TableRow
                            key={event.id}
                            className="hover:bg-muted/50 cursor-pointer"
                            onClick={(e) => {
                              // Don't navigate if clicking on action buttons
                              if (!(e.target instanceof HTMLElement) ||
                                  e.target.closest('button, a, [role="button"]')) {
                                return;
                              }
                              router.push(`/dashboard/events/${event.slug}`);
                            }}
                          >
                            <TableCell className="font-medium">{event.title}</TableCell>
                            <TableCell>{formatDate(event.start_date)}</TableCell>
                            <TableCell>{event.location}</TableCell>
                            <TableCell>{getStatusBadge(event.status)}</TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}`)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}/edit`)}>
                                    <Edit className="mr-2 h-4 w-4" />
                                    Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=attendees`)}>
                                    <Users className="mr-2 h-4 w-4" />
                                    Attendees
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=certificates`)}>
                                    <FileText className="mr-2 h-4 w-4" />
                                    Certificates
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => {
                                    // Publish event logic - to be implemented
                                  }}>
                                    <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                                    Publish
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setSelectedEvent(event);
                                      setIsArchiveDialogOpen(true);
                                    }}
                                    className="text-red-600"
                                  >
                                    <Trash className="mr-2 h-4 w-4" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="cancelled" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Cancelled Events</CardTitle>
                  <CardDescription>View your cancelled events</CardDescription>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Event Name</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredEvents
                        .filter((event) => event.status === "cancelled")
                        .map((event) => (
                          <TableRow key={event.id}>
                            <TableCell className="font-medium">{event.title}</TableCell>
                            <TableCell>{formatDate(event.start_date)}</TableCell>
                            <TableCell>{event.location}</TableCell>
                            <TableCell>{getStatusBadge(event.status)}</TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="icon">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}`)}>
                                    <Eye className="mr-2 h-4 w-4" />
                                    View
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=attendees`)}>
                                    <Users className="mr-2 h-4 w-4" />
                                    Attendees
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=certificates`)}>
                                    <FileText className="mr-2 h-4 w-4" />
                                    Certificates
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>
          </>
        ) : (
          <>
            {/* Grid view content - keeping this as a fallback */}
            <TabsContent value="upcoming" className="mt-6">
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {filteredEvents
                  .filter(
                    (event) => !isPastEvent(event.end_date) && event.status !== "draft" && event.status !== "cancelled",
                  )
                  .map((event) => (
                    <Card key={event.id} className="overflow-hidden">
                      <div className="aspect-video w-full overflow-hidden relative">
                        {(() => {
                          const displayImage = getEventDisplayImage(event)
                          return displayImage && !imageErrors[event.id] ? (
                            <Image
                              src={displayImage.url}
                              alt={displayImage.alt}
                              fill
                              className="object-cover"
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                              onError={() => handleImageError(event.id)}
                            />
                          ) : (
                            (() => {
                              const gradient = generateEventGradient(event.title)
                              return (
                                <div
                                  className={`w-full h-full ${gradient.className} flex items-center justify-center`}
                                  style={gradient.style}
                                >
                                  <span className="text-white font-bold text-xl md:text-2xl">
                                    {getEventInitials(event.title)}
                                  </span>
                                </div>
                              )
                            })()
                          )
                        })()}

                        {/* Date Badge - Top Right */}
                        {(() => {
                          const dateBadge = formatDateBadge(event.start_date)
                          return (
                            <div className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm border border-white/20 rounded-md px-2 py-1 text-center shadow-sm min-w-[50px]">
                              <div className="text-lg font-bold text-gray-800 leading-none">
                                {dateBadge.day}
                              </div>
                              <div className="text-xs font-bold text-gray-700 uppercase leading-none">
                                {dateBadge.month}
                              </div>
                              <div className="text-xs text-gray-600 leading-none">
                                {dateBadge.dayOfWeek}
                              </div>
                            </div>
                          )
                        })()}
                      </div>
                      <CardHeader className="p-4">
                        <div className="flex justify-between items-start">
                          <CardTitle className="text-lg">{event.title}</CardTitle>
                          {getStatusBadge(event.status)}
                        </div>
                        <CardDescription>{formatDate(event.start_date)}</CardDescription>
                      </CardHeader>
                      <CardContent className="p-4 pt-0">
                        <div className="space-y-2 text-sm">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span>{formatDate(event.start_date)}</span>
                          </div>
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span>{event.location}</span>
                          </div>
                          <div className="flex items-center">
                            <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span>
                              {event.current_participants} / {event.max_participants || "∞"}
                            </span>
                          </div>
                        </div>
                      </CardContent>
                      <CardFooter className="p-4 pt-0 flex justify-between">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/dashboard/events/${event.slug}`)}
                        >
                          View Details
                        </Button>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}/edit`)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=attendees`)}>
                              <Users className="mr-2 h-4 w-4" />
                              Attendees
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => router.push(`/dashboard/events/${event.slug}?tab=certificates`)}>
                              <FileText className="mr-2 h-4 w-4" />
                              Certificates
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedEvent(event)
                                setIsArchiveDialogOpen(true)
                              }}
                              className="text-red-600"
                            >
                              <Archive className="mr-2 h-4 w-4" />
                              Archive
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </CardFooter>
                    </Card>
                  ))}
              </div>
            </TabsContent>
            {/* Similar grid views for other tabs */}
          </>
        )}
      </Tabs>

      {/* Archive Confirmation Dialog */}
      <Dialog open={isArchiveDialogOpen} onOpenChange={setIsArchiveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Archive Event</DialogTitle>
            <DialogDescription>
              Are you sure you want to archive {selectedEvent?.title}? It will no longer be visible to the public.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsArchiveDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleArchive}>
              Archive
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
