"use client"

import { useState } from "react"
import Image from "next/image"
import { getEventInitials, generateEventGradient } from "@/lib/utils"
import { CountdownTimer } from "@/components/countdown-timer"

interface EventImage {
  url: string
  alt_text?: string
  order?: number
  is_primary?: boolean
}

interface EventHeroImageProps {
  images?: EventImage[]
  fallbackImageUrl?: string | null
  eventTitle: string
  className?: string
  startDate?: string | Date
  showCountdown?: boolean
}

export function EventHeroImage({
  images = [],
  fallbackImageUrl,
  eventTitle,
  className,
  startDate,
  showCountdown = false
}: EventHeroImageProps) {
  const [imageError, setImageError] = useState(false)

  // Get the primary image or first image from the array
  const primaryImage = images.find(img => img.is_primary) || images[0]

  // Use primary image, fallback to image_url, or show gradient
  const heroImageUrl = primaryImage?.url || fallbackImageUrl

  const handleImageError = () => {
    setImageError(true)
  }

  const renderFallbackImage = () => {
    const gradient = generateEventGradient(eventTitle)
    return (
      <div
        className={`w-full h-full ${gradient.className} flex items-center justify-center`}
        style={gradient.style}
      >
        <span className="text-white font-bold text-4xl md:text-6xl">
          {getEventInitials(eventTitle)}
        </span>
      </div>
    )
  }

  return (
    <div className={`relative h-64 md:h-96 w-full bg-gray-200 ${className}`}>
      {heroImageUrl && !imageError ? (
        <Image
          src={heroImageUrl}
          alt={primaryImage?.alt_text || `${eventTitle} - Hero Image`}
          fill
          className="object-cover"
          priority
          onError={handleImageError}
          sizes="100vw"
        />
      ) : (
        renderFallbackImage()
      )}

      {/* Optional overlay for better text readability */}
      <div className="absolute inset-0 bg-black/20" />

      {/* Countdown Timer positioned at bottom right, aligned with sidebar */}
      {showCountdown && startDate && (
        <>
          {/* Mobile and tablet view */}
          <div className="absolute bottom-4 right-4 md:right-6 w-56 md:w-64 lg:hidden">
            <CountdownTimer
              targetDate={startDate}
              eventTitle={eventTitle}
              variant="minimal"
            />
          </div>

          {/* Desktop view - positioned to align with sidebar */}
          <div className="hidden lg:block absolute bottom-4 inset-x-0">
            <div className="container px-4 md:px-6">
              <div className="mx-auto max-w-7xl">
                <div className="grid grid-cols-3 gap-8">
                  <div className="col-span-2"></div>
                  <div className="flex justify-start">
                    <CountdownTimer
                      targetDate={startDate}
                      eventTitle={eventTitle}
                      variant="minimal"
                      className="w-72"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}
