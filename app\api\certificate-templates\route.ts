import { NextRequest, NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { verifyUserAccess } from "@/lib/api-helpers/auth-verification"

export async function GET(request: NextRequest) {
  try {
    // Verify user authentication
    const authResult = await verifyUserAccess(request)
    if (!authResult.isAuthenticated || !authResult.userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const supabaseAdmin = getSupabaseAdmin()
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') // 'default', 'user', 'shared', 'all'

    let query = supabaseAdmin
      .from("certificate_templates")
      .select("*")
      .eq("is_active", true)

    switch (type) {
      case 'default':
        query = query.eq("is_default", true)
        break
      case 'user':
        query = query.eq("created_by", authResult.userId)
        break
      case 'shared':
        query = query.eq("is_shared", true).neq("created_by", authResult.userId)
        break
      case 'all':
        // Admin only - return all templates
        if (!authResult.isAdmin) {
          return NextResponse.json(
            { error: "Admin access required" },
            { status: 403 }
          )
        }
        // No additional filters for admin
        break
      default:
        // Return user's own templates by default
        query = query.eq("created_by", authResult.userId)
    }

    query = query.order("created_at", { ascending: false })

    const { data, error } = await query

    if (error) {
      console.error("Error fetching templates:", error)
      return NextResponse.json(
        { error: "Failed to fetch templates" },
        { status: 500 }
      )
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error("Error in GET /api/certificate-templates:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Verify user authentication
    const authResult = await verifyUserAccess(request)
    if (!authResult.isAuthenticated || !authResult.userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      name,
      description,
      fields,
      html_template,
      css_styles,
      background_image_url,
      background_color,
      show_frame,
      orientation,
      is_default
    } = body

    // Only admins can create default templates
    if (is_default && !authResult.isAdmin) {
      return NextResponse.json(
        { error: "Only admins can create default templates" },
        { status: 403 }
      )
    }

    const supabaseAdmin = getSupabaseAdmin()

    const insertData = {
      name,
      description: description || "",
      fields: fields || [],
      html_template: html_template || "",
      css_styles: css_styles || "",
      background_image_url: background_image_url || null,
      background_color: background_color || '#ffffff',
      show_frame: show_frame !== undefined ? show_frame : true,
      orientation: orientation || 'landscape',
      template_data: {},
      thumbnail_url: null,
      is_premium: false,
      is_default: is_default || false,
      is_shared: false,
      is_active: true,
      created_by: authResult.userId,
    }

    const { data, error } = await supabaseAdmin
      .from("certificate_templates")
      .insert([insertData])
      .select()
      .single()

    if (error) {
      console.error("Error creating template:", error)
      return NextResponse.json(
        { error: "Failed to create template" },
        { status: 500 }
      )
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error("Error in POST /api/certificate-templates:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
