import { PageLayout } from "@/components/page-layout"
import { HeroSection } from "@/components/hero-section"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Zap, Mail, CreditCard, Calendar, Users, BarChart } from "lucide-react"

export const metadata = {
  title: "Integrations | mTicket.my",
  description: "Connect mTicket.my with your favorite tools and services",
}

export default function IntegrationsPage() {
  const integrations = [
    {
      name: "Email Marketing",
      description: "Sync attendee data with your email marketing platform",
      icon: Mail,
      status: "Available",
      tools: ["Mailchimp", "SendGrid", "ConvertKit"]
    },
    {
      name: "Payment Gateways",
      description: "Accept payments through multiple payment providers",
      icon: CreditCard,
      status: "Available",
      tools: ["Stripe", "PayPal", "FPX"]
    },
    {
      name: "Calendar Apps",
      description: "Sync events with popular calendar applications",
      icon: Calendar,
      status: "Coming Soon",
      tools: ["Google Calendar", "Outlook", "Apple Calendar"]
    },
    {
      name: "CRM Systems",
      description: "Connect with customer relationship management tools",
      icon: Users,
      status: "Coming Soon",
      tools: ["Salesforce", "HubSpot", "Pipedrive"]
    },
    {
      name: "Analytics",
      description: "Track event performance with analytics platforms",
      icon: BarChart,
      status: "Available",
      tools: ["Google Analytics", "Facebook Pixel", "Custom Tracking"]
    },
    {
      name: "Automation",
      description: "Automate workflows with popular automation tools",
      icon: Zap,
      status: "Coming Soon",
      tools: ["Zapier", "Make", "IFTTT"]
    }
  ]

  return (
    <PageLayout>
      {/* Corporate Hero Section */}
      <section className="relative w-full py-20 md:py-32 lg:py-40 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}
          ></div>
        </div>

        <div className="container relative px-4 md:px-6">
          <div className="flex flex-col items-center space-y-8 text-center">
            {/* Main Heading */}
            <div className="space-y-4 max-w-4xl">
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl lg:text-7xl">
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">Integrations</span>
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-gray-300 md:text-2xl leading-relaxed">
                Connect mTicket.my with your favorite tools and services to streamline your event management workflow.
              </p>
            </div>
          </div>
        </div>
      </section>

      <div className="container px-4 md:px-6 py-12">
        <div className="mx-auto max-w-7xl">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Popular Integrations</h2>
              <p className="text-lg text-muted-foreground">
                Enhance your event management with powerful integrations
              </p>
            </div>

            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-12">
              {integrations.map((integration, index) => (
                <Card key={index} className="relative">
                  <CardHeader>
                    <div className="flex items-center justify-between mb-2">
                      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                        <integration.icon className="h-6 w-6 text-primary" />
                      </div>
                      <Badge
                        variant={integration.status === "Available" ? "default" : "secondary"}
                        className={integration.status === "Available" ? "bg-green-100 text-green-800" : ""}
                      >
                        {integration.status}
                      </Badge>
                    </div>
                    <CardTitle>{integration.name}</CardTitle>
                    <CardDescription>{integration.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <p className="text-sm font-medium text-muted-foreground">Supported Tools:</p>
                      <div className="flex flex-wrap gap-1">
                        {integration.tools.map((tool, toolIndex) => (
                          <Badge key={toolIndex} variant="outline" className="text-xs">
                            {tool}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="bg-card p-8 rounded-lg border mb-12">
              <div className="text-center">
                <h3 className="text-2xl font-bold mb-4">Custom Integrations</h3>
                <p className="text-muted-foreground mb-6">
                  Need a specific integration that's not listed? Our API makes it easy to build custom integrations
                  or we can help you create one.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <a
                    href="/api-docs"
                    className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90"
                  >
                    View API Docs
                  </a>
                  <a
                    href="/contact"
                    className="inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground"
                  >
                    Request Integration
                  </a>
                </div>
              </div>
            </div>

            <div className="text-center">
              <h3 className="text-2xl font-bold mb-4">Getting Started</h3>
              <p className="text-muted-foreground mb-6">
                Most integrations can be set up in minutes from your dashboard.
                Check out our documentation for step-by-step guides.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="/dashboard"
                  className="inline-flex items-center justify-center rounded-md bg-primary px-6 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90"
                >
                  Go to Dashboard
                </a>
                <a
                  href="/docs"
                  className="inline-flex items-center justify-center rounded-md border border-input bg-background px-6 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground"
                >
                  View Documentation
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  )
}
