import { NextResponse } from "next/server";
import { verifyPassword } from "@/lib/auth";
import { getSupabaseAdmin } from "@/lib/supabase";
import jwt from "jsonwebtoken";

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json();

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 }
      );
    }

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin();

    // Get user from database with role information
    const { data: userData, error: userError } = await supabaseAdmin
      .from("users")
      .select(`
        *,
        user_roles!role_id (
          id,
          role_name,
          description
        )
      `)
      .eq("email", email.toLowerCase())
      .single();

    console.log("Login API: User data retrieved:", JSON.stringify(userData, null, 2));
    console.log("Login API: User error:", userError);

    if (userError || !userData) {
      console.error("User not found:", userError);
      return NextResponse.json(
        { error: "Invalid email or password" },
        { status: 401 }
      );
    }

    // Verify password
    if (!userData.password_hash) {
      console.error("No password hash found for user");
      return NextResponse.json(
        { error: "Invalid email or password" },
        { status: 401 }
      );
    }

    const isValidPassword = await verifyPassword(password, userData.password_hash);
    if (!isValidPassword) {
      console.error("Invalid password");
      return NextResponse.json(
        { error: "Invalid email or password" },
        { status: 401 }
      );
    }

    // Create JWT token
    const jwtSecret = process.env.JWT_SECRET || "your-secret-key";
    console.log(`Login API: Creating token with secret length: ${jwtSecret.length}`);

    const token = jwt.sign(
      {
        userId: userData.id,
        email: userData.email,
        role_id: userData.role_id,
        role_name: userData.user_roles?.role_name || null
      },
      jwtSecret,
      { expiresIn: "7d" }
    );

    console.log(`Login API: Token created, preview: ${token.substring(0, 20)}...`);

    // Prepare user data (exclude sensitive fields)
    console.log("Login API: userData.user_roles:", userData.user_roles);
    console.log("Login API: role_name extracted:", userData.user_roles?.role_name);

    const user = {
      id: userData.id,
      email: userData.email,
      full_name: userData.full_name,
      phone: userData.phone,
      bio: userData.bio,
      role_id: userData.role_id,
      role_name: userData.user_roles?.role_name || null,
      subscription_status: userData.subscription_status,
      subscription_end_date: userData.subscription_end_date,
      created_at: userData.created_at,
      organization: userData.organization,
      organization_id: userData.organization_id,
      profile_image_url: userData.profile_image_url,
      events_created: userData.events_created || 0,
      total_earnings: userData.total_earnings || 0,
      available_balance: userData.available_balance || 0,
    };

    console.log("Login API: Final user object:", JSON.stringify(user, null, 2));

    return NextResponse.json({
      success: true,
      token,
      user,
    });

  } catch (error: any) {
    console.error("Login error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
