# View PDF and Group Receipts Guide

## Overview
This guide explains the updated view-PDF functionality and how to access group receipts in the mTicket.my system.

## View-PDF API Updates

### Enhanced Error Handling
The `/api/tickets/view-pdf` endpoint now includes:
- Detailed console logging for debugging
- Better error messages for missing data
- Improved validation for receipt availability
- Enhanced error reporting with stack traces

### Supported Endpoints
1. **Individual Ticket**: `GET /api/tickets/view-pdf?ticketId=xxx&type=ticket`
2. **Individual Receipt**: `GET /api/tickets/view-pdf?ticketId=xxx&type=receipt`
3. **Group Receipt**: `GET /api/tickets/view-pdf?groupId=xxx&type=group-receipt`

## Group Receipt Access

### Where to View Group Receipts

#### 1. My Tickets Page - Card View
- **Location**: `/dashboard/my-tickets`
- **Access**: For group registrations with paid status, a purple "View Group Receipt" button appears above the individual action buttons
- **Visibility**: Only shown for tickets where:
  - User created the group registration (`created_by` matches current user)
  - Ticket has a `group_registration_id`
  - Payment status is 'paid'

#### 2. My Tickets Page - Table View
- **Location**: `/dashboard/my-tickets` (when table view is selected)
- **Access**: Purple group receipt button (Users icon) in the Actions column
- **Styling**: Purple background with Users icon for easy identification

#### 3. Individual Receipt Dialog
- **Location**: Within individual receipt view
- **Access**: Blue link "View group receipt" appears when viewing an individual receipt that's part of a group
- **Context**: Shows group total information and provides direct access to full group receipt

#### 4. Direct PDF Download
- **Access**: Download button in group receipt dialog
- **Functionality**: Opens group receipt in new tab with auto-print functionality

### Group Receipt Features

#### Content Includes:
- **Group Summary**: Total participants, total amount, processing fees
- **Event Information**: Event title, date, location
- **Transaction Details**: Main receipt number, invoice number, transaction ID, payment date
- **Participant List**: All participants with individual receipt numbers and amounts
- **Grand Total**: Complete breakdown of costs and fees

#### Visual Design:
- Purple-themed header for group receipts (vs blue for individual)
- Organized sections with clear information hierarchy
- Professional layout suitable for printing
- Auto-print functionality when opened in new tab

## Technical Implementation

### Database Requirements
Group receipts require:
- `group_registration_id` field in registrations table
- Transaction records with proper linking
- Financial transaction records for fee tracking

### Security
- User access validation (must be creator or participant)
- JWT token authentication required
- RLS policies enforced through admin client

### Error Handling
- Graceful handling of missing data
- Clear error messages for users
- Detailed logging for developers

## Usage Examples

### Accessing Group Receipt from Card View
```typescript
// Button appears automatically for group registrations
{isGroupRegistration(ticket) && ticket.group_registration_id && ticket.payment_status === 'paid' && (
  <Button onClick={() => handleViewGroupReceipt(ticket)}>
    <Users className="mr-2 h-4 w-4" />
    View Group Receipt
  </Button>
)}
```

### API Call for Group Receipt
```typescript
const response = await fetch(`/api/tickets/view-pdf?groupId=${groupId}&type=group-receipt`, {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

## Troubleshooting

### Error: "Failed to fetch group receipt data"

This error typically occurs when:

#### 1. **Database Query Issues (FIXED)**
- **Symptom**: 500 error with "Failed to fetch group registrations"
- **Cause**: Invalid `financial_transaction:id` join in Supabase query
- **Solution**: ✅ **FIXED** - Removed problematic join, simplified query

#### 2. **No Group Registrations Exist**
- **Symptom**: Group receipt buttons don't appear, or error when clicking
- **Cause**: No registrations have `group_registration_id` set
- **Solution**: Create test group registrations using debug endpoint

#### 3. **Missing group_registration_id Field**
- **Symptom**: Tickets don't show group badges or receipt buttons
- **Cause**: Database records missing `group_registration_id`
- **Solution**: Check database schema and update records

#### 4. **Access Permission Issues**
- **Symptom**: 404 error when fetching group receipt
- **Cause**: User not authorized (not creator or participant)
- **Solution**: Verify user_id or created_by matches current user

### Debug Steps

#### Step 1: Test Group Receipt Functionality
```bash
# Comprehensive test of group receipt functionality
GET /api/debug/test-group-receipt
Authorization: Bearer <your-token>
```

#### Step 2: Check for Group Registrations
```bash
# Call debug endpoint to check existing group registrations
GET /api/debug/check-group-registrations
Authorization: Bearer <your-token>
```

#### Step 3: Create Test Group Registration
```bash
# Create test group registrations for testing
POST /api/debug/create-group-registration
Authorization: Bearer <your-token>
```

#### Step 4: Check Console Logs
Open browser console and look for:
- "GROUP REGISTRATION DEBUG" logs showing ticket data
- "Group receipt button check" logs showing button visibility logic
- API error responses with detailed error messages

#### Step 5: Verify Database Schema
Ensure `registrations` table has:
- `group_registration_id` UUID column
- Proper indexes on the column
- RLS policies allowing access

### Common Issues
1. **Group receipt not showing**: Check if payment status is 'paid' and group_registration_id exists
2. **Access denied**: Verify user is either creator or participant of the group
3. **Missing transaction data**: Ensure proper transaction records exist
4. **Button not visible**: Check console logs for button visibility conditions

### Debug Information
The API now logs detailed information for troubleshooting:
- Group query results with user access validation
- Transaction data availability
- Button visibility conditions
- Error details with stack traces
- Database query parameters and results

## Future Enhancements
- Email delivery of group receipts
- Bulk download functionality
- Enhanced styling options
- Integration with accounting systems
