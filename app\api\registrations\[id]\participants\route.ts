import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: registrationId } = await params;
    console.log("Participants API: Fetching participants for registration:", registrationId);

    // Create Supabase admin client
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });

    // Fetch the specific registration
    const { data: registration, error: regError } = await supabase
      .from('registrations')
      .select(`
        id,
        attendee_name,
        attendee_email,
        attendee_phone,
        ic_reg,
        ticket_type,
        custom_field_responses,
        group_registration_id
      `)
      .eq('id', registrationId)
      .single();

    if (regError || !registration) {
      console.log("Participants API: Registration not found:", regError);
      return NextResponse.json(
        { error: "Registration not found" },
        { status: 404 }
      );
    }

    // If this is part of a group registration, fetch all registrations in the group
    let allRegistrations = [registration];
    let mainContact = null;

    if (registration.group_registration_id) {
      const { data: groupRegistrations, error: groupError } = await supabase
        .from('registrations')
        .select(`
          id,
          attendee_name,
          attendee_email,
          attendee_phone,
          ic_reg,
          ticket_type,
          custom_field_responses,
          created_by
        `)
        .eq('group_registration_id', registration.group_registration_id)
        .order('created_at');

      if (!groupError && groupRegistrations) {
        allRegistrations = groupRegistrations;
        // The first registration is typically the main contact
        mainContact = groupRegistrations[0];
      }
    } else {
      // Single registration, this person is the main contact
      mainContact = registration;
    }

    // Format participants data
    const participants = allRegistrations.map(reg => ({
      name: reg.attendee_name,
      email: reg.attendee_email,
      phone: reg.attendee_phone,
      ic: reg.ic_reg,
      ticket_type: reg.ticket_type,
      custom_fields: reg.custom_field_responses || {}
    }));

    console.log("Participants API: Successfully fetched participant data");

    return NextResponse.json({
      success: true,
      participants: participants,
      main_contact: mainContact ? {
        name: mainContact.attendee_name,
        email: mainContact.attendee_email,
        phone: mainContact.attendee_phone,
        ic: mainContact.ic_reg
      } : null
    });

  } catch (error) {
    console.error("Participants API: Error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
