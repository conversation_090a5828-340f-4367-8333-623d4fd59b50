"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { Eye, EyeOff, LogIn } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>onte<PERSON>, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { AuthFormLayout } from "@/components/auth-form-layout"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"

export default function LoginPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<any>(null)
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const { login, user } = useAuth()

  // Get the redirect URL from the query parameters
  const redirectTo = searchParams.get("redirectTo") || "/dashboard"

  // Check for plan selection from URL or sessionStorage
  useEffect(() => {
    // First check URL parameter (for backward compatibility)
    const planParam = searchParams.get('plan')
    if (planParam) {
      try {
        const plan = JSON.parse(decodeURIComponent(planParam))
        setSelectedPlan(plan)
        return
      } catch (error) {
        console.error('Error parsing plan parameter:', error)
      }
    }

    // Then check sessionStorage for plan after login
    const storedPlan = sessionStorage.getItem('planAfterLogin')
    if (storedPlan) {
      try {
        const plan = JSON.parse(storedPlan)
        setSelectedPlan(plan)
      } catch (error) {
        console.error('Error parsing stored plan:', error)
        sessionStorage.removeItem('planAfterLogin')
      }
    }
  }, [searchParams])

  // Check if user is already logged in
  useEffect(() => {
    if (user) {
      // Determine redirect destination based on role
      let finalRedirectTo = redirectTo
      if (redirectTo === "/dashboard" && user.role_name === "user") {
        // For users with "user" role, redirect to My Tickets page as default
        finalRedirectTo = "/dashboard/my-tickets"
        console.log("Login page: User role detected, redirecting to My Tickets")
      }

      console.log("Login page: User already logged in, redirecting to:", finalRedirectTo)
      // Use a small delay to ensure the component is fully mounted
      setTimeout(() => {
        router.push(finalRedirectTo)
      }, 50)
    }
  }, [user, redirectTo, router])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Login page: Form submitted with email:", email)
    console.log("Login page: Form submitted with password:", password)
    setIsLoading(true)

    try {
      console.log("Login page: Attempting to login with credentials")
      // Attempt to login with provided credentials
      const result = await login(email, password)
      console.log("Login page: Login result:", result ? "success" : "failed")

      if (result) {
        console.log("Login page: Login successful with role:", result.role_name)

        // Determine redirect destination based on role and other factors
        let finalRedirectTo = redirectTo

        if (selectedPlan) {
          // Clear the plan from sessionStorage
          sessionStorage.removeItem('planAfterLogin')
          // If a plan was selected, redirect to pricing page
          finalRedirectTo = "/pricing"
          toast({
            title: "Login successful",
            description: `Welcome back! You can now subscribe to the ${selectedPlan.planName} plan.`,
          })
        } else if (redirectTo === "/dashboard" && result.role_name === "user") {
          // For users with "user" role, redirect to My Tickets page as default
          finalRedirectTo = "/dashboard/my-tickets"
          console.log("Login page: User role detected, redirecting to My Tickets")
        }

        console.log("Login page: Redirecting to:", finalRedirectTo)

        // Add a small delay to ensure auth context is updated
        setTimeout(() => {
          console.log("Login page: Executing redirect after delay")

          // Try router.push first
          try {
            router.push(finalRedirectTo)
            console.log("Login page: Router.push executed")
          } catch (routerError) {
            console.error("Login page: Router.push failed, using window.location:", routerError)
            // Fallback to window.location if router.push fails
            window.location.href = finalRedirectTo
          }
        }, 100) // Small delay to ensure state updates
      } else {
        console.log("Login page: Login failed, no result returned")
        // This should not happen with the current implementation, but just in case
        toast({
          title: "Login failed",
          description: "Unable to log in. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error: any) {
      console.error("Login error:", error)
      toast({
        title: "Login failed",
        description: error.message || "Please check your credentials and try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <AuthFormLayout
      title="Login"
      description="Enter your email and password to access your account"
      footer={
        <div className="text-center text-sm">
          Don't have an account?{" "}
          <Link href="/auth/register" className="font-medium text-primary underline-offset-4 hover:underline">
            Sign up
          </Link>
        </div>
      }
    >
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">Password</Label>
                  <Link
                    href="/auth/forgot-password"
                    className="text-sm font-medium text-primary underline-offset-4 hover:underline"
                  >
                    Forgot password?
                  </Link>
                </div>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full px-3 py-2 text-muted-foreground"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    <span className="sr-only">{showPassword ? "Hide password" : "Show password"}</span>
                  </Button>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex flex-col space-y-4">
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <div className="flex items-center">
                    <svg
                      className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Logging in...
                  </div>
                ) : (
                  <div className="flex items-center">
                    <LogIn className="mr-2 h-4 w-4" />
                    Login
                  </div>
                )}
              </Button>
        </CardFooter>
      </form>
    </AuthFormLayout>
  )
}
