import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@/lib/supabase"
import { verifyPayment } from "@/lib/payment-gateway"
import { logActivity, ActivityCategory } from "@/utils/activity-logger"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { transactionId, planId } = body

    // Validate required fields
    if (!transactionId) {
      return NextResponse.json(
        {
          success: false,
          error: "Transaction ID is required",
        },
        { status: 400 }
      )
    }

    const supabase = createClient()

    // Find the pending subscription
    const { data: subscription, error: subError } = await supabase
      .from('user_subscription')
      .select('*')
      .eq('payment_id', transactionId)
      .single()

    if (subError || !subscription) {
      return NextResponse.json(
        {
          success: false,
          error: "Subscription not found",
        },
        { status: 404 }
      )
    }

    // Verify payment with payment gateway
    const isPaymentVerified = await verifyPayment(transactionId)

    if (!isPaymentVerified) {
      return NextResponse.json(
        {
          success: false,
          error: "Payment verification failed",
        },
        { status: 400 }
      )
    }

    // Calculate subscription end date (1 month from now)
    const subscriptionEndDate = new Date()
    subscriptionEndDate.setMonth(subscriptionEndDate.getMonth() + 1)

    // Update subscription to active
    const { error: updateSubError } = await supabase
      .from('user_subscription')
      .update({
        end_date: subscriptionEndDate.toISOString(),
        is_active: true,
        updated_at: new Date().toISOString(),
      })
      .eq('id', subscription.id)

    if (updateSubError) {
      console.error('Error updating subscription:', updateSubError)
      return NextResponse.json(
        {
          success: false,
          error: "Failed to activate subscription",
        },
        { status: 500 }
      )
    }

    // Update user subscription status
    const { error: userUpdateError } = await supabase
      .from('users')
      .update({
        subscription_status: 'active',
        subscription_end_date: subscriptionEndDate.toISOString(),
      })
      .eq('id', subscription.user_id)

    if (userUpdateError) {
      console.error('Error updating user:', userUpdateError)
      return NextResponse.json(
        {
          success: false,
          error: "Failed to update user subscription status",
        },
        { status: 500 }
      )
    }

    // Log activity
    try {
      await logActivity({
        userId: subscription.user_id,
        action: "subscription_activated",
        entityType: "subscription",
        entityId: planId || subscription.subscription_type,
        category: ActivityCategory.SUBSCRIPTION,
        details: {
          subscription_type: subscription.subscription_type,
          transaction_id: transactionId,
          end_date: subscriptionEndDate.toISOString(),
        },
      })
    } catch (logError) {
      console.error("Error logging subscription activation:", logError)
    }

    return NextResponse.json({
      success: true,
      message: "Subscription activated successfully",
      subscription: {
        type: subscription.subscription_type,
        endDate: subscriptionEndDate.toISOString(),
        isActive: true,
      },
    })

  } catch (error: any) {
    console.error("Error verifying subscription payment:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    )
  }
}
