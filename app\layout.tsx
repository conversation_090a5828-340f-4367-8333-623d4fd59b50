import type React from "react"
import "@/app/globals.css"
import { Inter } from "next/font/google"
// Import polyfills for Next.js 15
import "@/app/polyfills"
// Import crypto polyfill
import "@/app/crypto-polyfill"

import { ThemeProvider } from "@/components/theme-provider"
import { AuthProvider } from "@/contexts/auth-context"
import { SettingsProvider } from "@/contexts/settings-context"
import { EventProvider } from "@/contexts/event-context"
import { RegistrationProvider } from "@/contexts/registration-context"
import { CertificateProvider } from "@/contexts/certificate-context"
import { FinancialProvider } from "@/contexts/financial-context"
import { Toaster } from "@/components/ui/toaster"


const inter = Inter({ subsets: ["latin"] })

import type { Metadata } from "next";

export const metadata: Metadata = {
  title: {
    template: '%s | mTicket.my',
    default: 'mTicket.my - Event Management Platform',
  },
  description: "A comprehensive event management platform for creating, managing, and selling tickets to events."
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="light" disableTransitionOnChange>
          <AuthProvider>
            <SettingsProvider>
              <EventProvider>
                <RegistrationProvider>
                  <CertificateProvider>
                    <FinancialProvider>
                      {children}
                      <Toaster />
                    </FinancialProvider>
                  </CertificateProvider>
                </RegistrationProvider>
              </EventProvider>
            </SettingsProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
