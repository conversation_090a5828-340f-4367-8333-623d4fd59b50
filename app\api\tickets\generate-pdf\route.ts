import { NextResponse } from "next/server"
import { getJWTTokenFromRequest, verifyJ<PERSON>TToken } from "@/lib/auth"
import { getSupabaseAdmin } from "@/lib/supabase"

/**
 * POST /api/tickets/generate-pdf
 * Generate PDF ticket for a specific registration
 * Requires authentication
 */
export async function POST(request: Request) {
  console.log("PDF Generation API: Starting request")
  try {
    // Get JWT token from request
    const token = getJWTTokenFromRequest(request)
    console.log("PDF Generation API: Token present:", !!token)

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Verify the JWT token and get user data
    const authResult = await verifyJWTToken(token)
    if (!authResult?.user) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 }
      )
    }

    // Parse request body
    const body = await request.json()
    const { ticketId, type = 'ticket' } = body
    console.log("PDF Generation API: Request body:", { ticketId, type })

    if (!ticketId) {
      console.log("PDF Generation API: Missing ticket ID")
      return NextResponse.json(
        { error: "Ticket ID is required" },
        { status: 400 }
      )
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin()

    // Fetch ticket data with event details
    // Support both direct registrations (user_id) and group registrations (created_by)
    console.log("PDF Generation API: Looking for ticket:", ticketId, "for user:", authResult.user.id)
    const { data: ticket, error: ticketError } = await supabaseAdmin
      .from("registrations")
      .select(`
        *,
        event:event_id (
          id,
          title,
          slug,
          description,
          location,
          start_date,
          end_date,
          image_url,
          price
        )
      `)
      .eq("id", ticketId)
      .or(`user_id.eq.${authResult.user.id},created_by.eq.${authResult.user.id}`)
      .single()

    console.log("PDF Generation API: Ticket query result:", { ticket: !!ticket, error: ticketError })

    if (ticketError || !ticket) {
      console.log("PDF Generation API: Ticket not found or access denied")
      return NextResponse.json(
        { error: "Ticket not found or access denied" },
        { status: 404 }
      )
    }

    // Prepare ticket data for PDF generation
    const ticketData = {
      id: ticket.id,
      event: {
        title: ticket.event?.title || "Unknown Event",
        start_date: ticket.event?.start_date || "",
        end_date: ticket.event?.end_date || "",
        location: ticket.event?.location || "TBA",
        image_url: ticket.event?.image_url || ""
      },
      attendee_name: ticket.attendee_name || "Unknown Attendee",
      attendee_email: ticket.attendee_email || "",
      attendee_phone: ticket.attendee_phone || "",
      payment_amount: ticket.payment_amount || 0,
      payment_status: ticket.payment_status || "pending",
      registration_code: `REG${ticket.id.substring(0, 8).toUpperCase()}`,
      created_at: ticket.created_at,
      qr_code_data: JSON.stringify({
        id: ticket.id,
        event: ticket.event?.title || "Unknown Event",
        attendee: ticket.attendee_name,
        ticket: ticket.id,
        timestamp: new Date().toISOString(),
      })
    }

    // Return ticket data for client-side PDF generation
    // Note: We return the data instead of generating PDF server-side
    // because html2canvas requires DOM access which is not available on server
    return NextResponse.json({
      success: true,
      ticketData,
      type,
      message: "Ticket data prepared for PDF generation"
    })

  } catch (error) {
    console.error("Error preparing ticket PDF data:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

/**
 * GET /api/tickets/generate-pdf?ticketId=xxx&type=ticket|receipt
 * Alternative endpoint for GET requests
 */
export async function GET(request: Request) {
  try {
    const url = new URL(request.url)
    const ticketId = url.searchParams.get('ticketId')
    const type = url.searchParams.get('type') || 'ticket'

    if (!ticketId) {
      return NextResponse.json(
        { error: "Ticket ID is required" },
        { status: 400 }
      )
    }

    // Convert to POST-like request body
    const postRequest = new Request(request.url, {
      method: 'POST',
      headers: request.headers,
      body: JSON.stringify({ ticketId, type })
    })

    return await POST(postRequest)

  } catch (error) {
    console.error("Error in GET ticket PDF:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
