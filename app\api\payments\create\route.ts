import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth";
import { createPayment, getActivePaymentGateways } from "@/lib/payment-gateway";
import { logActivity } from "@/lib/activity-logger";

/**
 * POST /api/payments/create
 * Creates a payment for event registration without creating registration records first
 * Only creates registrations after successful payment callback
 * Requires authentication
 */
export async function POST(request: Request) {
  try {
    console.log("Payment Create API: Starting request");

    // Get JWT token from request
    const token = getJWTTokenFromRequest(request);
    console.log("Payment Create API: Token present:", !!token);

    if (!token) {
      return NextResponse.json(
        { error: "Missing or invalid Authorization header" },
        { status: 401 }
      );
    }

    // Verify the JWT token and get user data
    const authResult = await verifyJWTToken(token);
    if (!authResult || !authResult.user) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 }
      );
    }

    const userId = authResult.user.id;
    console.log("Payment Create API: User ID:", userId);

    // Parse request body
    const body = await request.json();
    const { 
      event_id, 
      participants, 
      main_contact, 
      selected_tickets,
      amount, 
      currency = 'MYR', 
      description,
      gateway_id 
    } = body;

    // Validate required fields
    if (!event_id || !participants || !Array.isArray(participants) || participants.length === 0) {
      return NextResponse.json(
        { error: "Event ID and participants are required" },
        { status: 400 }
      );
    }

    if (!main_contact || !main_contact.name || !main_contact.email) {
      return NextResponse.json(
        { error: "Main contact information is required" },
        { status: 400 }
      );
    }

    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: "Valid payment amount is required" },
        { status: 400 }
      );
    }

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin();

    // Verify the event exists and is published
    console.log("Payment Create API: Fetching event with ID:", event_id);
    const { data: event, error: eventError } = await supabaseAdmin
      .from("events")
      .select("id, title, slug, is_published, max_participants")
      .eq("id", event_id)
      .single();

    if (eventError || !event) {
      console.error("Payment Create API: Error fetching event:", eventError);
      return NextResponse.json(
        { error: "Event not found" },
        { status: 404 }
      );
    }

    if (!event.is_published) {
      return NextResponse.json(
        { error: "Event is not published" },
        { status: 400 }
      );
    }

    // Get active payment gateways
    const paymentGateways = await getActivePaymentGateways();
    if (!paymentGateways || paymentGateways.length === 0) {
      return NextResponse.json(
        { error: "No payment gateways available" },
        { status: 500 }
      );
    }

    // Use the specified gateway or the first available one
    let selectedGateway = paymentGateways[0];
    if (gateway_id) {
      const requestedGateway = paymentGateways.find(gw => gw.id === gateway_id);
      if (requestedGateway) {
        selectedGateway = requestedGateway;
      } else {
        return NextResponse.json(
          { error: "Requested payment gateway not available" },
          { status: 400 }
        );
      }
    }

    // Generate a unique reference ID for this payment
    const paymentReference = `payment_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

    // Store registration data temporarily in a pending_registrations table or use the reference in the payment
    const registrationData = {
      event_id,
      participants,
      main_contact,
      selected_tickets,
      user_id: userId,
      payment_reference: paymentReference
    };

    // Create payment request
    const paymentRequest = {
      amount: parseFloat(amount.toString()),
      currency: currency,
      description: description || `Registration for ${event.title}`,
      customer_email: authResult.user.email,
      customer_name: authResult.user.name || authResult.user.email,
      reference_id: paymentReference,
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/return`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/return`,
    };

    console.log("Payment Create API: Creating payment with gateway:", selectedGateway.id);

    // Create payment with selected gateway
    const paymentResponse = await createPayment(selectedGateway.id, paymentRequest);

    if (!paymentResponse.success) {
      console.error("Payment creation failed:", paymentResponse.error);
      return NextResponse.json(
        {
          success: false,
          error: paymentResponse.error || "Failed to create payment",
        },
        { status: 500 }
      );
    }

    // Create transaction record with registration data stored in metadata
    const transactionData = {
      id: crypto.randomUUID(),
      user_id: authResult.user.id,
      registration_id: null, // Will be set after successful payment
      gateway_id: selectedGateway.id,
      transaction_type: 'registration_payment',
      amount: parseFloat(amount.toString()),
      currency: currency,
      status: 'processing',
      gateway_transaction_id: paymentResponse.transaction_id,
      gateway_response: paymentResponse,
      metadata: registrationData, // Store registration data here
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data: transactionRecord, error: transactionError } = await supabaseAdmin
      .from("transactions")
      .insert([transactionData])
      .select()
      .single();

    if (transactionError) {
      console.error("Failed to create transaction record:", transactionError);
      return NextResponse.json(
        {
          success: false,
          error: "Failed to create transaction record",
        },
        { status: 500 }
      );
    }

    // Log activity
    await logActivity({
      action: "payment_initiated",
      user_id: userId,
      details: {
        event_id: event_id,
        amount: amount,
        currency: currency,
        payment_gateway: selectedGateway.id,
        transaction_id: paymentResponse.transaction_id,
        participants_count: participants.length
      },
    });

    console.log("Payment Create API: Payment created successfully");

    return NextResponse.json({
      success: true,
      payment_url: paymentResponse.payment_url,
      transaction_id: paymentResponse.transaction_id,
      gateway: selectedGateway.name,
      message: "Payment initiated successfully"
    });

  } catch (error) {
    console.error("Payment Create API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
