# Guides Documentation

This directory contains comprehensive guides for users and developers of the mTicket.my platform.

## Contents

### User Guides
- [Getting Started](./user/getting-started.md) - Quick start guide for new users
- [Event Management](./user/event-management.md) - Creating and managing events
- [Payment Processing](./user/payment-guide.md) - Understanding payment flows
- [Certificate Management](./user/certificates.md) - Creating and managing certificates
- [Receipt Management](./user/receipts.md) - Viewing and downloading receipts

### Developer Guides
- [Development Setup](./developer/setup.md) - Setting up the development environment
- [Code Organization](./developer/code-structure.md) - Understanding the codebase structure
- [Testing Guide](./developer/testing.md) - Running and writing tests
- [Deployment Guide](./developer/deployment.md) - Deploying the application
- [Contributing Guide](./developer/contributing.md) - How to contribute to the project

### Administrator Guides
- [System Administration](./admin/system-admin.md) - Managing the platform
- [User Management](./admin/user-management.md) - Managing users and roles
- [Payment Gateway Setup](./admin/payment-gateways.md) - Configuring payment gateways
- [Monitoring & Maintenance](./admin/monitoring.md) - System monitoring and maintenance

### Integration Guides
- [API Integration](./integration/api-integration.md) - Integrating with the API
- [Webhook Setup](./integration/webhooks.md) - Setting up webhooks
- [Third-party Integrations](./integration/third-party.md) - Available integrations

## Quick Start

1. **New Users**: Start with [User Getting Started Guide](./user/getting-started.md)
2. **Developers**: Begin with [Development Setup](./developer/setup.md)
3. **Administrators**: See [System Administration Guide](./admin/system-admin.md)

## Support

For additional help, see our [Support Documentation](./support/) or contact the development team.
