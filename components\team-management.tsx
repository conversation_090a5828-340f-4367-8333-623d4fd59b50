"use client"

import { useState, useEffect } from "react"
import { Plus, <PERSON>, Eye, EyeOff, <PERSON><PERSON>, Edit, Trash2, ExternalLink, Calendar, Power, PowerOff } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { useToast } from "@/hooks/use-toast"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { generateSecureTeamURL } from "@/lib/security-token"

interface Team {
  id: string
  team_name: string
  location?: string
  access_token: string
  permissions: {
    can_scan_qr: boolean
    can_view_attendance: boolean
  }
  is_active: boolean
  expires_at?: string
  last_used_at?: string
  created_at: string
  users?: {
    full_name: string
    email: string
  }
}

interface TeamManagementProps {
  eventSlug: string
  eventId: string
  eventTitle: string
}

export function TeamManagement({ eventSlug, eventId, eventTitle }: TeamManagementProps) {
  const { toast } = useToast()
  const [teams, setTeams] = useState<Team[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [showEditDialog, setShowEditDialog] = useState(false)
  const [editingTeam, setEditingTeam] = useState<Team | null>(null)
  const [showTokens, setShowTokens] = useState<Record<string, boolean>>({})

  // Form state
  const [formData, setFormData] = useState({
    team_name: "",
    location: "",
    can_scan_qr: true,
    can_view_attendance: true,
    expires_at: ""
  })

  // Helper function to get auth token from cookies
  const getAuthToken = (): string | null => {
    if (typeof document === 'undefined') return null;
    const value = `; ${document.cookie}`;
    const parts = value.split(`; auth_token=`);
    return parts.length === 2 ? parts.pop()?.split(';').shift() || null : null;
  };

  // Helper function to get headers with auth token
  const getAuthHeaders = (): HeadersInit => {
    const token = getAuthToken();
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  };

  useEffect(() => {
    fetchTeams()
  }, [eventSlug])

  const fetchTeams = async () => {
    try {
      const headers = getAuthHeaders()

      const response = await fetch(`/api/events/${eventSlug}/teams`, {
        headers
      })

      const data = await response.json()

      if (response.ok) {
        setTeams(data.teams || [])
      } else {
        // Handle specific error cases
        if (response.status === 401) {
          toast({
            title: "Authentication Required",
            description: "Please log in to view team access management.",
            variant: "destructive",
          })
        } else if (response.status === 403) {
          toast({
            title: "Access Denied",
            description: "You don't have permission to manage teams for this event.",
            variant: "destructive",
          })
        } else {
          throw new Error(data.error || "Failed to fetch teams")
        }
      }
    } catch (error: any) {
      console.error("Error fetching teams:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to fetch teams",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateTeam = async () => {
    try {
      const response = await fetch(`/api/events/${eventSlug}/teams`, {
        method: "POST",
        headers: getAuthHeaders(),
        body: JSON.stringify({
          team_name: formData.team_name,
          location: formData.location || null,
          permissions: {
            can_scan_qr: formData.can_scan_qr,
            can_view_attendance: formData.can_view_attendance,
          },
          expires_at: formData.expires_at || null,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to create team")
      }

      toast({
        title: "Success",
        description: "Team created successfully",
      })

      setShowCreateDialog(false)
      resetForm()
      fetchTeams()
    } catch (error: any) {
      console.error("Error creating team:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to create team",
        variant: "destructive",
      })
    }
  }

  const handleUpdateTeam = async (teamId: string, updates: Partial<Team>) => {
    try {
      const response = await fetch(`/api/events/${eventSlug}/teams/${teamId}`, {
        method: "PUT",
        headers: getAuthHeaders(),
        body: JSON.stringify(updates),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to update team")
      }

      toast({
        title: "Success",
        description: "Team updated successfully",
      })

      fetchTeams()
    } catch (error: any) {
      console.error("Error updating team:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to update team",
        variant: "destructive",
      })
    }
  }

  const handleDeleteTeam = async (teamId: string, teamName: string) => {
    if (!confirm(`Are you sure you want to delete the team "${teamName}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/events/${eventSlug}/teams/${teamId}`, {
        method: "DELETE",
        headers: getAuthHeaders(),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to delete team")
      }

      toast({
        title: "Success",
        description: "Team deleted successfully",
      })

      fetchTeams()
    } catch (error: any) {
      console.error("Error deleting team:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to delete team",
        variant: "destructive",
      })
    }
  }

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "Copied",
        description: `${label} copied to clipboard`,
      })
    } catch (error) {
      console.error("Failed to copy:", error)
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      })
    }
  }

  const handleEditTeam = async () => {
    if (!editingTeam) return

    try {
      const response = await fetch(`/api/events/${eventSlug}/teams/${editingTeam.id}`, {
        method: "PUT",
        headers: getAuthHeaders(),
        body: JSON.stringify({
          team_name: formData.team_name,
          location: formData.location || null,
          permissions: {
            can_scan_qr: formData.can_scan_qr,
            can_view_attendance: formData.can_view_attendance,
          },
          expires_at: formData.expires_at || null,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Failed to update team")
      }

      toast({
        title: "Success",
        description: "Team updated successfully",
      })

      setShowEditDialog(false)
      resetForm()
      fetchTeams()
    } catch (error: any) {
      console.error("Error updating team:", error)
      toast({
        title: "Error",
        description: error.message || "Failed to update team",
        variant: "destructive",
      })
    }
  }

  const startEditTeam = (team: Team) => {
    setEditingTeam(team)
    setFormData({
      team_name: team.team_name,
      location: team.location || "",
      can_scan_qr: team.permissions.can_scan_qr,
      can_view_attendance: team.permissions.can_view_attendance,
      expires_at: team.expires_at ? new Date(team.expires_at).toISOString().slice(0, 16) : ""
    })
    setShowEditDialog(true)
  }

  const resetForm = () => {
    setFormData({
      team_name: "",
      location: "",
      can_scan_qr: true,
      can_view_attendance: true,
      expires_at: ""
    })
    setEditingTeam(null)
  }

  const toggleTokenVisibility = (teamId: string) => {
    setShowTokens(prev => ({
      ...prev,
      [teamId]: !prev[teamId]
    }))
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getQRScannerUrl = (eventSlug: string) => {
    return `${window.location.origin}/${eventSlug}/qrscan`
  }

  const openQRScannerWithToken = (team: Team) => {
    // Generate secure token for the URL
    const secureUrl = generateSecureTeamURL(
      team.id,
      eventSlug,
      team.access_token,
      window.location.origin
    )
    window.open(secureUrl, '_blank')
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Team Access Management
            </CardTitle>
            <CardDescription>
              Create teams with access to the QR attendance scanner
            </CardDescription>
          </div>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button onClick={() => resetForm()}>
                <Plus className="mr-2 h-4 w-4" />
                Add Team
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Team</DialogTitle>
                <DialogDescription>
                  Create a team with access to the QR attendance scanner for this event.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="team_name">Team Name</Label>
                  <Input
                    id="team_name"
                    placeholder="Enter team name"
                    value={formData.team_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, team_name: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location">Location (Optional)</Label>
                  <Input
                    id="location"
                    placeholder="e.g., Gate A, Gate B, Main Entrance"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                  />
                </div>

                <div className="space-y-3">
                  <Label>Permissions</Label>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="can_scan_qr" className="text-sm font-normal">
                      Can scan QR codes
                    </Label>
                    <Switch
                      id="can_scan_qr"
                      checked={formData.can_scan_qr}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, can_scan_qr: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="can_view_attendance" className="text-sm font-normal">
                      Can view attendance
                    </Label>
                    <Switch
                      id="can_view_attendance"
                      checked={formData.can_view_attendance}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, can_view_attendance: checked }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="expires_at">Expiration Date (Optional)</Label>
                  <Input
                    id="expires_at"
                    type="datetime-local"
                    value={formData.expires_at}
                    onChange={(e) => setFormData(prev => ({ ...prev, expires_at: e.target.value }))}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCreateTeam} disabled={!formData.team_name.trim()}>
                  Create Team
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Edit Team Dialog */}
          <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Team</DialogTitle>
                <DialogDescription>
                  Update team details and permissions.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="edit_team_name">Team Name</Label>
                  <Input
                    id="edit_team_name"
                    placeholder="Enter team name"
                    value={formData.team_name}
                    onChange={(e) => setFormData(prev => ({ ...prev, team_name: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit_location">Location (Optional)</Label>
                  <Input
                    id="edit_location"
                    placeholder="e.g., Gate A, Gate B, Main Entrance"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                  />
                </div>

                <div className="space-y-3">
                  <Label>Permissions</Label>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="edit_can_scan_qr" className="text-sm font-normal">
                      Can scan QR codes
                    </Label>
                    <Switch
                      id="edit_can_scan_qr"
                      checked={formData.can_scan_qr}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, can_scan_qr: checked }))}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="edit_can_view_attendance" className="text-sm font-normal">
                      Can view attendance
                    </Label>
                    <Switch
                      id="edit_can_view_attendance"
                      checked={formData.can_view_attendance}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, can_view_attendance: checked }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit_expires_at">Expiration Date (Optional)</Label>
                  <Input
                    id="edit_expires_at"
                    type="datetime-local"
                    value={formData.expires_at}
                    onChange={(e) => setFormData(prev => ({ ...prev, expires_at: e.target.value }))}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setShowEditDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleEditTeam} disabled={!formData.team_name.trim()}>
                  Update Team
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading teams...</p>
          </div>
        ) : teams.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No teams created yet</p>
            <p className="text-sm">Create teams to give others access to the QR scanner</p>
          </div>
        ) : (
          <div className="space-y-4">
            <Alert>
              <ExternalLink className="h-4 w-4" />
              <AlertDescription>
                <strong>QR Scanner URL:</strong>{" "}
                <button
                  onClick={() => copyToClipboard(getQRScannerUrl(eventSlug), "QR Scanner URL")}
                  className="text-primary hover:underline"
                >
                  {getQRScannerUrl(eventSlug)}
                </button>
              </AlertDescription>
            </Alert>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Team Name</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Access Token</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Used</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {teams.map((team) => (
                    <TableRow key={team.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{team.team_name}</div>
                          <div className="text-sm text-muted-foreground">
                            {team.permissions.can_scan_qr && <Badge variant="outline" className="mr-1">QR Scan</Badge>}
                            {team.permissions.can_view_attendance && <Badge variant="outline">View</Badge>}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {team.location ? (
                          <Badge variant="secondary">{team.location}</Badge>
                        ) : (
                          <span className="text-sm text-muted-foreground">Not specified</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <code className="text-xs bg-muted px-2 py-1 rounded">
                            {showTokens[team.id] ? team.access_token : "••••••••••••••••"}
                          </code>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleTokenVisibility(team.id)}
                          >
                            {showTokens[team.id] ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(team.access_token, "Access token")}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Badge variant={team.is_active ? "default" : "secondary"}>
                            {team.is_active ? "Active" : "Inactive"}
                          </Badge>
                          {team.expires_at && (
                            <Badge variant="outline" className="text-xs">
                              <Calendar className="h-3 w-3 mr-1" />
                              Expires {formatDateTime(team.expires_at)}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {team.last_used_at ? (
                          <span className="text-sm">{formatDateTime(team.last_used_at)}</span>
                        ) : (
                          <span className="text-sm text-muted-foreground">Never</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <TooltipProvider>
                          <div className="flex items-center gap-1">
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => openQRScannerWithToken(team)}
                                >
                                  <ExternalLink className="h-3 w-3" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Open QR Scanner with Token</p>
                              </TooltipContent>
                            </Tooltip>

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => startEditTeam(team)}
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Edit Team</p>
                              </TooltipContent>
                            </Tooltip>

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleUpdateTeam(team.id, { is_active: !team.is_active })}
                                >
                                  {team.is_active ? <PowerOff className="h-3 w-3" /> : <Power className="h-3 w-3" />}
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>{team.is_active ? "Disable Team" : "Enable Team"}</p>
                              </TooltipContent>
                            </Tooltip>

                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteTeam(team.id, team.team_name)}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Delete Team</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                        </TooltipProvider>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
