import { NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";
import fs from "fs";
import path from "path";

// This endpoint is for applying migrations directly from the application
// It should only be accessible to administrators
export async function POST(request: Request) {
  try {
    const { migrationName } = await request.json();

    // Validate input
    if (!migrationName) {
      return NextResponse.json(
        { error: "Migration name is required" },
        { status: 400 }
      );
    }

    // Create a Supabase client with direct database access
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || "";
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || "";
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // Read the migration file
    const migrationPath = path.join(process.cwd(), "supabase", "migrations", migrationName);

    if (!fs.existsSync(migrationPath)) {
      return NextResponse.json(
        { error: `Migration file ${migrationName} not found` },
        { status: 404 }
      );
    }

    const migrationSql = fs.readFileSync(migrationPath, "utf8");

    // Apply the migration
    const { error } = await supabase.rpc("apply_migration", {
      migration_sql: migrationSql,
      migration_name: migrationName,
    });

    if (error) {
      return NextResponse.json(
        { error: `Failed to apply migration: ${error.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Migration ${migrationName} applied successfully`,
    });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || "Failed to apply migration" },
      { status: 500 }
    );
  }
}
