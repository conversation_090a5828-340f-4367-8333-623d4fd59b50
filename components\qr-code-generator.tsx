"use client"

import { useState } from "react"
import { QRCodeSVG } from "qrcode.react"
import { Copy, Download, QrCode, Share2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { generateShortUrl, generateEventUrl } from "@/lib/qr-utils"

interface QRCodeGeneratorProps {
  eventSlug: string
  eventTitle: string
  className?: string
}

export function QRCodeGenerator({ eventSlug, eventTitle, className }: QRCodeGeneratorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const { toast } = useToast()

  const shortUrl = generateShortUrl(eventSlug)
  const fullUrl = generateEventUrl(eventSlug)

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "Copied!",
        description: `${label} copied to clipboard`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      })
    }
  }

  const downloadQRCode = () => {
    try {
      const svg = document.querySelector('#qr-code-svg') as SVGElement
      if (!svg) return

      const svgData = new XMLSerializer().serializeToString(svg)
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        canvas.width = img.width
        canvas.height = img.height
        ctx?.drawImage(img, 0, 0)
        
        const pngFile = canvas.toDataURL('image/png')
        const downloadLink = document.createElement('a')
        downloadLink.download = `${eventSlug}-qr-code.png`
        downloadLink.href = pngFile
        downloadLink.click()
      }

      img.src = 'data:image/svg+xml;base64,' + btoa(svgData)

      toast({
        title: "Downloaded!",
        description: "QR code saved as PNG",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to download QR code",
        variant: "destructive",
      })
    }
  }

  const shareEvent = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: eventTitle,
          text: `Check out this event: ${eventTitle}`,
          url: shortUrl,
        })
      } catch (error) {
        // User cancelled sharing or error occurred
        copyToClipboard(shortUrl, "Event link")
      }
    } else {
      copyToClipboard(shortUrl, "Event link")
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className={className}>
          <QrCode className="mr-2 h-4 w-4" />
          QR Code
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Event QR Code & Links</DialogTitle>
          <DialogDescription>
            Share this event easily with QR code or direct links
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* QR Code */}
          <Card>
            <CardHeader className="text-center pb-4">
              <CardTitle className="text-lg">QR Code</CardTitle>
              <CardDescription>Scan to visit event page</CardDescription>
            </CardHeader>
            <CardContent className="flex justify-center">
              <div className="p-4 bg-white rounded-lg">
                <QRCodeSVG
                  id="qr-code-svg"
                  value={shortUrl}
                  size={200}
                  level="M"
                  includeMargin={true}
                  fgColor="#000000"
                  bgColor="#ffffff"
                />
              </div>
            </CardContent>
          </Card>

          {/* URLs */}
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="short-url">Short URL</Label>
              <div className="flex gap-2">
                <Input
                  id="short-url"
                  value={shortUrl}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => copyToClipboard(shortUrl, "Short URL")}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="full-url">Full URL</Label>
              <div className="flex gap-2">
                <Input
                  id="full-url"
                  value={fullUrl}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => copyToClipboard(fullUrl, "Full URL")}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4">
            <Button onClick={shareEvent} className="flex-1">
              <Share2 className="mr-2 h-4 w-4" />
              Share Event
            </Button>
            <Button variant="outline" onClick={downloadQRCode}>
              <Download className="mr-2 h-4 w-4" />
              Download QR
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
