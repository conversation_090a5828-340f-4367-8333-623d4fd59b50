"use client"

import { useState, useEffect } from "react"
import { CreditCard, CheckCircle2, AlertCircle, ExternalLink } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { useRegistrations } from "@/contexts/registration-context"
import type { EventType } from "@/contexts/event-context"
import { createPayment, verifyPayment } from "@/lib/payment-gateway"

interface PaymentProcessorProps {
  event: EventType
  registrationId: string
  participantCount: number
  onSuccess?: () => void
  onCancel?: () => void
}

export function PaymentProcessorUpdated({
  event,
  registrationId,
  participantCount,
  onSuccess,
  onCancel,
}: PaymentProcessorProps) {
  const { processPayment } = useRegistrations()
  const [paymentMethod, setPaymentMethod] = useState<string>("billplz")
  const [paymentStatus, setPaymentStatus] = useState<"idle" | "processing" | "redirecting" | "success" | "error">(
    "idle",
  )
  const [paymentUrl, setPaymentUrl] = useState<string | null>(null)
  const [transactionId, setTransactionId] = useState<string | null>(null)
  const [verificationInterval, setVerificationInterval] = useState<NodeJS.Timeout | null>(null)
  const { toast } = useToast()

  // Calculate total amount
  const totalAmount = event.price ? event.price * participantCount : 0

  // Clean up verification interval on unmount
  useEffect(() => {
    return () => {
      if (verificationInterval) {
        clearInterval(verificationInterval)
      }
    }
  }, [verificationInterval])

  // Handle payment submission
  const handlePayment = async () => {
    if (totalAmount <= 0) {
      toast({
        title: "Error",
        description: "Invalid payment amount",
        variant: "destructive",
      })
      return
    }

    setPaymentStatus("processing")
    try {
      // Create payment request
      const paymentRequest = {
        amount: totalAmount,
        currency: "MYR",
        description: `Registration for ${event.title}`,
        customer_email: "<EMAIL>", // This would come from the registration data
        customer_name: "Customer Name", // This would come from the registration data
        reference_id: registrationId,
        success_url: `${window.location.origin}/events/${event.slug}/register/success`,
        cancel_url: `${window.location.origin}/events/${event.slug}/register/cancel`,
        payment_gateway_id: paymentMethod, // Use the selected payment method as the gateway ID
      }

      // Create payment with payment gateway
      const paymentResponse = await createPayment(paymentRequest)

      if (!paymentResponse.success || !paymentResponse.payment_url) {
        throw new Error(paymentResponse.error || "Payment processing failed")
      }

      // Update registration with payment info
      const result = await processPayment(registrationId, totalAmount, paymentMethod, paymentResponse.payment_gateway_id)

      if (!result) {
        throw new Error("Failed to update registration with payment information")
      }

      // Set payment URL and transaction ID
      setPaymentUrl(paymentResponse.payment_url)
      setTransactionId(paymentResponse.transaction_id || null)
      setPaymentStatus("redirecting")

      // Start verification interval if transaction ID is available
      if (paymentResponse.transaction_id) {
        const interval = setInterval(async () => {
          const verified = await verifyPayment(paymentResponse.transaction_id!)
          if (verified) {
            clearInterval(interval)
            setPaymentStatus("success")
            toast({
              title: "Success",
              description: "Payment processed successfully",
            })
            if (onSuccess) {
              onSuccess()
            }
          }
        }, 5000) // Check every 5 seconds

        setVerificationInterval(interval)
      }
    } catch (error) {
      console.error("Error processing payment:", error)
      setPaymentStatus("error")
      toast({
        title: "Error",
        description: "Failed to process payment",
        variant: "destructive",
      })
    }
  }

  // Handle redirect to payment gateway
  const handleRedirect = () => {
    if (paymentUrl) {
      window.open(paymentUrl, "_blank")
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Payment</CardTitle>
        <CardDescription>Complete your payment to confirm registration</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {paymentStatus === "idle" && (
          <>
            <div className="space-y-4">
              <div className="flex justify-between text-sm">
                <span>Event:</span>
                <span className="font-medium">{event.title}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Participants:</span>
                <span>{participantCount}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Price per participant:</span>
                <span>RM {event.price?.toFixed(2)}</span>
              </div>
              <div className="flex justify-between font-medium">
                <span>Total Amount:</span>
                <span>RM {totalAmount.toFixed(2)}</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Select Payment Method</Label>
              <RadioGroup
                defaultValue={paymentMethod}
                onValueChange={setPaymentMethod}
                className="grid grid-cols-1 gap-4"
              >
                <div className="flex items-center space-x-2 rounded-md border p-3">
                  <RadioGroupItem value="billplz" id="billplz" />
                  <Label htmlFor="billplz" className="flex-1 cursor-pointer">
                    <div className="flex items-center justify-between">
                      <span>Billplz</span>
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </Label>
                </div>
                <div className="flex items-center space-x-2 rounded-md border p-3">
                  <RadioGroupItem value="toyyibpay" id="toyyibpay" />
                  <Label htmlFor="toyyibpay" className="flex-1 cursor-pointer">
                    <div className="flex items-center justify-between">
                      <span>ToyyibPay</span>
                      <CreditCard className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </Label>
                </div>
              </RadioGroup>
            </div>
          </>
        )}

        {paymentStatus === "processing" && (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            <p className="mt-4 text-center">
              Processing your payment...
              <br />
              Please do not close this window.
            </p>
          </div>
        )}

        {paymentStatus === "redirecting" && (
          <div className="flex flex-col items-center justify-center py-8">
            <div className="rounded-full bg-blue-100 p-3">
              <ExternalLink className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="mt-4 text-xl font-semibold">Payment Ready</h3>
            <p className="mt-2 text-center text-muted-foreground">
              Click the button below to proceed to the payment gateway.
              <br />
              After completing payment, return to this page.
            </p>
            <Button onClick={handleRedirect} className="mt-4">
              <ExternalLink className="mr-2 h-4 w-4" />
              Proceed to Payment
            </Button>
            <p className="mt-4 text-sm text-muted-foreground">Transaction ID: {transactionId || "Not available"}</p>
          </div>
        )}

        {paymentStatus === "success" && (
          <div className="flex flex-col items-center justify-center py-8">
            <CheckCircle2 className="h-16 w-16 text-green-500" />
            <h3 className="mt-4 text-xl font-semibold">Payment Successful!</h3>
            <p className="mt-2 text-center text-muted-foreground">
              Your registration is now confirmed.
              <br />
              You will receive a confirmation email shortly.
            </p>
          </div>
        )}

        {paymentStatus === "error" && (
          <div className="flex flex-col items-center justify-center py-8">
            <AlertCircle className="h-16 w-16 text-destructive" />
            <h3 className="mt-4 text-xl font-semibold">Payment Failed</h3>
            <p className="mt-2 text-center text-muted-foreground">
              There was an issue processing your payment.
              <br />
              Please try again or contact support.
            </p>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        {paymentStatus === "idle" && (
          <>
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button onClick={handlePayment}>Pay RM {totalAmount.toFixed(2)}</Button>
          </>
        )}

        {paymentStatus === "redirecting" && (
          <Button variant="outline" className="w-full" onClick={onCancel}>
            Cancel Payment
          </Button>
        )}

        {paymentStatus === "error" && (
          <>
            <Button variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button onClick={() => setPaymentStatus("idle")}>Try Again</Button>
          </>
        )}

        {paymentStatus === "success" && (
          <Button className="w-full" onClick={onSuccess}>
            Continue
          </Button>
        )}
      </CardFooter>
    </Card>
  )
}
