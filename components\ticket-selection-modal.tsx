"use client"

import { useState } from "react"
import { <PERSON><PERSON><PERSON>, Minus, Plus, Ticket, ShoppingCart, X, Calendar, MapPin, Clock, Users, Star, Check } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Dialog, DialogContent, DialogDescription, <PERSON><PERSON>Footer, <PERSON>alogHeader, <PERSON>alog<PERSON>itle, DialogTrigger } from "@/components/ui/dialog"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { type EventType } from "@/contexts/event-context"

// Define ticket types
export interface TicketType {
  id: string
  name: string
  description: string
  price: number
  maxQuantity: number
  availableQuantity: number
  features: string[]
  isPopular?: boolean
}

export interface SelectedTicket {
  ticketType: TicketType
  quantity: number
}

interface TicketSelectionModalProps {
  event: EventType
  onContinue: (selectedTickets: SelectedTicket[]) => void
}

export function TicketSelectionModal({ event, onContinue }: TicketSelectionModalProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedTickets, setSelectedTickets] = useState<SelectedTicket[]>([])
  const [isLoading, setIsLoading] = useState(false)

  // Generate ticket types based on event
  const getTicketTypes = (): TicketType[] => {
    const basePrice = event.price || 0

    if (basePrice === 0) {
      // Free event - only one ticket type
      return [
        {
          id: "free",
          name: "Free Ticket",
          description: "Complimentary access to the event",
          price: 0,
          maxQuantity: 10,
          availableQuantity: 100,
          features: ["Event access", "Digital materials"]
        }
      ]
    }

    // Paid event - multiple ticket types
    return [
      {
        id: "early-bird",
        name: "Early Bird",
        description: "Limited time offer with special pricing",
        price: Math.round(basePrice * 0.8), // 20% discount
        maxQuantity: 5,
        availableQuantity: 25,
        features: ["Event access", "Digital materials", "Early bird discount"],
        isPopular: true
      },
      {
        id: "standard",
        name: "Standard",
        description: "Regular admission ticket",
        price: basePrice,
        maxQuantity: 10,
        availableQuantity: 150,
        features: ["Event access", "Digital materials", "Networking session"]
      },
      {
        id: "vip",
        name: "VIP",
        description: "Premium experience with exclusive benefits",
        price: Math.round(basePrice * 1.5), // 50% premium
        maxQuantity: 3,
        availableQuantity: 20,
        features: ["Event access", "Digital materials", "VIP seating", "Meet & greet", "Premium lunch"]
      }
    ]
  }

  const ticketTypes = getTicketTypes()

  const updateTicketQuantity = (ticketTypeId: string, newQuantity: number) => {
    const ticketType = ticketTypes.find(t => t.id === ticketTypeId)
    if (!ticketType) return

    // Ensure quantity is within bounds
    const clampedQuantity = Math.max(0, Math.min(newQuantity, ticketType.maxQuantity))

    setSelectedTickets(prev => {
      const existing = prev.find(t => t.ticketType.id === ticketTypeId)

      if (clampedQuantity === 0) {
        // Remove ticket if quantity is 0
        return prev.filter(t => t.ticketType.id !== ticketTypeId)
      }

      if (existing) {
        // Update existing ticket quantity
        return prev.map(t =>
          t.ticketType.id === ticketTypeId
            ? { ...t, quantity: clampedQuantity }
            : t
        )
      } else {
        // Add new ticket
        return [...prev, { ticketType, quantity: clampedQuantity }]
      }
    })
  }

  const getTicketQuantity = (ticketTypeId: string): number => {
    return selectedTickets.find(t => t.ticketType.id === ticketTypeId)?.quantity || 0
  }

  const calculateTotal = (): number => {
    return selectedTickets.reduce((total, ticket) => {
      return total + (ticket.ticketType.price * ticket.quantity)
    }, 0)
  }

  const getTotalTickets = (): number => {
    return selectedTickets.reduce((total, ticket) => total + ticket.quantity, 0)
  }

  const handleContinue = async () => {
    if (selectedTickets.length > 0) {
      setIsLoading(true)
      try {
        // Small delay to show loading state
        await new Promise(resolve => setTimeout(resolve, 500))
        onContinue(selectedTickets)
        setIsOpen(false)
        // Reset selected tickets for next time
        setSelectedTickets([])
      } finally {
        setIsLoading(false)
      }
    }
  }

  const handleReset = () => {
    setSelectedTickets([])
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="w-full" size="lg">
          <ShoppingCart className="mr-2 h-5 w-5" />
          Register Now
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-7xl max-h-[95vh] p-0 gap-0 w-[95vw]">
        <div className="flex flex-col lg:flex-row h-full max-h-[95vh]">
          {/* Left Panel - Event Info & Tickets */}
          <div className="flex-1 p-6 lg:p-8 overflow-y-auto">
            {/* Mobile Cart Summary - Shows on mobile when items in cart */}
            {selectedTickets.length > 0 && (
              <Card className="lg:hidden mb-6 bg-primary/5 border-primary/20">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <ShoppingCart className="h-4 w-4 text-primary" />
                      <span className="font-medium">
                        {getTotalTickets()} ticket{getTotalTickets() !== 1 ? 's' : ''} selected
                      </span>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-primary">RM {calculateTotal().toFixed(2)}</div>
                      <div className="text-xs text-muted-foreground">Total</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            <DialogHeader className="mb-6">
              <DialogTitle className="flex items-center gap-3 text-2xl">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Ticket className="h-6 w-6 text-primary" />
                </div>
                Select Your Tickets
              </DialogTitle>
              <DialogDescription className="text-base">
                Choose your ticket type and quantity for this amazing event
              </DialogDescription>
            </DialogHeader>

            {/* Event Summary Card */}
            <Card className="mb-6 border-2 border-primary/20">
              <CardContent className="p-4">
                <h3 className="font-semibold text-lg mb-3">{event.title}</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 text-sm">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-primary" />
                    <span className="text-muted-foreground">
                      {new Date(event.start_date).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-primary" />
                    <span className="text-muted-foreground">
                      {new Date(event.start_date).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-primary" />
                    <span className="text-muted-foreground">{event.location}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-primary" />
                    <span className="text-muted-foreground">
                      {event.max_participants ? `${event.max_participants} max` : "Unlimited"}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Ticket Types */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Available Tickets</h3>
              <div className="grid gap-4">
                {ticketTypes.map((ticketType) => (
                  <Card key={ticketType.id} className={`relative transition-all duration-300 hover:shadow-xl hover:scale-[1.02] ${
                    ticketType.isPopular
                      ? 'ring-2 ring-primary shadow-lg border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10'
                      : 'hover:border-primary/30'
                  } ${getTicketQuantity(ticketType.id) > 0 ? 'bg-primary/5 ring-1 ring-primary/30 shadow-md' : ''}`}>
                    {ticketType.isPopular && (
                      <div className="absolute -top-3 left-4 z-10">
                        <Badge className="bg-gradient-to-r from-primary to-primary/80 text-white px-3 py-1 shadow-lg">
                          <Star className="w-3 h-3 mr-1" />
                          Most Popular
                        </Badge>
                      </div>
                    )}

                    <CardContent className="p-4 sm:p-6">
                      <div className="flex flex-col gap-4">
                        {/* Header with Title and Price */}
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                          <div>
                            <h4 className="text-lg sm:text-xl font-bold">{ticketType.name}</h4>
                            <p className="text-sm text-muted-foreground">{ticketType.description}</p>
                          </div>
                          <div className="text-xl sm:text-2xl font-bold text-primary">
                            {ticketType.price === 0 ? 'Free' : `RM ${ticketType.price.toFixed(2)}`}
                          </div>
                        </div>

                        {/* Features */}
                        <div className="flex flex-wrap gap-2">
                          {ticketType.features.map((feature, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              <Check className="w-3 h-3 mr-1" />
                              {feature}
                            </Badge>
                          ))}
                        </div>

                        {/* Availability Info */}
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 text-sm text-muted-foreground">
                          <div className="flex gap-4">
                            <span>Available: {ticketType.availableQuantity}</span>
                            <span>Max per order: {ticketType.maxQuantity}</span>
                          </div>
                        </div>

                        {/* Quantity Selector and Subtotal */}
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 pt-2 border-t">
                          <div className="flex items-center gap-3">
                            <span className="text-sm font-medium">Quantity:</span>
                            <div className="flex items-center border rounded-lg">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-9 w-9 rounded-r-none"
                                onClick={() => updateTicketQuantity(ticketType.id, getTicketQuantity(ticketType.id) - 1)}
                                disabled={getTicketQuantity(ticketType.id) === 0}
                              >
                                <Minus className="h-4 w-4" />
                              </Button>
                              <div className="w-12 h-9 flex items-center justify-center border-x bg-muted/30 font-semibold">
                                {getTicketQuantity(ticketType.id)}
                              </div>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-9 w-9 rounded-l-none"
                                onClick={() => updateTicketQuantity(ticketType.id, getTicketQuantity(ticketType.id) + 1)}
                                disabled={getTicketQuantity(ticketType.id) >= ticketType.maxQuantity}
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>

                          {getTicketQuantity(ticketType.id) > 0 && (
                            <div className="text-right">
                              <div className="text-lg font-bold text-primary">
                                RM {(ticketType.price * getTicketQuantity(ticketType.id)).toFixed(2)}
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {getTicketQuantity(ticketType.id)} × RM {ticketType.price.toFixed(2)}
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Mobile Continue Button */}
            {selectedTickets.length > 0 && (
              <div className="lg:hidden mt-6 pt-6 border-t">
                <Button
                  onClick={handleContinue}
                  className="w-full"
                  size="lg"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      Continue to Registration
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleReset}
                  className="w-full mt-2"
                  size="sm"
                >
                  Clear Cart
                </Button>
              </div>
            )}
          </div>

          {/* Right Panel - Shopping Cart (Desktop Only) */}
          <div className="hidden lg:flex lg:w-96 bg-muted/30 border-l">
            <div className="sticky top-0 h-full flex flex-col">
              {/* Cart Header */}
              <div className="p-6 border-b bg-background">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold flex items-center gap-2">
                    <ShoppingCart className="h-5 w-5" />
                    Your Cart
                  </h3>
                  {selectedTickets.length > 0 && (
                    <Badge variant="secondary" className="px-2 py-1">
                      {getTotalTickets()} item{getTotalTickets() !== 1 ? 's' : ''}
                    </Badge>
                  )}
                </div>
              </div>

              {/* Cart Content */}
              <ScrollArea className="flex-1 p-6">
                {selectedTickets.length === 0 ? (
                  <div className="flex flex-col items-center justify-center h-64 text-center">
                    <div className="p-4 bg-muted rounded-full mb-4">
                      <ShoppingCart className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <h4 className="font-medium text-muted-foreground mb-2">Your cart is empty</h4>
                    <p className="text-sm text-muted-foreground">
                      Select tickets from the left to add them to your cart
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {selectedTickets.map((ticket) => (
                      <Card key={ticket.ticketType.id} className="border-2">
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start mb-2">
                            <div className="flex-1">
                              <h4 className="font-semibold">{ticket.ticketType.name}</h4>
                              <p className="text-sm text-muted-foreground">
                                RM {ticket.ticketType.price.toFixed(2)} each
                              </p>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                              onClick={() => updateTicketQuantity(ticket.ticketType.id, 0)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => updateTicketQuantity(ticket.ticketType.id, ticket.quantity - 1)}
                                disabled={ticket.quantity <= 1}
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <span className="w-8 text-center font-medium">{ticket.quantity}</span>
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-8 w-8 p-0"
                                onClick={() => updateTicketQuantity(ticket.ticketType.id, ticket.quantity + 1)}
                                disabled={ticket.quantity >= ticket.ticketType.maxQuantity}
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                            <div className="text-right">
                              <div className="font-bold">
                                RM {(ticket.ticketType.price * ticket.quantity).toFixed(2)}
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </ScrollArea>

              {/* Cart Footer */}
              {selectedTickets.length > 0 && (
                <div className="p-6 border-t bg-background space-y-4">
                  {/* Order Summary */}
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Subtotal ({getTotalTickets()} ticket{getTotalTickets() !== 1 ? 's' : ''})</span>
                      <span>RM {calculateTotal().toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <span>Processing fee</span>
                      <span>RM 0.00</span>
                    </div>
                    <Separator />
                    <div className="flex justify-between text-lg font-bold">
                      <span>Total</span>
                      <span className="text-primary">RM {calculateTotal().toFixed(2)}</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-2">
                    <Button
                      onClick={handleContinue}
                      className="w-full"
                      size="lg"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          Continue to Registration
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </>
                      )}
                    </Button>
                    <Button
                      variant="outline"
                      onClick={handleReset}
                      className="w-full"
                      size="sm"
                    >
                      Clear Cart
                    </Button>
                  </div>
                </div>
              )}

              {/* Empty Cart Footer */}
              {selectedTickets.length === 0 && (
                <div className="p-6 border-t bg-background">
                  <p className="text-sm text-muted-foreground text-center">
                    Please select at least one ticket to continue
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
