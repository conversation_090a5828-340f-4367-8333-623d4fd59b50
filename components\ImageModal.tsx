'use client';

import { useState, useEffect, useRef } from 'react';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';

type ImageModalProps = {
  isOpen: boolean;
  onClose: () => void;
  imageUrl: string;
  alt: string;
  description?: string;
};

export function ImageModal({ isOpen, onClose, imageUrl, alt, description = '' }: ImageModalProps) {
  const [isZoomed, setIsZoomed] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const modalRef = useRef<HTMLDivElement>(null);

  // Close modal when pressing Escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        handleClose();
      }
    };

    if (isOpen) {
      document.body.style.overflow = 'hidden';
      document.addEventListener('keydown', handleEscape);
      // Reset zoom state when opening
      setIsZoomed(false);
      setIsLoaded(false);
    }

    return () => {
      document.body.style.overflow = 'unset';
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen]);

  const handleClose = () => {
    if (modalRef.current) {
      modalRef.current.style.opacity = '0';
      setTimeout(() => {
        onClose();
      }, 200);
    } else {
      onClose();
    }
  };

  const toggleZoom = () => {
    setIsZoomed(!isZoomed);
  };

  if (!isOpen) return null;

  return (
    <div 
      ref={modalRef}
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/90 p-4 backdrop-blur-sm transition-opacity duration-200 opacity-0"
      style={{ animation: 'fadeIn 200ms forwards' }}
      onClick={(e) => {
        // Close when clicking outside the image
        if (e.target === e.currentTarget) {
          handleClose();
        }
      }}
    >
      <div className="relative w-full h-full max-w-6xl max-h-[90vh] flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <button 
            onClick={handleClose}
            className="text-white hover:bg-white/10 p-2 rounded-full transition-colors"
            aria-label="Close modal"
          >
            <X className="h-6 w-6" />
          </button>
          <button 
            onClick={toggleZoom}
            className="text-white hover:bg-white/10 p-2 rounded-full transition-colors"
            aria-label={isZoomed ? 'Zoom out' : 'Zoom in'}
          >
            {isZoomed ? (
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                <line x1="8" y1="11" x2="14" y2="11"></line>
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                <line x1="11" y1="8" x2="11" y2="14"></line>
                <line x1="8" y1="11" x2="14" y2="11"></line>
              </svg>
            )}
          </button>
        </div>
        
        <div className="flex-1 flex flex-col items-center justify-center overflow-hidden">
          <div 
            className={`relative w-full flex-1 flex items-center justify-center transition-transform duration-300 ease-in-out ${
              isZoomed ? 'cursor-zoom-out' : 'cursor-zoom-in'
            }`}
            onClick={toggleZoom}
          >
            <img 
              ref={imgRef}
              src={imageUrl} 
              alt={alt}
              className={`max-w-full max-h-[calc(90vh-180px)] object-contain transition-all duration-300 ${
                isLoaded ? 'opacity-100' : 'opacity-0'
              } ${isZoomed ? 'w-auto h-auto' : 'w-full h-auto'}`}
              onLoad={() => setIsLoaded(true)}
              style={{
                transform: isZoomed ? 'scale(1.25)' : 'scale(1)',
                transition: 'transform 0.3s ease-in-out',
              }}
            />
            {!isLoaded && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="animate-pulse w-16 h-16 border-4 border-white/20 border-t-white/60 rounded-full"></div>
              </div>
            )}
          </div>
          
          {description && (
            <div className="w-full max-w-full mt-4 px-4">
              <div className="bg-black/40 rounded-lg text-white p-4 w-full">
                <p className="text-sm md:text-base text-center">{description}</p>
              </div>
            </div>
          )}
        </div>
      </div>
      
      <style jsx global>{`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
      `}</style>
    </div>
  );
}
