import { supabase } from "@/lib/supabase";

export interface WebhookPayload {
  event: string;
  data: any;
  timestamp: string;
}

/**
 * Sends webhook notifications to all registered webhooks for a specific event
 * @param event The event type (e.g., "registration.created")
 * @param data The data to send with the webhook
 * @returns A promise that resolves when all webhooks have been processed
 */
export async function sendWebhookNotifications(event: string, data: any): Promise<void> {
  try {
    // Get all active webhooks that are subscribed to this event
    const { data: webhooks, error } = await supabase
      .from("webhooks")
      .select("*")
      .filter("active", "eq", true)
      .filter("events", "cs", `{"${event}"}`)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching webhooks:", error);
      return;
    }

    if (!webhooks || webhooks.length === 0) {
      console.log(`No active webhooks found for event: ${event}`);
      return;
    }

    // Prepare the webhook payload
    const payload: WebhookPayload = {
      event,
      data,
      timestamp: new Date().toISOString(),
    };

    // Send the webhook notifications in parallel
    const webhookPromises = webhooks.map(async (webhook) => {
      try {
        // Send the webhook notification
        const response = await fetch(webhook.url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "X-Webhook-Signature": generateWebhookSignature(webhook.id, payload),
          },
          body: JSON.stringify(payload),
        });

        // Update the webhook stats
        const success = response.ok;
        const updateData = {
          last_triggered_at: new Date().toISOString(),
          ...(success
            ? { success_count: (webhook.success_count || 0) + 1 }
            : { failure_count: (webhook.failure_count || 0) + 1 }),
        };

        await supabase.from("webhooks").update(updateData).eq("id", webhook.id);

        console.log(
          `Webhook ${webhook.id} ${success ? "succeeded" : "failed"} for event ${event}:`,
          success ? response.status : await response.text()
        );
      } catch (error) {
        console.error(`Error sending webhook ${webhook.id} for event ${event}:`, error);

        // Update the failure count
        await supabase
          .from("webhooks")
          .update({
            last_triggered_at: new Date().toISOString(),
            failure_count: (webhook.failure_count || 0) + 1,
          })
          .eq("id", webhook.id);
      }
    });

    // Wait for all webhook notifications to be processed
    await Promise.all(webhookPromises);
  } catch (error) {
    console.error("Error sending webhook notifications:", error);
  }
}

/**
 * Generates a signature for webhook payloads to verify authenticity
 * @param webhookId The ID of the webhook
 * @param payload The webhook payload
 * @returns A signature string
 */
function generateWebhookSignature(webhookId: string, payload: WebhookPayload): string {
  // In a real implementation, this would use a cryptographic algorithm
  // For demo purposes, we'll just use a simple hash
  const data = JSON.stringify(payload);
  const timestamp = Date.now().toString();
  
  // This is a simplified example - in production, use a proper HMAC
  return `sha256=${Buffer.from(`${webhookId}:${data}:${timestamp}`).toString("base64")}`;
}

/**
 * Verifies a webhook signature to ensure the request is authentic
 * @param webhookId The ID of the webhook
 * @param payload The webhook payload
 * @param signature The signature to verify
 * @returns True if the signature is valid, false otherwise
 */
export function verifyWebhookSignature(
  webhookId: string,
  payload: WebhookPayload,
  signature: string
): boolean {
  // In a real implementation, this would verify the signature using a cryptographic algorithm
  // For demo purposes, we'll just regenerate the signature and compare
  const expectedSignature = generateWebhookSignature(webhookId, payload);
  return signature === expectedSignature;
}
