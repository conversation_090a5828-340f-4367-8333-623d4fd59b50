-- Fix certificate templates table and ensure data exists
-- This migration ensures the certificate_templates table has the correct structure and data

-- First, let's make sure the table has all required columns
DO $$ 
BEGIN
  -- Add missing columns if they don't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificate_templates' AND column_name = 'is_default') THEN
    ALTER TABLE certificate_templates ADD COLUMN is_default BOOLEAN DEFAULT false;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificate_templates' AND column_name = 'is_shared') THEN
    ALTER TABLE certificate_templates ADD COLUMN is_shared BOOLEAN DEFAULT false;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificate_templates' AND column_name = 'html_template') THEN
    ALTER TABLE certificate_templates ADD COLUMN html_template TEXT;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificate_templates' AND column_name = 'css_styles') THEN
    ALTER TABLE certificate_templates ADD COLUMN css_styles TEXT;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificate_templates' AND column_name = 'fields') THEN
    ALTER TABLE certificate_templates ADD COLUMN fields JSONB DEFAULT '[]';
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificate_templates' AND column_name = 'is_premium') THEN
    ALTER TABLE certificate_templates ADD COLUMN is_premium BOOLEAN DEFAULT false;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificate_templates' AND column_name = 'created_by') THEN
    ALTER TABLE certificate_templates ADD COLUMN created_by UUID REFERENCES users(id) ON DELETE SET NULL;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificate_templates' AND column_name = 'orientation') THEN
    ALTER TABLE certificate_templates ADD COLUMN orientation VARCHAR(20) DEFAULT 'landscape';
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificate_templates' AND column_name = 'background_image_url') THEN
    ALTER TABLE certificate_templates ADD COLUMN background_image_url TEXT;
  END IF;
END $$;

-- Clear existing templates and insert fresh default templates
DELETE FROM certificate_templates WHERE is_default = true;

-- Insert default certificate templates
INSERT INTO certificate_templates (
  name, 
  description, 
  template_data, 
  is_active, 
  is_default,
  is_shared,
  is_premium,
  orientation,
  html_template,
  css_styles
) VALUES 
  (
    'Completion Certificate',
    'Standard completion certificate for events',
    '{"background_color": "#ffffff", "text_color": "#000000", "font_family": "Arial", "layout": "standard"}',
    true,
    true,
    false,
    false,
    'landscape',
    '<div class="certificate"><h1>Certificate of Completion</h1><p>{{participant_name}}</p><p>{{event_name}}</p></div>',
    '.certificate { text-align: center; padding: 50px; }'
  ),
  (
    'Attendance Certificate',
    'Certificate for event attendance',
    '{"background_color": "#f8f9fa", "text_color": "#333333", "font_family": "Times New Roman", "layout": "modern"}',
    true,
    true,
    false,
    false,
    'landscape',
    '<div class="certificate"><h1>Certificate of Attendance</h1><p>{{participant_name}}</p><p>{{event_name}}</p></div>',
    '.certificate { text-align: center; padding: 50px; background: #f8f9fa; }'
  ),
  (
    'Achievement Certificate',
    'Certificate for special achievements',
    '{"background_color": "#fff3cd", "text_color": "#856404", "font_family": "Georgia", "layout": "elegant"}',
    true,
    true,
    false,
    false,
    'landscape',
    '<div class="certificate"><h1>Certificate of Achievement</h1><p>{{participant_name}}</p><p>{{event_name}}</p></div>',
    '.certificate { text-align: center; padding: 50px; background: #fff3cd; }'
  ),
  (
    'Professional Certificate',
    'Professional-looking certificate for corporate events',
    '{"background_color": "#f8f9fa", "text_color": "#2c3e50", "font_family": "Georgia", "layout": "professional"}',
    true,
    true,
    false,
    false,
    'landscape',
    '<div class="certificate"><h1>Professional Certificate</h1><p>{{participant_name}}</p><p>{{event_name}}</p></div>',
    '.certificate { text-align: center; padding: 50px; font-family: Georgia; }'
  ),
  (
    'Modern Certificate',
    'Modern design certificate with clean layout',
    '{"background_color": "#ffffff", "text_color": "#1a202c", "font_family": "Inter", "layout": "modern"}',
    true,
    true,
    false,
    false,
    'landscape',
    '<div class="certificate"><h1>Modern Certificate</h1><p>{{participant_name}}</p><p>{{event_name}}</p></div>',
    '.certificate { text-align: center; padding: 50px; font-family: Inter; }'
  );

-- Disable RLS temporarily to ensure data can be accessed
ALTER TABLE certificate_templates DISABLE ROW LEVEL SECURITY;

-- Re-enable RLS and create simple policies
ALTER TABLE certificate_templates ENABLE ROW LEVEL SECURITY;

-- Drop existing policies
DROP POLICY IF EXISTS "Users can view default and own templates" ON certificate_templates;
DROP POLICY IF EXISTS "Users can create own templates" ON certificate_templates;
DROP POLICY IF EXISTS "Users can update own templates" ON certificate_templates;
DROP POLICY IF EXISTS "Users can delete own templates" ON certificate_templates;
DROP POLICY IF EXISTS "Admins can manage all templates" ON certificate_templates;

-- Create simple policies that allow access
CREATE POLICY "Allow all authenticated users to read templates" ON certificate_templates
  FOR SELECT TO authenticated USING (true);

CREATE POLICY "Allow authenticated users to create templates" ON certificate_templates
  FOR INSERT TO authenticated WITH CHECK (true);

CREATE POLICY "Allow users to update their own templates" ON certificate_templates
  FOR UPDATE TO authenticated USING (created_by = auth.uid() OR auth.uid() IS NULL);

CREATE POLICY "Allow users to delete their own templates" ON certificate_templates
  FOR DELETE TO authenticated USING (created_by = auth.uid() OR auth.uid() IS NULL);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_certificate_templates_is_default ON certificate_templates(is_default);
CREATE INDEX IF NOT EXISTS idx_certificate_templates_is_shared ON certificate_templates(is_shared);
CREATE INDEX IF NOT EXISTS idx_certificate_templates_created_by ON certificate_templates(created_by);
CREATE INDEX IF NOT EXISTS idx_certificate_templates_is_active ON certificate_templates(is_active);
