"use client"

import { useState, useR<PERSON>, use<PERSON><PERSON><PERSON>, useEffect } from "react"
import { Type, Image, QrCode, Save, Eye, Upload, Code, Trash2, RotateCcw, RotateCw, Frame } from "lucide-react"
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
  closestCenter,
} from '@dnd-kit/core'
import { CSS } from '@dnd-kit/utilities'
import { useDraggable, useDroppable } from '@dnd-kit/core'
import { QRCodeSVG } from "qrcode.react"
import dynamic from "next/dynamic"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"

// Dynamically import Monaco Editor to avoid SSR issues
const MonacoEditor = dynamic(() => import("@monaco-editor/react"), { ssr: false })

export interface CertificateField {
  id: string
  type: "text" | "image" | "qr"
  label: string
  x: number
  y: number
  width: number
  height: number
  fontSize?: number
  fontWeight?: string
  color?: string
  textAlign?: "left" | "center" | "right"
  content?: string
  imageUrl?: string
}

interface CertificateTemplateEditorProps {
  template?: {
    id?: string
    name: string
    html_template?: string
    css_styles?: string
    fields?: CertificateField[]
    background_image_url?: string
    background_color?: string
    show_frame?: boolean
    description?: string
    orientation?: OrientationType | "landscape" | "portrait" // Support both old and new formats
  }
  onSave: (template: any) => void
  onCancel: () => void
}

// Orientation and dimension utilities
type OrientationType = "landscape-150" | "portrait-150" | "landscape-300" | "portrait-300"

const getOrientationBase = (orientation: OrientationType): "landscape" | "portrait" => {
  return orientation.includes("landscape") ? "landscape" : "portrait"
}

const getDimensions = (orientation: OrientationType) => {
  const isLandscape = orientation.includes("landscape")
  const is300dpi = orientation.includes("300")

  if (is300dpi) {
    // 300 DPI - Print ready A4 dimensions
    return {
      width: isLandscape ? 3508 : 2480,
      height: isLandscape ? 2480 : 3508,
      displayWidth: isLandscape ? 800 : 600,
      displayHeight: isLandscape ? 600 : 800,
      dpi: 300
    }
  } else {
    // 150 DPI - Standard dimensions
    return {
      width: isLandscape ? 1754 : 1240,
      height: isLandscape ? 1240 : 1754,
      displayWidth: isLandscape ? 800 : 600,
      displayHeight: isLandscape ? 600 : 800,
      dpi: 150
    }
  }
}

const getOrientationLabel = (orientation: OrientationType) => {
  const dims = getDimensions(orientation)
  const base = getOrientationBase(orientation)
  return `${base.charAt(0).toUpperCase() + base.slice(1)} (${dims.dpi}DPI - ${dims.width}×${dims.height}px)`
}

// HTML template generation utilities
const generateHtmlFromFields = (fields: CertificateField[], orientation: OrientationType, backgroundColor: string, backgroundImage: string, showFrame: boolean = true) => {
  const dims = getDimensions(orientation)
  const baseWidth = dims.displayWidth
  const baseHeight = dims.displayHeight

  const fieldsHtml = fields.map(field => {
    const style = `position: absolute; left: ${field.x}px; top: ${field.y}px; width: ${field.width}px; height: ${field.height}px;`

    switch (field.type) {
      case "text":
        return `    <div class="field-${field.id}" style="${style} font-size: ${field.fontSize || 16}px; font-weight: ${field.fontWeight || 'normal'}; color: ${field.color || '#000000'}; text-align: ${field.textAlign || 'left'}; display: flex; align-items: center;">
      ${field.content || field.label}
    </div>`
      case "image":
        return field.imageUrl
          ? `    <div class="field-${field.id}" style="${style} display: flex; align-items: center; justify-content: center; overflow: hidden;">
      <img src="${field.imageUrl}" alt="${field.label}" style="width: 100%; height: 100%; object-fit: contain;" />
    </div>`
          : `    <div class="field-${field.id}" style="${style} background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #6b7280;">
      [Image: ${field.label}]
    </div>`
      case "qr":
        return `    <div class="field-${field.id}" style="${style} display: flex; align-items: center; justify-content: center;">
      <div style="width: 80px; height: 80px; background: #f3f4f6; border: 1px solid #d1d5db; display: flex; align-items: center; justify-content: center; font-size: 12px; color: #6b7280;">QR Code</div>
    </div>`
      default:
        return ""
    }
  }).join('\n')

  // Generate background style
  let backgroundStyle = `background: ${backgroundColor};`
  if (backgroundImage) {
    backgroundStyle = `background: url('${backgroundImage}') center/cover, ${backgroundColor};`
  }

  // Conditionally apply frame styling
  const frameStyle = showFrame
    ? `background: rgba(255, 255, 255, 0.9); margin: 20px; padding: 40px; height: calc(100% - 40px); position: relative; border: 2px solid #e2e8f0;`
    : `margin: 0; padding: 20px; height: 100%; position: relative;`

  return `<div class="certificate" style="width: ${baseWidth}px; height: ${baseHeight}px; ${backgroundStyle} border: 8px solid #4a5568; position: relative; font-family: 'Georgia', serif; color: #2d3748;">
  <div class="certificate-content" style="${frameStyle}">
${fieldsHtml}
  </div>
</div>`
}

// Enhanced HTML parsing to handle any HTML elements, not just field- classes
const parseHtmlToFields = (html: string): CertificateField[] => {
  const parser = new DOMParser()
  const doc = parser.parseFromString(html, 'text/html')
  const fields: CertificateField[] = []

  // First, try to find elements with field- class (existing structure)
  const fieldElements = doc.querySelectorAll('[class*="field-"]')

  if (fieldElements.length > 0) {
    // Use existing parsing logic for field- elements
    fieldElements.forEach((element, index) => {
      const className = element.className
      const fieldIdMatch = className.match(/field-([^\\s]+)/)
      const fieldId = fieldIdMatch ? fieldIdMatch[1] : `field-${Date.now()}-${index}`

      const style = (element as HTMLElement).style
      const x = parseInt(style.left) || 0
      const y = parseInt(style.top) || 0
      const width = parseInt(style.width) || 200
      const height = parseInt(style.height) || 40

      // Determine field type based on content
      let type: "text" | "image" | "qr" = "text"
      let content = element.textContent?.trim() || ""

      if (content.includes('[Image:')) {
        type = "image"
        content = content.replace(/\\[Image:\\s*(.*)\\]/, '$1')
      } else if (content.includes('QR Code') || content.includes('{{qr_code_url}}') || content.includes('{{verification_url}}')) {
        type = "qr"
        content = ""
      }

      fields.push({
        id: fieldId,
        type,
        label: content || `Field ${index + 1}`,
        x,
        y,
        width,
        height,
        fontSize: parseInt(style.fontSize) || 16,
        fontWeight: style.fontWeight || "normal",
        color: style.color || "#000000",
        textAlign: style.textAlign as any || "left",
        content: type === "text" ? content : undefined,
      })
    })
  } else {
    // Enhanced parsing for any HTML elements with position styles
    const allElements = doc.querySelectorAll('*')

    allElements.forEach((element, index) => {
      const htmlElement = element as HTMLElement
      const style = htmlElement.style

      // Only process elements that have positioning or are likely to be fields
      const hasPosition = style.position === 'absolute' || style.position === 'relative'
      const hasText = htmlElement.textContent?.trim()
      const isImg = htmlElement.tagName.toLowerCase() === 'img'
      const isSvg = htmlElement.tagName.toLowerCase() === 'svg'
      const hasQrContent = hasText && (
        hasText.includes('QR') ||
        hasText.includes('{{qr_code_url}}') ||
        hasText.includes('{{verification_url}}') ||
        hasText.includes('verification')
      )

      if (hasPosition || hasText || isImg || isSvg || hasQrContent) {
        const x = parseInt(style.left) || parseInt(style.marginLeft) || 0
        const y = parseInt(style.top) || parseInt(style.marginTop) || 0
        const width = parseInt(style.width) || htmlElement.offsetWidth || 200
        const height = parseInt(style.height) || htmlElement.offsetHeight || 40

        // Determine field type
        let type: "text" | "image" | "qr" = "text"
        let content = hasText || ""

        if (isImg || isSvg || content.includes('[Image:')) {
          type = "image"
          content = content.replace(/\\[Image:\\s*(.*)\\]/, '$1')
        } else if (hasQrContent) {
          type = "qr"
          content = ""
        }

        // Extract styling
        const fontSize = parseInt(style.fontSize) || 16
        const fontWeight = style.fontWeight || "normal"
        const color = style.color || "#000000"
        const textAlign = style.textAlign as any || "left"

        fields.push({
          id: `parsed-field-${Date.now()}-${index}`,
          type,
          label: content || `${type === 'image' ? 'Image' : type === 'qr' ? 'QR Code' : 'Text'} ${index + 1}`,
          x: Math.max(0, x),
          y: Math.max(0, y),
          width: Math.max(50, width),
          height: Math.max(20, height),
          fontSize,
          fontWeight,
          color,
          textAlign,
          content: type === "text" ? content : undefined,
        })
      }
    })
  }

  return fields
}

// Draggable field component with @dnd-kit
function DraggableField({
  field,
  isSelected,
  onSelect,
  scale = 1
}: {
  field: CertificateField
  isSelected: boolean
  onSelect: (field: CertificateField) => void
  scale?: number
}) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: field.id,
    data: {
      type: 'field',
      field,
    },
  })

  const style = {
    position: 'absolute' as const,
    left: field.x * scale,
    top: field.y * scale,
    width: field.width * scale,
    height: field.height * scale,
    transform: CSS.Translate.toString(transform),
    cursor: isDragging ? 'grabbing' : 'grab',
    opacity: isDragging ? 0.5 : 1,
    border: isSelected ? '2px solid #6366f1' : '1px solid #e5e7eb',
    borderRadius: '4px',
    padding: '2px',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    zIndex: isDragging ? 1000 : isSelected ? 100 : 1,
    transition: isDragging ? 'none' : 'all 0.2s ease',
  }

  const renderFieldContent = () => {
    switch (field.type) {
      case "text":
        return (
          <div
            style={{
              fontSize: `${(field.fontSize || 16) * scale}px`,
              fontWeight: field.fontWeight || "normal",
              color: field.color || "#000000",
              textAlign: field.textAlign as any || "left",
              width: '100%',
              height: '100%',
              overflow: 'hidden',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            {field.content || field.label}
          </div>
        )
      case "image":
        return field.imageUrl ? (
          <div className="w-full h-full overflow-hidden rounded">
            <img
              src={field.imageUrl}
              alt={field.label}
              className="w-full h-full object-contain"
              style={{ maxWidth: '100%', maxHeight: '100%' }}
            />
          </div>
        ) : (
          <div className="w-full h-full bg-gray-100 border border-gray-300 rounded flex items-center justify-center text-xs text-gray-500">
            <Image className="w-4 h-4" />
          </div>
        )
      case "qr":
        return (
          <div className="w-full h-full flex items-center justify-center">
            <QRCodeSVG
              value="https://mticket.my/verify/sample"
              size={Math.min(field.width * scale - 4, field.height * scale - 4)}
              level="M"
            />
          </div>
        )
      default:
        return null
    }
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      onClick={(e) => {
        e.stopPropagation()
        onSelect(field)
      }}
      className="hover:border-blue-400 transition-colors"
    >
      {renderFieldContent()}
      {isSelected && (
        <div className="absolute -top-6 left-0 bg-blue-600 text-white text-xs px-2 py-1 rounded pointer-events-none">
          {field.label}
        </div>
      )}
    </div>
  )
}

// Droppable canvas with @dnd-kit
function DroppableCanvas({
  fields,
  selectedField,
  onFieldSelect,
  orientation = "landscape-150",
  backgroundColor = "#ffffff",
  backgroundImage = "",
  showFrame = true,
  children
}: {
  fields: CertificateField[]
  selectedField: CertificateField | null
  onFieldSelect: (field: CertificateField) => void
  orientation?: OrientationType
  backgroundColor?: string
  backgroundImage?: string
  showFrame?: boolean
  children?: React.ReactNode
}) {
  const canvasRef = useRef<HTMLDivElement>(null)
  const [scale, setScale] = useState(1)

  const { setNodeRef, isOver } = useDroppable({
    id: 'canvas',
  })

  // Calculate responsive scale
  useEffect(() => {
    const updateScale = () => {
      if (canvasRef.current) {
        const container = canvasRef.current.parentElement
        if (container) {
          const containerWidth = container.clientWidth
          const dims = getDimensions(orientation)
          const newScale = Math.min(1, (containerWidth - 32) / dims.displayWidth)
          setScale(newScale)
        }
      }
    }

    updateScale()
    const resizeObserver = new ResizeObserver(updateScale)
    if (canvasRef.current?.parentElement) {
      resizeObserver.observe(canvasRef.current.parentElement)
    }

    return () => resizeObserver.disconnect()
  }, [orientation])

  const dims = getDimensions(orientation)
  const baseWidth = dims.displayWidth
  const baseHeight = dims.displayHeight

  return (
    <div
      ref={(node) => {
        setNodeRef(node)
        canvasRef.current = node
      }}
      className={`relative bg-white border-2 border-dashed ${
        isOver ? "border-blue-400 bg-blue-50" : "border-gray-300"
      } rounded-lg overflow-hidden mx-auto transition-all duration-200`}
      style={{
        width: `${baseWidth * scale}px`,
        height: `${baseHeight * scale}px`,
        aspectRatio: getOrientationBase(orientation) === "portrait" ? "3/4" : "4/3"
      }}
      onClick={() => onFieldSelect(null as any)}
    >
      {/* Certificate background */}
      <div
        className="absolute inset-0 border-8 border-gray-600 rounded-lg"
        style={{
          background: backgroundImage
            ? `url('${backgroundImage}') center/cover, ${backgroundColor}`
            : backgroundColor
        }}
      >
        <div className={`absolute ${showFrame ? 'inset-4 border-2 border-gray-300 rounded-lg bg-white bg-opacity-90 shadow-lg' : 'inset-0'}`}>
          {/* Render all fields */}
          {fields.map((field) => (
            <DraggableField
              key={field.id}
              field={field}
              isSelected={selectedField?.id === field.id}
              onSelect={onFieldSelect}
              scale={scale}
            />
          ))}
          {children}
        </div>
      </div>

      {/* Drop zone indicator */}
      {isOver && (
        <div className="absolute inset-0 bg-blue-100 bg-opacity-50 flex items-center justify-center pointer-events-none">
          <div className="text-blue-600 font-medium">Drop here to add field</div>
        </div>
      )}
    </div>
  )
}

// Draggable field type from toolbar with @dnd-kit
function DraggableFieldType({ type, icon: Icon, label }: { type: string, icon: any, label: string }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging,
  } = useDraggable({
    id: `new-${type}`,
    data: {
      type: 'new-field',
      fieldType: type,
    },
  })

  const style = {
    transform: CSS.Translate.toString(transform),
    opacity: isDragging ? 0.5 : 1,
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className="flex items-center gap-2 p-3 border rounded-lg cursor-grab hover:bg-gray-50 transition-colors active:cursor-grabbing"
    >
      <Icon className="w-4 h-4" />
      <span className="text-sm">{label}</span>
    </div>
  )
}

export function CertificateTemplateEditor({ template, onSave, onCancel }: CertificateTemplateEditorProps) {
  const [templateName, setTemplateName] = useState(template?.name || "")
  const [description, setDescription] = useState(template?.description || "")
  const [fields, setFields] = useState<CertificateField[]>(template?.fields || [])
  const [selectedField, setSelectedField] = useState<CertificateField | null>(null)
  const [htmlTemplate, setHtmlTemplate] = useState("")
  const [orientation, setOrientation] = useState<OrientationType>(() => {
    // Handle backward compatibility
    const templateOrientation = template?.orientation
    if (templateOrientation === "landscape") return "landscape-150"
    if (templateOrientation === "portrait") return "portrait-150"
    return templateOrientation ? (templateOrientation as OrientationType) : "landscape-150"
  })
  const [activeTab, setActiveTab] = useState("visual")
  const [draggedField, setDraggedField] = useState<CertificateField | null>(null)
  const [backgroundColor, setBackgroundColor] = useState(template?.background_color || "#ffffff")
  const [backgroundImage, setBackgroundImage] = useState(template?.background_image_url || "")
  const [showFrame, setShowFrame] = useState(template?.show_frame !== false) // Default to true
  const [rawHtmlMode, setRawHtmlMode] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [isUploadingBackground, setIsUploadingBackground] = useState(false)
  const [uploadingFieldIds, setUploadingFieldIds] = useState<Set<string>>(new Set())
  const { toast } = useToast()

  // Predefined content options for text fields
  const contentOptions = [
    { value: "{{participant_name}}", label: "Participant Name" },
    { value: "{{ic_number}}", label: "IC Number" },
    { value: "{{event_name}}", label: "Event Name" },
    { value: "{{event_date}}", label: "Event Date" },
    { value: "{{completion_date}}", label: "Completion Date" },
    { value: "{{organizer_name}}", label: "Organizer Name" },
    { value: "{{certificate_id}}", label: "Certificate ID" },
    { value: "custom", label: "Custom Text" },
  ]

  // Configure sensors for better touch and mouse support
  const mouseSensor = useSensor(MouseSensor, {
    activationConstraint: {
      distance: 8, // Require 8px of movement before activating
    },
  })

  const touchSensor = useSensor(TouchSensor, {
    activationConstraint: {
      delay: 200, // 200ms delay for touch
      tolerance: 8,
    },
  })

  const sensors = useSensors(mouseSensor, touchSensor)

  // Synchronize HTML template with fields whenever fields, orientation, or background change
  useEffect(() => {
    const generatedHtml = generateHtmlFromFields(fields, orientation, backgroundColor, backgroundImage, showFrame)
    setHtmlTemplate(generatedHtml)
  }, [fields, orientation, backgroundColor, backgroundImage, showFrame])

  // Initialize fields from template if provided, ensure QR code exists
  useEffect(() => {
    let initialFields: CertificateField[] = []

    if (template?.html_template && !fields.length) {
      initialFields = parseHtmlToFields(template.html_template)
    } else if (template?.fields) {
      initialFields = template.fields
    }

    // Ensure QR code field exists (mandatory for certificate verification)
    const hasQrCode = initialFields.some(field => field.type === "qr")
    if (!hasQrCode) {
      const qrField: CertificateField = {
        id: "qr-verification",
        type: "qr",
        label: "Verification QR Code",
        x: getOrientationBase(orientation) === "portrait" ? 450 : 650,
        y: getOrientationBase(orientation) === "portrait" ? 650 : 450,
        width: 100,
        height: 100,
        fontSize: 16,
        fontWeight: "normal",
        color: "#000000",
        textAlign: "center",
        content: "{{verification_url}}",
      }
      initialFields.push(qrField)
    }

    if (initialFields.length > 0) {
      setFields(initialFields)
    }
  }, [template, orientation])

  // Handle drag events
  const handleDragStart = useCallback((event: DragStartEvent) => {
    const { active } = event
    if (active.data.current?.type === 'field') {
      setDraggedField(active.data.current.field)
    }
  }, [])

  const handleDragEnd = useCallback((event: DragEndEvent) => {
    const { active, over, delta } = event
    setDraggedField(null)

    if (!over) return

    if (active.data.current?.type === 'field' && over.id === 'canvas') {
      // Moving existing field
      const fieldId = active.id as string
      const field = fields.find(f => f.id === fieldId)
      if (field) {
        const newX = Math.max(0, field.x + delta.x)
        const newY = Math.max(0, field.y + delta.y)

        setFields(prev => prev.map(f =>
          f.id === fieldId ? { ...f, x: newX, y: newY } : f
        ))
      }
    } else if (active.data.current?.type === 'new-field' && over.id === 'canvas') {
      // Adding new field
      const fieldType = active.data.current.fieldType
      const x = Math.max(0, delta.x)
      const y = Math.max(0, delta.y)

      handleAddField(fieldType, x, y)
    }
  }, [fields])

  const handleAddField = useCallback((type: string, x: number, y: number) => {
    const newField: CertificateField = {
      id: `field-${Date.now()}`,
      type: type as "text" | "image" | "qr",
      label: type === "text" ? "Sample Text" : type === "image" ? "Logo" : "QR Code",
      x: Math.max(0, x - 50),
      y: Math.max(0, y - 25),
      width: type === "qr" ? 100 : type === "image" ? 80 : 200,
      height: type === "qr" ? 100 : type === "image" ? 80 : 40,
      fontSize: 16,
      fontWeight: "normal",
      color: "#000000",
      textAlign: "left",
      content: type === "text" ? "{{participant_name}}" : undefined,
    }

    setFields(prev => [...prev, newField])
    setSelectedField(newField)
  }, [])

  // Handle HTML template changes and sync back to fields
  const handleHtmlTemplateChange = useCallback((value: string | undefined) => {
    const htmlValue = value || ""
    setHtmlTemplate(htmlValue)

    // Skip field parsing in raw HTML mode
    if (rawHtmlMode) {
      return
    }

    try {
      const parsedFields = parseHtmlToFields(htmlValue)

      // Ensure QR code field exists (mandatory for certificate verification)
      const hasQrCode = parsedFields.some(field => field.type === "qr")
      if (!hasQrCode && parsedFields.length > 0) {
        const qrField: CertificateField = {
          id: "qr-verification",
          type: "qr",
          label: "Verification QR Code",
          x: getOrientationBase(orientation) === "portrait" ? 450 : 650,
          y: getOrientationBase(orientation) === "portrait" ? 650 : 450,
          width: 100,
          height: 100,
          fontSize: 16,
          fontWeight: "normal",
          color: "#000000",
          textAlign: "center",
          content: "{{verification_url}}",
        }
        parsedFields.push(qrField)
      }

      setFields(parsedFields)
      setSelectedField(null)

      // Show success message if fields were parsed
      if (parsedFields.length > 0) {
        toast({
          title: "HTML parsed successfully",
          description: `Found ${parsedFields.length} field(s) in your HTML`,
        })
      }
    } catch (error) {
      console.warn('Failed to parse HTML template:', error)
      toast({
        title: "HTML parsing warning",
        description: "Some elements couldn't be converted to draggable fields. You can still use the HTML directly.",
        variant: "destructive",
      })
    }
  }, [orientation, toast, rawHtmlMode])

  const handleFieldUpdate = (updatedField: CertificateField) => {
    setFields(prev => prev.map(field =>
      field.id === updatedField.id ? updatedField : field
    ))
    setSelectedField(updatedField)
  }

  // Upload image to storage with compression
  const uploadImage = async (file: File, imageType: 'background' | 'field'): Promise<string> => {
    try {
      console.log('Starting image upload:', { fileName: file.name, size: file.size, type: file.type, imageType })

      // Import compression function dynamically
      const { compressCertificateImage, isValidImageFile } = await import("@/lib/image-compression")

      // Validate file
      if (!isValidImageFile(file)) {
        throw new Error("Please upload a valid image file (JPG, PNG, GIF, WebP)")
      }

      // Show compression message
      toast({
        title: "Processing image...",
        description: "Compressing image for optimal storage",
      })

      // Compress the image
      console.log('Compressing image...')
      const compressedFile = await compressCertificateImage(file)
      console.log('Image compressed:', { originalSize: file.size, compressedSize: compressedFile.size })

      // Get auth token using the same method as profile upload
      const getCookie = (name: string): string | null => {
        if (typeof document === 'undefined') return null;
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
        return null;
      };

      const token = getCookie('auth_token');
      console.log('Auth token found:', !!token)

      if (!token) {
        throw new Error("Authentication required - please log in again")
      }

      // Upload to server
      console.log('Creating form data...')
      const formData = new FormData()
      formData.append('file', compressedFile)
      formData.append('type', imageType)

      console.log('Sending upload request...')
      const response = await fetch('/api/upload/certificate-image', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      })

      console.log('Upload response status:', response.status)

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Upload failed with response:', errorText)
        let errorData
        try {
          errorData = JSON.parse(errorText)
        } catch {
          errorData = { error: errorText || 'Upload failed' }
        }
        throw new Error(errorData.error || 'Upload failed')
      }

      const result = await response.json()
      console.log('Upload successful:', result)

      toast({
        title: "Image uploaded",
        description: `Image compressed and uploaded successfully (${Math.round((1 - compressedFile.size / file.size) * 100)}% reduction)`,
      })

      return result.url
    } catch (error) {
      console.error('Image upload error:', error)
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Failed to upload image",
        variant: "destructive",
      })
      throw error
    }
  }

  // Handle background image upload
  const handleBackgroundImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsUploadingBackground(true)
    try {
      const imageUrl = await uploadImage(file, 'background')
      setBackgroundImage(imageUrl)
      toast({
        title: "Background image uploaded",
        description: "Background image has been compressed and uploaded successfully.",
      })
    } catch (error) {
      // Error is already handled in uploadImage function
    } finally {
      setIsUploadingBackground(false)
    }
  }

  const handleSave = () => {
    if (!templateName.trim()) {
      toast({
        title: "Error",
        description: "Template name is required",
        variant: "destructive",
      })
      return
    }

    // In raw HTML mode, check if HTML contains QR code placeholder
    if (rawHtmlMode) {
      const hasQrPlaceholder = htmlTemplate.includes('{{qr_code_url}}') ||
                              htmlTemplate.includes('{{verification_url}}') ||
                              htmlTemplate.toLowerCase().includes('qr')

      if (!hasQrPlaceholder) {
        toast({
          title: "Warning",
          description: "Consider adding a QR code placeholder ({{qr_code_url}}) for certificate verification",
          variant: "destructive",
        })
        // Don't return - allow saving without QR in raw mode
      }
    } else {
      // Ensure QR code exists before saving in field mode
      const hasQrCode = fields.some(field => field.type === "qr")
      if (!hasQrCode) {
        toast({
          title: "Error",
          description: "QR code is mandatory for certificate verification",
          variant: "destructive",
        })
        return
      }
    }

    // Generate CSS styles from visual editor settings
    const generateCssStyles = () => {
      const dims = getDimensions(orientation)
      const baseStyles = `
        .certificate-container {
          width: ${dims.displayWidth}px;
          height: ${dims.displayHeight}px;
          background-color: ${backgroundColor};
          ${backgroundImage ? `background-image: url(${backgroundImage});` : ''}
          background-size: cover;
          background-position: center;
          position: relative;
          font-family: Arial, sans-serif;
          overflow: hidden;
        }
      `

      const fieldStyles = fields.map(field => `
        .field-${field.id} {
          position: absolute;
          left: ${field.x}px;
          top: ${field.y}px;
          width: ${field.width}px;
          height: ${field.height}px;
          ${field.type === 'text' ? `
            font-size: ${field.fontSize || 16}px;
            color: ${field.color || '#000000'};
            text-align: ${field.textAlign || 'left'};
            font-weight: ${field.fontWeight || 'normal'};
            display: flex;
            align-items: center;
            justify-content: ${field.textAlign === 'center' ? 'center' : field.textAlign === 'right' ? 'flex-end' : 'flex-start'};
          ` : ''}
        }
      `).join('\n')

      return baseStyles + fieldStyles
    }

    const templateData = {
      id: template?.id,
      name: templateName,
      description,
      fields: rawHtmlMode ? [] : fields, // Empty fields array in raw HTML mode
      html_template: htmlTemplate,
      css_styles: rawHtmlMode ? "" : generateCssStyles(), // No CSS generation in raw HTML mode
      orientation,
      background_image_url: backgroundImage || null,
      background_color: backgroundColor,
      show_frame: showFrame,
      raw_html_mode: rawHtmlMode,
    }

    onSave(templateData)
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="space-y-1">
            <h2 className="text-xl sm:text-2xl font-bold">Certificate Template Editor</h2>
            <p className="text-sm sm:text-base text-muted-foreground">Design your certificate template with drag-and-drop fields or custom HTML</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <Button variant="outline" onClick={onCancel} className="w-full sm:w-auto">
              Cancel
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowPreview(true)}
              disabled={!templateName}
              className="w-full sm:w-auto"
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
            <Button onClick={handleSave} className="w-full sm:w-auto">
              <Save className="w-4 h-4 mr-2" />
              Save Template
            </Button>
          </div>
        </div>

        {/* Template Basic Info */}
        <Card>
          <CardHeader>
            <CardTitle>Template Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="template-name">Template Name</Label>
                <Input
                  id="template-name"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  placeholder="Enter template name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="template-description">Description</Label>
                <Input
                  id="template-description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Enter template description"
                />
              </div>
              <div className="space-y-2 sm:col-span-2 lg:col-span-1">
                <Label htmlFor="orientation">Orientation</Label>
                <Select value={orientation} onValueChange={(value: OrientationType) => setOrientation(value)}>
                  <SelectTrigger id="orientation">
                    <SelectValue placeholder="Select orientation" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="landscape-150">
                      <div className="flex items-center gap-2">
                        <RotateCw className="w-4 h-4" />
                        <span className="hidden sm:inline">Landscape (150DPI - 1754×1240px)</span>
                        <span className="sm:hidden">Landscape 150DPI</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="portrait-150">
                      <div className="flex items-center gap-2">
                        <RotateCcw className="w-4 h-4" />
                        <span className="hidden sm:inline">Portrait (150DPI - 1240×1754px)</span>
                        <span className="sm:hidden">Portrait 150DPI</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="landscape-300">
                      <div className="flex items-center gap-2">
                        <RotateCw className="w-4 h-4" />
                        <span className="hidden sm:inline">Landscape (300DPI - 3508×2480px)</span>
                        <span className="sm:hidden">Landscape 300DPI</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="portrait-300">
                      <div className="flex items-center gap-2">
                        <RotateCcw className="w-4 h-4" />
                        <span className="hidden sm:inline">Portrait (300DPI - 2480×3508px)</span>
                        <span className="sm:hidden">Portrait 300DPI</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Background Settings */}
            <div className="space-y-4 pt-4 border-t">
              <h4 className="font-medium">Background Settings</h4>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="background-color">Background Color</Label>
                  <div className="flex gap-2">
                    <Input
                      id="background-color"
                      type="color"
                      value={backgroundColor}
                      onChange={(e) => setBackgroundColor(e.target.value)}
                      className="w-16 h-10 p-1 border rounded flex-shrink-0"
                    />
                    <Input
                      value={backgroundColor}
                      onChange={(e) => setBackgroundColor(e.target.value)}
                      placeholder="#ffffff"
                      className="flex-1 min-w-0"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="background-image">Background Image</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="background-image"
                      type="file"
                      accept="image/*"
                      onChange={handleBackgroundImageUpload}
                      className="hidden"
                    />
                    <Button
                      variant="outline"
                      onClick={() => document.getElementById('background-image')?.click()}
                      className="flex-1 min-w-0"
                      disabled={isUploadingBackground}
                    >
                      <Upload className="w-4 h-4 mr-2 flex-shrink-0" />
                      <span className="truncate">{isUploadingBackground ? "Uploading..." : "Upload Background"}</span>
                    </Button>
                    {backgroundImage && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setBackgroundImage("")}
                        className="flex-shrink-0"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                  {backgroundImage && (
                    <p className="text-xs text-muted-foreground">Background image uploaded</p>
                  )}
                </div>
              </div>

              {/* Frame Toggle */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 pt-2">
                <div className="space-y-1 flex-1">
                  <Label htmlFor="show-frame" className="flex items-center gap-2">
                    <Frame className="w-4 h-4" />
                    White Frame Border
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Add a white decorative frame around the certificate content
                  </p>
                </div>
                <input
                  id="show-frame"
                  type="checkbox"
                  checked={showFrame}
                  onChange={(e) => setShowFrame(e.target.checked)}
                  className="w-4 h-4 flex-shrink-0"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Editor Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="visual" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Visual Editor
            </TabsTrigger>
            <TabsTrigger value="html" className="flex items-center gap-2">
              <Code className="w-4 h-4" />
              HTML Editor
            </TabsTrigger>
          </TabsList>

          <TabsContent value="visual" className="space-y-6">
            {/* Mobile-first responsive layout */}
            <div className="flex flex-col lg:grid lg:grid-cols-4 gap-6">
              {/* Toolbar - Full width on mobile, collapsible */}
              <Card className="lg:col-span-1 order-1 lg:order-1">
                <CardHeader>
                  <CardTitle className="text-lg">Field Toolkit</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label>Drag Fields to Canvas</Label>
                    <div className="grid grid-cols-3 lg:grid-cols-1 gap-2 lg:space-y-2 lg:gap-0">
                      <DraggableFieldType type="text" icon={Type} label="Text Field" />
                      <DraggableFieldType type="image" icon={Image} label="Image/Logo" />
                      <DraggableFieldType type="qr" icon={QrCode} label="QR Code" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Canvas - Center on desktop, full width on mobile */}
              <Card className="lg:col-span-2 order-2 lg:order-2">
                <CardHeader>
                  <CardTitle className="text-lg">Certificate Preview</CardTitle>
                </CardHeader>
                <CardContent className="overflow-x-auto">
                  <DroppableCanvas
                    fields={fields}
                    selectedField={selectedField}
                    onFieldSelect={setSelectedField}
                    orientation={orientation}
                    backgroundColor={backgroundColor}
                    backgroundImage={backgroundImage}
                    showFrame={showFrame}
                  />
                </CardContent>
              </Card>

              {/* Properties Panel - Full width on mobile, right side on desktop */}
              <Card className="lg:col-span-1 order-3 lg:order-3">
                <CardHeader>
                  <CardTitle className="text-lg">Field Properties</CardTitle>
                </CardHeader>
                <CardContent>
                  {selectedField ? (
                    <FieldPropertiesPanel
                      field={selectedField}
                      onUpdate={handleFieldUpdate}
                      onDelete={() => {
                        // Prevent deletion of QR code
                        if (selectedField.type === "qr") {
                          toast({
                            title: "Cannot delete QR code",
                            description: "QR code is mandatory for certificate verification",
                            variant: "destructive",
                          })
                          return
                        }
                        setFields(prev => prev.filter(f => f.id !== selectedField.id))
                        setSelectedField(null)
                      }}
                      contentOptions={contentOptions}
                      uploadingFieldIds={uploadingFieldIds}
                      setUploadingFieldIds={setUploadingFieldIds}
                      uploadImage={uploadImage}
                    />
                  ) : (
                    <div className="text-center text-muted-foreground py-8">
                      Select a field to edit its properties
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="html" className="space-y-6">
            <div className="flex flex-col lg:grid lg:grid-cols-2 gap-6">
              {/* HTML Editor */}
              <Card className="order-1 lg:order-1">
                <CardHeader>
                  <CardTitle className="text-lg">HTML Template</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Paste your custom HTML here. Elements with positioning will be converted to draggable fields.
                    Use placeholders like {`{{participant_name}}`}, {`{{event_name}}`}, {`{{completion_date}}`}, {`{{qr_code_url}}`}
                  </p>
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mt-2">
                    <div className="flex flex-wrap gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const sampleHtml = `<div style="position: absolute; left: 50px; top: 50px; width: 300px; height: 40px; font-size: 24px; font-weight: bold; color: #333;">
  {{participant_name}}
</div>
<div style="position: absolute; left: 50px; top: 100px; width: 200px; height: 30px; font-size: 16px; color: #666;">
  {{event_name}}
</div>
<div style="position: absolute; left: 50px; top: 140px; width: 150px; height: 25px; font-size: 14px; color: #999;">
  {{completion_date}}
</div>`
                          setHtmlTemplate(sampleHtml)
                        }}
                      >
                        Load Sample
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setHtmlTemplate("")
                          setFields([])
                        }}
                      >
                        Clear All
                      </Button>
                      {rawHtmlMode && htmlTemplate && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            try {
                              const parsedFields = parseHtmlToFields(htmlTemplate)

                              // Ensure QR code field exists
                              const hasQrCode = parsedFields.some(field => field.type === "qr")
                              if (!hasQrCode && parsedFields.length > 0) {
                                const qrField: CertificateField = {
                                  id: "qr-verification",
                                  type: "qr",
                                  label: "Verification QR Code",
                                  x: getOrientationBase(orientation) === "portrait" ? 450 : 650,
                                  y: getOrientationBase(orientation) === "portrait" ? 650 : 450,
                                  width: 100,
                                  height: 100,
                                  fontSize: 16,
                                  fontWeight: "normal",
                                  color: "#000000",
                                  textAlign: "center",
                                  content: "{{verification_url}}",
                                }
                                parsedFields.push(qrField)
                              }

                              setFields(parsedFields)
                              setSelectedField(null)
                              setRawHtmlMode(false)

                              if (parsedFields.length > 0) {
                                toast({
                                  title: "HTML converted successfully",
                                  description: `Created ${parsedFields.length} draggable field(s) and switched to Visual Editor`,
                                })
                              } else {
                                toast({
                                  title: "No fields found",
                                  description: "No positioned elements found in HTML. Add elements with position:absolute to create fields.",
                                  variant: "destructive",
                                })
                              }
                            } catch (error) {
                              console.warn('Failed to convert HTML to fields:', error)
                              toast({
                                title: "Conversion failed",
                                description: "Could not convert HTML to fields. Check your HTML structure.",
                                variant: "destructive",
                              })
                            }
                          }}
                          className="whitespace-nowrap"
                        >
                          Convert to Fields
                        </Button>
                      )}
                    </div>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <Label htmlFor="raw-html-mode" className="text-sm whitespace-nowrap">Raw HTML Mode</Label>
                      <input
                        id="raw-html-mode"
                        type="checkbox"
                        checked={rawHtmlMode}
                        onChange={(e) => {
                          const isRawMode = e.target.checked
                          setRawHtmlMode(isRawMode)

                          if (isRawMode) {
                            toast({
                              title: "Raw HTML Mode enabled",
                              description: "HTML will not be converted to draggable fields",
                            })
                          } else {
                            // When disabling raw mode, parse current HTML into fields
                            toast({
                              title: "Raw HTML Mode disabled",
                              description: "Converting HTML to draggable fields...",
                            })

                            try {
                              const parsedFields = parseHtmlToFields(htmlTemplate)

                              // Ensure QR code field exists
                              const hasQrCode = parsedFields.some(field => field.type === "qr")
                              if (!hasQrCode && parsedFields.length > 0) {
                                const qrField: CertificateField = {
                                  id: "qr-verification",
                                  type: "qr",
                                  label: "Verification QR Code",
                                  x: getOrientationBase(orientation) === "portrait" ? 450 : 650,
                                  y: getOrientationBase(orientation) === "portrait" ? 650 : 450,
                                  width: 100,
                                  height: 100,
                                  fontSize: 16,
                                  fontWeight: "normal",
                                  color: "#000000",
                                  textAlign: "center",
                                  content: "{{verification_url}}",
                                }
                                parsedFields.push(qrField)
                              }

                              setFields(parsedFields)
                              setSelectedField(null)

                              if (parsedFields.length > 0) {
                                toast({
                                  title: "HTML converted successfully",
                                  description: `Created ${parsedFields.length} draggable field(s) from your HTML`,
                                })
                              } else {
                                toast({
                                  title: "No fields found",
                                  description: "No positioned elements found in HTML. Add elements with position:absolute to create fields.",
                                  variant: "destructive",
                                })
                              }
                            } catch (error) {
                              console.warn('Failed to parse HTML when disabling raw mode:', error)
                              toast({
                                title: "Conversion failed",
                                description: "Could not convert HTML to fields. Check your HTML structure.",
                                variant: "destructive",
                              })
                            }
                          }
                        }}
                        className="w-4 h-4"
                      />
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="h-64 sm:h-80 lg:h-96 border rounded-md overflow-hidden">
                    <MonacoEditor
                      height="100%"
                      language="html"
                      value={htmlTemplate}
                      onChange={handleHtmlTemplateChange}
                      options={{
                        minimap: { enabled: false },
                        fontSize: 14,
                        wordWrap: "on",
                        automaticLayout: true,
                        formatOnPaste: true,
                        formatOnType: true,
                        autoIndent: "full",
                        tabSize: 2,
                        insertSpaces: true,
                        scrollBeyondLastLine: false,
                        renderWhitespace: "selection",
                        bracketPairColorization: { enabled: true },
                        suggest: {
                          showKeywords: true,
                          showSnippets: true,
                        },
                        quickSuggestions: {
                          other: true,
                          comments: true,
                          strings: true,
                        },
                      }}
                    />
                  </div>
                  <div className="mt-2 text-xs text-muted-foreground">
                    💡 Tip: {rawHtmlMode
                      ? "Raw HTML mode is enabled. Use 'Convert to Fields' button or disable Raw HTML Mode to create draggable fields."
                      : "Paste any HTML with inline styles. Elements with position:absolute will become draggable fields."}
                  </div>
                </CardContent>
              </Card>

              {/* Visual Preview */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Visual Preview</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    {rawHtmlMode
                      ? "Raw HTML preview (fields are not draggable in this mode)"
                      : "Visual representation of your HTML template"}
                  </p>
                </CardHeader>
                <CardContent>
                  {rawHtmlMode ? (
                    <div className="border rounded-lg p-4 bg-gray-50">
                      <div className="text-center text-muted-foreground mb-4">
                        <Code className="w-8 h-8 mx-auto mb-2" />
                        <p>Raw HTML Mode Active</p>
                        <p className="text-xs">Disable Raw HTML Mode to see draggable fields</p>
                      </div>
                      <div
                        className="border bg-white rounded p-4 min-h-[300px]"
                        dangerouslySetInnerHTML={{ __html: htmlTemplate }}
                      />
                    </div>
                  ) : (
                    <DroppableCanvas
                      fields={fields}
                      selectedField={selectedField}
                      onFieldSelect={setSelectedField}
                      orientation={orientation}
                      backgroundColor={backgroundColor}
                      backgroundImage={backgroundImage}
                      showFrame={showFrame}
                    />
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Drag Overlay */}
      <DragOverlay>
        {draggedField ? (
          <div className="opacity-80 transform rotate-3">
            <DraggableField
              field={draggedField}
              isSelected={false}
              onSelect={() => {}}
            />
          </div>
        ) : null}
      </DragOverlay>

      {/* Preview Modal */}
      {showPreview && (
        <CertificatePreviewModal
          templateName={templateName}
          fields={fields}
          htmlTemplate={htmlTemplate}
          orientation={orientation}
          backgroundColor={backgroundColor}
          backgroundImage={backgroundImage}
          showFrame={showFrame}
          onClose={() => setShowPreview(false)}
        />
      )}
    </DndContext>
  )
}

// Field properties panel component
function FieldPropertiesPanel({
  field,
  onUpdate,
  onDelete,
  contentOptions,
  uploadingFieldIds,
  setUploadingFieldIds,
  uploadImage
}: {
  field: CertificateField
  onUpdate: (field: CertificateField) => void
  onDelete: () => void
  contentOptions: Array<{ value: string; label: string }>
  uploadingFieldIds: Set<string>
  setUploadingFieldIds: (fn: (prev: Set<string>) => Set<string>) => void
  uploadImage: (file: File, imageType: 'background' | 'field') => Promise<string>
}) {
  const [customContent, setCustomContent] = useState("")
  const [selectedContentType, setSelectedContentType] = useState(() => {
    const option = contentOptions.find(opt => opt.value === field.content)
    return option ? option.value : "custom"
  })

  const handleContentTypeChange = (value: string) => {
    setSelectedContentType(value)
    if (value === "custom") {
      onUpdate({ ...field, content: customContent })
    } else {
      onUpdate({ ...field, content: value })
    }
  }

  const handleCustomContentChange = (value: string) => {
    setCustomContent(value)
    if (selectedContentType === "custom") {
      onUpdate({ ...field, content: value })
    }
  }

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label>Label</Label>
        <Input
          value={field.label}
          onChange={(e) => onUpdate({ ...field, label: e.target.value })}
        />
      </div>

      {field.type === "text" && (
        <>
          <div className="space-y-2">
            <Label>Content Type</Label>
            <Select value={selectedContentType} onValueChange={handleContentTypeChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select content type" />
              </SelectTrigger>
              <SelectContent>
                {contentOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedContentType === "custom" && (
            <div className="space-y-2">
              <Label>Custom Content</Label>
              <Textarea
                value={customContent}
                onChange={(e) => handleCustomContentChange(e.target.value)}
                placeholder="Enter custom text"
                rows={3}
              />
            </div>
          )}

          {selectedContentType !== "custom" && (
            <div className="space-y-2">
              <Label>Preview</Label>
              <div className="p-2 bg-gray-50 rounded text-sm text-gray-600">
                {field.content}
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-2">
            <div className="space-y-2">
              <Label>Font Size</Label>
              <Input
                type="number"
                value={field.fontSize || 16}
                onChange={(e) => onUpdate({ ...field, fontSize: parseInt(e.target.value) })}
              />
            </div>
            <div className="space-y-2">
              <Label>Color</Label>
              <Input
                type="color"
                value={field.color || "#000000"}
                onChange={(e) => onUpdate({ ...field, color: e.target.value })}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label>Text Align</Label>
            <Select
              value={field.textAlign || "left"}
              onValueChange={(value) => onUpdate({ ...field, textAlign: value as "left" | "center" | "right" })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="left">Left</SelectItem>
                <SelectItem value="center">Center</SelectItem>
                <SelectItem value="right">Right</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Font Weight</Label>
            <Select
              value={field.fontWeight || "normal"}
              onValueChange={(value) => onUpdate({ ...field, fontWeight: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="normal">Normal</SelectItem>
                <SelectItem value="bold">Bold</SelectItem>
                <SelectItem value="600">Semi Bold</SelectItem>
                <SelectItem value="700">Bold</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </>
      )}

      {field.type === "image" && (
        <div className="space-y-2">
          <Label>Image Upload</Label>
          {field.imageUrl ? (
            <div className="space-y-2">
              <div className="border rounded-lg p-2 bg-gray-50">
                <img
                  src={field.imageUrl}
                  alt={field.label}
                  className="w-full h-20 object-contain rounded"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById(`image-upload-${field.id}`)?.click()}
                  className="flex-1"
                  disabled={uploadingFieldIds.has(field.id)}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  {uploadingFieldIds.has(field.id) ? "Uploading..." : "Change Image"}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onUpdate({ ...field, imageUrl: undefined })}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
              <Image className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-600 mb-2">Upload an image</p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => document.getElementById(`image-upload-${field.id}`)?.click()}
                disabled={uploadingFieldIds.has(field.id)}
              >
                <Upload className="w-4 h-4 mr-2" />
                {uploadingFieldIds.has(field.id) ? "Uploading..." : "Choose File"}
              </Button>
            </div>
          )}
          <Input
            id={`image-upload-${field.id}`}
            type="file"
            accept="image/*"
            className="hidden"
            onChange={async (e) => {
              const file = e.target.files?.[0]
              if (!file) return

              // Add field to uploading set
              setUploadingFieldIds(prev => new Set(prev).add(field.id))

              try {
                const imageUrl = await uploadImage(file, 'field')
                onUpdate({ ...field, imageUrl })
              } catch (error) {
                // Error is already handled in uploadImage function
              } finally {
                // Remove field from uploading set
                setUploadingFieldIds(prev => {
                  const newSet = new Set(prev)
                  newSet.delete(field.id)
                  return newSet
                })
              }
            }}
          />
          <p className="text-xs text-gray-500">
            Supported formats: JPG, PNG, GIF. Max size: 5MB
          </p>
        </div>
      )}

      <div className="grid grid-cols-2 gap-2">
        <div className="space-y-2">
          <Label>Width</Label>
          <Input
            type="number"
            value={field.width}
            onChange={(e) => onUpdate({ ...field, width: parseInt(e.target.value) })}
          />
        </div>
        <div className="space-y-2">
          <Label>Height</Label>
          <Input
            type="number"
            value={field.height}
            onChange={(e) => onUpdate({ ...field, height: parseInt(e.target.value) })}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-2">
        <div className="space-y-2">
          <Label>X Position</Label>
          <Input
            type="number"
            value={field.x}
            onChange={(e) => onUpdate({ ...field, x: parseInt(e.target.value) })}
          />
        </div>
        <div className="space-y-2">
          <Label>Y Position</Label>
          <Input
            type="number"
            value={field.y}
            onChange={(e) => onUpdate({ ...field, y: parseInt(e.target.value) })}
          />
        </div>
      </div>

      {field.type === "qr" ? (
        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            <strong>QR Code is mandatory</strong> for certificate verification and cannot be deleted.
          </p>
        </div>
      ) : (
        <Button variant="destructive" size="sm" onClick={onDelete} className="w-full">
          <Trash2 className="w-4 h-4 mr-2" />
          Delete Field
        </Button>
      )}
    </div>
  )
}

// Certificate Preview Modal Component
function CertificatePreviewModal({
  templateName,
  fields,
  htmlTemplate,
  orientation,
  backgroundColor,
  backgroundImage,
  showFrame,
  onClose
}: {
  templateName: string
  fields: CertificateField[]
  htmlTemplate: string
  orientation: OrientationType
  backgroundColor: string
  backgroundImage: string
  showFrame: boolean
  onClose: () => void
}) {
  const dims = getDimensions(orientation)

  // Mock data for preview
  const mockData = {
    participant_name: "John Doe",
    event_name: "Advanced Web Development Workshop",
    completion_date: new Date().toLocaleDateString(),
    ic_reg: "123456-78-9012",
    verification_url: "https://mticket.my/certificates/verify/abc123",
    organization_name: "Tech Academy Malaysia",
    certificate_id: "CERT-2025-001"
  }

  // Replace placeholders in HTML template
  const processedHtml = htmlTemplate.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return mockData[key as keyof typeof mockData] || match
  })

  // Process fields with mock data
  const processedFields = fields.map(field => ({
    ...field,
    content: field.content?.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return mockData[key as keyof typeof mockData] || match
    }) || field.content
  }))

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-auto">
        <div className="p-6 border-b">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold">Certificate Preview</h3>
              <p className="text-sm text-muted-foreground">
                {templateName} - {getOrientationLabel(orientation)}
              </p>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              ✕
            </Button>
          </div>
        </div>

        <div className="p-6">
          <div className="flex justify-center">
            <div
              className="border shadow-lg"
              style={{
                width: `${dims.width}px`,
                height: `${dims.height}px`,
                maxWidth: '100%',
                maxHeight: '70vh',
                transform: 'scale(0.5)',
                transformOrigin: 'top center'
              }}
            >
              {/* Certificate Content */}
              <div
                className="relative w-full h-full overflow-hidden"
                style={{
                  backgroundColor,
                  backgroundImage: backgroundImage ? `url(${backgroundImage})` : undefined,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center'
                }}
              >
                {/* White Frame */}
                {showFrame && (
                  <div
                    className="absolute inset-4 border-4 border-white/80 rounded-lg"
                    style={{ zIndex: 1 }}
                  />
                )}

                {/* Fields */}
                {processedFields.map((field) => (
                  <div
                    key={field.id}
                    className="absolute"
                    style={{
                      left: `${field.x}px`,
                      top: `${field.y}px`,
                      width: `${field.width}px`,
                      height: `${field.height}px`,
                      fontSize: `${field.fontSize}px`,
                      fontWeight: field.fontWeight,
                      color: field.color,
                      textAlign: field.textAlign as any,
                      zIndex: 2
                    }}
                  >
                    {field.type === "text" && (
                      <div className="w-full h-full flex items-center">
                        {field.content}
                      </div>
                    )}
                    {field.type === "image" && field.content && (
                      <img
                        src={field.content}
                        alt={field.label}
                        className="w-full h-full object-contain"
                      />
                    )}
                    {field.type === "qr" && (
                      <QRCodeSVG
                        value={field.content || mockData.verification_url}
                        size={Math.min(field.width, field.height)}
                        level="M"
                      />
                    )}
                  </div>
                ))}

                {/* Raw HTML Content */}
                {htmlTemplate && (
                  <div
                    className="absolute inset-0"
                    style={{ zIndex: 2 }}
                    dangerouslySetInnerHTML={{ __html: processedHtml }}
                  />
                )}
              </div>
            </div>
          </div>

          <div className="mt-4 text-center text-sm text-muted-foreground">
            <p>Preview shows actual print dimensions: {dims.width} × {dims.height}px at {dims.dpi}DPI</p>
            <p>Scaled to 50% for display. Mock data is used for demonstration.</p>
          </div>
        </div>
      </div>
    </div>
  )
}