"use client"

import { create<PERSON>ontext, useContext, useState, useEffect, type ReactNode } from "react"
import { useToast } from "@/hooks/use-toast"

// Types
export type UserRole = "free" | "paid" | "manager" | "admin"

export type RoleType = {
  id: string
  name: string
  description?: string
  permissions?: Record<string, any>
  created_at?: string
  updated_at?: string
}

export type UserType = {
  id: string
  email: string
  full_name: string
  role: UserRole // Legacy field, kept for backward compatibility
  role_id?: string // UUID reference to roles table
  role_info?: RoleType // Optional role information when joined with roles table
  subscription_status: "none" | "active" | "canceled" | "expired"
  subscription_end_date: string | null
  created_at: string
  organization: string | null
  organization_id: string | null
  profile_image_url: string | null
  events_created: number
  total_earnings: number
  available_balance: number
}

export type SubscriptionTier = {
  id: string
  name: string
  price: number
  description: string
  features: string[]
  max_events: number | null
  max_participants: number | null
}

type UserContextType = {
  user: UserType | null
  loading: boolean
  error: string | null
  login: (email: string, password: string) => Promise<UserType | null>
  register: (email: string, password: string, fullName: string) => Promise<UserType | null>
  logout: () => Promise<void>
  updateProfile: (data: Partial<UserType>) => Promise<UserType | null>
  upgradeSubscription: () => Promise<boolean>
  cancelSubscription: () => Promise<boolean>
  getSubscriptionTiers: () => Promise<SubscriptionTier[]>
  canCreateEvent: () => boolean
  getParticipantLimit: () => number | null
  isAdmin: () => boolean
  isManager: () => boolean
  isPaidUser: () => boolean
  refreshUser: () => Promise<void>
}

const UserContext = createContext<UserContextType | undefined>(undefined)

// Mock user data
const mockUser: UserType = {
  id: "user-123",
  email: "<EMAIL>",
  full_name: "Demo User",
  role: "admin",
  subscription_status: "active",
  subscription_end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
  created_at: new Date().toISOString(),
  organization: "Demo Organization",
  organization_id: "org-123",
  profile_image_url: null,
  events_created: 8,
  total_earnings: 12500.0,
  available_balance: 9750.0,
}

export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<UserType | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  // Simulate checking for existing session on mount
  useEffect(() => {
    // Always set the mock user immediately for demo purposes
    setUser(mockUser)
    setLoading(false)
  }, [])

  // Login user - accepts any credentials
  const login = async (email: string, password: string): Promise<UserType | null> => {
    setLoading(true)
    setError(null)

    try {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Create a custom user with the provided email
      const customUser = {
        ...mockUser,
        email,
        full_name: email.split("@")[0],
      }

      setUser(customUser)

      toast({
        title: "Success",
        description: "Logged in successfully",
      })

      return customUser
    } catch (err: any) {
      console.error("Error logging in:", err)
      setError("Failed to log in")
      toast({
        title: "Error",
        description: "Failed to log in",
        variant: "destructive",
      })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Register user - accepts any details
  const register = async (email: string, password: string, fullName: string): Promise<UserType | null> => {
    setLoading(true)
    setError(null)

    try {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const newUser = {
        ...mockUser,
        id: `user-${Math.random().toString(36).substring(2, 9)}`,
        email,
        full_name: fullName,
        created_at: new Date().toISOString(),
      }

      setUser(newUser)

      toast({
        title: "Success",
        description: "Account created successfully",
      })

      return newUser
    } catch (err: any) {
      console.error("Error registering:", err)
      setError("Failed to register")
      toast({
        title: "Error",
        description: "Failed to create account",
        variant: "destructive",
      })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Logout user
  const logout = async (): Promise<void> => {
    try {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 500))

      setUser(null)

      toast({
        title: "Success",
        description: "Logged out successfully",
      })
    } catch (err: any) {
      console.error("Error logging out:", err)
      setError("Failed to log out")
      toast({
        title: "Error",
        description: "Failed to log out",
        variant: "destructive",
      })
    }
  }

  // Update user profile
  const updateProfile = async (data: Partial<UserType>): Promise<UserType | null> => {
    if (!user) return null

    setLoading(true)
    setError(null)

    try {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      const updatedUser = { ...user, ...data }
      setUser(updatedUser)

      toast({
        title: "Success",
        description: "Profile updated successfully",
      })

      return updatedUser
    } catch (err: any) {
      console.error("Error updating profile:", err)
      setError("Failed to update profile")
      toast({
        title: "Error",
        description: "Failed to update profile",
        variant: "destructive",
      })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Upgrade subscription
  const upgradeSubscription = async (): Promise<boolean> => {
    if (!user) return false

    setLoading(true)
    setError(null)

    try {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      // Calculate subscription end date (1 month from now)
      const endDate = new Date()
      endDate.setMonth(endDate.getMonth() + 1)

      setUser({
        ...user,
        role: "paid",
        subscription_status: "active",
        subscription_end_date: endDate.toISOString(),
      })

      toast({
        title: "Success",
        description: "Subscription upgraded successfully",
      })

      return true
    } catch (err: any) {
      console.error("Error upgrading subscription:", err)
      setError("Failed to upgrade subscription")
      toast({
        title: "Error",
        description: "Failed to upgrade subscription",
        variant: "destructive",
      })
      return false
    } finally {
      setLoading(false)
    }
  }

  // Cancel subscription
  const cancelSubscription = async (): Promise<boolean> => {
    if (!user) return false

    setLoading(true)
    setError(null)

    try {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 1000))

      setUser({
        ...user,
        subscription_status: "canceled",
      })

      toast({
        title: "Success",
        description: "Subscription canceled successfully",
      })

      return true
    } catch (err: any) {
      console.error("Error canceling subscription:", err)
      setError("Failed to cancel subscription")
      toast({
        title: "Error",
        description: "Failed to cancel subscription",
        variant: "destructive",
      })
      return false
    } finally {
      setLoading(false)
    }
  }

  // Get subscription tiers
  const getSubscriptionTiers = async (): Promise<SubscriptionTier[]> => {
    // Mock subscription tiers
    return [
      {
        id: "tier-1",
        name: "Basic",
        price: 0,
        description: "Free tier with limited features",
        features: ["Create 1 event", "Up to 30 participants", "Basic analytics"],
        max_events: 1,
        max_participants: 30,
      },
      {
        id: "tier-2",
        name: "Pro",
        price: 29.99,
        description: "Professional tier with advanced features",
        features: ["Unlimited events", "Unlimited participants", "Advanced analytics", "Priority support"],
        max_events: null,
        max_participants: null,
      },
      {
        id: "tier-3",
        name: "Enterprise",
        price: 99.99,
        description: "Enterprise tier with all features",
        features: [
          "Unlimited events",
          "Unlimited participants",
          "Advanced analytics",
          "Priority support",
          "Custom branding",
          "API access",
        ],
        max_events: null,
        max_participants: null,
      },
    ]
  }

  // Check if user can create an event
  const canCreateEvent = (): boolean => {
    if (!user) return false

    // Admin and manager can always create events
    if (user.role === "admin" || user.role === "manager") return true

    // Paid users with active subscription can create unlimited events
    if (user.role === "paid" && user.subscription_status === "active") return true

    // Free users can create only 1 event
    if (user.role === "free") {
      return user.events_created < 1
    }

    return false
  }

  // Get participant limit based on user role
  const getParticipantLimit = (): number | null => {
    if (!user) return null

    // Admin, manager, and paid users have no participant limit
    if (
      user.role === "admin" ||
      user.role === "manager" ||
      (user.role === "paid" && user.subscription_status === "active")
    ) {
      return null
    }

    // Free users have a 30 participant limit
    return 30
  }

  // Check if user is admin (only "admin" role has full access)
  const isAdmin = (): boolean => {
    return user?.role === "admin"
  }

  // Check if user is manager
  const isManager = (): boolean => {
    return user?.role === "manager" || user?.role === "admin"
  }

  // Check if user is paid
  const isPaidUser = (): boolean => {
    return (
      (user?.role === "paid" && user?.subscription_status === "active") ||
      user?.role === "admin" ||
      user?.role === "manager"
    )
  }

  const refreshUser = async (): Promise<void> => {
    if (!user?.id) return

    try {
      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 500))

      // Just refresh with the same user data for demo purposes
      setUser({ ...user })
    } catch (err: any) {
      console.error("Error refreshing user data:", err)
      setError("Failed to refresh user data")
    }
  }

  const value = {
    user,
    loading,
    error,
    login,
    register,
    logout,
    updateProfile,
    upgradeSubscription,
    cancelSubscription,
    getSubscriptionTiers,
    canCreateEvent,
    getParticipantLimit,
    isAdmin,
    isManager,
    isPaidUser,
    refreshUser,
  }

  return <UserContext.Provider value={value}>{children}</UserContext.Provider>
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider")
  }
  return context
}
