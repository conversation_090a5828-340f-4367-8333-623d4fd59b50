# Event Registration & Certificate Generation Flow

This document provides comprehensive flowcharts for the event registration process, payment handling, and certificate generation workflow in the mTicket.my platform.

## Event Registration Process

The event registration process involves multiple steps from event discovery to ticket confirmation:

```mermaid
flowchart TD
    Start([User Discovers Event]) --> ViewEvent[View Event Details]
    ViewEvent --> CheckAuth{User Logged In?}
    CheckAuth -->|No| LoginPrompt[Prompt to Login/Register]
    LoginPrompt --> Login[User Logs In]
    Login --> ViewEvent
    
    CheckAuth -->|Yes| SelectTickets[Select Ticket Types & Quantities]
    SelectTickets --> ReviewOrder[Review Order Summary]
    ReviewOrder --> EnterDetails[Enter Attendee Details]
    EnterDetails --> ValidateForm{Form Valid?}
    ValidateForm -->|No| EnterDetails
    ValidateForm -->|Yes| CreateRegistration[Create Registration Record]
    
    CreateRegistration --> CheckPrice{Free Event?}
    CheckPrice -->|Yes| AutoConfirm[Auto-confirm Registration]
    CheckPrice -->|No| InitiatePayment[Initiate Payment Process]
    
    InitiatePayment --> SelectGateway[Select Payment Gateway]
    SelectGateway --> RedirectPayment[Redirect to Payment Gateway]
    RedirectPayment --> ProcessPayment[User Completes Payment]
    ProcessPayment --> PaymentResult{Payment Successful?}
    
    PaymentResult -->|No| PaymentFailed[Payment Failed]
    PaymentFailed --> RetryPayment{Retry Payment?}
    RetryPayment -->|Yes| SelectGateway
    RetryPayment -->|No| PendingPayment[Registration Pending Payment]
    
    PaymentResult -->|Yes| ConfirmPayment[Confirm Payment]
    ConfirmPayment --> AutoConfirm
    AutoConfirm --> GenerateTicket[Generate Digital Ticket]
    GenerateTicket --> SendConfirmation[Send Confirmation Email]
    SendConfirmation --> TicketReady[Ticket Ready for Use]
    
    PendingPayment --> PayLater[User Can Pay Later]
    PayLater --> SelectGateway
    
    style Start fill:#e1f5fe
    style TicketReady fill:#c8e6c9
    style PaymentFailed fill:#ffcdd2
    style PendingPayment fill:#fff3e0
```

## Payment Processing Workflow

Detailed payment processing flow with gateway integration:

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant PaymentFactory
    participant Gateway
    participant Database
    participant EmailService
    
    User->>Frontend: Click "Pay Now"
    Frontend->>API: POST /api/registrations/payment
    API->>Database: Validate registration
    API->>PaymentFactory: Create payment request
    PaymentFactory->>Gateway: Generate payment URL
    Gateway-->>PaymentFactory: Return payment details
    PaymentFactory-->>API: Payment URL & transaction ID
    API->>Database: Update status to 'processing'
    API-->>Frontend: Return payment URL
    Frontend->>User: Redirect to gateway
    
    User->>Gateway: Complete payment
    Gateway->>API: Webhook notification
    API->>Database: Update payment status
    Gateway->>Frontend: Redirect to success page
    
    Frontend->>API: Verify payment
    API->>PaymentFactory: Check payment status
    PaymentFactory->>Gateway: Verify transaction
    Gateway-->>PaymentFactory: Confirmation
    PaymentFactory-->>API: Payment confirmed
    API->>Database: Final status update
    API->>EmailService: Send confirmation email
    API-->>Frontend: Success response
    Frontend->>User: Show confirmation
```

## Certificate Generation Workflow

Certificate generation process for completed events:

```mermaid
flowchart TD
    EventEnd([Event Ends]) --> CheckAttendance{Check Attendance}
    CheckAttendance -->|Not Attended| NoEligible[Not Eligible for Certificate]
    CheckAttendance -->|Attended| EligibleCert[Eligible for Certificate]
    
    EligibleCert --> AdminTrigger{Admin Triggers Generation?}
    AdminTrigger -->|Manual| AdminSelect[Admin Selects Attendees]
    AdminTrigger -->|Bulk| BulkGenerate[Bulk Generate All]
    
    AdminSelect --> SelectTemplate[Select Certificate Template]
    BulkGenerate --> SelectTemplate
    
    SelectTemplate --> ValidateTemplate{Template Valid?}
    ValidateTemplate -->|No| TemplateError[Template Error]
    ValidateTemplate -->|Yes| GenerateFields[Generate Dynamic Fields]
    
    GenerateFields --> PopulateData[Populate Attendee Data]
    PopulateData --> CreatePDF[Create PDF Certificate]
    CreatePDF --> GenerateQR[Generate QR Verification Code]
    GenerateQR --> SaveCertificate[Save to Database]
    SaveCertificate --> UploadStorage[Upload to Storage]
    UploadStorage --> NotifyUser[Notify User via Email]
    NotifyUser --> CertificateReady[Certificate Ready]
    
    CertificateReady --> UserAccess[User Can Download/View]
    UserAccess --> QRVerify[QR Code Verification Available]
    
    TemplateError --> SelectTemplate
    NoEligible --> EventComplete[Event Complete - No Certificate]
    
    style EventEnd fill:#e1f5fe
    style CertificateReady fill:#c8e6c9
    style TemplateError fill:#ffcdd2
    style NoEligible fill:#f5f5f5
```

## QR Code Security Flow

Dynamic QR code generation and verification process:

```mermaid
flowchart TD
    UserRequest([User Requests Ticket QR]) --> CheckAuth{User Authenticated?}
    CheckAuth -->|No| AuthRequired[Authentication Required]
    CheckAuth -->|Yes| ValidateTicket{Valid Ticket?}
    
    ValidateTicket -->|No| InvalidTicket[Invalid Ticket Error]
    ValidateTicket -->|Yes| CheckStatus{Already Checked In?}
    
    CheckStatus -->|Yes| ShowStatic[Show Static QR Code]
    CheckStatus -->|No| GenerateToken[Generate Time-based Token]
    
    GenerateToken --> CreateHMAC[Create HMAC Signature]
    CreateHMAC --> SetExpiry[Set 30-second Expiry]
    SetExpiry --> GenerateQR[Generate Dynamic QR Code]
    GenerateQR --> DisplayQR[Display QR to User]
    
    DisplayQR --> AutoRefresh[Auto-refresh Every 30s]
    AutoRefresh --> CycleCheck{Refresh Cycles < 6?}
    CycleCheck -->|Yes| GenerateToken
    CycleCheck -->|No| ManualRefresh[Require Manual Refresh]
    
    ManualRefresh --> UserRefresh{User Refreshes?}
    UserRefresh -->|Yes| ResetCycles[Reset Cycle Counter]
    UserRefresh -->|No| ExpiredQR[QR Code Expired]
    ResetCycles --> GenerateToken
    
    %% Scanner Side
    ScannerScan([Scanner Scans QR]) --> ValidateToken{Valid Token?}
    ValidateToken -->|No| ScanError[Invalid QR Code]
    ValidateToken -->|Yes| CheckExpiry{Token Expired?}
    CheckExpiry -->|Yes| ExpiredError[Token Expired]
    CheckExpiry -->|No| VerifyHMAC{HMAC Valid?}
    VerifyHMAC -->|No| SecurityError[Security Violation]
    VerifyHMAC -->|Yes| CheckIn[Mark as Checked In]
    CheckIn --> ScanSuccess[Check-in Successful]
    
    AuthRequired --> UserRequest
    
    style UserRequest fill:#e1f5fe
    style ScanSuccess fill:#c8e6c9
    style InvalidTicket fill:#ffcdd2
    style SecurityError fill:#ff5722
    style ExpiredQR fill:#ff9800
```

## Key Features

### Registration Features
- **Multi-step Process**: Clear progression through ticket selection, review, and payment
- **Session Management**: Cart persistence across browser sessions
- **Payment Flexibility**: Multiple gateway options with retry capabilities
- **Real-time Validation**: Immediate feedback on form inputs and availability

### Payment Security
- **Gateway Agnostic**: Support for multiple payment providers
- **Webhook Verification**: Real-time payment status updates
- **Transaction Tracking**: Complete audit trail for all payments
- **Retry Mechanism**: Graceful handling of payment failures

### Certificate Security
- **Attendance Verification**: Only attended users receive certificates
- **Template Validation**: Ensures certificate integrity
- **QR Verification**: Tamper-proof certificate validation
- **Bulk Processing**: Efficient generation for large events

### QR Code Security
- **Time-based Tokens**: 30-second expiry prevents replay attacks
- **HMAC Signatures**: Cryptographic verification prevents forgery
- **Cycle Limits**: Automatic refresh with manual override for security
- **Real-time Validation**: Immediate verification at scanning

## Related Documentation

- [Authentication System](./authentication.md) - User authentication and authorization
- [Payment System](../architecture/payment-system.md) - Payment processing architecture
- [Certificate System](./certificate-system.md) - Certificate generation and verification
- [Database Schema](../architecture/database-schema.md) - Data model and relationships

---

**All workflows are production-ready and fully tested. The system handles edge cases and provides comprehensive error handling throughout the process.**
