import { NextResponse } from "next/server"
import { supabase } from "@/lib/supabase"

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get("code")

    if (!code) {
      return NextResponse.json({ error: "Verification code is required" }, { status: 400 })
    }

    // Extract verification code from certificate URL
    const verificationCode = code.toUpperCase()

    // Find certificate by verification code with comprehensive data
    const { data: certificate, error } = await supabase
      .from("certificates")
      .select(`
        *,
        events (
          title,
          start_date,
          end_date,
          location,
          organization_id,
          organizations (
            name
          )
        )
      `)
      .eq("verification_code", verificationCode)
      .single()

    console.log("Certificate query error:", error)
    console.log("Certificate data:", certificate)

    if (error || !certificate) {
      console.log("Certificate not found for code:", verificationCode)
      return NextResponse.json({ error: "Certificate not found" }, { status: 404 })
    }

    // Note: Registration details removed as they were not being used in the UI

    // Note: Timeline building removed - using audit trail component instead

    // Format response data to match the expected structure
    const verificationResult = {
      verified: true,
      certificate: {
        id: certificate.id,
        participant_name: certificate.participant_name || "Unknown Participant",
        issued_at: certificate.issued_at,
        verification_code: verificationCode,
        is_valid: !certificate.is_revoked,
      },
      event: {
        title: certificate.events?.title || "Unknown Event",
        start_date: certificate.events?.start_date || "",
        end_date: certificate.events?.end_date || "",
        location: certificate.events?.location || "",
      },
      organizer: certificate.events?.organizations?.name || "mTicket.my Platform"
    }

    // Update certificate verification tracking
    await supabase
      .from("certificates")
      .update({
        last_verified_at: new Date().toISOString(),
        verification_count: (certificate.verification_count || 0) + 1
      })
      .eq("id", certificate.id)

    // Note: We don't log verification attempts from unknown users to activity logs
    // Only track verification count and timestamp on the certificate record

    return NextResponse.json(verificationResult, { status: 200 })
  } catch (error) {
    console.error("Error verifying certificate:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
