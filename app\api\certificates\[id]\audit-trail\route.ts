import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

/**
 * GET /api/certificates/[id]/audit-trail
 *
 * Get complete audit trail for a certificate from registration to generation
 * Tracks: Registration → Payment → Attendance → Certificate Generation → Verification
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: certificateId } = await params

    // First, get certificate info to find related registration and event
    const { data: certificate, error: certError } = await supabase
      .from('certificates')
      .select(`
        id,
        registration_id,
        event_id,
        participant_name,
        issued_at,
        registrations (
          id,
          user_id,
          attendee_name,
          attendee_email,
          payment_status,
          created_at
        ),
        events (
          id,
          title,
          slug
        )
      `)
      .eq('id', certificateId)
      .single()

    if (certError || !certificate) {
      return NextResponse.json(
        { error: "Certificate not found" },
        { status: 404 }
      )
    }

    // Get complete audit trail using foreign key relationships
    const { data: auditTrail, error: auditError } = await supabase
      .from('activity_logs')
      .select(`
        id,
        created_at,
        action,
        category,
        details,
        user_id,
        entity_type,
        entity_id,
        registration_id,
        event_id,
        certificate_id,
        payment_id,
        users (
          id,
          full_name,
          email
        )
      `)
      .or(`registration_id.eq.${certificate.registration_id},certificate_id.eq.${certificateId},event_id.eq.${certificate.event_id}`)
      .order('created_at', { ascending: true })

    if (auditError) {
      console.error('Error fetching audit trail:', auditError)
      return NextResponse.json(
        { error: "Failed to fetch audit trail" },
        { status: 500 }
      )
    }

    // Process and categorize the audit trail
    const processedTrail = auditTrail.map(log => {
      // Determine timeline step
      let timelineStep = 'Other'
      let stepOrder = 99

      if (log.category === 'registration' || log.category === 'auth') {
        if (log.action.includes('register') || log.action.includes('created')) {
          timelineStep = '1. Registration'
          stepOrder = 1
        } else if (log.action.includes('confirmed')) {
          timelineStep = '1. Registration Confirmed'
          stepOrder = 1.5
        }
      } else if (log.category === 'payment') {
        if (log.action.includes('initiat') || log.action.includes('pending')) {
          timelineStep = '2. Payment Initiated'
          stepOrder = 2
        } else if (log.action.includes('complet') || log.action.includes('success')) {
          timelineStep = '2. Payment Completed'
          stepOrder = 3
        } else if (log.action.includes('fail')) {
          timelineStep = '2. Payment Failed'
          stepOrder = 2.5
        }
      } else if (log.category === 'event' || log.category === 'attendance') {
        if (log.action.includes('attendance') || log.action.includes('check')) {
          timelineStep = '3. Attendance Marked'
          stepOrder = 4
        }
      } else if (log.category === 'certificate') {
        if (log.action.includes('generat')) {
          timelineStep = '4. Certificate Generated'
          stepOrder = 5
        } else if (log.action.includes('download') || log.action.includes('sent') || log.action.includes('deliver')) {
          timelineStep = '5. Certificate Downloaded'
          stepOrder = 6
        }
      }

      return {
        ...log,
        timeline_step: timelineStep,
        step_order: stepOrder,
        performed_by: log.users?.full_name || 'System',
        performed_by_email: log.users?.email || null
      }
    })

    // Sort by step order and then by created_at
    processedTrail.sort((a, b) => {
      if (a.step_order !== b.step_order) {
        return a.step_order - b.step_order
      }
      return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    })

    // Create summary statistics
    const summary = {
      total_activities: processedTrail.length,
      registration_date: certificate.registrations?.created_at,
      certificate_issued_date: certificate.issued_at,
      participant_name: certificate.participant_name,
      event_title: certificate.events?.title,
      payment_status: certificate.registrations?.payment_status,
      timeline_steps: {
        registration: processedTrail.some(log => log.step_order === 1 || log.step_order === 1.5),
        payment_initiated: processedTrail.some(log => log.step_order === 2),
        payment_completed: processedTrail.some(log => log.step_order === 3),
        attendance_marked: processedTrail.some(log => log.step_order === 4),
        certificate_generated: processedTrail.some(log => log.step_order === 5),
        certificate_downloaded: processedTrail.some(log => log.step_order === 6)
      }
    }

    return NextResponse.json({
      success: true,
      certificate: {
        id: certificate.id,
        participant_name: certificate.participant_name,
        event_title: certificate.events?.title,
        registration_id: certificate.registration_id,
        event_id: certificate.event_id
      },
      summary,
      audit_trail: processedTrail
    })

  } catch (error) {
    console.error('Error in audit trail API:', error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

/**
 * Example response structure:
 * {
 *   "success": true,
 *   "certificate": {
 *     "id": "cert-123",
 *     "participant_name": "John Doe",
 *     "event_title": "Annual Conference 2024"
 *   },
 *   "summary": {
 *     "total_activities": 8,
 *     "registration_date": "2025-01-15T10:00:00Z",
 *     "certificate_issued_date": "2025-01-20T17:00:00Z",
 *     "timeline_steps": {
 *       "registration": true,
 *       "payment_completed": true,
 *       "attendance_marked": true,
 *       "certificate_generated": true
 *     }
 *   },
 *   "audit_trail": [
 *     {
 *       "created_at": "2025-01-15T10:00:00Z",
 *       "action": "register_event",
 *       "category": "registration",
 *       "timeline_step": "1. Registration",
 *       "performed_by": "John Doe",
 *       "details": { ... }
 *     },
 *     ...
 *   ]
 * }
 */
