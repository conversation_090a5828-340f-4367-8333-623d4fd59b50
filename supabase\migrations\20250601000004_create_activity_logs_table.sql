-- Alter the existing activity_logs table to match our new schema
-- First, check if the new columns exist and add them if they don't
DO $$
BEGIN
    -- Add entity_type column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'entity_type') THEN
        ALTER TABLE activity_logs ADD COLUMN entity_type TEXT;
    END IF;

    -- Add entity_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'entity_id') THEN
        ALTER TABLE activity_logs ADD COLUMN entity_id UUID;
    END IF;

    -- Add action column if it doesn't exist (rename action_type to action if it exists)
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'action_type') THEN
        ALTER TABLE activity_logs RENAME COLUMN action_type TO action;
    ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'action') THEN
        ALTER TABLE activity_logs ADD COLUMN action TEXT NOT NULL DEFAULT 'unknown';
    END IF;

    -- Add details column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'details') THEN
        ALTER TABLE activity_logs ADD COLUMN details JSONB DEFAULT '{}'::JSONB;

        -- If description column exists, migrate data to details
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'description') THEN
            UPDATE activity_logs SET details = jsonb_build_object('description', description);
        END IF;
    END IF;

    -- Add category column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'category') THEN
        ALTER TABLE activity_logs ADD COLUMN category TEXT;
    END IF;

    -- Add ip_address column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'ip_address') THEN
        ALTER TABLE activity_logs ADD COLUMN ip_address TEXT;
    END IF;

    -- Add user_agent column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'activity_logs' AND column_name = 'user_agent') THEN
        ALTER TABLE activity_logs ADD COLUMN user_agent TEXT;
    END IF;
END $$;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_entity_id ON activity_logs(entity_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_category ON activity_logs(category);
CREATE INDEX IF NOT EXISTS idx_activity_logs_created_at ON activity_logs(created_at);

-- Enable Row Level Security
ALTER TABLE activity_logs ENABLE ROW LEVEL SECURITY;

-- Create policies for activity_logs
-- Only admins can view all logs
CREATE POLICY "Enable admin access to all logs"
ON activity_logs
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM users
    WHERE users.id = auth.uid()
    AND users.role = 'admin'
  )
);

-- Users can view their own logs
CREATE POLICY "Enable users to view their own logs"
ON activity_logs
FOR SELECT
USING (user_id = auth.uid());
