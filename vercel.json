{"env": {"NEXT_PUBLIC_SUPABASE_URL": "https://bivslxeghhebmkelieue.supabase.co", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJpdnNseGVnaGhlYm1rZWxpZXVlIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3MTA2MTgsImV4cCI6MjA2MjI4NjYxOH0.cn_GEpn-C6EqbGsJltkRt3Os5GWa4eGDScOhlx9iBEQ", "SUPABASE_SERVICE_ROLE_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJpdnNseGVnaGhlYm1rZWxpZXVlIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjcxMDYxOCwiZXhwIjoyMDYyMjg2NjE4fQ.vSk3dgzH7_URvzBVZfmfiuGilM32k3BqxPygo-QQ8ms", "DATABASE_URL": "postgres://postgres.bivslxeghhebmkelieue:<EMAIL>:6543/postgres?sslmode=require&supa=base-pooler.x", "NEXT_PUBLIC_APP_URL": "https://mticket.my", "NEXTAUTH_URL": "https://mticket.my", "NEXTAUTH_SECRET": "MU4nK6vFzqekKt9sXvP0JGTlLyL126nVecQcT0TCNxI=", "JWT_SECRET": "MU4nK6vFzqekKt9sXvP0JGTlLyL126nVecQcT0TCNxI=", "COOKIE_DOMAIN": "mticket.my", "NODE_ENV": "production"}, "build": {"env": {"NEXT_PUBLIC_APP_URL": "https://mticket.my", "NEXTAUTH_URL": "https://mticket.my", "COOKIE_DOMAIN": "mticket.my", "NODE_ENV": "production"}}}