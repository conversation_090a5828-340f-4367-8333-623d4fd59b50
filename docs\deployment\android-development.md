# Android App Development Guide for mTicket.my

## Overview

This document provides comprehensive guidance for developing an Android mobile application for mTicket.my that enables users to:
- <PERSON><PERSON> securely using existing credentials
- View their purchased tickets
- Display QR codes for easy scanning at events
- Access ticket details and event information

## Architecture Overview

### Technology Stack Recommendations
- **Language**: Ko<PERSON>in (preferred) or Java
- **Architecture**: MVVM (Model-View-ViewModel) with Repository pattern
- **Networking**: Retrofit 2 + OkHttp for API communication
- **Authentication**: JWT token-based authentication
- **Local Storage**: Room Database + SharedPreferences
- **QR Code**: ZXing library for QR code generation and display
- **Image Loading**: Glide or Picasso for event images
- **UI Framework**: Jetpack Compose (modern) or traditional XML layouts

### App Architecture Components
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Presentation  │    │    Domain       │    │      Data       │
│   (UI Layer)    │◄──►│   (Business)    │◄──►│   (Repository)  │
│                 │    │                 │    │                 │
│ • Activities    │    │ • Use Cases     │    │ • API Service   │
│ • Fragments     │    │ • Models        │    │ • Local DB      │
│ • ViewModels    │    │ • Repositories  │    │ • Preferences   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## API Integration

### Base Configuration
```kotlin
// API Base URL
const val BASE_URL = "https://your-domain.com/api/"

// API Endpoints for Mobile App
object ApiEndpoints {
    const val LOGIN = "auth/login"
    const val REGISTER = "auth/register"
    const val USER_TICKETS = "dashboard/tickets"
    const val SECURE_QR = "tickets/secure-qr"
    const val VERIFY_TOKEN = "auth/verify"
}
```

### Authentication Flow

#### 1. Login API Integration
```kotlin
// Login Request Model
data class LoginRequest(
    val email: String,
    val password: String
)

// Login Response Model
data class LoginResponse(
    val token: String,
    val user: User,
    val role: String
)

// User Model
data class User(
    val id: String,
    val email: String,
    val full_name: String,
    val role_id: String,
    val profile_image_url: String?,
    val phone: String?,
    val organization_id: String?
)
```

#### 2. JWT Token Management
```kotlin
class TokenManager(private val sharedPreferences: SharedPreferences) {
    
    fun saveToken(token: String) {
        sharedPreferences.edit()
            .putString("jwt_token", token)
            .apply()
    }
    
    fun getToken(): String? {
        return sharedPreferences.getString("jwt_token", null)
    }
    
    fun clearToken() {
        sharedPreferences.edit()
            .remove("jwt_token")
            .apply()
    }
    
    fun isTokenValid(): Boolean {
        val token = getToken()
        return token != null && !isTokenExpired(token)
    }
    
    private fun isTokenExpired(token: String): Boolean {
        // Implement JWT token expiry check
        // Parse JWT and check exp claim
        return false
    }
}
```

### Ticket Management

#### 1. Fetch User Tickets
```kotlin
// Ticket Response Model
data class TicketResponse(
    val id: String,
    val event_id: String,
    val attendee_name: String,
    val attendee_email: String,
    val ticket_type: String,
    val payment_status: String,
    val status: String,
    val checked_in: Boolean,
    val checked_in_at: String?,
    val created_at: String,
    val event: EventDetails
)

data class EventDetails(
    val id: String,
    val title: String,
    val slug: String,
    val start_date: String,
    val end_date: String,
    val location: String,
    val image_url: String?,
    val organizer: OrganizerDetails
)

data class OrganizerDetails(
    val name: String,
    val email: String
)
```

#### 2. API Service Interface
```kotlin
interface ApiService {
    
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequest): Response<LoginResponse>
    
    @GET("dashboard/tickets")
    suspend fun getUserTickets(
        @Header("Authorization") token: String
    ): Response<List<TicketResponse>>
    
    @POST("tickets/secure-qr")
    suspend fun generateSecureQR(
        @Header("Authorization") token: String,
        @Body ticketData: SecureQRRequest
    ): Response<SecureQRResponse>
    
    @GET("auth/verify")
    suspend fun verifyToken(
        @Header("Authorization") token: String
    ): Response<TokenVerificationResponse>
}
```

### Secure QR Code Generation

#### 1. QR Code Request/Response Models
```kotlin
data class SecureQRRequest(
    val ticketData: TicketData
)

data class TicketData(
    val id: String,
    val event_id: String,
    val guest_name: String
)

data class SecureQRResponse(
    val success: Boolean,
    val qrData: String,
    val timestamp: String
)
```

#### 2. QR Code Generation Implementation
```kotlin
class QRCodeGenerator {
    
    fun generateQRCode(data: String, size: Int = 512): Bitmap? {
        return try {
            val writer = QRCodeWriter()
            val bitMatrix = writer.encode(data, BarcodeFormat.QR_CODE, size, size)
            val width = bitMatrix.width
            val height = bitMatrix.height
            val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565)
            
            for (x in 0 until width) {
                for (y in 0 until height) {
                    bitmap.setPixel(x, y, if (bitMatrix[x, y]) Color.BLACK else Color.WHITE)
                }
            }
            bitmap
        } catch (e: Exception) {
            null
        }
    }
}
```

## Local Data Storage

### Room Database Schema
```kotlin
@Entity(tableName = "tickets")
data class TicketEntity(
    @PrimaryKey val id: String,
    val event_id: String,
    val attendee_name: String,
    val attendee_email: String,
    val ticket_type: String,
    val payment_status: String,
    val status: String,
    val checked_in: Boolean,
    val checked_in_at: String?,
    val created_at: String,
    val event_title: String,
    val event_start_date: String,
    val event_end_date: String,
    val event_location: String,
    val event_image_url: String?,
    val organizer_name: String,
    val last_synced: Long = System.currentTimeMillis()
)

@Dao
interface TicketDao {
    @Query("SELECT * FROM tickets ORDER BY event_start_date DESC")
    suspend fun getAllTickets(): List<TicketEntity>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTickets(tickets: List<TicketEntity>)
    
    @Query("DELETE FROM tickets")
    suspend fun clearAllTickets()
    
    @Query("SELECT * FROM tickets WHERE id = :ticketId")
    suspend fun getTicketById(ticketId: String): TicketEntity?
}
```

## Security Considerations

### 1. Token Security
- Store JWT tokens securely using Android Keystore
- Implement token refresh mechanism
- Clear tokens on app uninstall/logout

### 2. Network Security
- Use HTTPS for all API communications
- Implement certificate pinning
- Add request/response encryption for sensitive data

### 3. QR Code Security
- Generate time-based secure QR codes (30-second expiry)
- Implement replay protection
- Use HMAC-signed tokens

```kotlin
class SecurityManager {
    
    fun encryptToken(token: String): String {
        // Implement encryption using Android Keystore
        return token
    }
    
    fun decryptToken(encryptedToken: String): String {
        // Implement decryption using Android Keystore
        return encryptedToken
    }
    
    fun generateSecureQRData(ticketId: String, eventId: String): String {
        val timestamp = System.currentTimeMillis()
        val data = "$ticketId:$eventId:$timestamp"
        return signData(data)
    }
    
    private fun signData(data: String): String {
        // Implement HMAC signing
        return data
    }
}
```

## User Interface Design

### 1. Main Screens
- **Login Screen**: Email/password authentication
- **Ticket List Screen**: Display all user tickets
- **Ticket Detail Screen**: Show individual ticket with QR code
- **Profile Screen**: User information and settings

### 2. Ticket List UI Components
```kotlin
@Composable
fun TicketListScreen(
    tickets: List<TicketResponse>,
    onTicketClick: (String) -> Unit
) {
    LazyColumn {
        items(tickets) { ticket ->
            TicketCard(
                ticket = ticket,
                onClick = { onTicketClick(ticket.id) }
            )
        }
    }
}

@Composable
fun TicketCard(
    ticket: TicketResponse,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(8.dp)
            .clickable { onClick() },
        elevation = 4.dp
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = ticket.event.title,
                style = MaterialTheme.typography.h6
            )
            Text(
                text = "Attendee: ${ticket.attendee_name}",
                style = MaterialTheme.typography.body2
            )
            Text(
                text = "Date: ${formatDate(ticket.event.start_date)}",
                style = MaterialTheme.typography.body2
            )
            Text(
                text = "Status: ${ticket.status}",
                style = MaterialTheme.typography.body2,
                color = getStatusColor(ticket.status)
            )
        }
    }
}
```

### 3. QR Code Display
```kotlin
@Composable
fun QRCodeDisplay(
    qrData: String,
    ticketInfo: TicketResponse
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = ticketInfo.event.title,
            style = MaterialTheme.typography.h5,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // QR Code Image
        val qrBitmap = remember(qrData) {
            QRCodeGenerator().generateQRCode(qrData)
        }
        
        qrBitmap?.let { bitmap ->
            Image(
                bitmap = bitmap.asImageBitmap(),
                contentDescription = "Ticket QR Code",
                modifier = Modifier.size(300.dp)
            )
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "Show this QR code at the event entrance",
            style = MaterialTheme.typography.body1,
            textAlign = TextAlign.Center
        )
        
        // Ticket Details
        TicketDetailsCard(ticket = ticketInfo)
    }
}
```

## Implementation Phases

### Phase 1: Core Authentication (Week 1-2)
- [ ] Set up project structure and dependencies
- [ ] Implement login/logout functionality
- [ ] JWT token management
- [ ] Basic navigation between screens

### Phase 2: Ticket Management (Week 3-4)
- [ ] Fetch and display user tickets
- [ ] Local data caching with Room
- [ ] Pull-to-refresh functionality
- [ ] Offline support for cached tickets

### Phase 3: QR Code Integration (Week 5-6)
- [ ] Secure QR code generation
- [ ] QR code display with ticket details
- [ ] Auto-refresh QR codes (30-second intervals)
- [ ] Error handling and retry mechanisms

### Phase 4: Polish & Testing (Week 7-8)
- [ ] UI/UX improvements
- [ ] Performance optimization
- [ ] Security testing
- [ ] Beta testing and bug fixes

## Testing Strategy

### 1. Unit Tests
- API service methods
- Token management
- QR code generation
- Data models and transformations

### 2. Integration Tests
- API integration with mock server
- Database operations
- Authentication flow

### 3. UI Tests
- Login flow
- Ticket list navigation
- QR code display

## Deployment Considerations

### 1. Build Configuration
```gradle
android {
    compileSdk 34
    
    defaultConfig {
        applicationId "com.mticket.mobile"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "1.0.0"
    }
    
    buildTypes {
        debug {
            buildConfigField "String", "API_BASE_URL", "\"https://staging-api.mticket.my/\""
            buildConfigField "String", "API_VERSION", "\"v1\""
        }
        release {
            buildConfigField "String", "API_BASE_URL", "\"https://api.mticket.my/\""
            buildConfigField "String", "API_VERSION", "\"v1\""
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}
```

### 2. Security Configuration
- Enable R8 code obfuscation
- Implement certificate pinning
- Add ProGuard rules for API models
- Use encrypted SharedPreferences

### 3. Performance Optimization
- Implement image caching for event images
- Use pagination for large ticket lists
- Optimize QR code generation
- Implement proper memory management

## API Endpoints Summary

The following existing API endpoints will be used by the mobile app:

| Endpoint | Method | Purpose | Authentication |
|----------|--------|---------|----------------|
| `/api/auth/login` | POST | User authentication | Public |
| `/api/dashboard/tickets` | GET | Fetch user tickets | JWT Required |
| `/api/tickets/secure-qr` | POST | Generate secure QR | JWT Required |
| `/api/auth/verify` | GET | Verify token validity | JWT Required |

## Next Steps

1. **Review and approve this documentation**
2. **Set up development environment**
3. **Create detailed wireframes and UI mockups**
4. **Begin Phase 1 implementation**
5. **Set up CI/CD pipeline for automated testing**

For questions or clarifications, refer to the main API documentation at `docs/api/README.md` and the database schema at `docs/database-schema.md`.
