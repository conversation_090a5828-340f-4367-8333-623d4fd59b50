"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { CalendarIcon, Download, Printer, Search } from "lucide-react"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { cn } from "@/lib/utils"

export default function ReportsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [reportType, setReportType] = useState("events")
  const [dateRange, setDateRange] = useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })

  // Mock data for events report
  const eventsData = [
    {
      id: "evt-001",
      title: "Tech Conference 2023",
      date: "2023-12-15",
      registrations: 120,
      attendance: 98,
      revenue: 11980.0,
    },
    {
      id: "evt-002",
      title: "Digital Marketing Workshop",
      date: "2023-11-25",
      registrations: 45,
      attendance: 40,
      revenue: 4500.0,
    },
    {
      id: "evt-003",
      title: "Startup Networking Event",
      date: "2023-12-05",
      registrations: 75,
      attendance: 62,
      revenue: 3750.0,
    },
    {
      id: "evt-004",
      title: "AI & Machine Learning Summit",
      date: "2024-01-10",
      registrations: 150,
      attendance: 0, // Future event
      revenue: 15000.0,
    },
    {
      id: "evt-005",
      title: "Web Development Bootcamp",
      date: "2023-12-20",
      registrations: 30,
      attendance: 0, // Future event
      revenue: 4500.0,
    },
  ]

  // Mock data for registrations report
  const registrationsData = [
    {
      id: "reg-001",
      attendee: "John Smith",
      email: "<EMAIL>",
      event: "Tech Conference 2023",
      date: "2023-11-10",
      status: "Confirmed",
      payment: "Paid",
      amount: 99.0,
    },
    {
      id: "reg-002",
      attendee: "Sarah Johnson",
      email: "<EMAIL>",
      event: "Digital Marketing Workshop",
      date: "2023-11-05",
      status: "Confirmed",
      payment: "Paid",
      amount: 100.0,
    },
    {
      id: "reg-003",
      attendee: "Michael Brown",
      email: "<EMAIL>",
      event: "Startup Networking Event",
      date: "2023-11-15",
      status: "Pending",
      payment: "Unpaid",
      amount: 50.0,
    },
    {
      id: "reg-004",
      attendee: "Emily Davis",
      email: "<EMAIL>",
      event: "AI & Machine Learning Summit",
      date: "2023-11-20",
      status: "Confirmed",
      payment: "Paid",
      amount: 100.0,
    },
    {
      id: "reg-005",
      attendee: "David Wilson",
      email: "<EMAIL>",
      event: "Web Development Bootcamp",
      date: "2023-11-18",
      status: "Confirmed",
      payment: "Paid",
      amount: 150.0,
    },
  ]

  // Mock data for revenue report
  const revenueData = [
    {
      id: "rev-001",
      date: "2023-11-01",
      source: "Tech Conference 2023",
      type: "Registration Fees",
      amount: 5000.0,
    },
    {
      id: "rev-002",
      date: "2023-11-05",
      source: "Digital Marketing Workshop",
      type: "Registration Fees",
      amount: 2500.0,
    },
    {
      id: "rev-003",
      date: "2023-11-10",
      source: "Startup Networking Event",
      type: "Registration Fees",
      amount: 1500.0,
    },
    {
      id: "rev-004",
      date: "2023-11-15",
      source: "AI & Machine Learning Summit",
      type: "Registration Fees",
      amount: 7500.0,
    },
    {
      id: "rev-005",
      date: "2023-11-20",
      source: "Web Development Bootcamp",
      type: "Registration Fees",
      amount: 3000.0,
    },
  ]

  // Filter events data based on search query
  const filteredEventsData = eventsData.filter((event) => event.title.toLowerCase().includes(searchQuery.toLowerCase()))

  // Filter registrations data based on search query
  const filteredRegistrationsData = registrationsData.filter(
    (registration) =>
      registration.attendee.toLowerCase().includes(searchQuery.toLowerCase()) ||
      registration.event.toLowerCase().includes(searchQuery.toLowerCase()) ||
      registration.email.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  // Filter revenue data based on search query
  const filteredRevenueData = revenueData.filter(
    (revenue) =>
      revenue.source.toLowerCase().includes(searchQuery.toLowerCase()) ||
      revenue.type.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  // Handle export report
  const handleExportReport = () => {
    alert("Report exported successfully!")
  }

  // Handle print report
  const handlePrintReport = () => {
    window.print()
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Reports</h1>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="gap-2" onClick={handleExportReport}>
            <Download className="h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" size="sm" className="gap-2" onClick={handlePrintReport}>
            <Printer className="h-4 w-4" />
            Print
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Generate Reports</CardTitle>
          <CardDescription>View and export reports for your events and registrations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="grid w-full items-center gap-1.5">
              <Label htmlFor="report-type">Report Type</Label>
              <Select value={reportType} onValueChange={setReportType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select report type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="events">Events Report</SelectItem>
                  <SelectItem value="registrations">Registrations Report</SelectItem>
                  <SelectItem value="revenue">Revenue Report</SelectItem>
                  <SelectItem value="attendance">Attendance Report</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid w-full items-center gap-1.5">
              <Label>Date Range</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !dateRange.from && "text-muted-foreground",
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.from ? (
                      dateRange.to ? (
                        <>
                          {format(dateRange.from, "LLL dd, y")} - {format(dateRange.to, "LLL dd, y")}
                        </>
                      ) : (
                        format(dateRange.from, "LLL dd, y")
                      )
                    ) : (
                      <span>Select date range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    initialFocus
                    mode="range"
                    defaultMonth={dateRange.from}
                    selected={dateRange}
                    onSelect={setDateRange}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="grid w-full items-center gap-1.5">
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  type="search"
                  placeholder="Search reports..."
                  className="pl-8"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>
          </div>

          <Tabs defaultValue="events" value={reportType} onValueChange={setReportType}>
            <TabsList className="mb-4">
              <TabsTrigger value="events">Events</TabsTrigger>
              <TabsTrigger value="registrations">Registrations</TabsTrigger>
              <TabsTrigger value="revenue">Revenue</TabsTrigger>
              <TabsTrigger value="attendance">Attendance</TabsTrigger>
            </TabsList>

            <TabsContent value="events">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Event Name</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Registrations</TableHead>
                      <TableHead>Attendance</TableHead>
                      <TableHead>Revenue (RM)</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredEventsData.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="h-24 text-center">
                          No events found
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredEventsData.map((event) => (
                        <TableRow key={event.id}>
                          <TableCell className="font-medium">{event.title}</TableCell>
                          <TableCell>{new Date(event.date).toLocaleDateString()}</TableCell>
                          <TableCell>{event.registrations}</TableCell>
                          <TableCell>
                            {event.attendance > 0
                              ? `${event.attendance} (${Math.round((event.attendance / event.registrations) * 100)}%)`
                              : "N/A"}
                          </TableCell>
                          <TableCell>{event.revenue.toFixed(2)}</TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="registrations">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Attendee</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Event</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Payment</TableHead>
                      <TableHead>Amount (RM)</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRegistrationsData.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="h-24 text-center">
                          No registrations found
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredRegistrationsData.map((registration) => (
                        <TableRow key={registration.id}>
                          <TableCell className="font-medium">{registration.attendee}</TableCell>
                          <TableCell>{registration.email}</TableCell>
                          <TableCell>{registration.event}</TableCell>
                          <TableCell>{new Date(registration.date).toLocaleDateString()}</TableCell>
                          <TableCell>
                            <span
                              className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                registration.status === "Confirmed"
                                  ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                                  : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300"
                              }`}
                            >
                              {registration.status}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span
                              className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                registration.payment === "Paid"
                                  ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300"
                                  : "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300"
                              }`}
                            >
                              {registration.payment}
                            </span>
                          </TableCell>
                          <TableCell>{registration.amount.toFixed(2)}</TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="revenue">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Date</TableHead>
                      <TableHead>Source</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Amount (RM)</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRevenueData.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} className="h-24 text-center">
                          No revenue data found
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredRevenueData.map((revenue) => (
                        <TableRow key={revenue.id}>
                          <TableCell>{new Date(revenue.date).toLocaleDateString()}</TableCell>
                          <TableCell className="font-medium">{revenue.source}</TableCell>
                          <TableCell>{revenue.type}</TableCell>
                          <TableCell>{revenue.amount.toFixed(2)}</TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="attendance">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Event Name</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Registrations</TableHead>
                      <TableHead>Attendance</TableHead>
                      <TableHead>Attendance Rate</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredEventsData.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={5} className="h-24 text-center">
                          No events found
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredEventsData
                        .filter((event) => new Date(event.date) <= new Date()) // Only past events
                        .map((event) => (
                          <TableRow key={event.id}>
                            <TableCell className="font-medium">{event.title}</TableCell>
                            <TableCell>{new Date(event.date).toLocaleDateString()}</TableCell>
                            <TableCell>{event.registrations}</TableCell>
                            <TableCell>{event.attendance}</TableCell>
                            <TableCell>
                              {event.attendance > 0
                                ? `${Math.round((event.attendance / event.registrations) * 100)}%`
                                : "N/A"}
                            </TableCell>
                          </TableRow>
                        ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
