import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { logActivity, ActivityCategory } from "@/utils/activity-logger";

/**
 * Handles incoming webhooks from external services
 * This endpoint is public and does not require API key authentication
 */
export async function POST(request: NextRequest) {
  try {
    // Get the webhook ID from the URL
    const { searchParams } = new URL(request.url);
    const webhookId = searchParams.get("id");

    if (!webhookId) {
      return NextResponse.json(
        { error: "Webhook ID is required" },
        { status: 400 }
      );
    }

    // Get the webhook from the database
    const { data: webhook, error } = await supabase
      .from("webhooks")
      .select("*")
      .eq("id", webhookId)
      .single();

    if (error || !webhook) {
      console.error("Webhook not found:", error);
      return NextResponse.json(
        { error: "Webhook not found" },
        { status: 404 }
      );
    }

    // Check if the webhook is active
    if (!webhook.active) {
      return NextResponse.json(
        { error: "Webhook is not active" },
        { status: 403 }
      );
    }

    // Parse the request body
    const payload = await request.json();

    // Log the webhook event
    await logActivity({
      action: "webhook_received",
      entityType: "webhook",
      category: ActivityCategory.SYSTEM,
      details: {
        webhook_id: webhookId,
        webhook_name: webhook.name,
        payload,
        timestamp: new Date().toISOString(),
      },
    });

    // Update the webhook stats
    await supabase
      .from("webhooks")
      .update({
        last_triggered_at: new Date().toISOString(),
        success_count: (webhook.success_count || 0) + 1,
      })
      .eq("id", webhookId);

    return NextResponse.json(
      {
        message: "Webhook received successfully",
        webhook_id: webhookId,
        webhook_name: webhook.name,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error processing incoming webhook:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
