"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Check, CreditCard, Star } from "lucide-react"
import { PageLayout } from "@/components/page-layout"
import { HeroSection } from "@/components/hero-section"
import SubscriptionPlanCard from "@/components/subscription-plan-card"
import { useToast } from "@/hooks/use-toast"

type SubscriptionPlan = {
  id: string
  name: string
  price: number
  description: string
  features: string[]
  is_popular?: boolean
  max_events?: number | null
  max_attendees_per_event?: number | null
  // Feature toggles
  certificates_enabled?: boolean
  attendance_enabled?: boolean
  webhooks_enabled?: boolean
  analytics_enabled?: boolean
  reports_enabled?: boolean
}



export default function PricingPage() {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([])
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()

  // Set document title
  useEffect(() => {
    document.title = "Pricing | mTicket.my - Event Management Platform"
  }, [])

  useEffect(() => {
    const fetchPlans = async () => {
      setLoading(true)
      try {
        // Fetch plans from the existing public API endpoint
        const response = await fetch('/api/subscriptions')

        if (!response.ok) {
          throw new Error('Failed to fetch subscription plans')
        }

        const data = await response.json()
        setPlans(data.plans || [])
      } catch (error) {
        console.error("Error fetching subscription plans:", error)
        toast({
          title: "Error",
          description: "Failed to load pricing plans. Please try again later.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchPlans()
  }, [toast])

  if (loading) {
    return (
      <PageLayout showCTA={false}>
        {/* Corporate Hero Section */}
        <section className="relative w-full py-20 md:py-32 lg:py-40 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-20">
            <div
              className="w-full h-full"
              style={{
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
              }}
            ></div>
          </div>

          <div className="container relative px-4 md:px-6">
            <div className="flex flex-col items-center space-y-8 text-center">
              {/* Main Heading */}
              <div className="space-y-4 max-w-4xl">
                <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl lg:text-7xl">
                  Choose Your
                  <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> Plan</span>
                </h1>
                <p className="mx-auto max-w-3xl text-xl text-gray-300 md:text-2xl leading-relaxed">
                  Whether you're organizing small workshops or large conferences, we have a plan
                  that fits your needs and budget.
                </p>
              </div>
            </div>
          </div>
        </section>

        <div className="container mx-auto py-12 px-4">
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            {[1, 2, 3, 4].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader className="space-y-2">
                  <div className="h-6 w-24 bg-muted rounded"></div>
                  <div className="h-8 w-32 bg-muted rounded"></div>
                  <div className="h-4 w-full bg-muted rounded"></div>
                </CardHeader>
                <CardContent className="space-y-2">
                  {[1, 2, 3, 4, 5].map((j) => (
                    <div key={j} className="h-4 w-full bg-muted rounded"></div>
                  ))}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </PageLayout>
    )
  }

  return (
    <PageLayout>
      {/* Corporate Hero Section */}
      <section className="relative w-full py-20 md:py-32 lg:py-40 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div
            className="w-full h-full"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}
          ></div>
        </div>

        <div className="container relative px-4 md:px-6">
          <div className="flex flex-col items-center space-y-8 text-center">
            {/* Main Heading */}
            <div className="space-y-4 max-w-4xl">
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl lg:text-7xl">
                Choose Your
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> Plan</span>
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-gray-300 md:text-2xl leading-relaxed">
                Whether you're organizing small workshops or large conferences, we have a plan
                that fits your needs and budget.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Plans Section */}
      <div className="container mx-auto py-12 px-4">
        <div className="max-w-7xl mx-auto">
          {/* Plans Grid */}
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4 mb-12">
            {plans.map((plan) => (
              <SubscriptionPlanCard key={plan.id} plan={plan} />
            ))}
          </div>

          {/* Features Comparison */}
          <div className="mt-16">
            <h3 className="text-2xl font-bold text-center mb-8">Why Choose mTicket.my?</h3>
            <div className="grid gap-6 md:grid-cols-3 mb-12">
              <div className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Star className="h-6 w-6 text-primary" />
                </div>
                <h4 className="font-semibold mb-2">Easy to Use</h4>
                <p className="text-muted-foreground text-sm">
                  Intuitive interface that makes event management simple and efficient.
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <CreditCard className="h-6 w-6 text-primary" />
                </div>
                <h4 className="font-semibold mb-2">Flexible Pricing</h4>
                <p className="text-muted-foreground text-sm">
                  Plans that scale with your needs, from free to enterprise-level features.
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Check className="h-6 w-6 text-primary" />
                </div>
                <h4 className="font-semibold mb-2">Reliable Support</h4>
                <p className="text-muted-foreground text-sm">
                  24/7 customer support to help you succeed with your events.
                </p>
              </div>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="mt-16">
            <h3 className="text-2xl font-bold text-center mb-8">Frequently Asked Questions</h3>
            <div className="max-w-3xl mx-auto space-y-6">
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <h4 className="font-semibold mb-2">Can I change my plan anytime?</h4>
                <p className="text-muted-foreground">
                  Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.
                </p>
              </div>
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <h4 className="font-semibold mb-2">What payment methods do you accept?</h4>
                <p className="text-muted-foreground">
                  We accept all major credit cards, PayPal, and bank transfers for annual subscriptions.
                </p>
              </div>
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <h4 className="font-semibold mb-2">Is there a free trial?</h4>
                <p className="text-muted-foreground">
                  Yes! Our Free plan allows you to get started immediately with basic features. No credit card required.
                </p>
              </div>
              <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                <h4 className="font-semibold mb-2">Can I cancel my subscription?</h4>
                <p className="text-muted-foreground">
                  Absolutely. You can cancel your subscription at any time from your dashboard. Your plan will remain active until the end of your billing period.
                </p>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <h3 className="text-2xl font-bold mb-4">Ready to Get Started?</h3>
            <p className="text-lg text-muted-foreground mb-8">
              Join thousands of event organizers who trust mTicket.my for their event management needs.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/auth/register"
                className="inline-flex items-center justify-center rounded-md bg-primary px-8 py-3 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
              >
                Start Free Trial
              </a>
              <a
                href="/contact"
                className="inline-flex items-center justify-center rounded-md border border-input bg-background px-8 py-3 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
              >
                Contact Sales
              </a>
            </div>
          </div>
        </div>
      </div>
    </PageLayout>
  )
}
