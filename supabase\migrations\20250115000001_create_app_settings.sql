-- Create app_settings table for admin configuration
CREATE TABLE IF NOT EXISTS app_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  login_enabled BOOLEAN DEFAULT true,
  register_enabled BOOLEAN DEFAULT true,
  password_reset_enabled BOOLEAN DEFAULT true,
  api_enabled BOOLEAN DEFAULT true,
  maintenance_mode BOOLEAN DEFAULT false,
  maintenance_message TEXT DEFAULT 'We are currently performing maintenance. We will be back soon!',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_by UUID REFERENCES users(id)
);

-- Insert default settings
INSERT INTO app_settings (
  login_enabled,
  register_enabled,
  password_reset_enabled,
  api_enabled,
  maintenance_mode,
  maintenance_message
) VALUES (
  true,
  true,
  true,
  true,
  false,
  'We are currently performing maintenance. We will be back soon!'
) ON CONFLICT DO NOTHING;

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_app_settings_updated_at ON app_settings;
CREATE TRIGGER update_app_settings_updated_at
  BEFORE UPDATE ON app_settings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS
ALTER TABLE app_settings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Admin users can read and update app settings
CREATE POLICY "Admin users can read app settings" ON app_settings
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role_name = 'admin'
    )
  );

CREATE POLICY "Admin users can update app settings" ON app_settings
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role_name = 'admin'
    )
  );

-- Allow public read access for maintenance mode check (specific fields only)
CREATE POLICY "Public can check maintenance mode" ON app_settings
  FOR SELECT USING (true);
