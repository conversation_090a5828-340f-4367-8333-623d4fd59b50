import { Metada<PERSON> } from "next"
import { Clock, Wrench } from "lucide-react"
import { getMaintenanceStatus } from "@/lib/app-settings"

export const metadata: Metadata = {
  title: "Maintenance | mTicket.my",
  description: "mTicket.my is currently under maintenance. We'll be back soon!",
}

export default async function MaintenancePage() {
  // Fetch maintenance message securely from server-side
  let message = "We are currently performing maintenance. We will be back soon!"

  try {
    const maintenanceStatus = await getMaintenanceStatus()
    if (maintenanceStatus.message) {
      // Message is already sanitized by getMaintenanceStatus()
      message = maintenanceStatus.message
    }
  } catch (error) {
    // Use default message if fetch fails
  }

  return (
    <div className="flex min-h-screen flex-col">
      {/* Header */}
      <header className="w-full border-b bg-background">
        <div className="container flex h-16 items-center px-4 md:px-6">
          <span className="text-xl font-bold text-purple-600">mTicket.my</span>
        </div>
      </header>

      {/* Main Content - Using same hero section style as main page */}
      <main className="flex-1">
        <section className="relative w-full py-20 md:py-32 lg:py-40 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-20">
            <div className="w-full h-full" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
            }}></div>
          </div>

          <div className="container relative px-4 md:px-6">
            <div className="flex flex-col items-center space-y-8 text-center">
              {/* Icon */}
              <div className="flex items-center justify-center w-24 h-24 rounded-full bg-purple-600/20 backdrop-blur-sm border border-purple-400/30">
                <Wrench className="w-12 h-12 text-purple-400" />
              </div>

              {/* Main Heading */}
              <div className="space-y-4 max-w-4xl">
                <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl lg:text-7xl">
                  Under
                  <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> Maintenance</span>
                </h1>
                <p className="mx-auto max-w-3xl text-xl text-gray-300 md:text-2xl leading-relaxed">
                  {message}
                </p>
              </div>

              {/* Status Info */}
              <div className="flex items-center gap-3 px-6 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20">
                <Clock className="w-5 h-5 text-purple-400" />
                <span className="text-white font-medium">
                  We're working hard to improve your experience
                </span>
              </div>

              {/* Additional Info */}
              <div className="space-y-4 text-center max-w-2xl">
                <p className="text-gray-400">
                  Our team is performing scheduled maintenance to enhance the platform.
                  Thank you for your patience.
                </p>
                <p className="text-sm text-gray-500">
                  For urgent inquiries, please <NAME_EMAIL>.
                </p>
                <div className="mt-6 pt-4 border-t border-gray-700">
                  {/* <p className="text-xs text-gray-500">
                    System administrators can still{" "}
                    <a href="/auth/login" className="text-purple-400 hover:text-purple-300 underline">
                      log in here
                    </a>{" "}
                    to manage the system.
                  </p> */}
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="w-full border-t bg-background">
        <div className="container flex h-16 items-center justify-center px-4 md:px-6">
          <p className="text-sm text-muted-foreground">
            © 2025 mTicket.my. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  )
}
