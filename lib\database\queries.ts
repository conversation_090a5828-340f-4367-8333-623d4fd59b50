import { getSupabaseAdmin } from './client'
import type { 
  DashboardStats, 
  EventWithDetails, 
  UserWithRole, 
  RegistrationWithDetails,
  OrganizationWithStats,
  CertificateWithDetails,
  EventFilters,
  UserFilters,
  RegistrationFilters,
  QueryOptions,
  PaginationResult,
  DatabaseResult,
  DatabaseListResult
} from './types'

/**
 * Optimized database queries to reduce N+1 query patterns
 * and improve performance across the application
 */

/**
 * Get dashboard statistics for a user
 */
export async function getDashboardStats(userId?: string): Promise<DashboardStats> {
  try {
    const supabaseAdmin = getSupabaseAdmin()

    // Get events count
    let eventsQuery = supabaseAdmin
      .from("events")
      .select("id", { count: "exact", head: true })

    if (userId) {
      eventsQuery = eventsQuery.eq("event_manager_id", userId)
    }

    const { count: totalEvents } = await eventsQuery

    // Get registrations count
    let registrationsQuery = supabaseAdmin
      .from("registrations")
      .select("id", { count: "exact", head: true })

    if (userId) {
      // Get registrations for user's events
      registrationsQuery = registrationsQuery.in(
        "event_id",
        supabaseAdmin
          .from("events")
          .select("id")
          .eq("event_manager_id", userId)
      )
    }

    const { count: totalRegistrations } = await registrationsQuery

    // Get revenue (simplified calculation)
    const { data: revenueData } = await supabaseAdmin
      .from("registrations")
      .select("total_amount")
      .eq("payment_status", "completed")

    const totalRevenue = revenueData?.reduce((sum, reg) => sum + (reg.total_amount || 0), 0) || 0

    return {
      totalEvents: totalEvents || 0,
      totalRegistrations: totalRegistrations || 0,
      totalRevenue,
      totalWithdrawals: 0, // Placeholder
      attendanceRate: 0.85 // Placeholder
    }
  } catch (error) {
    console.error("Error fetching dashboard stats:", error)
    return {
      totalEvents: 0,
      totalRegistrations: 0,
      totalRevenue: 0,
      totalWithdrawals: 0,
      attendanceRate: 0
    }
  }
}

/**
 * Fetch events with all related data in a single optimized query
 */
export async function fetchEventsWithDetails(
  filters: EventFilters = {},
  options: QueryOptions = {}
): Promise<PaginationResult<EventWithDetails>> {
  try {
    const supabaseAdmin = getSupabaseAdmin()

    let query = supabaseAdmin
      .from("events")
      .select(`
        id,
        title,
        slug,
        description,
        location,
        start_date,
        end_date,
        image_url,
        price,
        is_published,
        registrations(id),
        organizations:organization_id(id, name),
        category:category_id(id, name, color, icon)
      `, { count: 'exact' })

    // Apply filters
    if (filters.userId) {
      query = query.eq("event_manager_id", filters.userId)
    }

    if (filters.organizationId) {
      query = query.eq("organization_id", filters.organizationId)
    }

    if (filters.categoryId) {
      query = query.eq("category_id", filters.categoryId)
    }

    if (filters.isPublished !== undefined) {
      query = query.eq("is_published", filters.isPublished)
    }

    if (filters.searchTerm) {
      query = query.or(`title.ilike.%${filters.searchTerm}%,description.ilike.%${filters.searchTerm}%`)
    }

    if (filters.startDate) {
      query = query.gte("start_date", filters.startDate)
    }

    if (filters.endDate) {
      query = query.lte("end_date", filters.endDate)
    }

    // Apply ordering
    const orderBy = options.orderBy || 'created_at'
    const ascending = options.ascending !== false
    query = query.order(orderBy, { ascending })

    // Apply pagination
    const limit = options.limit || 20
    const offset = options.offset || 0
    query = query.range(offset, offset + limit - 1)

    const { data, error, count } = await query

    if (error) {
      console.error("Error fetching events:", error)
      return { data: [], total: 0, hasMore: false, page: 1, limit }
    }

    // Transform data to include registration count
    const events: EventWithDetails[] = (data || []).map(event => ({
      ...event,
      registrations_count: event.registrations?.length || 0,
      organizer: event.organizations,
      category: event.category
    }))

    const total = count || 0
    const page = Math.floor(offset / limit) + 1
    const hasMore = offset + limit < total

    return { data: events, total, hasMore, page, limit }
  } catch (error) {
    console.error("Error in fetchEventsWithDetails:", error)
    return { data: [], total: 0, hasMore: false, page: 1, limit: options.limit || 20 }
  }
}

/**
 * Fetch users with role information
 */
export async function fetchUsersWithRoles(
  filters: UserFilters = {},
  options: QueryOptions = {}
): Promise<PaginationResult<UserWithRole>> {
  try {
    const supabaseAdmin = getSupabaseAdmin()

    let query = supabaseAdmin
      .from("users")
      .select(`
        *,
        user_roles!role_id(id, role_name, description)
      `, { count: 'exact' })

    // Apply filters
    if (filters.roleId) {
      query = query.eq("role_id", filters.roleId)
    }

    if (filters.emailVerified !== undefined) {
      query = query.eq("email_verified", filters.emailVerified)
    }

    if (filters.searchTerm) {
      query = query.or(`full_name.ilike.%${filters.searchTerm}%,email.ilike.%${filters.searchTerm}%`)
    }

    // Apply ordering
    const orderBy = options.orderBy || 'created_at'
    const ascending = options.ascending !== false
    query = query.order(orderBy, { ascending })

    // Apply pagination
    const limit = options.limit || 20
    const offset = options.offset || 0
    query = query.range(offset, offset + limit - 1)

    const { data, error, count } = await query

    if (error) {
      console.error("Error fetching users:", error)
      return { data: [], total: 0, hasMore: false, page: 1, limit }
    }

    const total = count || 0
    const page = Math.floor(offset / limit) + 1
    const hasMore = offset + limit < total

    return { data: data || [], total, hasMore, page, limit }
  } catch (error) {
    console.error("Error in fetchUsersWithRoles:", error)
    return { data: [], total: 0, hasMore: false, page: 1, limit: options.limit || 20 }
  }
}

/**
 * Fetch registrations with event and user details
 */
export async function fetchRegistrationsWithDetails(
  filters: RegistrationFilters = {},
  options: QueryOptions = {}
): Promise<PaginationResult<RegistrationWithDetails>> {
  try {
    const supabaseAdmin = getSupabaseAdmin()

    let query = supabaseAdmin
      .from("registrations")
      .select(`
        *,
        event:event_id(id, title, start_date, location),
        user:user_id(id, full_name, email)
      `, { count: 'exact' })

    // Apply filters
    if (filters.eventId) {
      query = query.eq("event_id", filters.eventId)
    }

    if (filters.userId) {
      query = query.eq("user_id", filters.userId)
    }

    if (filters.paymentStatus) {
      query = query.eq("payment_status", filters.paymentStatus)
    }

    if (filters.startDate) {
      query = query.gte("registration_date", filters.startDate)
    }

    if (filters.endDate) {
      query = query.lte("registration_date", filters.endDate)
    }

    // Apply ordering
    const orderBy = options.orderBy || 'registration_date'
    const ascending = options.ascending !== false
    query = query.order(orderBy, { ascending })

    // Apply pagination
    const limit = options.limit || 20
    const offset = options.offset || 0
    query = query.range(offset, offset + limit - 1)

    const { data, error, count } = await query

    if (error) {
      console.error("Error fetching registrations:", error)
      return { data: [], total: 0, hasMore: false, page: 1, limit }
    }

    const total = count || 0
    const page = Math.floor(offset / limit) + 1
    const hasMore = offset + limit < total

    return { data: data || [], total, hasMore, page, limit }
  } catch (error) {
    console.error("Error in fetchRegistrationsWithDetails:", error)
    return { data: [], total: 0, hasMore: false, page: 1, limit: options.limit || 20 }
  }
}

/**
 * Get single event by ID with all details
 */
export async function getEventById(eventId: string): Promise<DatabaseResult<EventWithDetails>> {
  try {
    const supabaseAdmin = getSupabaseAdmin()

    const { data, error } = await supabaseAdmin
      .from("events")
      .select(`
        *,
        registrations(id),
        organizations:organization_id(id, name),
        category:category_id(id, name, color, icon)
      `)
      .eq("id", eventId)
      .single()

    if (error) {
      return { data: null, error: error.message, success: false }
    }

    const event: EventWithDetails = {
      ...data,
      registrations_count: data.registrations?.length || 0,
      organizer: data.organizations,
      category: data.category
    }

    return { data: event, error: null, success: true }
  } catch (error: any) {
    return { data: null, error: error.message, success: false }
  }
}

/**
 * Get single user by ID with role information
 */
export async function getUserById(userId: string): Promise<DatabaseResult<UserWithRole>> {
  try {
    const supabaseAdmin = getSupabaseAdmin()

    const { data, error } = await supabaseAdmin
      .from("users")
      .select(`
        *,
        user_roles!role_id(id, role_name, description)
      `)
      .eq("id", userId)
      .single()

    if (error) {
      return { data: null, error: error.message, success: false }
    }

    return { data, error: null, success: true }
  } catch (error: any) {
    return { data: null, error: error.message, success: false }
  }
}
