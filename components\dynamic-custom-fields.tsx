"use client"

import { useFormContext } from "react-hook-form"
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { type CustomField } from "@/lib/db/supabase-schema"

interface DynamicCustomFieldsProps {
  customFields: CustomField[]
  participantIndex?: number // For group registrations
  namePrefix?: string // Form field name prefix
}

export function DynamicCustomFields({
  customFields,
  participantIndex,
  namePrefix = "custom_field_responses"
}: DynamicCustomFieldsProps) {
  const { control } = useFormContext()

  if (!customFields || customFields.length === 0) {
    return null
  }

  // Sort fields by order
  const sortedFields = [...customFields].sort((a, b) => a.order - b.order)

  const getFieldName = (fieldId: string) => {
    if (participantIndex !== undefined) {
      return `participants.${participantIndex}.${namePrefix}.${fieldId}`
    }
    return `${namePrefix}.${fieldId}`
  }

  const renderField = (field: CustomField) => {
    const fieldName = getFieldName(field.id)

    switch (field.type) {
      case 'text':
      case 'email':
      case 'phone':
        return (
          <FormField
            key={field.id}
            control={control}
            name={fieldName}
            rules={{
              required: field.required ? `${field.label} is required` : false,
              pattern: field.type === 'email' ? {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: 'Invalid email address'
              } : field.type === 'phone' ? {
                value: /^[\+]?[0-9\s\-\(\)]{8,}$/,
                message: 'Invalid phone number'
              } : undefined,
              minLength: field.validation?.minLength ? {
                value: field.validation.minLength,
                message: `Minimum ${field.validation.minLength} characters required`
              } : undefined,
              maxLength: field.validation?.maxLength ? {
                value: field.validation.maxLength,
                message: `Maximum ${field.validation.maxLength} characters allowed`
              } : undefined,
            }}
            render={({ field: formField }) => (
              <FormItem>
                <FormLabel>
                  {field.label}
                  {field.required && <span className="text-red-500 ml-1">*</span>}
                </FormLabel>
                <FormControl>
                  <Input
                    type={field.type}
                    placeholder={field.placeholder}
                    {...formField}
                    value={formField.value || ""}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )

      case 'number':
        return (
          <FormField
            key={field.id}
            control={control}
            name={fieldName}
            rules={{
              required: field.required ? `${field.label} is required` : false,
            }}
            render={({ field: formField }) => (
              <FormItem>
                <FormLabel>
                  {field.label}
                  {field.required && <span className="text-red-500 ml-1">*</span>}
                </FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder={field.placeholder}
                    {...formField}
                    value={formField.value || ""}
                    onChange={(e) => formField.onChange(e.target.value ? Number(e.target.value) : '')}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )

      case 'textarea':
        return (
          <FormField
            key={field.id}
            control={control}
            name={fieldName}
            rules={{
              required: field.required ? `${field.label} is required` : false,
              minLength: field.validation?.minLength ? {
                value: field.validation.minLength,
                message: `Minimum ${field.validation.minLength} characters required`
              } : undefined,
              maxLength: field.validation?.maxLength ? {
                value: field.validation.maxLength,
                message: `Maximum ${field.validation.maxLength} characters allowed`
              } : undefined,
            }}
            render={({ field: formField }) => (
              <FormItem>
                <FormLabel>
                  {field.label}
                  {field.required && <span className="text-red-500 ml-1">*</span>}
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder={field.placeholder}
                    className="min-h-20"
                    {...formField}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )

      case 'select':
        return (
          <FormField
            key={field.id}
            control={control}
            name={fieldName}
            rules={{
              required: field.required ? `${field.label} is required` : false,
            }}
            render={({ field: formField }) => (
              <FormItem>
                <FormLabel>
                  {field.label}
                  {field.required && <span className="text-red-500 ml-1">*</span>}
                </FormLabel>
                <Select onValueChange={formField.onChange} defaultValue={formField.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={field.placeholder || `Select ${field.label.toLowerCase()}`} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {field.options?.map((option, index) => (
                      <SelectItem key={index} value={option}>
                        {option}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        )

      case 'checkbox':
        return (
          <FormField
            key={field.id}
            control={control}
            name={fieldName}
            rules={{
              required: field.required ? `${field.label} must be checked` : false,
            }}
            render={({ field: formField }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={formField.value}
                    onCheckedChange={formField.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                    {field.label}
                    {field.required && <span className="text-red-500 ml-1">*</span>}
                  </FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />
        )

      default:
        return null
    }
  }

  return (
    <div className="space-y-4">
      <div className="border-t pt-4">
        <h4 className="text-sm font-medium text-muted-foreground mb-4">
          Additional Information
        </h4>
        <div className="grid gap-4 md:grid-cols-2">
          {sortedFields.map(renderField)}
        </div>
      </div>
    </div>
  )
}
