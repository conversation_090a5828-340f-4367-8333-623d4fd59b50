import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { verifyAuthToken } from "@/lib/auth/jwt"
import { isElevated } from "@/lib/auth/roles"
import { logActivity, ActivityCategory } from "@/utils/activity-logger"

const supabaseAdmin = getSupabaseAdmin()

interface RouteParams {
  params: { slug: string }
}

/**
 * GET /api/events/[slug]/teams
 * Get all teams for an event
 */
export async function GET(request: Request, { params }: RouteParams) {
  try {
    const { slug } = await params

    // Authenticate user
    const authResult = await verifyAuthToken(request)
    if (!authResult.isAuthenticated || !authResult.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get event by slug
    const { data: event, error: eventError } = await supabaseAdmin
      .from("events")
      .select("id, title, event_manager_id")
      .eq("slug", slug)
      .single()

    if (eventError || !event) {
      return NextResponse.json(
        { error: "Event not found" },
        { status: 404 }
      )
    }

    // Check permissions
    const userRole = authResult.user.user_roles?.role_name || authResult.user.role
    const isEventManager = event.event_manager_id === authResult.user.id
    const hasElevatedAccess = isElevated(userRole)

    if (!isEventManager && !hasElevatedAccess) {
      return NextResponse.json(
        { error: "Unauthorized. Only event managers can view teams." },
        { status: 403 }
      )
    }

    // Get teams for the event
    const { data: teams, error: teamsError } = await supabaseAdmin
      .from("event_teams")
      .select(`
        id,
        team_name,
        location,
        access_token,
        permissions,
        is_active,
        expires_at,
        last_used_at,
        created_at,
        created_by,
        users:created_by(full_name, email)
      `)
      .eq("event_id", event.id)
      .order("created_at", { ascending: false })

    if (teamsError) {
      console.error("Error fetching teams:", teamsError)
      return NextResponse.json(
        { error: "Failed to fetch teams" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      teams: teams || [],
      event: {
        id: event.id,
        title: event.title
      }
    })

  } catch (error: any) {
    console.error("Error in teams GET:", error)
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    )
  }
}

/**
 * POST /api/events/[slug]/teams
 * Create a new team for an event
 */
export async function POST(request: Request, { params }: RouteParams) {
  try {
    const { slug } = await params
    const body = await request.json()
    const { team_name, location, permissions, expires_at } = body

    if (!team_name) {
      return NextResponse.json(
        { error: "Team name is required" },
        { status: 400 }
      )
    }

    // Authenticate user
    const authResult = await verifyAuthToken(request)
    if (!authResult.isAuthenticated || !authResult.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get event by slug
    const { data: event, error: eventError } = await supabaseAdmin
      .from("events")
      .select("id, title, event_manager_id")
      .eq("slug", slug)
      .single()

    if (eventError || !event) {
      return NextResponse.json(
        { error: "Event not found" },
        { status: 404 }
      )
    }

    // Check permissions
    const userRole = authResult.user.user_roles?.role_name || authResult.user.role
    const isEventManager = event.event_manager_id === authResult.user.id
    const hasElevatedAccess = isElevated(userRole)

    if (!isEventManager && !hasElevatedAccess) {
      return NextResponse.json(
        { error: "Unauthorized. Only event managers can create teams." },
        { status: 403 }
      )
    }

    // Generate unique access token
    const accessToken = `team_${Math.random().toString(36).substring(2, 15)}_${Date.now()}`

    // Create team
    const { data: team, error: createError } = await supabaseAdmin
      .from("event_teams")
      .insert({
        event_id: event.id,
        team_name,
        location: location || null,
        access_token: accessToken,
        permissions: permissions || { can_scan_qr: true, can_view_attendance: true },
        created_by: authResult.user.id,
        expires_at: expires_at || null
      })
      .select()
      .single()

    if (createError) {
      console.error("Error creating team:", createError)
      return NextResponse.json(
        { error: "Failed to create team" },
        { status: 500 }
      )
    }

    // Log activity
    await logActivity({
      userId: authResult.user.id,
      action: "create_team",
      category: ActivityCategory.EVENT_MANAGEMENT,
      entityType: "event_team",
      entityId: team.id,
      details: {
        event_id: event.id,
        event_title: event.title,
        team_name
      }
    })

    return NextResponse.json({
      success: true,
      team,
      message: "Team created successfully"
    })

  } catch (error: any) {
    console.error("Error in teams POST:", error)
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    )
  }
}
