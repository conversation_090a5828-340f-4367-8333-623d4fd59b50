"use client"

import React from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RegistrationForm, type ParticipantType } from "@/components/registration-form"
import { type EventType } from "@/contexts/event-context"
import { type SelectedTicket } from "@/types/ticket-types"
import { FileText, Users, Clock } from "lucide-react"

interface RegistrationStepProps {
  event: EventType
  userId: string | null
  selectedTickets: SelectedTicket[]
  onSuccess: (participants: ParticipantType[], mainContact: any, mainContactIndex: number) => void
  savedFormData?: any
  onFormDataChange?: (data: any) => void
}

export function RegistrationStep({
  event,
  userId,
  selectedTickets,
  onSuccess,
  savedFormData,
  onFormDataChange
}: RegistrationStepProps) {
  const totalTickets = selectedTickets.reduce((total, ticket) => total + ticket.quantity, 0)

  // Calculate total required fields (4 standard + custom required fields)
  const standardRequiredFields = 4 // Name, IC/Passport, Phone, Email
  const customRequiredFields = event.custom_fields?.filter(field => field.required).length || 0
  const totalRequiredFields = standardRequiredFields + customRequiredFields

  return (
    <div className="space-y-6">
      {/* Step Header */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-full mb-4">
          <FileText className="h-8 w-8" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Registration Details</h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Please provide the required information for all participants. This information will be used for event check-in and communication.
        </p>
      </div>

      {/* Registration Info Cards */}
      <div className="grid gap-4 md:grid-cols-3 mb-6">
        <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100">
          <CardContent className="p-4 text-center">
            <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-blue-900">Participants</p>
            <p className="text-2xl font-bold text-blue-700">{totalTickets}</p>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-gradient-to-br from-green-50 to-green-100">
          <CardContent className="p-4 text-center">
            <Clock className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-green-900">Estimated Time</p>
            <p className="text-2xl font-bold text-green-700">3-5 min</p>
          </CardContent>
        </Card>

        <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100">
          <CardContent className="p-4 text-center">
            <FileText className="h-8 w-8 text-purple-600 mx-auto mb-2" />
            <p className="text-sm font-medium text-purple-900">Required Fields</p>
            <p className="text-2xl font-bold text-purple-700">{totalRequiredFields}</p>
          </CardContent>
        </Card>
      </div>

      {/* Registration Form */}
      <Card className="shadow-lg border-0 bg-white">
        <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 border-b">
          <CardTitle className="text-xl text-gray-900">Participant Information</CardTitle>
          <CardDescription className="text-gray-600">
            Fill in the details for each participant. One person will be designated as the main contact.
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6">
          <RegistrationForm
            event={event}
            userId={userId}
            selectedTickets={selectedTickets}
            onSuccess={onSuccess}
            savedFormData={savedFormData}
            onFormDataChange={onFormDataChange}
          />
        </CardContent>
      </Card>

      {/* Help Text */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">Important Information</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• All fields marked with an asterisk (*) are required</li>
          <li>• The main contact will receive all event communications</li>
          <li>• Please ensure all information is accurate for smooth check-in</li>
          <li>• You can edit this information later from your dashboard</li>
        </ul>
      </div>
    </div>
  )
}
