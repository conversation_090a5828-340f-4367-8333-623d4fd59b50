import { NextResponse } from "next/server"
import { getMaintenanceStatus } from "@/lib/app-settings"

/**
 * GET /api/maintenance
 * Check maintenance mode status (public endpoint)
 */
export async function GET() {
  try {
    const maintenanceStatus = await getMaintenanceStatus()
    
    return NextResponse.json(maintenanceStatus)
  } catch (error: any) {
    console.error("Error checking maintenance status:", error)
    return NextResponse.json(
      { 
        enabled: false,
        message: 'We are currently performing maintenance. We will be back soon!'
      }
    )
  }
}
