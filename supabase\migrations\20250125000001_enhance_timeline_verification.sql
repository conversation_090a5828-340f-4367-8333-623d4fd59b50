-- Enhance timeline verification by adding missing timestamp fields
-- This migration adds comprehensive tracking fields for better verification trail

DO $$ 
BEGIN
  -- Enhance registrations table
  
  -- Add created_at if it doesn't exist (some tables might be missing this)
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'registrations' AND column_name = 'created_at') THEN
    ALTER TABLE registrations ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
  END IF;
  
  -- Add updated_at if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'registrations' AND column_name = 'updated_at') THEN
    ALTER TABLE registrations ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
  END IF;
  
  -- Standardize payment_date column (some schemas use payment_date, others use different names)
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'registrations' AND column_name = 'payment_date') THEN
    ALTER TABLE registrations ADD COLUMN payment_date TIMESTAMP WITH TIME ZONE;
  END IF;
  
  -- Standardize check-in fields (some use checked_in_at, others use check_in_time)
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'registrations' AND column_name = 'checked_in') THEN
    ALTER TABLE registrations ADD COLUMN checked_in BOOLEAN DEFAULT FALSE;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'registrations' AND column_name = 'checked_in_at') THEN
    ALTER TABLE registrations ADD COLUMN checked_in_at TIMESTAMP WITH TIME ZONE;
  END IF;
  
  -- Add payment_id for better transaction tracking
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'registrations' AND column_name = 'payment_id') THEN
    ALTER TABLE registrations ADD COLUMN payment_id TEXT;
  END IF;
  
  -- Enhance certificates table
  
  -- Add created_at if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificates' AND column_name = 'created_at') THEN
    ALTER TABLE certificates ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
  END IF;
  
  -- Add updated_at if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificates' AND column_name = 'updated_at') THEN
    ALTER TABLE certificates ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
  END IF;
  
  -- Standardize issued_at column (some schemas use issue_date, others use issued_at)
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificates' AND column_name = 'issued_at') THEN
    ALTER TABLE certificates ADD COLUMN issued_at TIMESTAMP WITH TIME ZONE;
  END IF;
  
  -- Add verification tracking fields
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificates' AND column_name = 'last_verified_at') THEN
    ALTER TABLE certificates ADD COLUMN last_verified_at TIMESTAMP WITH TIME ZONE;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'certificates' AND column_name = 'verification_count') THEN
    ALTER TABLE certificates ADD COLUMN verification_count INTEGER DEFAULT 0;
  END IF;
  
  -- Enhance transactions table
  
  -- Add created_at if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'transactions' AND column_name = 'created_at') THEN
    ALTER TABLE transactions ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
  END IF;
  
  -- Add updated_at if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'transactions' AND column_name = 'updated_at') THEN
    ALTER TABLE transactions ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
  END IF;
  
  -- Ensure processed_at exists for payment completion tracking
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'transactions' AND column_name = 'processed_at') THEN
    ALTER TABLE transactions ADD COLUMN processed_at TIMESTAMP WITH TIME ZONE;
  END IF;
  
END $$;

-- Update existing data to populate new fields

-- Update registrations: copy check_in_time to checked_in_at if needed
UPDATE registrations 
SET checked_in_at = check_in_time 
WHERE checked_in_at IS NULL AND check_in_time IS NOT NULL;

-- Update registrations: set checked_in to true if check_in_time exists
UPDATE registrations 
SET checked_in = TRUE 
WHERE checked_in IS FALSE AND (checked_in_at IS NOT NULL OR check_in_time IS NOT NULL);

-- Update certificates: copy issue_date to issued_at if needed
UPDATE certificates 
SET issued_at = issue_date 
WHERE issued_at IS NULL AND issue_date IS NOT NULL;

-- Update certificates: set issued_at to created_at if both are null
UPDATE certificates 
SET issued_at = created_at 
WHERE issued_at IS NULL AND created_at IS NOT NULL;

-- Update transactions: set processed_at to created_at for completed transactions
UPDATE transactions 
SET processed_at = created_at 
WHERE processed_at IS NULL AND status = 'completed';

-- Create indexes for better performance on timeline queries
CREATE INDEX IF NOT EXISTS idx_registrations_created_at ON registrations(created_at);
CREATE INDEX IF NOT EXISTS idx_registrations_payment_date ON registrations(payment_date);
CREATE INDEX IF NOT EXISTS idx_registrations_checked_in_at ON registrations(checked_in_at);
CREATE INDEX IF NOT EXISTS idx_certificates_issued_at ON certificates(issued_at);
CREATE INDEX IF NOT EXISTS idx_certificates_last_verified_at ON certificates(last_verified_at);
CREATE INDEX IF NOT EXISTS idx_transactions_processed_at ON transactions(processed_at);

-- Create triggers for updated_at columns
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers for registrations
DROP TRIGGER IF EXISTS update_registrations_updated_at ON registrations;
CREATE TRIGGER update_registrations_updated_at
BEFORE UPDATE ON registrations
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Add triggers for certificates
DROP TRIGGER IF EXISTS update_certificates_updated_at ON certificates;
CREATE TRIGGER update_certificates_updated_at
BEFORE UPDATE ON certificates
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Add triggers for transactions
DROP TRIGGER IF EXISTS update_transactions_updated_at ON transactions;
CREATE TRIGGER update_transactions_updated_at
BEFORE UPDATE ON transactions
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Add comments to document the purpose of new fields
COMMENT ON COLUMN certificates.last_verified_at IS 'Timestamp of the last verification attempt';
COMMENT ON COLUMN certificates.verification_count IS 'Number of times this certificate has been verified';
COMMENT ON COLUMN registrations.checked_in_at IS 'Timestamp when attendee checked in to the event';
COMMENT ON COLUMN registrations.payment_date IS 'Timestamp when payment was confirmed';
COMMENT ON COLUMN transactions.processed_at IS 'Timestamp when transaction was processed by gateway';
