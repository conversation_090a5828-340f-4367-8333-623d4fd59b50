"use client"

import { useState, useMemo, useEffect } from "react"
import { ImageModal } from "@/components/ImageModal"
import { useSearchParams, useRouter } from "next/navigation"
import Link from "next/link"
import { Search, X, Users, Calendar, Shield, Zap, BarChart3, Award, CheckCircle, ArrowRight, Star, TrendingUp, MousePointer, Sparkles } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

import EventCard from "@/components/event-card"
import { PageLayout } from "@/components/page-layout"
import { useToast } from "@/hooks/use-toast"
import { stripHtmlTags } from "@/lib/utils"

import { EventType } from "@/contexts/event-context"



export default function HomePage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isClient, setIsClient] = useState(false)
  const [modalImage, setModalImage] = useState<{url: string, alt: string, description: string} | null>(null)
  const [searchQuery, setSearchQuery] = useState("")
  const [events, setEvents] = useState<EventType[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [organizerFilter, setOrganizerFilter] = useState<string | null>(null)
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null)
  const [organizerInfo, setOrganizerInfo] = useState<{ id: string; name: string } | null>(null)
  const [categoryInfo, setCategoryInfo] = useState<{ id: string; name: string; color: string } | null>(null)
  const { toast } = useToast()

  // Handle URL parameters
  useEffect(() => {
    const organizer = searchParams.get('organizer')
    const category = searchParams.get('category')
    setOrganizerFilter(organizer)
    setCategoryFilter(category)
  }, [searchParams])

  // Fetch events from the database
  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true)
        setError(null)

        let url = '/api/events'
        if (organizerFilter) {
          url = `/api/events/organizer/${organizerFilter}`
        } else if (categoryFilter) {
          if (categoryFilter === 'others') {
            url = `/api/events/category/others`
          } else {
            url = `/api/events/category/${categoryFilter}`
          }
        }

        const response = await fetch(url)

        if (!response.ok) {
          throw new Error(`Failed to fetch events: ${response.status}`)
        }

        const data = await response.json()

        if (data.error) {
          throw new Error(data.error)
        }

        setEvents(data.events || [])
        if (data.organizer) {
          setOrganizerInfo(data.organizer)
        } else {
          setOrganizerInfo(null)
        }

        if (data.category) {
          setCategoryInfo(data.category)
        } else {
          setCategoryInfo(null)
        }
      } catch (err: any) {
        console.error('Error fetching events:', err)
        setError(err.message || 'Failed to load events')
        toast({
          title: "Error",
          description: "Failed to load events. Please try again later.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    fetchEvents()
  }, [organizerFilter, categoryFilter])

  // Clear organizer filter
  const clearOrganizerFilter = () => {
    router.push('/')
  }

  // Clear category filter
  const clearCategoryFilter = () => {
    router.push('/')
  }

  const filterEvents = (eventList: EventType[]): EventType[] => {
    // API already filters by is_published, so no need to filter by status here
    let filtered = eventList

    // Filter by search query
    if (searchQuery) {
      const lowercasedQuery = searchQuery.toLowerCase()
      filtered = filtered.filter(event => {
        // Get description text (strip HTML if it's WYSIWYG content)
        const descriptionText = event.description_html
          ? stripHtmlTags(event.description_html)
          : event.description || ''

        return event.title.toLowerCase().includes(lowercasedQuery) ||
          descriptionText.toLowerCase().includes(lowercasedQuery) ||
          event.location.toLowerCase().includes(lowercasedQuery) ||
          (event.organizations?.name && event.organizations.name.toLowerCase().includes(lowercasedQuery)) ||
          (event.event_category?.name && event.event_category.name.toLowerCase().includes(lowercasedQuery))
      })
    }

    // Filter by category
    if (categoryFilter) {
      filtered = filtered.filter(event =>
        event.event_category?.id === categoryFilter || event.event_category?.name === categoryFilter
      )
    }

    return filtered
  }

  // Helper function to apply smart sorting (available events first, then ended events)
  const applySmartSort = (eventList: EventType[]) => {
    return [...eventList].sort((a, b) => {
      const now = new Date();
      const aEnded = new Date(a.end_date) < now;
      const bEnded = new Date(b.end_date) < now;

      // If one is ended and the other isn't, prioritize the non-ended one
      if (aEnded && !bEnded) return 1;
      if (!aEnded && bEnded) return -1;

      // If both have the same status (both ended or both available), sort by start_date
      return new Date(a.start_date).getTime() - new Date(b.start_date).getTime();
    });
  }

  // Categorize events by type while preserving smart sorting
  const latestEvents = useMemo(() => {
    // Sort by created_at for latest, but then apply smart sorting
    const sortedByCreated = [...events].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    const filtered = filterEvents(sortedByCreated)
    return applySmartSort(filtered)
  }, [events, searchQuery, categoryFilter])

  const featuredEvents = useMemo(() => {
    const featured = events.filter(event => event.is_featured === true)
    const filtered = filterEvents(featured)
    return applySmartSort(filtered)
  }, [events, searchQuery, categoryFilter])

  const popularEvents = useMemo(() => {
    // Sort by max_participants for popularity, but then apply smart sorting
    const sortedByPopularity = [...events].sort((a, b) => (b.max_participants || 0) - (a.max_participants || 0))
    const filtered = filterEvents(sortedByPopularity)
    return applySmartSort(filtered)
  }, [events, searchQuery, categoryFilter])



  return (
    <>
      <PageLayout>
      {/* Enhanced Corporate Hero Section */}
      <section className="relative w-full py-20 md:py-32 lg:py-40 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-20">
          <div className="w-full h-full" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}></div>
        </div>

        <div className="container relative px-4 md:px-6">
          <div className="flex flex-col items-center space-y-8 text-center">
            {/* Main Heading */}
            <div className="space-y-4 max-w-4xl">
              <h1 className="text-4xl font-bold tracking-tight text-white sm:text-5xl md:text-6xl lg:text-7xl">
                The Future of
                <span className="bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent"> Event Management</span>
              </h1>
              <p className="mx-auto max-w-3xl text-xl text-gray-300 md:text-2xl leading-relaxed">
                Create, manage, and scale your events with Malaysia's most trusted platform featuring
                enterprise-grade security, dynamic QR codes, and professional certificate management.
                Join thousands of organizers who choose mTicket.my for seamless event experiences.
              </p>
            </div>

            {/* Key Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 w-full max-w-4xl">
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-white">50K+</div>
                <div className="text-sm text-gray-400 uppercase tracking-wide">Events Created</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-white">2M+</div>
                <div className="text-sm text-gray-400 uppercase tracking-wide">Tickets Sold</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-white">10K+</div>
                <div className="text-sm text-gray-400 uppercase tracking-wide">Organizers</div>
              </div>
              <div className="text-center">
                <div className="text-3xl md:text-4xl font-bold text-white">99.9%</div>
                <div className="text-sm text-gray-400 uppercase tracking-wide">Uptime</div>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 pt-4">
              <Link href="/auth/register" className="inline-flex items-center justify-center rounded-md bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 text-lg font-semibold transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">
                Start Creating Events
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
              <Link href="/events" className="inline-flex items-center justify-center rounded-md border border-white text-white hover:bg-white hover:text-purple-900 px-8 py-4 text-lg font-semibold transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">
                Browse Events
              </Link>
            </div>

            {/* Search Section */}
            <div className="w-full max-w-2xl pt-8">
              <div className="relative">
                <Search className="absolute left-4 top-4 h-5 w-5 text-gray-400" />
                <Input
                  type="search"
                  placeholder="Search events by name, location, or category..."
                  className="w-full bg-white/10 backdrop-blur-sm border-white/20 text-white placeholder:text-gray-300 pl-12 py-4 text-lg rounded-xl"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              {/* Filter badges */}
              <div className="flex flex-col items-center gap-3 mt-4">
                {/* Organizer filter badge */}
                {organizerFilter && organizerInfo && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-300">Showing events by:</span>
                    <Badge className="bg-white/20 text-white border-white/30 flex items-center gap-1">
                      {organizerInfo.name}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-white/20 text-white"
                        onClick={clearOrganizerFilter}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  </div>
                )}

                {/* Category filter badge */}
                {categoryFilter && categoryInfo && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-300">Showing events in:</span>
                    <Badge
                      className="bg-white/20 text-white border-white/30 flex items-center gap-1"
                    >
                      {categoryInfo.name}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-white/20 text-white"
                        onClick={clearCategoryFilter}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Events Section */}
      <section className="w-full py-16 md:py-20 bg-white">
        <div className="container px-4 md:px-6">
          {/* Section Header */}
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Discover Amazing Events
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Find the best events happening near you. From sports to conferences,
              discover opportunities that match your interests.
            </p>
          </div>

          {/* Show loading state */}
          {loading && (
            <div className="flex justify-center items-center py-16">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
              <span className="ml-4 text-lg text-gray-600">Loading amazing events...</span>
            </div>
          )}

          {/* Show error state */}
          {error && !loading && (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                  <p className="text-red-600 mb-4 font-medium">{error}</p>
                  <Button
                    onClick={() => window.location.reload()}
                    variant="outline"
                    className="border-red-300 text-red-600 hover:bg-red-50"
                  >
                    Try Again
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Show content when not loading and no error */}
          {!loading && !error && (
            <div className="space-y-8">
              <Tabs defaultValue="featured" className="w-full">
                <div className="flex justify-center mb-8">
                  <TabsList className="grid w-full max-w-lg grid-cols-3 h-12 bg-gray-100 rounded-xl p-1">
                    <TabsTrigger
                      value="latest"
                      className="rounded-lg font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm"
                    >
                      Latest Events
                    </TabsTrigger>
                    <TabsTrigger
                      value="featured"
                      className="rounded-lg font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm"
                    >
                      Featured Events
                    </TabsTrigger>
                    <TabsTrigger
                      value="popular"
                      className="rounded-lg font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm"
                    >
                      Popular Events
                    </TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="latest" className="mt-8">
                  <div className="mx-auto grid max-w-7xl grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                    {latestEvents.length === 0 ? (
                      <div className="col-span-full text-center py-16">
                        <div className="max-w-md mx-auto">
                          <Calendar className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-xl font-semibold text-gray-600 mb-2">No Latest Events Found</h3>
                          <p className="text-gray-500">
                            {searchQuery || organizerFilter || categoryFilter
                              ? "Try adjusting your search criteria or filters."
                              : "Check back soon for new events!"
                            }
                          </p>
                        </div>
                      </div>
                    ) : (
                      latestEvents.map((event: EventType) => (
                        <EventCard key={event.id} event={event} />
                      ))
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="featured" className="mt-8">
                  <div className="mx-auto grid max-w-7xl grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                    {featuredEvents.length === 0 ? (
                      <div className="col-span-full text-center py-16">
                        <div className="max-w-md mx-auto">
                          <Star className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-xl font-semibold text-gray-600 mb-2">No Featured Events Found</h3>
                          <p className="text-gray-500">
                            {searchQuery || organizerFilter || categoryFilter
                              ? "Try adjusting your search criteria or filters."
                              : "Featured events will appear here soon!"
                            }
                          </p>
                        </div>
                      </div>
                    ) : (
                      featuredEvents.map((event: EventType) => (
                        <EventCard key={event.id} event={event} />
                      ))
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="popular" className="mt-8">
                  <div className="mx-auto grid max-w-7xl grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
                    {popularEvents.length === 0 ? (
                      <div className="col-span-full text-center py-16">
                        <div className="max-w-md mx-auto">
                          <TrendingUp className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-xl font-semibold text-gray-600 mb-2">No Popular Events Found</h3>
                          <p className="text-gray-500">
                            {searchQuery || organizerFilter || categoryFilter
                              ? "Try adjusting your search criteria or filters."
                              : "Popular events will be shown here!"
                            }
                          </p>
                        </div>
                      </div>
                    ) : (
                      popularEvents.map((event: EventType) => (
                        <EventCard key={event.id} event={event} />
                      ))
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}
        </div>
      </section>

      {/* Corporate Features Section */}
      <section className="w-full py-20 md:py-24 bg-gray-50">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need to Succeed
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            From small meetups to large-scale conferences, our all-in-one platform delivers enterprise-grade security, dynamic QR codes, customizable registration fields, and professional certificate management.
            </p>
          </div>

          <div className="mx-auto max-w-7xl grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 mb-16">
            {/* Event Creation Feature */}
            <div 
              className="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-2 cursor-pointer"
              onClick={() => {
                setModalImage({
                  url: 'https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//event-creation.jpg',
                  alt: 'Event creation',
                  description: 'Build beautiful and fully branded event pages with customizable image carousels, drag-and-drop content editing, and tailored registration forms. Control every element from ticket types to event branding using a flexible event page builder designed to enhance the attendee experience.'
                });
              }}
            >
              <div className="h-full">
                {/* Image Section */}
                <div className="h-48 relative overflow-hidden bg-white">
                  <div className="absolute inset-0 group-hover:bg-gradient-to-br group-hover:from-purple-600 group-hover:to-purple-800 transition-all duration-500">
                    <img 
                      src="https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//event-creation.jpg" 
                      alt="Event creation"
                      className="absolute inset-0 w-full h-full object-cover group-hover:opacity-30 transition-opacity duration-500 z-10"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = 'flex';
                      }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <Calendar className="h-16 w-16 text-white/80 group-hover:scale-110 transition-transform duration-500" />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    {/* Animated particles on hover */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <Sparkles className="absolute top-4 right-4 h-5 w-5 text-white/60 animate-pulse" />
                      <Sparkles className="absolute bottom-4 left-4 h-4 w-4 text-white/40 animate-pulse delay-300" />
                      <Sparkles className="absolute top-1/2 left-4 h-4 w-4 text-white/50 animate-pulse delay-150" />
                    </div>
                  </div>
                </div>

                {/* Content Section */}
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors duration-300">
                        <Calendar className="h-5 w-5 text-purple-600" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 group-hover:text-purple-700 transition-colors duration-300">Event Creation</h3>
                    </div>
                    <p className="text-gray-600 leading-relaxed">
                    Design branded event pages with carousels, easy editing, and custom registration options.
                    </p>
                    <div className="flex items-center text-purple-600 font-medium group-hover:text-purple-700 transition-colors duration-300">
                      <span>Learn more</span>
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Attendee Management Feature */}
            <div 
              className="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-2 cursor-pointer"
              onClick={() => {
                setModalImage({
                  url: 'https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//attendee-management.jpg',
                  alt: 'Attendee management',
                  description: 'Seamlessly manage event registrations with customizable fields, secure dynamic QR codes, and real-time attendee check-ins. Ensure enterprise-grade protection with advanced security and built-in anti-fraud safeguards.'
                });
              }}
            >
              <div className="h-full">
                {/* Image Section */}
                <div className="h-48 relative overflow-hidden bg-white">
                  <div className="absolute inset-0 group-hover:bg-gradient-to-br group-hover:from-blue-600 group-hover:to-blue-800 transition-all duration-500">
                    <img 
                      src="https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//attendee-management.jpg" 
                      alt="Attendee management"
                      className="absolute inset-0 w-full h-full object-cover group-hover:opacity-30 transition-opacity duration-500 z-10"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = 'flex';
                      }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <Users className="h-16 w-16 text-white/80 group-hover:scale-110 transition-transform duration-500" />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    {/* Animated elements on hover */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <MousePointer className="absolute top-4 left-4 h-5 w-5 text-white/60 animate-bounce" />
                      <div className="absolute bottom-4 right-4 w-3 h-3 bg-white/40 rounded-full animate-ping"></div>
                      <div className="absolute top-1/3 right-6 w-2 h-2 bg-white/50 rounded-full animate-pulse delay-200"></div>
                    </div>
                  </div>
                </div>

                {/* Content Section */}
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors duration-300">
                        <Users className="h-5 w-5 text-blue-600" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-700 transition-colors duration-300">Attendee Management</h3>
                    </div>
                    <p className="text-gray-600 leading-relaxed">
                      Manage registrations with custom fields, dynamic QR codes, and real-time check-ins backed by enterprise-level security.
                    </p>
                    <div className="flex items-center text-blue-600 font-medium group-hover:text-blue-700 transition-colors duration-300">
                      <span>Learn more</span>
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Secure Payments Feature */}
            <div 
              className="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-2 cursor-pointer"
              onClick={() => {
                setModalImage({
                  url: 'https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//secure-payments.jpg',
                  alt: 'Secure payments',
                  description: 'Accept payments securely with multiple payment gateways and automatic fallback support. Enable FPX and DuitNow QR payments when needed. Instant payouts are available based on your plan, along with detailed financial reporting for full visibility.'
                });
              }}
            >
              <div className="h-full">
                {/* Image Section */}
                <div className="h-48 relative overflow-hidden bg-white">
                  <div className="absolute inset-0 group-hover:bg-gradient-to-br group-hover:from-green-600 group-hover:to-green-800 transition-all duration-500">
                    <img 
                      src="https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//secure-payments.jpg" 
                      alt="Secure payments"
                      className="absolute inset-0 w-full h-full object-cover group-hover:opacity-30 transition-opacity duration-500 z-10"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = 'flex';
                      }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <Shield className="h-16 w-16 text-white/80 group-hover:scale-110 transition-transform duration-500" />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    {/* Security indicators on hover */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="absolute top-4 right-4 w-3 h-3 bg-white/60 rounded-full animate-pulse"></div>
                      <div className="absolute bottom-4 left-4 w-2 h-2 bg-white/40 rounded-full animate-pulse delay-300"></div>
                      <CheckCircle className="absolute top-1/3 right-6 h-5 w-5 text-white/70 animate-pulse delay-150" />
                    </div>
                  </div>
                </div>

                {/* Content Section */}
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors duration-300">
                        <Shield className="h-5 w-5 text-green-600" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 group-hover:text-green-700 transition-colors duration-300">Secure Payments</h3>
                    </div>
                    <p className="text-gray-600 leading-relaxed">
                      Accept secure payments with gateway fallback, FPX, and DuitNow QR. Get instant payouts and full financial reporting.
                    </p>
                    <div className="flex items-center text-green-600 font-medium group-hover:text-green-700 transition-colors duration-300">
                      <span>Learn more</span>
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Analytics & Insights Feature */}
            <div 
              className="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-2 cursor-pointer"
              onClick={() => {
                setModalImage({
                  url: 'https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//analytics.jpg',
                  alt: 'Analytics dashboard',
                  description: 'Track event performance with detailed analytics and audience behavior insights. Understand your attendees and optimize future events using real-time, actionable data.'
                });
              }}
            >
              <div className="h-full">
                {/* Image Section */}
                <div className="h-48 relative overflow-hidden bg-white">
                  <div className="absolute inset-0 group-hover:bg-gradient-to-br group-hover:from-orange-600 group-hover:to-orange-800 transition-all duration-500">
                    <img 
                      src="https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//analytics.jpg" 
                      alt="Analytics dashboard"
                      className="absolute inset-0 w-full h-full object-cover group-hover:opacity-30 transition-opacity duration-500 z-10"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = 'flex';
                      }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <BarChart3 className="h-16 w-16 text-white/80 group-hover:scale-110 transition-transform duration-500" />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    {/* Chart elements on hover */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <TrendingUp className="absolute top-4 right-4 h-5 w-5 text-white/60 animate-pulse" />
                      <div className="absolute bottom-4 left-4 flex space-x-1">
                        <div className="w-1 h-4 bg-white/40 rounded animate-pulse"></div>
                        <div className="w-1 h-6 bg-white/50 rounded animate-pulse delay-100"></div>
                        <div className="w-1 h-3 bg-white/30 rounded animate-pulse delay-200"></div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content Section */}
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center group-hover:bg-orange-200 transition-colors duration-300">
                        <BarChart3 className="h-5 w-5 text-orange-600" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 group-hover:text-orange-700 transition-colors duration-300">Analytics & Insights</h3>
                    </div>
                    <p className="text-gray-600 leading-relaxed">
                    Get detailed analytics and insights to understand your audience and improve future events.
                    </p>
                    <div className="flex items-center text-orange-600 font-medium group-hover:text-orange-700 transition-colors duration-300">
                      <span>Learn more</span>
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Digital Certificates Feature */}
            <div 
              className="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-2 cursor-pointer"
              onClick={() => {
                setModalImage({
                  url: 'https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//digital-cert.jpg',
                  alt: 'Digital certificates',
                  description: 'Issue professional certificates using a drag-and-drop editor with HTML and visual preview sync. Maintain comprehensive audit trails, and support A4 printing in both landscape and portrait formats.'
                });
              }}
            >
              <div className="h-full">
                {/* Image Section */}
                <div className="h-48 relative overflow-hidden bg-white">
                  <div className="absolute inset-0 group-hover:bg-gradient-to-br group-hover:from-red-600 group-hover:to-red-800 transition-all duration-500">
                    <img 
                      src="https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//digital-cert.jpg" 
                      alt="Digital certificates"
                      className="absolute inset-0 w-full h-full object-cover group-hover:opacity-30 transition-opacity duration-500 z-10"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = 'flex';
                      }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <Award className="h-16 w-16 text-white/80 group-hover:scale-110 transition-transform duration-500" />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    {/* Certificate elements on hover */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="absolute top-4 left-4 w-6 h-4 border-2 border-white/40 rounded animate-pulse"></div>
                      <div className="absolute bottom-4 right-4 w-4 h-4 border-2 border-white/50 rounded-full animate-pulse delay-200"></div>
                      <Sparkles className="absolute top-1/3 right-6 h-4 w-4 text-white/60 animate-pulse delay-100" />
                    </div>
                  </div>
                </div>

                {/* Content Section */}
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center group-hover:bg-red-200 transition-colors duration-300">
                        <Award className="h-5 w-5 text-red-600" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 group-hover:text-red-700 transition-colors duration-300">Digital Certificates</h3>
                    </div>
                    <p className="text-gray-600 leading-relaxed">
                      Create professional certificates with drag-and-drop tools, audit trails, and A4 print support in any orientation.
                    </p>
                    <div className="flex items-center text-red-600 font-medium group-hover:text-red-700 transition-colors duration-300">
                      <span>Learn more</span>
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* API Integration Feature */}
            <div 
              className="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden transform hover:-translate-y-2 cursor-pointer"
              onClick={() => {
                setModalImage({
                  url: 'https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//api-integration.jpg',
                  alt: 'API integration',
                  description: 'Connect seamlessly with your existing tools using our robust API. Build custom integrations with webhooks and access developer-friendly documentation for fast, reliable implementation.'
                });
              }}
            >
              <div className="h-full">
                {/* Image Section */}
                <div className="h-48 relative overflow-hidden bg-white">
                  <div className="absolute inset-0 group-hover:bg-gradient-to-br group-hover:from-indigo-600 group-hover:to-indigo-800 transition-all duration-500">
                    <img 
                      src="https://bivslxeghhebmkelieue.supabase.co/storage/v1/object/public/img//api-integration.jpg" 
                      alt="API integration"
                      className="absolute inset-0 w-full h-full object-cover group-hover:opacity-30 transition-opacity duration-500 z-10"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const fallback = target.nextElementSibling as HTMLElement;
                        if (fallback) fallback.style.display = 'flex';
                      }}
                    />
                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <Zap className="h-16 w-16 text-white/80 group-hover:scale-110 transition-transform duration-500" />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    {/* API connection elements on hover */}
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="absolute top-4 left-4 w-2 h-2 bg-white/60 rounded-full animate-ping"></div>
                      <div className="absolute bottom-4 right-4 w-2 h-2 bg-white/40 rounded-full animate-pulse delay-300"></div>
                      <div className="absolute top-1/2 left-6 w-1 h-6 bg-white/30 rounded animate-pulse delay-150"></div>
                      <div className="absolute top-1/2 right-6 w-1 h-4 bg-white/40 rounded animate-pulse delay-75"></div>
                    </div>
                  </div>
                </div>

                {/* Content Section */}
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center group-hover:bg-indigo-200 transition-colors duration-300">
                        <Zap className="h-5 w-5 text-indigo-600" />
                      </div>
                      <h3 className="text-xl font-bold text-gray-900 group-hover:text-indigo-700 transition-colors duration-300">API Integration</h3>
                    </div>
                    <p className="text-gray-600 leading-relaxed">
                    Integrate with your tools using our API, webhooks, and clear developer docs.
                    </p>
                    <div className="flex items-center text-indigo-600 font-medium group-hover:text-indigo-700 transition-colors duration-300">
                      <span>Learn more</span>
                      <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Trust Indicators - Corporate Design */}
          <div className="mx-auto max-w-7xl bg-white rounded-2xl p-8 md:p-12 shadow-lg border border-gray-100">
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center px-4 py-2 bg-indigo-50 rounded-full mb-4">
                <Shield className="h-5 w-5 text-indigo-600 mr-2" />
                <span className="text-sm font-medium text-indigo-700">TRUSTED PLATFORM</span>
              </div>
              <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                Trusted by Leading Organizations
              </h3>
              <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                Join thousands of successful event organizers who trust our enterprise-grade platform for their most important events.
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Rating Card */}
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                <div className="flex items-center justify-center w-12 h-12 bg-yellow-50 rounded-lg mb-4 mx-auto">
                  <Star className="h-6 w-6 text-yellow-500 fill-current" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-1">4.9/5</div>
                <div className="text-sm font-medium text-gray-600 mb-2">Customer Rating</div>
                <div className="flex justify-center space-x-0.5">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-2">Based on 2,500+ reviews</p>
              </div>

              {/* Growth Card */}
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                <div className="flex items-center justify-center w-12 h-12 bg-green-50 rounded-lg mb-4 mx-auto">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-1">150%</div>
                <div className="text-sm font-medium text-gray-600 mb-2">Yearly Growth</div>
                <div className="h-1 bg-gray-100 rounded-full overflow-hidden">
                  <div className="h-full bg-gradient-to-r from-green-400 to-emerald-500 w-3/4"></div>
                </div>
                <p className="text-xs text-gray-500 mt-2">Consistent performance</p>
              </div>

              {/* Support Card */}
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                <div className="flex items-center justify-center w-12 h-12 bg-blue-50 rounded-lg mb-4 mx-auto">
                  <CheckCircle className="h-6 w-6 text-blue-600" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-1">24/7</div>
                <div className="text-sm font-medium text-gray-600 mb-2">Dedicated Support</div>
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-xs font-medium text-green-600">Live chat available</span>
                </div>
                <p className="text-xs text-gray-500 mt-2">Average response time: 2 min</p>
              </div>

              {/* Security Card */}
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow duration-300">
                <div className="flex items-center justify-center w-12 h-12 bg-purple-50 rounded-lg mb-4 mx-auto">
                  <Shield className="h-6 w-6 text-purple-600" />
                </div>
                <div className="flex flex-col items-center">
                  <div className="text-3xl font-bold text-gray-900 mb-1">ISO 27001</div>
                  <div className="text-sm font-medium text-gray-600 mb-3">Certified Security</div>
                  <div className="flex space-x-2">
                    <div className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">GDPR</div>
                    <div className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">SOC 2</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Client Logos
            <div className="mt-16">
              <p className="text-center text-sm font-medium text-gray-500 mb-6">TRUSTED BY INNOVATIVE TEAMS WORLDWIDE</p>
              <div className="grid grid-cols-2 md:grid-cols-6 gap-8 items-center opacity-70">
                {['microsoft', 'google', 'airbnb', 'spotify', 'amazon', 'netflix'].map((company) => (
                  <div key={company} className="h-8 flex items-center justify-center grayscale hover:grayscale-0 transition-all duration-300">
                    <img 
                      src={`/logos/${company}.svg`} 
                      alt={company} 
                      className="h-6 object-contain"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                  </div>
                ))}
              </div>
            </div> */}
          </div>
        </div>
      </section>
      {/* Image Modal */}
      {modalImage && (
        <ImageModal
          isOpen={true}
          onClose={() => setModalImage(null)}
          imageUrl={modalImage?.url || ''}
          alt={modalImage?.alt || 'Modal image'}
          description={modalImage?.description || ''}
        />
      )}
      </PageLayout>
    </>
  )
}
