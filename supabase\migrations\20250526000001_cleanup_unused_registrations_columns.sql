-- Clean up unused columns in registrations table
-- This migration removes columns that are no longer being used

-- First, let's check if there are any non-empty participants arrays
-- If all are empty, we can safely remove this column
DO $$
DECLARE
    non_empty_participants_count INTEGER;
BEGIN
    -- Count registrations with non-empty participants arrays
    SELECT COUNT(*) INTO non_empty_participants_count
    FROM registrations 
    WHERE participants IS NOT NULL 
    AND participants != '[]'::jsonb 
    AND jsonb_array_length(participants) > 0;
    
    -- If no registrations have actual participant data, drop the column
    IF non_empty_participants_count = 0 THEN
        -- Drop the participants column as it's unused (each participant gets their own row)
        ALTER TABLE registrations DROP COLUMN IF EXISTS participants;
        RAISE NOTICE 'Dropped unused participants column (% non-empty records found)', non_empty_participants_count;
    ELSE
        RAISE NOTICE 'Keeping participants column (% non-empty records found)', non_empty_participants_count;
    END IF;
END $$;

-- Add comments to clarify the purpose of remaining columns
COMMENT ON COLUMN registrations.payment_id IS 'Legacy payment ID field - use transaction_id for new records';
COMMENT ON COLUMN registrations.transaction_id IS 'Current transaction reference - links to transactions table';
COMMENT ON COLUMN registrations.ticket_type IS 'Type of ticket selected (Early Bird, Standard, VIP, etc.)';
COMMENT ON COLUMN registrations.group_registration_id IS 'Groups multiple registrations created together in a single transaction';

-- Ensure proper indexes exist for performance
CREATE INDEX IF NOT EXISTS idx_registrations_ticket_type ON registrations(ticket_type);
CREATE INDEX IF NOT EXISTS idx_registrations_payment_status ON registrations(payment_status);
CREATE INDEX IF NOT EXISTS idx_registrations_status ON registrations(status);

-- Add constraint to ensure ticket_type is not empty
ALTER TABLE registrations ADD CONSTRAINT chk_ticket_type_not_empty 
CHECK (ticket_type IS NOT NULL AND LENGTH(TRIM(ticket_type)) > 0);

-- Update any NULL or empty ticket_type values to 'Standard'
UPDATE registrations 
SET ticket_type = 'Standard' 
WHERE ticket_type IS NULL OR TRIM(ticket_type) = '';
