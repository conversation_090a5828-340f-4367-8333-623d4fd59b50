-- Create roles table
CREATE TABLE IF NOT EXISTS roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  permissions JSONB DEFAULT '{}'::JSON<PERSON>,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default roles
INSERT INTO roles (name, description, permissions) VALUES
('free', 'Free tier user with basic access', '{"events": {"view": true, "create": false}, "tickets": {"view": true, "purchase": true}}'::JSONB),
('paid', 'Paid subscriber with additional features', '{"events": {"view": true, "create": true, "edit": true}, "tickets": {"view": true, "purchase": true}}'::JSONB),
('manager', 'Event manager with extended permissions', '{"events": {"view": true, "create": true, "edit": true, "delete": true}, "tickets": {"view": true, "purchase": true, "manage": true}, "reports": {"view": true}}'::JSONB),
('admin', 'Administrator with full system access', '{"events": {"view": true, "create": true, "edit": true, "delete": true}, "tickets": {"view": true, "purchase": true, "manage": true}, "users": {"view": true, "create": true, "edit": true, "delete": true}, "reports": {"view": true, "export": true}, "settings": {"view": true, "edit": true}, "roles": {"view": true, "create": true, "edit": true, "delete": true}}'::JSONB)
ON CONFLICT (name) DO UPDATE
SET 
  description = EXCLUDED.description,
  permissions = EXCLUDED.permissions,
  updated_at = NOW();

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_roles_name ON roles(name);

-- Enable Row Level Security
ALTER TABLE roles ENABLE ROW LEVEL SECURITY;

-- Create policies for roles table
-- Only admins can manage roles
CREATE POLICY "Enable admin access to roles"
ON roles
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM users
    WHERE users.id = auth.uid()
    AND users.role = 'admin'
  )
);

-- Everyone can view roles
CREATE POLICY "Enable view access to roles for all users"
ON roles
FOR SELECT
USING (true);

-- Update existing users to link to the appropriate role_id
-- First, make sure all users have a role value
UPDATE users SET role = 'free' WHERE role IS NULL;

-- Then update the role_id based on the role name
UPDATE users 
SET role_id = roles.id
FROM roles
WHERE users.role = roles.name;
