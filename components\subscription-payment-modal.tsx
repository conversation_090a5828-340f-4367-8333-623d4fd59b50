"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Loader2, CreditCard, Check, AlertCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"

type SubscriptionPlan = {
  id: string
  name: string
  price: number
  description: string
  features: string[]
  is_popular?: boolean
  max_events?: number | null
  max_attendees_per_event?: number | null
}

type PaymentGateway = {
  id: string
  name: string
  type: string
  enabled: boolean
  description?: string
}

interface SubscriptionPaymentModalProps {
  isOpen: boolean
  onClose: () => void
  plan: SubscriptionPlan
}

export default function SubscriptionPaymentModal({
  isO<PERSON>,
  onClose,
  plan
}: SubscriptionPaymentModalProps) {
  const [paymentGateways, setPaymentGateways] = useState<PaymentGateway[]>([])
  const [selectedGateway, setSelectedGateway] = useState<string>("")
  const [isLoading, setIsLoading] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string>("")
  const { user } = useAuth()
  const { toast } = useToast()

  // Fetch available payment gateways
  useEffect(() => {
    if (isOpen) {
      fetchPaymentGateways()
    }
  }, [isOpen])

  const fetchPaymentGateways = async () => {
    setIsLoading(true)
    setError("")
    try {
      const response = await fetch('/api/admin/payment-gateways/list')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch payment gateways')
      }

      const enabledGateways = data.gateways.filter((gateway: PaymentGateway) => gateway.enabled)
      setPaymentGateways(enabledGateways)

      if (enabledGateways.length > 0) {
        setSelectedGateway(enabledGateways[0].id)
      }
    } catch (error: any) {
      console.error('Error fetching payment gateways:', error)
      setError('Failed to load payment options. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePayment = async () => {
    if (!selectedGateway) {
      toast({
        title: "Error",
        description: "Please select a payment method",
        variant: "destructive",
      })
      return
    }

    if (!user) {
      toast({
        title: "Error",
        description: "Please log in to continue",
        variant: "destructive",
      })
      return
    }

    setIsProcessing(true)
    setError("")

    try {
      // Create subscription and payment
      const response = await fetch('/api/subscriptions/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId: plan.id,
          planName: plan.name,
          price: plan.price,
          paymentGatewayId: selectedGateway,
        }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create subscription')
      }

      if (data.paymentUrl) {
        // Redirect to payment gateway
        window.location.href = data.paymentUrl
      } else {
        // Handle successful subscription (for free plans or direct processing)
        toast({
          title: "Success",
          description: `Successfully subscribed to ${plan.name} plan!`,
        })
        onClose()
        window.location.reload()
      }
    } catch (error: any) {
      console.error('Error processing payment:', error)
      setError(error.message || 'Failed to process payment. Please try again.')
      toast({
        title: "Error",
        description: error.message || "Failed to process payment",
        variant: "destructive",
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Subscribe to {plan.name} Plan</DialogTitle>
          <DialogDescription>
            Complete your subscription to unlock all features of the {plan.name} plan.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Plan Summary */}
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{plan.name}</CardTitle>
                {plan.is_popular && (
                  <Badge className="bg-primary text-primary-foreground">Popular</Badge>
                )}
              </div>
              <div className="flex items-baseline">
                <span className="text-2xl font-bold">RM{plan.price.toFixed(2)}</span>
                <span className="text-muted-foreground ml-1">/month</span>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground mb-3">{plan.description}</p>
                <div className="grid grid-cols-1 gap-1">
                  {plan.features.slice(0, 3).map((feature, index) => (
                    <div key={index} className="flex items-center text-sm">
                      <Check className="mr-2 h-3 w-3 text-primary" />
                      <span>{feature}</span>
                    </div>
                  ))}
                  {plan.features.length > 3 && (
                    <div className="text-sm text-muted-foreground">
                      +{plan.features.length - 3} more features
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Payment Gateway Selection */}
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading payment options...</span>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center py-8 text-destructive">
              <AlertCircle className="h-5 w-5 mr-2" />
              <span>{error}</span>
            </div>
          ) : paymentGateways.length > 0 ? (
            <div>
              <h4 className="font-medium mb-3">Select Payment Method</h4>
              <div className="space-y-2">
                {paymentGateways.map((gateway) => (
                  <Card
                    key={gateway.id}
                    className={`cursor-pointer transition-colors ${
                      selectedGateway === gateway.id
                        ? 'border-primary bg-primary/5'
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={() => setSelectedGateway(gateway.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center">
                        <div className={`w-4 h-4 rounded-full border-2 mr-3 ${
                          selectedGateway === gateway.id
                            ? 'border-primary bg-primary'
                            : 'border-muted-foreground'
                        }`}>
                          {selectedGateway === gateway.id && (
                            <div className="w-full h-full rounded-full bg-white scale-50"></div>
                          )}
                        </div>
                        <div>
                          <div className="font-medium">{gateway.name}</div>
                          {gateway.description && (
                            <div className="text-sm text-muted-foreground">{gateway.description}</div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              <AlertCircle className="h-8 w-8 mx-auto mb-2" />
              <p>No payment methods available</p>
              <p className="text-sm">Please contact support for assistance</p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isProcessing}>
            Cancel
          </Button>
          <Button
            onClick={handlePayment}
            disabled={isProcessing || !selectedGateway || paymentGateways.length === 0}
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                Pay RM{plan.price.toFixed(2)}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
