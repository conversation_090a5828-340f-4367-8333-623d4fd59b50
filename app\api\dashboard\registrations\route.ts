import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { verifyJWTToken } from "@/lib/auth"

/**
 * GET /api/dashboard/registrations
 * Fetches registrations for events owned by the authenticated user
 * Requires authentication
 */
export async function GET(request: Request) {
  try {
    console.log("Dashboard Registrations API: Starting request")

    // Get the authorization header
    const authHeader = request.headers.get("Authorization")
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid Authorization header" },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7) // Remove "Bearer " prefix

    // Verify the JWT token and get user data
    const authResult = await verifyJWTToken(token)
    if (!authResult || !authResult.user) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 }
      )
    }

    const userId = authResult.user.id
    console.log("Dashboard Registrations API: Fetching registrations for user events:", userId)

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin()

    // Fetch registrations for events owned by the user
    const { data, error } = await supabaseAdmin
      .from("registrations")
      .select(`
        *,
        events!inner(
          id,
          title,
          slug,
          start_date,
          end_date,
          location,
          image_url,
          price,
          is_published,
          event_manager_id
        )
      `)
      .eq("events.event_manager_id", userId)
      .order("created_at", { ascending: false })

    if (error) {
      console.error("Error fetching dashboard registrations:", error)
      return NextResponse.json(
        { error: "Failed to fetch registrations" },
        { status: 500 }
      )
    }

    // Transform the data to include event information properly
    const registrationsWithEvents = (data || []).map(registration => ({
      ...registration,
      event: registration.events,
      events: undefined // Remove the nested events object to clean up the response
    }))

    console.log(`Dashboard Registrations API: Successfully fetched ${registrationsWithEvents.length} registrations`)

    return NextResponse.json({
      registrations: registrationsWithEvents,
      count: registrationsWithEvents.length
    })
  } catch (error: any) {
    console.error("Error in dashboard registrations API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
