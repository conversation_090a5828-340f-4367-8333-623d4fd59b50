import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import { verifyJWTToken } from "@/lib/auth";

/**
 * GET /api/dashboard/events
 * Fetches events for the authenticated user's dashboard
 * Requires authentication
 */
export async function GET(request: Request) {
  try {
    console.log("Dashboard Events API: Starting request");

    // Get the authorization header
    const authHeader = request.headers.get("Authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid Authorization header" },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7); // Remove "Bearer " prefix

    // Verify the JWT token and get user data
    const authResult = await verifyJWTToken(token);
    if (!authResult || !authResult.user) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 }
      );
    }

    const userId = authResult.user.id;
    console.log("Dashboard Events API: Fetching events for user:", userId);

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin();

    // Fetch events created by the user with registration count, organizer info, and category info
    const { data, error } = await supabaseAdmin
      .from("events")
      .select(`
        *,
        registrations(id, status),
        organizations:organization_id(id, name),
        event_category:category_id(id, name, color, icon)
      `)
      .eq("event_manager_id", userId)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching dashboard events:", error);
      return NextResponse.json(
        { error: "Failed to fetch events" },
        { status: 500 }
      );
    }

    // Transform the data to include computed fields
    const eventsWithStatus = (data || []).map(event => {
      // Calculate status based on dates and is_published
      let status = "draft";
      if (event.is_published) {
        const now = new Date();
        const startDate = new Date(event.start_date);
        const endDate = new Date(event.end_date);

        if (endDate < now) {
          status = "completed";
        } else if (startDate <= now && endDate >= now) {
          status = "ongoing";
        } else {
          status = "published";
        }
      }

      // Get registration count - only count active registrations (registered and confirmed)
      const current_participants = Array.isArray(event.registrations)
        ? event.registrations.filter(reg => reg.status === 'registered' || reg.status === 'confirmed').length
        : 0;

      return {
        ...event,
        status,
        current_participants,
        // Remove the registrations array as we only needed the count
        registrations: undefined
      };
    });

    console.log(`Dashboard Events API: Successfully fetched ${eventsWithStatus.length} events`);

    return NextResponse.json({
      events: eventsWithStatus,
      count: eventsWithStatus.length
    });
  } catch (error: any) {
    console.error("Error in dashboard events API:", error);
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
