"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { CheckCircle, XCircle, Shield, Calendar, MapPin, User, Award, Clock } from "lucide-react"
import { QRCodeSVG } from "qrcode.react"

import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"

import { CertificateAuditTrail } from "@/components/certificate-audit-trail"

interface CertificateVerification {
  verified: boolean
  certificate: {
    id: string
    participant_name: string
    issued_at: string
    verification_code: string
    is_valid: boolean
  }
  event: {
    title: string
    start_date: string
    end_date: string
    location: string
  }
  organizer: string
}

export default function CertificateVerificationPage() {
  const params = useParams()
  const code = params.code as string
  const [certificate, setCertificate] = useState<CertificateVerification | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()

  // Set page title
  useEffect(() => {
    document.title = "Certificate Verification | mTicket.my - Event Management Platform"
  }, [])

  useEffect(() => {
    const verifyCertificate = async () => {
      if (!code) return

      setLoading(true)
      setError(null)

      try {
        const response = await fetch(`/api/certificates/verify?code=${encodeURIComponent(code)}`)
        const data = await response.json()

        if (!response.ok) {
          throw new Error(data.error || "Failed to verify certificate")
        }

        setCertificate(data)
      } catch (err: any) {
        console.error("Error verifying certificate:", err)
        setError(err.message || "Failed to verify certificate")
        toast({
          title: "Verification Failed",
          description: err.message || "Failed to verify certificate",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    verifyCertificate()
  }, [code, toast])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent mb-4"></div>
            <p className="text-muted-foreground">Verifying certificate...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error || !certificate) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
              <XCircle className="h-8 w-8 text-red-600" />
            </div>
            <CardTitle className="text-red-900">Certificate Not Found</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-muted-foreground mb-4">
              {error || "The certificate you're trying to verify could not be found or may have been revoked."}
            </p>
            <p className="text-sm text-muted-foreground">
              Verification Code: <code className="bg-gray-100 px-2 py-1 rounded">{code}</code>
            </p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center p-4">
      <div className="w-full max-w-4xl space-y-6">
        {/* Verification Status */}
        <Card className="border-green-200 bg-green-50">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <CardTitle className="text-green-900">Certificate Verified</CardTitle>
            <p className="text-green-700">This certificate is authentic and valid</p>
          </CardHeader>
        </Card>

        {/* Certificate Details */}
        <div className="grid gap-6 md:grid-cols-2">
          {/* Main Certificate Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Certificate Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Recipient</label>
                <p className="text-lg font-semibold">{certificate.certificate.participant_name}</p>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">Event</label>
                <p className="text-lg font-semibold">{certificate.event.title}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Event Date</label>
                  <p className="flex items-center gap-1 text-sm">
                    <Calendar className="h-4 w-4" />
                    {new Date(certificate.event.start_date).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Issue Date</label>
                  <p className="text-sm">
                    {new Date(certificate.certificate.issued_at).toLocaleDateString()}
                  </p>
                </div>
              </div>

              {certificate.event.location && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Location</label>
                  <p className="flex items-center gap-1 text-sm">
                    <MapPin className="h-4 w-4" />
                    {certificate.event.location}
                  </p>
                </div>
              )}

              {certificate.organizer && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Organizer</label>
                  <p className="flex items-center gap-1 text-sm">
                    <User className="h-4 w-4" />
                    {certificate.organizer}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Verification Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Verification Info
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className="mx-auto mb-4 w-32 h-32 bg-white p-2 rounded-lg border">
                  <QRCodeSVG
                    value={`https://mticket.my/certificates/verify/${certificate.certificate.verification_code}`}
                    size={112}
                    level="M"
                    includeMargin={false}
                  />
                </div>
                <p className="text-sm text-muted-foreground">Scan to verify</p>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">Verification Code</label>
                <code className="block bg-gray-100 px-3 py-2 rounded text-sm font-mono">
                  {certificate.certificate.verification_code}
                </code>
              </div>

              <div>
                <label className="text-sm font-medium text-muted-foreground">Certificate ID</label>
                <code className="block bg-gray-100 px-3 py-2 rounded text-xs font-mono break-all">
                  {certificate.certificate.id}
                </code>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-muted-foreground">Status</span>
                <Badge variant={certificate.certificate.is_valid ? "default" : "destructive"}>
                  {certificate.certificate.is_valid ? "Valid" : "Invalid"}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Audit Trail */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Complete Audit Trail
            </CardTitle>
            <CardDescription>
              Complete journey from registration to certificate verification - comprehensive audit trail with foreign key relationships
            </CardDescription>
          </CardHeader>
          <CardContent>
            <CertificateAuditTrail certificateId={certificate.certificate.id} />
          </CardContent>
        </Card>





        {/* Footer */}
        <Card>
          <CardContent className="text-center py-6">
            <p className="text-sm text-muted-foreground mb-2">
              This certificate was issued by mTicket.my Event Management Platform
            </p>
            <p className="text-xs text-muted-foreground">
              Verified on {new Date().toLocaleDateString()} at {new Date().toLocaleTimeString()}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
