import { NextRequest, NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { getJWTTokenFromRequest, verifyJWTToken } from "@/lib/auth"

/**
 * GET /api/dashboard/group-receipt
 * Fetches group receipt data for a group registration
 * Requires authentication
 */
export async function GET(request: NextRequest) {
  try {
    console.log("Group Receipt API: Starting request");

    // Get JWT token from request
    const token = getJWTTokenFromRequest(request);
    console.log("Group Receipt API: Token present:", !!token);

    if (!token) {
      return NextResponse.json(
        { error: "Missing or invalid Authorization header" },
        { status: 401 }
      );
    }

    // Verify the JWT token and get user data
    const authResult = await verifyJWTToken(token);
    if (!authResult || !authResult.user) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 }
      );
    }

    const userId = authResult.user.id;
    console.log("Group Receipt API: User ID:", userId);

    // Get group ID from query parameters
    const { searchParams } = new URL(request.url);
    const groupId = searchParams.get('groupId');

    if (!groupId) {
      return NextResponse.json(
        { error: "Group ID is required" },
        { status: 400 }
      );
    }

    console.log("Group Receipt API: Group ID:", groupId);

    // Get Supabase admin client
    const supabaseAdmin = getSupabaseAdmin();

    // Fetch all registrations in the group with transaction details
    console.log("Group Receipt API: Querying with:", {
      groupId,
      userId,
      query: `group_registration_id.eq.${groupId} AND (user_id.eq.${userId} OR created_by.eq.${userId})`
    });

    const { data: groupRegistrations, error: groupError } = await supabaseAdmin
      .from("registrations")
      .select(`
        *,
        event:event_id (
          id,
          title,
          slug,
          description,
          location,
          start_date,
          end_date,
          image_url,
          price
        ),
        transaction:transaction_id (
          id,
          status,
          amount,
          currency,
          gateway_transaction_id,
          invoice_number,
          receipt_number,
          group_transaction_id,
          processed_at,
          created_at
        )
      `)
      .eq("group_registration_id", groupId)
      .or(`user_id.eq.${userId},created_by.eq.${userId}`)
      .order("attendee_name", { ascending: true });

    console.log("Group Receipt API: Query result:", {
      error: groupError,
      count: groupRegistrations?.length || 0,
      firstRegistration: groupRegistrations?.[0] ? {
        id: groupRegistrations[0].id,
        user_id: groupRegistrations[0].user_id,
        created_by: groupRegistrations[0].created_by,
        group_registration_id: groupRegistrations[0].group_registration_id,
        attendee_name: groupRegistrations[0].attendee_name
      } : null
    });

    if (groupError) {
      console.error("Error fetching group registrations:", groupError);
      return NextResponse.json(
        { error: "Failed to fetch group registrations" },
        { status: 500 }
      );
    }

    if (!groupRegistrations || groupRegistrations.length === 0) {
      return NextResponse.json(
        { error: "Group registration not found or access denied" },
        { status: 404 }
      );
    }

    console.log(`Group Receipt API: Found ${groupRegistrations.length} registrations in group`);

    // Get the main group transaction (the one with group_transaction_id pointing to itself)
    const mainTransaction = groupRegistrations.find(reg =>
      reg.transaction?.group_transaction_id === reg.transaction?.id
    )?.transaction;

    // Calculate totals
    const totalAmount = groupRegistrations.reduce((sum, reg) =>
      sum + parseFloat(reg.payment_amount || '0'), 0
    );

    // For now, we'll calculate fees as 0 since we removed the financial_transaction join
    // In the future, this could be calculated from the transaction data or a separate fees table
    const totalFees = 0;

    // Prepare response data
    const responseData = {
      success: true,
      group: {
        id: groupId,
        event: groupRegistrations[0]?.event,
        mainTransaction,
        totalAmount,
        totalFees,
        participantCount: groupRegistrations.length,
        createdAt: groupRegistrations[0]?.created_at,
        paymentDate: groupRegistrations[0]?.payment_date,
      },
      participants: groupRegistrations.map(reg => ({
        id: reg.id,
        attendee_name: reg.attendee_name,
        attendee_email: reg.attendee_email,
        attendee_phone: reg.attendee_phone,
        ic_reg: reg.ic_reg,
        payment_amount: reg.payment_amount,
        payment_status: reg.payment_status,
        transaction: reg.transaction,
        // Note: financial_transaction removed due to schema issues
        // Fee information could be added from transaction data in the future
      }))
    };

    console.log("Group Receipt API: Response prepared successfully");

    return NextResponse.json(responseData);

  } catch (error) {
    console.error("Group Receipt API: Unexpected error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
