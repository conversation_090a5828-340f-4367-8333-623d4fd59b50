import jwt from 'jsonwebtoken'
import type { TokenPayload, User, AuthVerificationResult } from './types'
import { getSupabaseAdmin } from '../supabase'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d'

/**
 * Generate JWT token for user
 */
export function generateJWTToken(user: User): string {
  const payload: Omit<TokenPayload, 'iat' | 'exp'> = {
    userId: user.id,
    email: user.email,
    role: user.user_roles?.role_name || 'user'
  }

  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })
}

/**
 * Verify JWT token and return decoded payload
 */
export function verifyJWTToken(token: string): TokenPayload | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as TokenPayload
    return decoded
  } catch (error) {
    console.error('JWT verification failed:', error)
    return null
  }
}

/**
 * Extract JWT token from request headers or cookies
 */
export function getJWTTokenFromRequest(request: Request): string | null {
  // Try Authorization header first
  const authHeader = request.headers.get('authorization')
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }

  // Try cookies
  const cookieHeader = request.headers.get('Cookie')
  if (cookieHeader) {
    const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=')
      acc[key] = value
      return acc
    }, {} as Record<string, string>)

    return cookies.auth_token || null
  }

  return null
}

/**
 * Verify JWT token and get user data from database
 */
export async function verifyJWTTokenWithUser(token: string): Promise<{ user: User; error?: string } | null> {
  try {
    if (!token) {
      return null
    }

    // Verify JWT token
    const decoded = verifyJWTToken(token)
    if (!decoded || !decoded.userId) {
      return null
    }

    // Get fresh user data from database
    const supabaseAdmin = getSupabaseAdmin()
    const { data: userData, error: userError } = await supabaseAdmin
      .from("users")
      .select(`
        *,
        user_roles!role_id (
          id,
          role_name,
          description
        )
      `)
      .eq("id", decoded.userId)
      .single()

    if (userError || !userData) {
      return null
    }

    return { user: userData }
  } catch (error: any) {
    console.error('Error verifying JWT token with user:', error)
    return { user: null as any, error: error.message }
  }
}

/**
 * Verify authentication and get user permissions
 */
export async function verifyAuthToken(request: Request): Promise<AuthVerificationResult> {
  try {
    const token = getJWTTokenFromRequest(request)

    if (!token) {
      return {
        isAuthenticated: false,
        isAdmin: false,
        isManager: false,
        isElevated: false,
        error: "No token provided"
      }
    }

    const decoded = verifyJWTToken(token)
    if (!decoded) {
      return {
        isAuthenticated: false,
        isAdmin: false,
        isManager: false,
        isElevated: false,
        error: "Invalid token"
      }
    }

    // Get user details with role from database
    const supabaseAdmin = getSupabaseAdmin()
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .select(`
        id,
        email,
        full_name,
        role_id,
        user_roles!inner(role_name)
      `)
      .eq('id', decoded.userId)
      .single()

    if (userError || !userData) {
      return {
        isAuthenticated: false,
        isAdmin: false,
        isManager: false,
        isElevated: false,
        error: "User not found"
      }
    }

    const roleName = userData.user_roles?.role_name
    const isAdmin = roleName === 'admin'
    const isManager = ['manager', 'supermanager'].includes(roleName || '')
    const isElevated = ['admin', 'manager', 'supermanager', 'event_admin'].includes(roleName || '')

    return {
      isAuthenticated: true,
      isAdmin,
      isManager,
      isElevated,
      user: userData
    }
  } catch (error: any) {
    console.error('Error verifying auth token:', error)
    return {
      isAuthenticated: false,
      isAdmin: false,
      isManager: false,
      isElevated: false,
      error: error.message
    }
  }
}

/**
 * Refresh JWT token if it's close to expiry
 */
export function refreshTokenIfNeeded(token: string): string | null {
  try {
    const decoded = verifyJWTToken(token)
    if (!decoded) return null

    // Check if token expires within 1 hour
    const now = Math.floor(Date.now() / 1000)
    const timeUntilExpiry = decoded.exp - now

    if (timeUntilExpiry < 3600) { // 1 hour
      // Token needs refresh - would need user data to regenerate
      // This is a placeholder for refresh logic
      return null
    }

    return token
  } catch (error) {
    console.error('Error checking token refresh:', error)
    return null
  }
}
