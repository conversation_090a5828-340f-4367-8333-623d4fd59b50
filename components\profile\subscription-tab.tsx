"use client"

import type React from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/contexts/auth-context"

export function SubscriptionTab() {
  const { user } = useAuth()

  return (
    <Card>
      <CardHeader>
        <CardTitle>Subscription Details</CardTitle>
        <CardDescription>Manage your subscription plan and billing information</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="rounded-lg border p-4">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h3 className="text-lg font-semibold">Current Plan</h3>
              <div className="flex items-center mt-2">
                <Badge variant={user?.subscription_status === "active" ? "default" : "secondary"}>
                  {user?.role_name ? user.role_name.charAt(0).toUpperCase() + user.role_name.slice(1) : "Free"}
                </Badge>
                <Badge
                  variant={
                    user?.subscription_status === "active"
                      ? "default"
                      : user?.subscription_status === "canceled"
                        ? "destructive"
                        : "outline"
                  }
                  className="ml-2"
                >
                  {user?.subscription_status ? user.subscription_status.charAt(0).toUpperCase() + user.subscription_status.slice(1) : "None"}
                </Badge>
              </div>
              {user?.subscription_end_date && user?.subscription_status === "active" && (
                <p className="text-sm text-muted-foreground mt-1">
                  Renews on {new Date(user.subscription_end_date).toLocaleDateString()}
                </p>
              )}
            </div>
            <div className="flex gap-2">
              <Button variant="outline">Change Plan</Button>
              {user?.subscription_status === "active" && (
                <Button variant="destructive">Cancel Subscription</Button>
              )}
            </div>
          </div>
        </div>

        <div className="rounded-lg border p-4">
          <h3 className="text-lg font-semibold mb-4">Payment Method</h3>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="rounded-md bg-muted p-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-6 w-6"
                >
                  <rect width="20" height="14" x="2" y="5" rx="2" />
                  <line x1="2" x2="22" y1="10" y2="10" />
                </svg>
              </div>
              <div>
                <p className="font-medium">Visa ending in 4242</p>
                <p className="text-sm text-muted-foreground">Expires 12/2025</p>
              </div>
            </div>
            <Button variant="ghost" size="sm">
              Update
            </Button>
          </div>
        </div>

        <div className="rounded-lg border p-4">
          <h3 className="text-lg font-semibold mb-4">Billing History</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Invoice #INV-001</p>
                <p className="text-sm text-muted-foreground">Nov 1, 2023</p>
              </div>
              <div className="flex items-center gap-4">
                <Badge>Paid</Badge>
                <p className="font-medium">RM 79.00</p>
                <Button variant="ghost" size="sm">
                  Download
                </Button>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Invoice #INV-002</p>
                <p className="text-sm text-muted-foreground">Dec 1, 2023</p>
              </div>
              <div className="flex items-center gap-4">
                <Badge>Paid</Badge>
                <p className="font-medium">RM 79.00</p>
                <Button variant="ghost" size="sm">
                  Download
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
