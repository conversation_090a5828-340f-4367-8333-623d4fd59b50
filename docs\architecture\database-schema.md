# Database Schema Documentation

This document provides a comprehensive overview of the database schema for the mTicket.my application.

## Database Overview

The application uses PostgreSQL with Supabase as the backend service. The schema is designed to support:
- User management with role-based access control
- Event management and registration
- Subscription and payment processing
- Certificate generation and verification
- Activity logging and audit trails
- Webhook and API management

**✅ RLS Status**: All 24 tables have Row Level Security enabled with comprehensive policies for data protection.

**🗂️ Recent Updates**:
- Removed duplicate `user_subscriptions` table (kept `user_subscription` with data)
- Enabled RLS on all tables that were missing it
- Created comprehensive RLS policies for all tables
- All tables now have proper access control based on user roles

## Entity Relationship Diagram

The following diagram shows the complete database schema with relationships between all 24 tables:

```mermaid
erDiagram
    users {
        uuid id PK
        varchar email UK
        varchar password_hash
        int role_id FK
        boolean email_verified
        timestamp created_at
        uuid organization_id FK
        text auth_token
        text reset_token
    }

    user_roles {
        serial id PK
        varchar role_name UK
        text description
        jsonb permissions
        boolean is_active
        varchar color
    }

    organizations {
        uuid id PK
        varchar name
        varchar ssm_number
        varchar pic_name
        varchar pic_email
        text address
        uuid created_by FK
        timestamp created_at
    }

    events {
        uuid id PK
        varchar title
        varchar slug UK
        text description
        timestamp start_date
        timestamp end_date
        decimal price
        int category_id FK
        uuid organization_id FK
        uuid created_by FK
        boolean is_published
    }

    event_categories {
        serial id PK
        varchar name
        text description
        varchar color
        boolean is_active
    }

    registrations {
        uuid id PK
        uuid event_id FK
        uuid user_id FK
        uuid created_by FK
        varchar attendee_name
        varchar attendee_email
        varchar payment_status
        decimal payment_amount
        uuid transaction_id FK
        boolean checked_in
        uuid certificate_id FK
    }

    transactions {
        uuid id PK
        uuid user_id FK
        uuid registration_id FK
        uuid gateway_id FK
        varchar transaction_type
        decimal amount
        varchar status
        varchar invoice_number UK
        varchar receipt_number UK
        uuid group_transaction_id
    }

    payment_gateway_settings {
        uuid id PK
        varchar name
        varchar gateway_type
        text api_key_public
        text api_key_secret
        jsonb settings
        boolean is_active
    }

    certificates {
        uuid id PK
        uuid registration_id FK
        uuid template_id FK
        varchar certificate_code UK
        varchar recipient_name
        varchar event_title
        date issue_date
        jsonb certificate_data
    }

    certificate_templates {
        uuid id PK
        varchar name
        jsonb template_data
        text background_image_url
        varchar orientation
        boolean is_default
        uuid created_by FK
    }

    subscription_plans {
        uuid id PK
        varchar name
        decimal price
        varchar billing_cycle
        jsonb features
        int max_events
        boolean certificates_enabled
    }

    user_subscription {
        uuid id PK
        uuid user_id FK
        text subscription_type
        timestamp start_date
        timestamp end_date
        boolean is_active
    }

    activity_logs {
        uuid id PK
        uuid user_id FK
        varchar action
        varchar entity_type
        varchar entity_id
        varchar category
        jsonb details
        timestamp created_at
    }

    event_teams {
        uuid id PK
        uuid event_id FK
        varchar team_name
        varchar access_token UK
        jsonb permissions
        uuid created_by FK
        boolean is_active
    }

    api_keys {
        uuid id PK
        uuid user_id FK
        varchar name
        varchar key_hash UK
        jsonb permissions
        boolean is_active
    }

    webhooks {
        uuid id PK
        uuid user_id FK
        varchar name
        text url
        text[] events
        boolean is_active
    }

    webhook_deliveries {
        uuid id PK
        uuid webhook_id FK
        varchar event_type
        jsonb payload
        int response_status
        timestamp delivered_at
    }

    system_settings {
        serial id PK
        varchar setting_key UK
        jsonb setting_value
        text description
        boolean is_public
        uuid updated_by FK
    }

    %% Relationships
    users ||--o{ user_roles : "has role"
    users ||--o| organizations : "belongs to"
    users ||--o{ events : "creates"
    users ||--o{ registrations : "registers for"
    users ||--o{ user_subscription : "has subscription"
    users ||--o{ activity_logs : "performs actions"
    users ||--o{ api_keys : "owns"
    users ||--o{ webhooks : "configures"
    users ||--o{ certificate_templates : "creates"
    users ||--o{ event_teams : "creates"
    users ||--o{ organizations : "creates"

    organizations ||--o{ events : "hosts"

    events ||--o{ event_categories : "belongs to category"
    events ||--o{ registrations : "has registrations"
    events ||--o{ event_teams : "has teams"

    registrations ||--o| transactions : "has transaction"
    registrations ||--o| certificates : "generates certificate"

    transactions ||--o{ payment_gateway_settings : "uses gateway"

    certificates ||--o{ certificate_templates : "uses template"

    user_subscription ||--o{ subscription_plans : "follows plan"

    webhooks ||--o{ webhook_deliveries : "has deliveries"

    system_settings ||--o{ users : "updated by"
```

## Core Tables

### Users Table (`users`)
**RLS Status**: ✅ Enabled (8 policies)

```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  name VARCHAR(255) NOT NULL,
  role_id INTEGER REFERENCES user_roles(id),
  email_verified BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_login TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT TRUE,
  profile_image_url TEXT,
  phone VARCHAR(20),
  bio TEXT,
  organization_id UUID REFERENCES organizations(id),
  auth_token TEXT,
  auth_token_expiry TIMESTAMP WITH TIME ZONE,
  reset_token TEXT,
  reset_token_expiry TIMESTAMP WITH TIME ZONE
);
```

**Recent Updates**: Added auth token management (`auth_token`, `auth_token_expiry`), password reset functionality (`reset_token`, `reset_token_expiry`), and profile enhancement (`bio` field).

### User Roles Table (`user_roles`)
```sql
CREATE TABLE user_roles (
  id SERIAL PRIMARY KEY,
  role_name VARCHAR(50) UNIQUE NOT NULL,
  description TEXT,
  permissions JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  color VARCHAR(7) DEFAULT '#6366f1'
);
```

**The system defines exactly 5 roles:**

1. **admin** - Full system administrator with access to all systems
2. **user** - Registered users/participants
3. **manager** - Users who created organizations and can manage their own events and participants
4. **supermanager** - Same as manager but with commission access (future feature)
5. **event_admin** - Can manage all events created in the system (not just their own)

### Role Hierarchy and Permissions

```mermaid
graph TD
    subgraph "Role Hierarchy"
        Admin[admin<br/>Full System Access]
        EventAdmin[event_admin<br/>All Events Management]
        SuperManager[supermanager<br/>Manager + Commission]
        Manager[manager<br/>Organization Management]
        User[user<br/>Basic User Access]
    end

    subgraph "Permissions"
        SystemMgmt[System Management]
        UserMgmt[User Management]
        AllEvents[All Events Access]
        OwnEvents[Own Events Only]
        Commission[Commission Access]
        OrgMgmt[Organization Management]
        Registration[Event Registration]
        Tickets[Ticket Management]
    end

    Admin --> SystemMgmt
    Admin --> UserMgmt
    Admin --> AllEvents
    Admin --> Commission
    Admin --> OrgMgmt

    EventAdmin --> AllEvents
    EventAdmin --> Registration

    SuperManager --> OwnEvents
    SuperManager --> Commission
    SuperManager --> OrgMgmt

    Manager --> OwnEvents
    Manager --> OrgMgmt

    User --> Registration
    User --> Tickets

    style Admin fill:#ff6b6b
    style EventAdmin fill:#4ecdc4
    style SuperManager fill:#45b7d1
    style Manager fill:#96ceb4
    style User fill:#ffeaa7
```

### Organizations Table (`organizations`)
**RLS Status**: ✅ Enabled (3 policies)

```sql
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  ssm_number VARCHAR(50),
  pic_name VARCHAR(255),
  pic_phone VARCHAR(20),
  pic_email VARCHAR(255),
  address TEXT,
  website TEXT,
  description TEXT,
  logo_url TEXT,
  is_verified BOOLEAN DEFAULT FALSE,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Recent Updates**: Enhanced organization structure with SSM registration number, Person-in-Charge (PIC) details, and creator tracking for permission-based editing.

## Event Management

### Events Table (`events`)
```sql
CREATE TABLE events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  description TEXT,
  short_description TEXT,
  image_url TEXT,
  start_date TIMESTAMP WITH TIME ZONE NOT NULL,
  end_date TIMESTAMP WITH TIME ZONE NOT NULL,
  location TEXT,
  venue_details JSONB,
  price DECIMAL(10,2) DEFAULT 0,
  max_attendees INTEGER,
  category_id INTEGER REFERENCES event_categories(id),
  organization_id UUID REFERENCES organizations(id),
  created_by UUID REFERENCES users(id),
  is_published BOOLEAN DEFAULT FALSE,
  is_featured BOOLEAN DEFAULT FALSE,
  registration_deadline TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  metadata JSONB DEFAULT '{}'
);
```

### Event Categories Table (`event_categories`)
```sql
CREATE TABLE event_categories (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  color VARCHAR(7) DEFAULT '#6366f1',
  icon VARCHAR(50),
  is_active BOOLEAN DEFAULT TRUE,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Registrations Table (`registrations`)
```sql
CREATE TABLE registrations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id UUID REFERENCES events(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id),
  created_by UUID REFERENCES users(id) ON DELETE SET NULL,
  attendee_name VARCHAR(255) NOT NULL,
  attendee_email VARCHAR(255) NOT NULL,
  attendee_phone VARCHAR(20),
  ic_reg VARCHAR(50),                            -- IC/Registration number for attendee
  ticket_type VARCHAR(50) DEFAULT 'Standard',    -- Type of ticket (Early Bird, Standard, VIP, etc.)
  payment_status VARCHAR(20) DEFAULT 'pending',  -- pending, processing, paid, failed
  payment_id TEXT,                               -- Legacy: Transaction ID from payment gateway
  payment_amount DECIMAL(10,2) DEFAULT 0,        -- Amount paid for registration
  payment_date TIMESTAMP WITH TIME ZONE,         -- When payment was confirmed
  transaction_id UUID REFERENCES transactions(id), -- Links to transaction record
  group_registration_id UUID,                    -- Groups multiple registrations created together
  checked_in BOOLEAN DEFAULT FALSE,              -- Whether attendee has checked in
  checked_in_at TIMESTAMP WITH TIME ZONE,        -- When attendee checked in
  certificate_issued BOOLEAN DEFAULT FALSE,      -- Whether certificate has been issued
  certificate_id UUID REFERENCES certificates(id), -- Links to certificate record
  status VARCHAR(20) DEFAULT 'registered',       -- registered, confirmed, cancelled
  custom_field_responses JSONB DEFAULT '{}',     -- Responses to event custom fields
  payment_callback_data JSONB DEFAULT '{}',      -- Payment gateway callback data for debugging
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Recent Updates**:
- Added `created_by` column to support group registrations where one user can create registrations for multiple attendees
- Removed unused `participants` JSONB column (each participant now gets their own row)
- Fixed ticket type storage to properly capture selected ticket type (Early Bird, Standard, VIP, etc.)
- Added automatic payment confirmation for free events
- Added `payment_callback_data` JSONB column to store complete payment gateway callback data for debugging and verification

## Subscription Management

### Subscription Plans Table (`subscription_plans`)
**RLS Enabled**: ✅ Yes (4 policies)

```sql
CREATE TABLE subscription_plans (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  billing_cycle VARCHAR(20) DEFAULT 'monthly',
  features JSONB DEFAULT '{}',
  max_events INTEGER,
  max_attendees_per_event INTEGER,
  certificates_enabled BOOLEAN DEFAULT FALSE,
  attendances_enabled BOOLEAN DEFAULT FALSE,
  webhooks_enabled BOOLEAN DEFAULT FALSE,
  analytics_enabled BOOLEAN DEFAULT FALSE,
  reports_enabled BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### User Subscription Table (`user_subscription`)
**RLS Enabled**: ✅ Yes (4 policies)

```sql
CREATE TABLE user_subscription (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) NOT NULL,
  subscription_type TEXT NOT NULL DEFAULT 'free',
  start_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  end_date TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT TRUE,
  payment_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Note**: The duplicate `user_subscriptions` table has been removed. All subscription data is now in `user_subscription`.

## Payment Management

### Payment Gateway Settings Table (`payment_gateway_settings`)
```sql
CREATE TABLE payment_gateway_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  gateway_type VARCHAR(50) NOT NULL,
  api_key_public TEXT,
  api_key_secret TEXT,
  webhook_secret TEXT,
  settings JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT FALSE,
  is_test_mode BOOLEAN DEFAULT TRUE,
  display_order INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Transactions Table (`transactions`)
```sql
CREATE TABLE transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE SET NULL,
  registration_id UUID REFERENCES registrations(id) ON DELETE SET NULL,
  subscription_id UUID REFERENCES user_subscription(id) ON DELETE SET NULL,
  gateway_id UUID REFERENCES payment_gateway_settings(id) ON DELETE SET NULL,
  transaction_type VARCHAR(20) NOT NULL DEFAULT 'registration_payment',
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'MYR',
  status VARCHAR(20) DEFAULT 'pending',
  gateway_transaction_id VARCHAR(255),
  gateway_response JSONB DEFAULT '{}',
  invoice_number VARCHAR(50) UNIQUE,              -- Auto-generated (INV1000, INV1001, etc.)
  receipt_number VARCHAR(50) UNIQUE,              -- Auto-generated when paid (RCP1000, RCP1001, etc.)
  group_transaction_id UUID,                      -- Links multiple registrations in group payment
  processed_at TIMESTAMP WITH TIME ZONE,         -- When payment was processed
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Key Features:**
- **Auto-generated Invoice Numbers**: Every transaction gets a unique invoice number (INV1000, INV1001, etc.)
- **Auto-generated Receipt Numbers**: When payment is confirmed, a receipt number is generated (RCP1000, RCP1001, etc.)
- **Group Transaction Support**: Multiple registrations can be linked to the same transaction via `group_transaction_id`
- **Proper Status Tracking**: `pending` → `processing` → `paid` → `refunded` (if applicable)
- **Gateway Integration**: Stores gateway transaction ID and response data

## Certificate Management

### Certificate Templates Table (`certificate_templates`)
```sql
CREATE TABLE certificate_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  template_data JSONB NOT NULL,
  background_image_url TEXT, -- Stored in certificates/bg/ bucket
  background_color VARCHAR(7) DEFAULT '#ffffff', -- Hex color code
  show_frame BOOLEAN DEFAULT true, -- White frame border toggle
  html_template TEXT, -- Custom HTML template
  css_styles TEXT, -- Custom CSS styles
  fields JSONB DEFAULT '[]', -- Draggable field definitions
  orientation VARCHAR(20) DEFAULT 'landscape-150', -- 'landscape-150', 'portrait-150', 'landscape-300', 'portrait-300'
  is_default BOOLEAN DEFAULT FALSE,
  is_premium BOOLEAN DEFAULT FALSE,
  is_shared BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Storage Structure for Certificate Images
- **Background Images**: `certificates/bg/` - Certificate background images
- **Field Images**: `certificates/img/` - Logo and other field images
- **URL Format**: `https://[project].supabase.co/storage/v1/object/public/certificates/bg/[filename]`

### Certificates Table (`certificates`)
```sql
CREATE TABLE certificates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  registration_id UUID REFERENCES registrations(id) ON DELETE CASCADE,
  template_id UUID REFERENCES certificate_templates(id),
  certificate_code VARCHAR(100) UNIQUE NOT NULL,
  recipient_name VARCHAR(255) NOT NULL,
  event_title VARCHAR(255) NOT NULL,
  issue_date DATE NOT NULL,
  certificate_data JSONB,
  pdf_url TEXT,
  qr_code_url TEXT,
  is_verified BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Activity Logging

### Activity Logs Table (`activity_logs`)
```sql
CREATE TABLE activity_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  action VARCHAR(100) NOT NULL,
  entity_type VARCHAR(50) NOT NULL,
  entity_id VARCHAR(255),
  category VARCHAR(50) NOT NULL,
  details JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Team Management

### Event Teams Table (`event_teams`)
**RLS Status**: ✅ Enabled (3 policies)

```sql
CREATE TABLE event_teams (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_id UUID REFERENCES events(id) ON DELETE CASCADE NOT NULL,
  team_name VARCHAR(255) NOT NULL,
  access_token VARCHAR(255) UNIQUE NOT NULL,
  permissions JSONB DEFAULT '{"can_scan_qr": true, "can_view_attendance": true}',
  created_by UUID REFERENCES users(id) NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  expires_at TIMESTAMP WITH TIME ZONE,
  last_used_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Key Features:**
- **Event-specific Teams**: Each team belongs to a specific event
- **Access Token Authentication**: Unique tokens for team access to QR scanner
- **Granular Permissions**: JSON-based permissions for different team capabilities
- **Expiration Support**: Optional expiration dates for temporary team access
- **Activity Tracking**: Last used timestamp for monitoring team activity

## API and Webhook Management

### API Keys Table (`api_keys`)
```sql
CREATE TABLE api_keys (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  key_hash VARCHAR(255) UNIQUE NOT NULL,
  key_prefix VARCHAR(20) NOT NULL,
  permissions JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT TRUE,
  last_used_at TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Webhooks Table (`webhooks`)
```sql
CREATE TABLE webhooks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  url TEXT NOT NULL,
  events TEXT[] DEFAULT '{}',
  secret VARCHAR(255),
  is_active BOOLEAN DEFAULT TRUE,
  last_triggered_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Webhook Deliveries Table (`webhook_deliveries`)
```sql
CREATE TABLE webhook_deliveries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  webhook_id UUID REFERENCES webhooks(id) ON DELETE CASCADE,
  event_type VARCHAR(100) NOT NULL,
  payload JSONB NOT NULL,
  response_status INTEGER,
  response_body TEXT,
  delivered_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## System Configuration

### System Settings Table (`system_settings`)
```sql
CREATE TABLE system_settings (
  id SERIAL PRIMARY KEY,
  setting_key VARCHAR(100) UNIQUE NOT NULL,
  setting_value JSONB,
  description TEXT,
  is_public BOOLEAN DEFAULT FALSE,
  updated_by UUID REFERENCES users(id),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Indexes and Constraints

### Performance Indexes
```sql
-- User lookups
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role_id ON users(role_id);
CREATE INDEX idx_users_organization_id ON users(organization_id);
CREATE INDEX idx_users_auth_token ON users(auth_token);
CREATE INDEX idx_users_reset_token ON users(reset_token);

-- Event queries
CREATE INDEX idx_events_slug ON events(slug);
CREATE INDEX idx_events_category_id ON events(category_id);
CREATE INDEX idx_events_organization_id ON events(organization_id);
CREATE INDEX idx_events_published ON events(is_published);
CREATE INDEX idx_events_featured ON events(is_featured);
CREATE INDEX idx_events_start_date ON events(start_date);

-- Registration lookups
CREATE INDEX idx_registrations_event_id ON registrations(event_id);
CREATE INDEX idx_registrations_user_id ON registrations(user_id);
CREATE INDEX idx_registrations_code ON registrations(registration_code);
CREATE INDEX idx_registrations_email ON registrations(attendee_email);

-- Organization lookups
CREATE INDEX idx_organizations_name ON organizations(name);
CREATE INDEX idx_organizations_ssm_number ON organizations(ssm_number);
CREATE INDEX idx_organizations_created_by ON organizations(created_by);

-- Activity logs
CREATE INDEX idx_activity_logs_user_id ON activity_logs(user_id);
CREATE INDEX idx_activity_logs_action ON activity_logs(action);
CREATE INDEX idx_activity_logs_created_at ON activity_logs(created_at);

-- API keys
CREATE INDEX idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX idx_api_keys_hash ON api_keys(key_hash);

-- Certificates
CREATE INDEX idx_certificates_code ON certificates(certificate_code);
CREATE INDEX idx_certificates_registration_id ON certificates(registration_id);

-- Event teams
CREATE INDEX idx_event_teams_event_id ON event_teams(event_id);
CREATE INDEX idx_event_teams_access_token ON event_teams(access_token);
CREATE INDEX idx_event_teams_created_by ON event_teams(created_by);
```

## Security Implementation

### Dynamic QR Code Security
The application implements advanced security for ticket QR codes:

#### Security Features:
- **Time-based Tokens**: QR codes contain tokens that expire every 30 seconds
- **HMAC Signatures**: SHA-256 HMAC signatures prevent forgery
- **Replay Protection**: Unique nonces prevent ticket reuse
- **Cycle Management**: 6 automatic refresh cycles, then manual refresh required
- **Check-in Control**: QR codes stop updating once user is checked in

#### Security Token Structure:
```typescript
interface SecureTicketToken {
  ticketId: string;
  eventId: string;
  attendeeName: string;
  timestamp: number;
  windowStart: number;
  windowEnd: number;
  nonce: string;
  signature: string;
}
```

#### Implementation:
- **Token Generation**: `/api/tickets/secure-qr` endpoint
- **Token Verification**: `/api/verify/secure` endpoint
- **Client Component**: `DynamicQRCode` with real-time updates
- **Security Library**: `lib/security-token.ts` with HMAC validation

### Row Level Security (RLS)

**✅ All tables have RLS enabled with comprehensive policies**

RLS policies are implemented for data security based on user roles and ownership:

#### Policy Summary by Table:
- **users** (8 policies): Users can access their own data, admins can manage all users
- **events** (5 policies): Public read for published events, role-based write access
- **registrations** (5 policies): Users see their own registrations, event managers see event registrations
- **certificates** (4 policies): Users see their own certificates, event managers manage event certificates
- **activity_logs** (2 policies): Users see their own logs, admins see all
- **organizations** (3 policies): Role-based access for organization management
- **user_roles** (3 policies): Admin-only management, public read for active roles
- **user_subscription** (4 policies): Users manage their own subscriptions, admins manage all
- **subscription_plans** (4 policies): Public read, admin-only management
- **subscription_analytics** (4 policies): Admin and manager access only
- **reports** (4 policies): Creator and admin access
- **certificate_templates** (4 policies): Public read, role-based management
- **auth_sessions** (4 policies): User-only access to their own sessions
- **attendance_settings** (4 policies): Public read, role-based management
- **payment_gateway_settings** (2 policies): Admin-only access
- **financial_transactions** (5 policies): User and role-based access
- **system_settings** (4 policies): Role-based access for system configuration
- **event_categories** (7 policies): Public read, role-based management
- **participants** (5 policies): Event-based access control
- **ticket_types** (3 policies): Event-based access control
- **race_results** (2 policies): Event-based access control
- **webinar_sessions** (3 policies): Event-based access control
- **workshop_sessions** (3 policies): Event-based access control
- **event_teams** (3 policies): Event managers can manage teams for their events

#### Key RLS Principles:
1. **Role-based Access**: Policies check user roles (admin, manager, user) via `user_roles` table
2. **Ownership**: Users can access their own data (registrations, subscriptions, etc.)
3. **Event Management**: Event managers can access data related to their events
4. **Public Data**: Some data (published events, plans, categories) is publicly readable
5. **Admin Override**: Admins have broader access for management purposes

## Data Relationships

### Key Relationships
- Users belong to Organizations
- Users have Roles with Permissions
- Events belong to Organizations and Categories
- Registrations link Users to Events
- Certificates are generated from Registrations
- Subscriptions link Users to Plans
- Activity Logs track all User actions
- API Keys and Webhooks belong to Users

### Cascade Rules
- Deleting a User cascades to their Registrations, Subscriptions, API Keys, and Webhooks
- Deleting an Event cascades to its Registrations and Certificates
- Deleting a Registration cascades to its Certificate

This schema supports the full functionality of the mTicket.my platform while maintaining data integrity and security through proper constraints and RLS policies.
