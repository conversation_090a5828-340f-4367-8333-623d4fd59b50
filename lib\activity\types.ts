// Activity logging system types

export enum ActivityCategory {
  AUTH = 'auth',
  USER = 'user',
  EVENT = 'event',
  PAYMENT = 'payment',
  CERTIFICATE = 'certificate',
  ORGANIZATION = 'organization',
  SUBSCRIPTION = 'subscription',
  ADMIN = 'admin',
  SYSTEM = 'system'
}

export interface ActivityLogParams {
  userId?: string
  action: string
  entityType: string
  entityId?: string
  category: ActivityCategory
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
}

export interface ActivityLog {
  id: string
  user_id?: string
  action: string
  entity_type: string
  entity_id?: string
  category: ActivityCategory
  details: Record<string, any>
  ip_address?: string
  user_agent?: string
  created_at: string
  
  // Related entities (for joins)
  user?: {
    id: string
    full_name: string
    email: string
  }
  event?: {
    id: string
    title: string
  }
  organization?: {
    id: string
    name: string
  }
}

export interface ActivityLogFilter {
  userId?: string
  category?: ActivityCategory
  entityType?: string
  entityId?: string
  action?: string
  startDate?: string
  endDate?: string
  limit?: number
  offset?: number
}

export interface ActivityLogResponse {
  logs: ActivityLog[]
  total: number
  hasMore: boolean
}

// Predefined activity actions
export const ACTIVITY_ACTIONS = {
  // Authentication
  LOGIN: 'login',
  LOGOUT: 'logout',
  REGISTER: 'register',
  PASSWORD_RESET: 'password_reset',
  PASSWORD_CHANGE: 'password_change',
  EMAIL_VERIFY: 'email_verify',
  
  // User management
  USER_CREATE: 'user_create',
  USER_UPDATE: 'user_update',
  USER_DELETE: 'user_delete',
  ROLE_CHANGE: 'role_change',
  PROFILE_UPDATE: 'profile_update',
  
  // Event management
  EVENT_CREATE: 'event_create',
  EVENT_UPDATE: 'event_update',
  EVENT_DELETE: 'event_delete',
  EVENT_PUBLISH: 'event_publish',
  EVENT_UNPUBLISH: 'event_unpublish',
  
  // Registration
  REGISTRATION_CREATE: 'registration_create',
  REGISTRATION_UPDATE: 'registration_update',
  REGISTRATION_CANCEL: 'registration_cancel',
  
  // Payment
  PAYMENT_CREATE: 'payment_create',
  PAYMENT_SUCCESS: 'payment_success',
  PAYMENT_FAILED: 'payment_failed',
  PAYMENT_REFUND: 'payment_refund',
  
  // Certificate
  CERTIFICATE_GENERATE: 'certificate_generate',
  CERTIFICATE_VERIFY: 'certificate_verify',
  CERTIFICATE_REVOKE: 'certificate_revoke',
  
  // Organization
  ORGANIZATION_CREATE: 'organization_create',
  ORGANIZATION_UPDATE: 'organization_update',
  ORGANIZATION_LINK: 'organization_link',
  ORGANIZATION_UNLINK: 'organization_unlink',
  
  // Subscription
  SUBSCRIPTION_CREATE: 'subscription_create',
  SUBSCRIPTION_UPDATE: 'subscription_update',
  SUBSCRIPTION_CANCEL: 'subscription_cancel',
  
  // Admin
  ADMIN_LOGIN: 'admin_login',
  SETTINGS_UPDATE: 'settings_update',
  SYSTEM_MAINTENANCE: 'system_maintenance',
  
  // System
  API_ACCESS: 'api_access',
  WEBHOOK_TRIGGER: 'webhook_trigger',
  ERROR_OCCURRED: 'error_occurred'
} as const

export type ActivityAction = typeof ACTIVITY_ACTIONS[keyof typeof ACTIVITY_ACTIONS]

// Entity types
export const ENTITY_TYPES = {
  USER: 'user',
  EVENT: 'event',
  REGISTRATION: 'registration',
  PAYMENT: 'payment',
  CERTIFICATE: 'certificate',
  ORGANIZATION: 'organization',
  SUBSCRIPTION: 'subscription',
  WEBHOOK: 'webhook',
  SYSTEM: 'system',
  AUTH: 'auth'
} as const

export type EntityType = typeof ENTITY_TYPES[keyof typeof ENTITY_TYPES]
