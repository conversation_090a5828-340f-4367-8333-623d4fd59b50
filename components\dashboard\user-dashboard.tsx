"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { PlusCircle, Building2, ArrowRight, Ticket, Users, Calendar } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useAuth } from "@/contexts/auth-context"
import { useToast } from "@/hooks/use-toast"

export function UserDashboard() {
  const { user, refreshUser } = useAuth()
  const { toast } = useToast()
  const [isCreatingOrg, setIsCreatingOrg] = useState(false)

  // Set page title
  useEffect(() => {
    document.title = "Dashboard | mTicket.my - Event Management Platform"
  }, [])

  const handleCreateOrganization = async () => {
    setIsCreatingOrg(true)
    try {
      // Redirect to organization creation page
      window.location.href = "/dashboard/organization/create"
    } catch (error) {
      console.error("Error navigating to organization creation:", error)
      toast({
        title: "Error",
        description: "Failed to navigate to organization creation",
        variant: "destructive",
      })
    } finally {
      setIsCreatingOrg(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Welcome to mTicket.my</h1>
          <p className="text-sm text-muted-foreground">
            {user ? `Hello, ${user.full_name}! Ready to create your first event?` : "Get started with event management"}
          </p>
        </div>
        <Badge variant="outline" className="w-fit">
          User Account
        </Badge>
      </div>

      {/* Main Action Card */}
      <Card className="border-2 border-dashed border-primary/20 bg-primary/5">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary/10">
            <Calendar className="h-8 w-8 text-primary" />
          </div>
          <CardTitle className="text-xl">Create Your First Event</CardTitle>
          <CardDescription className="text-base">
            To start creating events, you need to set up an organization and upgrade to a manager account.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card className="border border-muted">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <Building2 className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Step 1: Create Organization</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  Set up your organization profile with business details and contact information.
                </p>
                <Button
                  onClick={handleCreateOrganization}
                  disabled={isCreatingOrg}
                  className="w-full"
                  variant="outline"
                >
                  {isCreatingOrg ? "Loading..." : "Create Organization"}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </CardContent>
            </Card>

            <Card className="border border-muted">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-primary" />
                  <CardTitle className="text-lg">Step 2: Upgrade to Manager</CardTitle>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  Automatically upgrade to manager role when you create an organization.
                </p>
                <Button disabled className="w-full" variant="outline">
                  Complete Step 1 First
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="rounded-lg bg-muted/50 p-4">
            <h4 className="font-medium mb-2">What you'll get as a Manager:</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>• Create and manage unlimited events</li>
              <li>• Access to event analytics and reports</li>
              <li>• Participant management tools</li>
              <li>• Certificate generation capabilities</li>
              <li>• Payment processing integration</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Quick Access Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Ticket className="h-5 w-5 text-primary" />
              <CardTitle className="text-lg">My Tickets</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              View and manage your event tickets and registrations.
            </p>
            <Link href="/dashboard/my-tickets">
              <Button variant="outline" className="w-full">
                View My Tickets
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-primary" />
              <CardTitle className="text-lg">Browse Events</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              Discover and register for exciting events happening around you.
            </p>
            <Link href="/events">
              <Button variant="outline" className="w-full">
                Browse Events
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-primary" />
              <CardTitle className="text-lg">Profile Settings</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground mb-3">
              Update your profile information and account settings.
            </p>
            <Link href="/dashboard/profile">
              <Button variant="outline" className="w-full">
                Manage Profile
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
