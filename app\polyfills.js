/**
 * Client-side polyfills for Next.js 15
 *
 * This file provides polyfills for Node.js core modules that were automatically
 * included in previous versions of Next.js but are no longer included in Next.js 15.
 */

// Only run this code on the client side
if (typeof window !== 'undefined') {
  // Polyfill for crypto
  if (!window.crypto) {
    // This is a minimal polyfill - in production you'd want a more complete solution
    window.crypto = {
      getRandomValues: function(buffer) {
        for (let i = 0; i < buffer.length; i++) {
          buffer[i] = Math.floor(Math.random() * 256);
        }
        return buffer;
      }
    };
  }

  // Polyfill for Buffer
  if (!window.Buffer) {
    window.Buffer = {
      from: function(data) {
        return new Uint8Array(data);
      }
    };
  }
}
