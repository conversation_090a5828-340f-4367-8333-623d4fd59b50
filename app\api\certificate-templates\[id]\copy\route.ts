import { NextRequest, NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { verifyUserAccess } from "@/lib/api-helpers/auth-verification"

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Verify user authentication
    const authResult = await verifyUserAccess(request)
    if (!authResult.isAuthenticated || !authResult.userId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      )
    }

    const templateId = params.id
    const supabaseAdmin = getSupabaseAdmin()

    // First, get the template to copy
    const { data: templateToCopy, error: fetchError } = await supabaseAdmin
      .from("certificate_templates")
      .select("*")
      .eq("id", templateId)
      .eq("is_active", true)
      .single()

    if (fetchError || !templateToCopy) {
      return NextResponse.json(
        { error: "Template not found" },
        { status: 404 }
      )
    }

    // Check if user can access this template (default, shared, or owned by user)
    const canAccess =
      templateToCopy.is_default ||
      templateToCopy.is_shared ||
      templateToCopy.created_by === authResult.userId ||
      authResult.isAdmin

    if (!canAccess) {
      return NextResponse.json(
        { error: "Permission denied" },
        { status: 403 }
      )
    }

    // Create the copy
    const newTemplate = {
      name: `${templateToCopy.name} (Copy)`,
      description: templateToCopy.description || "",
      html_template: templateToCopy.html_template || "",
      css_styles: templateToCopy.css_styles || "",
      fields: templateToCopy.fields || [],
      background_image_url: templateToCopy.background_image_url || null,
      background_color: templateToCopy.background_color || '#ffffff',
      show_frame: templateToCopy.show_frame !== undefined ? templateToCopy.show_frame : true,
      orientation: templateToCopy.orientation || 'landscape',
      thumbnail_url: templateToCopy.thumbnail_url || null,
      template_data: templateToCopy.template_data || {},
      is_premium: false,
      is_default: false,
      is_shared: false,
      is_active: true,
      created_by: authResult.userId,
    }

    const { data, error } = await supabaseAdmin
      .from("certificate_templates")
      .insert([newTemplate])
      .select()
      .single()

    if (error) {
      console.error("Error copying template:", error)
      return NextResponse.json(
        { error: "Failed to copy template" },
        { status: 500 }
      )
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error("Error in POST /api/certificate-templates/[id]/copy:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
