import { NextResponse } from "next/server";
import { hashPassword } from "@/lib/auth";
import { getSupabaseAdmin } from "@/lib/supabase";
import crypto from "crypto";

export async function POST(request: Request) {
  try {
    // Only allow this in development
    if (process.env.NODE_ENV !== "development") {
      return NextResponse.json(
        { error: "This endpoint is only available in development" },
        { status: 403 }
      );
    }

    const { email, password, fullName, roleName } = await request.json();

    // Default values
    const userEmail = email || "<EMAIL>";
    const userPassword = password || "admin123";
    const userFullName = fullName || "Admin User";
    const userRoleName = roleName || "admin";

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin();

    // Check if user already exists
    const { data: existingUser } = await supabaseAdmin
      .from("users")
      .select("id, email")
      .eq("email", userEmail)
      .single();

    if (existingUser) {
      return NextResponse.json({
        success: true,
        message: "User already exists",
        user: existingUser,
      });
    }

    // Get the role ID
    const { data: roleData, error: roleError } = await supabaseAdmin
      .from("user_roles")
      .select("id")
      .eq("role_name", userRoleName)
      .single();

    if (roleError) {
      console.error("Error finding role:", roleError);
      return NextResponse.json(
        { error: `Role '${userRoleName}' not found` },
        { status: 400 }
      );
    }

    // Generate user ID and hash password
    const userId = crypto.randomUUID();
    const passwordHash = await hashPassword(userPassword);

    // Create user
    const { data: newUser, error: createError } = await supabaseAdmin
      .from("users")
      .insert({
        id: userId,
        email: userEmail,
        password_hash: passwordHash,
        full_name: userFullName,
        role_id: roleData.id,
        subscription_status: "none",
        subscription_end_date: null,
        created_at: new Date().toISOString(),
        organization: null,
        organization_id: null,
        profile_image_url: null,
        events_created: 0,
        total_earnings: 0,
        available_balance: 0,
      })
      .select()
      .single();

    if (createError) {
      console.error("Error creating user:", createError);
      return NextResponse.json(
        { error: `Failed to create user: ${createError.message}` },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: "Test user created successfully",
      user: {
        id: newUser.id,
        email: newUser.email,
        full_name: newUser.full_name,
        role_id: newUser.role_id,
      },
      credentials: {
        email: userEmail,
        password: userPassword,
      },
    });

  } catch (error: any) {
    console.error("Create test user error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
