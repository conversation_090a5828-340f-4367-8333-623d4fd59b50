"use client"

import { useState } from "react"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Save } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { useSettings, type SystemSettings } from "@/contexts/settings-context"
import { useUser } from "@/contexts/user-context"

// Form schema
const formSchema = z.object({
  event_fee_percentage: z.coerce.number().min(0).max(100),
  withdrawal_fee_percentage: z.coerce.number().min(0).max(100),
  min_withdrawal_amount: z.coerce.number().min(0),
  payment_gateways: z.array(
    z.object({
      name: z.string(),
      enabled: z.boolean(),
      api_key: z.string().optional(),
      secret_key: z.string().optional(),
    }),
  ),
})

type FormValues = z.infer<typeof formSchema>

export function SettingsPanel() {
  const { settings, updateSettings } = useSettings()
  const { isAdmin } = useUser()
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Initialize form with current settings
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: settings
      ? {
          event_fee_percentage: settings.event_fee_percentage,
          withdrawal_fee_percentage: settings.withdrawal_fee_percentage,
          min_withdrawal_amount: settings.min_withdrawal_amount,
          payment_gateways: settings.payment_gateways,
        }
      : {
          event_fee_percentage: 5,
          withdrawal_fee_percentage: 1.5,
          min_withdrawal_amount: 50,
          payment_gateways: [
            { name: "Billplz", enabled: true },
            { name: "ToyyibPay", enabled: true },
            { name: "PayPal", enabled: false },
          ],
        },
  })

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    if (!isAdmin()) return

    setIsSubmitting(true)
    try {
      await updateSettings(values as Partial<SystemSettings>)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!settings) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>System Settings</CardTitle>
          <CardDescription>Loading settings...</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <Tabs defaultValue="fees">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="fees">Fees & Limits</TabsTrigger>
            <TabsTrigger value="payment">Payment Gateways</TabsTrigger>
            <TabsTrigger value="templates">Certificate Templates</TabsTrigger>
          </TabsList>

          <TabsContent value="fees">
            <Card>
              <CardHeader>
                <CardTitle>Fees & Limits</CardTitle>
                <CardDescription>Configure system fees and withdrawal limits</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <FormField
                  control={form.control}
                  name="event_fee_percentage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Event Fee Percentage</FormLabel>
                      <FormControl>
                        <div className="flex items-center">
                          <Input type="number" min="0" max="100" step="0.1" {...field} />
                          <span className="ml-2">%</span>
                        </div>
                      </FormControl>
                      <FormDescription>Fee charged on each event registration payment</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="withdrawal_fee_percentage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Withdrawal Fee Percentage</FormLabel>
                      <FormControl>
                        <div className="flex items-center">
                          <Input type="number" min="0" max="100" step="0.1" {...field} />
                          <span className="ml-2">%</span>
                        </div>
                      </FormControl>
                      <FormDescription>Fee charged when organizers withdraw funds</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="min_withdrawal_amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Minimum Withdrawal Amount (MYR)</FormLabel>
                      <FormControl>
                        <div className="flex items-center">
                          <span className="mr-2">RM</span>
                          <Input type="number" min="0" step="1" {...field} />
                        </div>
                      </FormControl>
                      <FormDescription>Minimum amount that can be withdrawn</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="payment">
            <Card>
              <CardHeader>
                <CardTitle>Payment Gateways</CardTitle>
                <CardDescription>Configure payment gateway settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {form.getValues().payment_gateways.map((gateway, index) => (
                  <div key={gateway.name} className="rounded-lg border p-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">{gateway.name}</h3>
                      <FormField
                        control={form.control}
                        name={`payment_gateways.${index}.enabled`}
                        render={({ field }) => (
                          <FormItem className="flex items-center space-x-2">
                            <FormControl>
                              <Switch checked={field.value} onCheckedChange={field.onChange} />
                            </FormControl>
                            <FormLabel className="m-0">Enabled</FormLabel>
                          </FormItem>
                        )}
                      />
                    </div>

                    {gateway.enabled && (
                      <div className="mt-4 grid gap-4 md:grid-cols-2">
                        <FormField
                          control={form.control}
                          name={`payment_gateways.${index}.api_key`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>API Key</FormLabel>
                              <FormControl>
                                <Input
                                  type="password"
                                  placeholder="Enter API key"
                                  {...field}
                                  value={field.value || ""}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name={`payment_gateways.${index}.secret_key`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Secret Key</FormLabel>
                              <FormControl>
                                <Input
                                  type="password"
                                  placeholder="Enter secret key"
                                  {...field}
                                  value={field.value || ""}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="templates">
            <Card>
              <CardHeader>
                <CardTitle>Certificate Templates</CardTitle>
                <CardDescription>Manage certificate templates</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  {settings.certificate_templates.map((template) => (
                    <div key={template.id} className="rounded-lg border p-4">
                      <img
                        src={template.thumbnail_url || "/placeholder.svg"}
                        alt={template.name}
                        className="aspect-video w-full rounded-md object-cover"
                      />
                      <div className="mt-2 flex items-center justify-between">
                        <h3 className="font-medium">{template.name}</h3>
                        {template.is_premium && (
                          <span className="rounded-full bg-amber-100 px-2 py-1 text-xs font-medium text-amber-800">
                            Premium
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="mt-6 flex justify-end">
          <Button type="submit" disabled={isSubmitting || !isAdmin()}>
            {isSubmitting ? "Saving..." : "Save Settings"}
            <Save className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </form>
    </Form>
  )
}
