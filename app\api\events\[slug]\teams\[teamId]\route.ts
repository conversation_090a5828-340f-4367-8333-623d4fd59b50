import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { verifyAuthToken } from "@/lib/auth/jwt"
import { isElevated } from "@/lib/auth/roles"
import { logActivity, ActivityCategory } from "@/utils/activity-logger"

const supabaseAdmin = getSupabaseAdmin()

interface RouteParams {
  params: { slug: string; teamId: string }
}

/**
 * PUT /api/events/[slug]/teams/[teamId]
 * Update a team
 */
export async function PUT(request: Request, { params }: RouteParams) {
  try {
    const { slug, teamId } = await params
    const body = await request.json()
    const { team_name, location, permissions, is_active, expires_at } = body

    // Authenticate user
    const authResult = await verifyAuthToken(request)
    if (!authResult.isAuthenticated || !authResult.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get event by slug
    const { data: event, error: eventError } = await supabaseAdmin
      .from("events")
      .select("id, title, event_manager_id")
      .eq("slug", slug)
      .single()

    if (eventError || !event) {
      return NextResponse.json(
        { error: "Event not found" },
        { status: 404 }
      )
    }

    // Check permissions
    const userRole = authResult.user.user_roles?.role_name || authResult.user.role
    const isEventManager = event.event_manager_id === authResult.user.id
    const hasElevatedAccess = isElevated(userRole)

    if (!isEventManager && !hasElevatedAccess) {
      return NextResponse.json(
        { error: "Unauthorized. Only event managers can update teams." },
        { status: 403 }
      )
    }

    // Get existing team
    const { data: existingTeam, error: teamError } = await supabaseAdmin
      .from("event_teams")
      .select("*")
      .eq("id", teamId)
      .eq("event_id", event.id)
      .single()

    if (teamError || !existingTeam) {
      return NextResponse.json(
        { error: "Team not found" },
        { status: 404 }
      )
    }

    // Update team
    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    if (team_name !== undefined) updateData.team_name = team_name
    if (location !== undefined) updateData.location = location
    if (permissions !== undefined) updateData.permissions = permissions
    if (is_active !== undefined) updateData.is_active = is_active
    if (expires_at !== undefined) updateData.expires_at = expires_at

    const { data: team, error: updateError } = await supabaseAdmin
      .from("event_teams")
      .update(updateData)
      .eq("id", teamId)
      .select()
      .single()

    if (updateError) {
      console.error("Error updating team:", updateError)
      return NextResponse.json(
        { error: "Failed to update team" },
        { status: 500 }
      )
    }

    // Log activity
    await logActivity({
      userId: authResult.user.id,
      action: "update_team",
      category: ActivityCategory.EVENT_MANAGEMENT,
      entityType: "event_team",
      entityId: teamId,
      details: {
        event_id: event.id,
        event_title: event.title,
        team_name: team.team_name,
        changes: updateData
      }
    })

    return NextResponse.json({
      success: true,
      team,
      message: "Team updated successfully"
    })

  } catch (error: any) {
    console.error("Error in team PUT:", error)
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/events/[slug]/teams/[teamId]
 * Delete a team
 */
export async function DELETE(request: Request, { params }: RouteParams) {
  try {
    const { slug, teamId } = await params

    // Authenticate user
    const authResult = await verifyAuthToken(request)
    if (!authResult.isAuthenticated || !authResult.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get event by slug
    const { data: event, error: eventError } = await supabaseAdmin
      .from("events")
      .select("id, title, event_manager_id")
      .eq("slug", slug)
      .single()

    if (eventError || !event) {
      return NextResponse.json(
        { error: "Event not found" },
        { status: 404 }
      )
    }

    // Check permissions
    const userRole = authResult.user.user_roles?.role_name || authResult.user.role
    const isEventManager = event.event_manager_id === authResult.user.id
    const hasElevatedAccess = isElevated(userRole)

    if (!isEventManager && !hasElevatedAccess) {
      return NextResponse.json(
        { error: "Unauthorized. Only event managers can delete teams." },
        { status: 403 }
      )
    }

    // Get existing team for logging
    const { data: existingTeam, error: teamError } = await supabaseAdmin
      .from("event_teams")
      .select("team_name")
      .eq("id", teamId)
      .eq("event_id", event.id)
      .single()

    if (teamError || !existingTeam) {
      return NextResponse.json(
        { error: "Team not found" },
        { status: 404 }
      )
    }

    // Delete team
    const { error: deleteError } = await supabaseAdmin
      .from("event_teams")
      .delete()
      .eq("id", teamId)

    if (deleteError) {
      console.error("Error deleting team:", deleteError)
      return NextResponse.json(
        { error: "Failed to delete team" },
        { status: 500 }
      )
    }

    // Log activity
    await logActivity({
      userId: authResult.user.id,
      action: "delete_team",
      category: ActivityCategory.EVENT_MANAGEMENT,
      entityType: "event_team",
      entityId: teamId,
      details: {
        event_id: event.id,
        event_title: event.title,
        team_name: existingTeam.team_name
      }
    })

    return NextResponse.json({
      success: true,
      message: "Team deleted successfully"
    })

  } catch (error: any) {
    console.error("Error in team DELETE:", error)
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    )
  }
}
