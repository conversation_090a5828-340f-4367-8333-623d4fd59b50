"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import { CalendarDays, Clock, MapPin, Tag, User } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/components/ui/card"
import { formatDate, formatTime, getEventInitials, generateEventGradient, formatDateBadge, stripHtmlTags } from "@/lib/utils"

import { EventType } from "@/contexts/event-context"

interface EventImage {
  url: string
  alt_text?: string
  order?: number
  is_primary?: boolean
}

interface EventCardProps {
  event: EventType
}

// Helper function to check if event has ended
function isEventEnded(endDate: string): boolean {
  return new Date(endDate) < new Date()
}

export default function EventCard({ event }: EventCardProps) {
  const [isMounted, setIsMounted] = useState(false)
  const [imageError, setImageError] = useState(false)
  const router = useRouter()

  // Check if event has ended
  const eventEnded = isEventEnded(event.end_date)

  // Get the primary image or first image from the array
  const primaryImage = event.images?.find(img => img.is_primary) || event.images?.[0]

  // Use primary image, fallback to image_url, or show gradient
  const displayImageUrl = primaryImage?.url || event.image_url

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Reset image error when event images or image_url changes
  useEffect(() => {
    setImageError(false)
  }, [displayImageUrl])

  const handleOrganizerClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (event.organizations?.id) {
      router.push(`/events?organizer=${event.organizations.id}`)
    }
  }

  const handleCategoryClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (event.event_category?.id) {
      router.push(`/events?category=${event.event_category.id}`)
    } else {
      // For events without category, navigate to show all events without categories
      router.push(`/events?category=others`)
    }
  }

  if (!isMounted) {
    // Return a skeleton or null during server-side rendering
    return (
      <Card className="overflow-hidden">
        <div className="aspect-[16/9] relative bg-gray-200 animate-pulse"></div>
        <CardHeader className="p-4">
          <div className="space-y-2">
            <div className="h-6 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
          </div>
        </CardHeader>
        <CardContent className="p-4 pt-0 space-y-2">
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded w-2/3"></div>
        </CardContent>
      </Card>
    )
  }

  const handleCardClick = () => {
    router.push(`/events/${event.slug}`)
  }

  return (
    <Card
      className={`overflow-hidden flex flex-col h-full cursor-pointer transition-all duration-300 hover:shadow-lg hover:shadow-primary/10 ${eventEnded ? 'opacity-75' : ''}`}
      onClick={handleCardClick}
    >
      <div className="aspect-[16/9] relative overflow-hidden">
        {displayImageUrl && !imageError ? (
          <Image
            src={displayImageUrl}
            alt={primaryImage?.alt_text || event.title}
            fill
            className="object-cover transition-transform duration-300 hover:scale-105"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            onError={() => setImageError(true)}
          />
        ) : (
          (() => {
            const gradient = generateEventGradient(event.title)
            return (
              <div
                className={`w-full h-full ${gradient.className} flex items-center justify-center transition-transform duration-300 hover:scale-105`}
                style={gradient.style}
              >
                <span className="text-white font-bold text-2xl md:text-3xl">
                  {getEventInitials(event.title)}
                </span>
              </div>
            )
          })()
        )}

        {/* Status Badge - Top Left */}
        {eventEnded && (
          <div className="absolute top-2 left-2">
            <Badge className="bg-transparent border border-white text-white backdrop-blur-sm">
              Ended
            </Badge>
          </div>
        )}

        {/* Date Badge - Top Right */}
        {(() => {
          const dateBadge = formatDateBadge(event.start_date)
          return (
            <div className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm border border-white/20 rounded-md px-2 py-1 text-center shadow-sm min-w-[50px]">
              <div className="text-lg font-bold text-gray-800 leading-none">
                {dateBadge.day}
              </div>
              <div className="text-xs font-bold text-gray-700 uppercase leading-none">
                {dateBadge.month}
              </div>
              <div className="text-xs text-gray-600 leading-none">
                {dateBadge.dayOfWeek}
              </div>
            </div>
          )
        })()}

        {/* Price Badge - Bottom Right */}
        <Badge
          className={`absolute bottom-2 right-2 flex items-center gap-1 ${
            (event.price || 0) === 0
              ? 'bg-green-600 hover:bg-green-700 text-white'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
        >
          <Tag className="h-3 w-3" />
          {(event.price || 0) === 0 ? "Free" : "Paid"}
        </Badge>

        {/* Category Badge - Bottom Left */}
        <Badge
          className="absolute bottom-2 left-2 flex items-center gap-1 bg-white/80 text-gray-800 hover:bg-white/90 cursor-pointer transition-all duration-200 backdrop-blur-sm border border-white/20"
          onClick={handleCategoryClick}
          title={`View all events in ${event.event_category?.name || 'Others'}`}
        >
          {event.event_category?.name || 'Others'}
        </Badge>
      </div>
      <CardHeader className="p-4">
        <div className="space-y-1">
          <h3 className="font-semibold text-lg line-clamp-1">{event.title}</h3>
          <div className="h-10 overflow-hidden">
            <p className="text-sm text-muted-foreground line-clamp-2">
              {(() => {
                // Use description_html if available (WYSIWYG content), otherwise use plain description
                const description = event.description_html
                  ? stripHtmlTags(event.description_html)
                  : event.description
                return description || '\u00A0'
              })()}
            </p>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4 pt-0 flex-grow flex flex-col">
        <div className="space-y-2 flex-grow">
          <div className="flex items-center text-sm">
            <CalendarDays className="mr-2 h-4 w-4 text-muted-foreground" />
            <span>{formatDate(event.start_date)}</span>
          </div>
          <div className="flex items-center text-sm">
            <Clock className="mr-2 h-4 w-4 text-muted-foreground" />
            <span>
              {formatTime(event.start_date)} - {formatTime(event.end_date)}
            </span>
          </div>
          <div className="flex items-center text-sm">
            <MapPin className="mr-2 h-4 w-4 text-muted-foreground" />
            <span className="line-clamp-1">{event.location}</span>
          </div>
          <div className="flex items-center text-sm min-h-[20px]">
            <User className="mr-2 h-4 w-4 text-muted-foreground" />
            {event.organizations ? (
              <span
                className="line-clamp-1 text-blue-600 hover:text-blue-800 cursor-pointer hover:underline"
                onClick={handleOrganizerClick}
                title={`View all events by ${event.organizations.name}`}
              >
                {event.organizations.name}
              </span>
            ) : (
              <span className="line-clamp-1 text-muted-foreground">
                No organizer specified
              </span>
            )}
          </div>
        </div>
        <div className="pt-2 mt-auto">
          <div className="text-lg font-bold">
            {(event.price || 0) === 0 ? "Free" : `RM ${(event.price || 0).toFixed(2)}`}
          </div>
        </div>
      </CardContent>
      <CardFooter className="p-4 pt-0">
        {eventEnded ? (
          <Button className="w-full" variant="secondary" disabled>
            Ended
          </Button>
        ) : (
          <Button className="w-full">Register</Button>
        )}
      </CardFooter>
    </Card>
  )
}
