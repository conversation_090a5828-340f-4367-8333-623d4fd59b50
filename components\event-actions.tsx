"use client"

import { useState } from "react"
import { QRCodeSVG } from "qrcode.react"
import { <PERSON><PERSON>, QrCode, Share2, Facebook, Twitter, Instagram, Linkedin, Download } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/hooks/use-toast"
import { generateShortUrl, generateEventUrl } from "@/lib/qr-utils"

interface EventActionButtonsProps {
  eventSlug: string
  eventTitle: string
  className?: string
}

export function EventActionButtons({ eventSlug, eventTitle, className }: EventActionButtonsProps) {
  const [isQROpen, setIsQROpen] = useState(false)
  const [isShareOpen, setIsShareOpen] = useState(false)
  const { toast } = useToast()

  const shortUrl = generateShortUrl(eventSlug)
  const fullUrl = generateEventUrl(eventSlug)

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "Copied!",
        description: `${label} copied to clipboard`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      })
    }
  }

  const downloadQRCode = () => {
    try {
      const svg = document.querySelector('#event-qr-code-svg') as SVGElement
      if (!svg) return

      const svgData = new XMLSerializer().serializeToString(svg)
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      const img = new Image()

      img.onload = () => {
        canvas.width = img.width
        canvas.height = img.height
        ctx?.drawImage(img, 0, 0)

        const pngFile = canvas.toDataURL('image/png')
        const downloadLink = document.createElement('a')
        downloadLink.download = `${eventSlug}-qr-code.png`
        downloadLink.href = pngFile
        downloadLink.click()
      }

      img.src = 'data:image/svg+xml;base64,' + btoa(svgData)

      toast({
        title: "Downloaded!",
        description: "QR code saved as PNG",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to download QR code",
        variant: "destructive",
      })
    }
  }

  const shareEvent = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: eventTitle,
          text: `Check out this event: ${eventTitle}`,
          url: shortUrl,
        })
      } catch (error) {
        copyToClipboard(shortUrl, "Event link")
      }
    } else {
      copyToClipboard(shortUrl, "Event link")
    }
  }

  const shareToSocial = (platform: string) => {
    const encodedTitle = encodeURIComponent(eventTitle)
    const encodedUrl = encodeURIComponent(shortUrl)
    const encodedText = encodeURIComponent(`Check out this event: ${eventTitle}`)

    let shareUrl = ""

    switch (platform) {
      case "facebook":
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`
        break
      case "twitter":
        shareUrl = `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedUrl}`
        break
      case "linkedin":
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`
        break
      case "instagram":
        copyToClipboard(shortUrl, "Event link for Instagram")
        return
    }

    if (shareUrl) {
      window.open(shareUrl, "_blank", "width=600,height=400")
    }
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Share Button with Popover */}
      <Popover open={isShareOpen} onOpenChange={setIsShareOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="p-2 hover:bg-primary hover:text-primary-foreground transition-colors"
          >
            <Share2 className="h-4 w-4" />
            <span className="sr-only">Share Event</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" align="center" sideOffset={8}>
          {/* Arrow indicator */}
          <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
            <div className="w-4 h-4 bg-popover border-l border-t border-border rotate-45 transform origin-center"></div>
          </div>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-sm mb-2">Share Event</h4>
              <p className="text-xs text-muted-foreground mb-3">
                Share this event with others
              </p>
            </div>

            {/* Native Share Button */}
            <Button
              onClick={shareEvent}
              className="w-full"
              size="sm"
            >
              <Share2 className="mr-2 h-4 w-4" />
              Share Event
            </Button>

            <Separator />

            {/* Social Media Buttons */}
            <div>
              <p className="text-xs text-muted-foreground mb-2">Share on social media:</p>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => shareToSocial("facebook")}
                  className="justify-start hover:bg-blue-50 hover:text-blue-600"
                >
                  <Facebook className="mr-2 h-4 w-4" />
                  Facebook
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => shareToSocial("twitter")}
                  className="justify-start hover:bg-sky-50 hover:text-sky-600"
                >
                  <Twitter className="mr-2 h-4 w-4" />
                  Twitter
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => shareToSocial("linkedin")}
                  className="justify-start hover:bg-blue-50 hover:text-blue-700"
                >
                  <Linkedin className="mr-2 h-4 w-4" />
                  LinkedIn
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => shareToSocial("instagram")}
                  className="justify-start hover:bg-pink-50 hover:text-pink-600"
                >
                  <Instagram className="mr-2 h-4 w-4" />
                  Instagram
                </Button>
              </div>
            </div>

            <Separator />

            {/* Copy Link */}
            <div className="space-y-2">
              <Label htmlFor="share-url" className="text-xs">Event Link</Label>
              <div className="flex gap-2">
                <Input
                  id="share-url"
                  value={shortUrl}
                  readOnly
                  className="flex-1 text-xs"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(shortUrl, "Event link")}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* QR Code Button with Popover */}
      <Popover open={isQROpen} onOpenChange={setIsQROpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="p-2 hover:bg-primary hover:text-primary-foreground transition-colors"
          >
            <QrCode className="h-4 w-4" />
            <span className="sr-only">QR Code</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80" align="center" sideOffset={8}>
          {/* Arrow indicator */}
          <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
            <div className="w-4 h-4 bg-popover border-l border-t border-border rotate-45 transform origin-center"></div>
          </div>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-sm mb-2">Event QR Code</h4>
              <p className="text-xs text-muted-foreground mb-3">
                Scan to visit event page or copy the link below
              </p>
            </div>

            {/* QR Code */}
            <div className="flex justify-center">
              <div className="p-3 bg-white rounded-lg border">
                <QRCodeSVG
                  id="event-qr-code-svg"
                  value={shortUrl}
                  size={160}
                  level="M"
                  includeMargin={true}
                  fgColor="#000000"
                  bgColor="#ffffff"
                />
              </div>
            </div>

            {/* URLs */}
            <div className="space-y-3">
              <div className="space-y-2">
                <Label htmlFor="short-url" className="text-xs">Short URL</Label>
                <div className="flex gap-2">
                  <Input
                    id="short-url"
                    value={shortUrl}
                    readOnly
                    className="flex-1 text-xs"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(shortUrl, "Short URL")}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="full-url" className="text-xs">Full URL</Label>
                <div className="flex gap-2">
                  <Input
                    id="full-url"
                    value={fullUrl}
                    readOnly
                    className="flex-1 text-xs"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard(fullUrl, "Full URL")}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Download Button */}
            <div className="pt-3 border-t">
              <Button
                onClick={downloadQRCode}
                className="w-full"
              >
                <Download className="h-4 w-4 mr-2" />
                Download QR Code
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

    </div>
  )
}
