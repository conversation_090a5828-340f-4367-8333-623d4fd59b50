-- Certificate Editor Enhancements Migration
-- This migration adds the necessary fields and RLS policies for the new Certificate Editor

-- Add RLS policies for certificate templates
-- Policy for users to view default templates and their own templates
CREATE POLICY "Users can view default and own templates" ON certificate_templates
  FOR SELECT USING (
    is_default = true 
    OR created_by = auth.uid() 
    OR is_shared = true
  );

-- Policy for users to create their own templates
CREATE POLICY "Users can create own templates" ON certificate_templates
  FOR INSERT WITH CHECK (created_by = auth.uid());

-- Policy for users to update their own templates
CREATE POLICY "Users can update own templates" ON certificate_templates
  FOR UPDATE USING (created_by = auth.uid());

-- Policy for users to delete their own templates (soft delete by setting is_active = false)
CREATE POLICY "Users can delete own templates" ON certificate_templates
  FOR UPDATE USING (created_by = auth.uid());

-- Admin policy to manage all templates
CREATE POLICY "<PERSON><PERSON> can manage all templates" ON certificate_templates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role_name = 'admin'
    )
  );

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_certificate_templates_is_default ON certificate_templates(is_default);
CREATE INDEX IF NOT EXISTS idx_certificate_templates_is_shared ON certificate_templates(is_shared);

-- Update existing default templates to ensure they have proper default status
UPDATE certificate_templates 
SET is_default = true 
WHERE name IN (
  'Completion Certificate',
  'Attendance Certificate', 
  'Achievement Certificate',
  'Professional Certificate',
  'Modern Certificate'
) AND is_default IS NOT TRUE;

-- Ensure RLS is enabled on certificate_templates table
ALTER TABLE certificate_templates ENABLE ROW LEVEL SECURITY;
