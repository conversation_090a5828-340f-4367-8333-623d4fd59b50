/**
 * Polyfills for Node.js core modules in the browser
 * 
 * Next.js 15 dropped automatic polyfills for Node-core modules in Edge runtime
 * This file provides polyfills for commonly used Node.js modules
 */

// Import the polyfills
import { <PERSON>uffer } from 'buffer';
import process from 'process';

// Make them available globally
if (typeof window !== 'undefined') {
  window.Buffer = Buffer;
  window.process = process;
}

// Export the polyfills for direct imports
export { Buffer, process };
