import { NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import jwt from "jsonwebtoken";

export async function POST(request: Request) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: "Token is required" },
        { status: 400 }
      );
    }

    // Verify JWT token
    let decoded: any;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET || "your-secret-key");
    } catch (jwtError) {
      return NextResponse.json(
        { error: "Invalid token" },
        { status: 401 }
      );
    }

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin();

    // Get fresh user data from database
    console.log("Verify API: Getting user data for userId:", decoded.userId);
    const { data: userData, error: userError } = await supabaseAdmin
      .from("users")
      .select(`
        *,
        user_roles!role_id (
          id,
          role_name,
          description
        )
      `)
      .eq("id", decoded.userId)
      .single();

    console.log("Verify API: Raw userData:", JSON.stringify(userData, null, 2));

    if (userError || !userData) {
      console.error("User not found during verification:", userError);
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Prepare user data (exclude sensitive fields)
    console.log("Verify API: userData.user_roles:", userData.user_roles);
    console.log("Verify API: role_name extracted:", userData.user_roles?.role_name);

    const user = {
      id: userData.id,
      email: userData.email,
      full_name: userData.full_name,
      phone: userData.phone,
      bio: userData.bio,
      role_id: userData.role_id,
      role_name: userData.user_roles?.role_name || null,
      subscription_status: userData.subscription_status,
      subscription_end_date: userData.subscription_end_date,
      created_at: userData.created_at,
      organization: userData.organization,
      organization_id: userData.organization_id,
      profile_image_url: userData.profile_image_url,
      events_created: userData.events_created || 0,
      total_earnings: userData.total_earnings || 0,
      available_balance: userData.available_balance || 0,
    };

    console.log("Verify API: Final user object:", JSON.stringify(user, null, 2));

    return NextResponse.json({
      success: true,
      user,
    });

  } catch (error: any) {
    console.error("Token verification error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
