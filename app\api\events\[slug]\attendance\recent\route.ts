import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"

const supabaseAdmin = getSupabaseAdmin()

interface RouteParams {
  params: { slug: string }
}

/**
 * GET /api/events/[slug]/attendance/recent
 * Get recent check-ins for an event (public endpoint for team QR scanner)
 */
export async function GET(request: Request, { params }: RouteParams) {
  try {
    const { slug } = await params
    const url = new URL(request.url)
    const limit = parseInt(url.searchParams.get('limit') || '10')

    // Get event by slug
    const { data: event, error: eventError } = await supabaseAdmin
      .from("events")
      .select("id, title")
      .eq("slug", slug)
      .single()

    if (eventError || !event) {
      return NextResponse.json(
        { error: "Event not found" },
        { status: 404 }
      )
    }

    // Get recent check-ins
    const { data: recentCheckIns, error: checkInsError } = await supabaseAdmin
      .from("registrations")
      .select(`
        id,
        attendee_name,
        attendee_email,
        attendee_phone,
        ticket_type,
        checked_in,
        checked_in_at,
        payment_status,
        created_at
      `)
      .eq("event_id", event.id)
      .eq("checked_in", true)
      .not("checked_in_at", "is", null)
      .order("checked_in_at", { ascending: false })
      .limit(limit)

    if (checkInsError) {
      console.error("Error fetching recent check-ins:", checkInsError)
      return NextResponse.json(
        { error: "Failed to fetch recent check-ins" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      recent_checkins: recentCheckIns || [],
      event: {
        id: event.id,
        title: event.title
      }
    })

  } catch (error: any) {
    console.error("Error in recent check-ins GET:", error)
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    )
  }
}
