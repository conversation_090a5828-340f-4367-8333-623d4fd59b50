"use client"

import { useEffect, useState } from "react"
import { Edit, Plus, Trash, Upload, Eye, Download } from "lucide-react"
import { useDropzone } from "react-dropzone"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"
import { CertificateTemplateEditor, CertificateField } from "@/components/certificate-template-editor"

type CertificateTemplate = {
  id: string
  name: string
  description?: string
  thumbnail_url?: string
  is_premium: boolean
  html_template?: string
  css_styles?: string
  fields?: CertificateField[]
  orientation?: "landscape" | "portrait"
  is_active?: boolean
  created_by?: string
  created_at?: string
  updated_at?: string
  background_image_url?: string
  background_color?: string
  show_frame?: boolean
  template_data?: any
  is_default?: boolean
  is_shared?: boolean
}

export default function CertificateTemplatesPage() {
  const [templates, setTemplates] = useState<CertificateTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [isEditorOpen, setIsEditorOpen] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState<CertificateTemplate | null>(null)
  const [newTemplate, setNewTemplate] = useState<Partial<CertificateTemplate>>({
    name: "",
    is_premium: false,
    fields: [],
  })
  const [templateFile, setTemplateFile] = useState<File | null>(null)
  const [currentUser, setCurrentUser] = useState<any>(null)
  const { toast } = useToast()

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      "image/jpeg": [],
      "image/png": [],
      "image/pdf": [],
    },
    maxFiles: 1,
    onDrop: (acceptedFiles) => {
      setTemplateFile(acceptedFiles[0])
    },
  })

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get current user
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          console.error("Session error:", sessionError)
          toast({
            title: "Authentication Error",
            description: "Failed to get session",
            variant: "destructive",
          })
          return
        }

        if (sessionData.session) {
          const { data: userData, error: userError } = await supabase
            .from("users")
            .select("*")
            .eq("id", sessionData.session.user.id)
            .single()

          if (userError && userError.code !== "PGRST116") {
            console.error("User fetch error:", userError)
            throw userError
          }

          if (userData) {
            setCurrentUser(userData)
          } else {
            // Mock user data if not found
            setCurrentUser({
              id: sessionData.session.user.id,
              role: "admin",
              name: "Admin User",
            })
          }
        } else {
          // Set a mock admin user for testing
          setCurrentUser({
            id: "mock-admin-id",
            role: "admin",
            name: "Admin User",
          })
        }

        // Fetch certificate templates from database
        const { data: templatesData, error: templatesError } = await supabase
          .from("certificate_templates")
          .select("*")
          .eq("is_active", true)
          .order("created_at", { ascending: false })

        if (templatesError) {
          console.error("Templates error:", templatesError)
          toast({
            title: "Error",
            description: `Failed to fetch certificate templates: ${templatesError.message}`,
            variant: "destructive",
          })
          setTemplates([])
        } else {
          setTemplates(templatesData || [])
        }
      } catch (error) {
        console.error("Error fetching certificate templates:", error)
        toast({
          title: "Error",
          description: "Failed to fetch certificate templates",
          variant: "destructive",
        })
        setTemplates([])
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [toast])

  const handleSaveTemplate = async (templateData: any) => {
    try {
      if (templateData.id) {
        // Update existing template
        const updateData = {
          name: templateData.name,
          description: templateData.description || "",
          fields: templateData.fields || [],
          html_template: templateData.html_template || "",
          css_styles: templateData.css_styles || "",
          background_image_url: templateData.background_image_url || null,
          background_color: templateData.background_color || '#ffffff',
          show_frame: templateData.show_frame !== undefined ? templateData.show_frame : true,
          orientation: templateData.orientation || 'landscape',
          updated_at: new Date().toISOString(),
        }

        const { error } = await supabase
          .from("certificate_templates")
          .update(updateData)
          .eq("id", templateData.id)

        if (error) {
          console.error("Update error:", error)
          throw error
        }

        setTemplates(prev => prev.map(t =>
          t.id === templateData.id ? { ...t, ...updateData } : t
        ))

        toast({
          title: "Success",
          description: "Template updated successfully",
        })
      } else {
        // Create new template
        const { data, error } = await supabase
          .from("certificate_templates")
          .insert([{
            name: templateData.name,
            description: templateData.description || "",
            fields: templateData.fields || [],
            html_template: templateData.html_template || "",
            css_styles: templateData.css_styles || "",
            background_image_url: templateData.background_image_url || "",
            background_color: templateData.background_color || '#ffffff',
            show_frame: templateData.show_frame !== undefined ? templateData.show_frame : true,
            orientation: templateData.orientation || 'landscape',
            is_premium: false,
            is_active: true,
            created_by: currentUser?.id,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          }])
          .select()
          .single()

        if (error) throw error

        setTemplates(prev => [data, ...prev])

        toast({
          title: "Success",
          description: "Template created successfully",
        })
      }

      setIsEditorOpen(false)
      setSelectedTemplate(null)
    } catch (error) {
      console.error("Error saving template:", error)
      toast({
        title: "Error",
        description: "Failed to save template",
        variant: "destructive",
      })
    }
  }

  const handleDeleteTemplate = async () => {
    try {
      if (!selectedTemplate) return

      const { error } = await supabase
        .from("certificate_templates")
        .update({ is_active: false })
        .eq("id", selectedTemplate.id)

      if (error) throw error

      setTemplates(prev => prev.filter(t => t.id !== selectedTemplate.id))
      setSelectedTemplate(null)
      setIsDeleteDialogOpen(false)

      toast({
        title: "Success",
        description: "Template deleted successfully",
      })
    } catch (error) {
      console.error("Error deleting template:", error)
      toast({
        title: "Error",
        description: "Failed to delete template",
        variant: "destructive",
      })
    }
  }

  // Set page title
  useEffect(() => {
    document.title = "Certificate Templates | mTicket.my - Event Management Platform"
  }, [])

  if (currentUser && currentUser.role !== "admin" && currentUser.role !== "manager") {
    return (
      <Card className="m-6">
        <CardHeader>
          <CardTitle>Certificate Templates</CardTitle>
          <CardDescription>You don't have permission to access this page</CardDescription>
        </CardHeader>
      </Card>
    )
  }

  // Show editor if open
  if (isEditorOpen) {
    return (
      <CertificateTemplateEditor
        template={selectedTemplate || undefined}
        onSave={handleSaveTemplate}
        onCancel={() => {
          setIsEditorOpen(false)
          setSelectedTemplate(null)
        }}
      />
    )
  }

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Certificate Templates</h1>
        <div className="flex gap-2">
          <Button onClick={() => {
            setSelectedTemplate(null)
            setIsEditorOpen(true)
          }}>
            <Plus className="mr-2 h-4 w-4" />
            Create Template
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Manage Certificate Templates</CardTitle>
          <CardDescription>Configure and manage certificate templates for your events</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex h-40 items-center justify-center">
              <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
            </div>
          ) : templates.length === 0 ? (
            <div className="flex h-40 flex-col items-center justify-center gap-4 rounded-lg border border-dashed p-8 text-center">
              <div className="text-muted-foreground">No certificate templates configured</div>
              <Button onClick={() => {
                setSelectedTemplate(null)
                setIsEditorOpen(true)
              }}>
                <Plus className="mr-2 h-4 w-4" />
                Create Template
              </Button>
            </div>
          ) : (
            <div className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3">
              {templates.map((template) => (
                <Card key={template.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="aspect-video w-full overflow-hidden bg-gradient-to-br from-blue-50 to-purple-50">
                    <img
                      src={template.thumbnail_url || "/placeholder.svg?height=200&width=300&text=" + encodeURIComponent(template.name)}
                      alt={template.name}
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-medium">{template.name}</h3>
                        <p className="text-sm text-muted-foreground">
                          {template.is_premium ? "Premium" : "Free"} • {template.fields?.length || 0} fields • {template.orientation || "landscape"}
                        </p>
                        {template.description && (
                          <p className="text-xs text-muted-foreground mt-1 line-clamp-2">{template.description}</p>
                        )}
                      </div>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setSelectedTemplate(template)
                            setIsEditorOpen(true)
                          }}
                          title="Edit Template"
                        >
                          <Edit className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => {
                            setSelectedTemplate(template)
                            setIsDeleteDialogOpen(true)
                          }}
                          title="Delete Template"
                        >
                          <Trash className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>



      {/* Delete Template Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Certificate Template</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {selectedTemplate?.name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteTemplate}>
              Delete Template
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
