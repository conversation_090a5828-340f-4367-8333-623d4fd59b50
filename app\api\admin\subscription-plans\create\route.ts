import { NextRequest, NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { getUserFromToken } from "@/lib/auth/token"
import { logActivity, ActivityCategory } from "@/lib/activity-logger"

export async function POST(request: NextRequest) {
  try {
    // Get user from token
    const userToken = await getUserFromToken(request)
    if (!userToken) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get the Supabase admin client to bypass RLS policies
    const supabaseAdmin = getSupabaseAdmin()

    // Get user details to check admin role
    const { data: user, error: userError } = await supabaseAdmin
      .from("users")
      .select(`
        *,
        role_info:role_id(id, role_name, description, permissions)
      `)
      .eq("id", userToken.userId)
      .single()

    if (userError || !user) {
      console.error("Error fetching user:", userError)
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Check if user has admin role
    const isAdmin = user.role === "admin" || user.role_info?.role_name === "admin"
    if (!isAdmin) {
      return NextResponse.json(
        { error: "Unauthorized. Admin access required." },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      id,
      name,
      price,
      description,
      features,
      max_events,
      max_attendees_per_event,
      is_popular,
      is_active = true,
      certificates_enabled = false,
      attendance_enabled = false,
      webhooks_enabled = false,
      analytics_enabled = false,
      reports_enabled = false
    } = body

    // Validate required fields
    if (!id || !name || price === undefined) {
      return NextResponse.json(
        { error: "Missing required fields: id, name, price" },
        { status: 400 }
      )
    }

    // Validate features array
    if (!Array.isArray(features)) {
      return NextResponse.json(
        { error: "Features must be an array" },
        { status: 400 }
      )
    }

    // Create the subscription plan
    const { data: plan, error } = await supabaseAdmin
      .from('subscription_plans')
      .insert({
        id,
        name,
        price: parseFloat(price.toString()),
        description: description || '',
        features: features.filter(f => f.trim() !== ''), // Remove empty features
        max_events: max_events === '' || max_events === null ? null : parseInt(max_events.toString()),
        max_attendees_per_event: max_attendees_per_event === '' || max_attendees_per_event === null ? null : parseInt(max_attendees_per_event.toString()),
        is_popular: Boolean(is_popular),
        is_active: Boolean(is_active),
        certificates_enabled: Boolean(certificates_enabled),
        attendance_enabled: Boolean(attendance_enabled),
        webhooks_enabled: Boolean(webhooks_enabled),
        analytics_enabled: Boolean(analytics_enabled),
        reports_enabled: Boolean(reports_enabled),
      })
      .select()
      .single()

    if (error) {
      console.error("Error creating subscription plan:", error)
      return NextResponse.json(
        { error: error.message || "Failed to create subscription plan" },
        { status: 500 }
      )
    }

    // Log activity
    try {
      await logActivity({
        userId: user.id,
        action: "create_subscription_plan",
        entityType: "subscription_plan",
        entityId: plan.id,
        category: ActivityCategory.ADMIN,
        details: {
          plan_name: plan.name,
          price: plan.price,
        },
      })
    } catch (logError) {
      console.error("Error logging activity:", logError)
    }

    return NextResponse.json({
      success: true,
      plan: {
        id: plan.id,
        name: plan.name,
        price: parseFloat(plan.price.toString()),
        description: plan.description,
        features: Array.isArray(plan.features) ? plan.features : [],
        max_events: plan.max_events,
        max_attendees_per_event: plan.max_attendees_per_event,
        is_popular: plan.is_popular,
        is_active: plan.is_active,
        certificates_enabled: plan.certificates_enabled,
        attendance_enabled: plan.attendance_enabled,
        webhooks_enabled: plan.webhooks_enabled,
        analytics_enabled: plan.analytics_enabled,
        reports_enabled: plan.reports_enabled,
        created_at: plan.created_at,
        updated_at: plan.updated_at,
      }
    })
  } catch (error: any) {
    console.error("Error in create subscription plan API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
