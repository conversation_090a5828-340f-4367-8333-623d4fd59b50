# User Guide: Payment System

## Overview

This guide explains how to use the payment system in mTicket.my for event registrations.

## For Event Attendees

### Viewing Your Tickets

1. **Navigate to My Tickets**
   - Go to `/dashboard/my-tickets`
   - View all your event registrations
   - Filter by "All Tickets", "Personal", or "Group"

2. **Understanding Payment Status**
   - **Yellow "Payment Pending"**: Payment not yet completed
   - **Green "Payment Confirmed"**: Payment successfully processed
   - **Blue "Attended"**: You've checked in at the event

### Making Payments

#### Step 1: Identify Pending Payments
- Look for tickets with yellow "Payment Pending" indicator
- These will show a pulsing yellow dot and status message

#### Step 2: Initiate Payment
- Click the green "Pay Now" button
- <PERSON><PERSON> shows the exact amount: "Pay Now - RM 50.00"
- You'll see a "Processing payment" notification

#### Step 3: Complete Payment
- You'll be redirected to the payment gateway (Billplz, ToyyibPay, etc.)
- Choose your preferred payment method:
  - Online banking (FPX)
  - Credit/debit cards
  - E-wallets (Touch 'n Go, GrabPay, etc.)
- Complete the payment process

#### Step 4: Return to mTicket.my
- After payment, you'll be redirected back
- Success: Green notification "Payment Successful"
- Cancelled: Red notification "Payment Cancelled"

#### Step 5: Access Your Ticket
- Status changes to green "Payment Confirmed"
- New buttons appear: "View Receipt" and "View Ticket"

### After Payment Confirmation

#### View Receipt
- Click "View Receipt" to see payment details
- Shows transaction information, amount paid, and receipt notes
- Can download/print receipt for records

#### View Ticket
- Click "View Ticket" to see your event ticket
- Shows QR code for event entry
- Displays attendee information and event details
- Can download/print ticket

#### Certificates (Post-Event)
- Available after attending the event
- Shows "Certificate Available" for attended events
- Click to view and download your certificate

## For Event Organizers

### Managing Registration Payments

#### Viewing Registration Status
1. Go to your event's management page in the dashboard
2. See payment status for all registrations:
   - **Pending**: Awaiting payment
   - **Processing**: Payment in progress
   - **Confirmed**: Payment completed
   - **Attended**: Participant checked in

#### Payment Gateway Configuration
1. Navigate to `/dashboard/settings/payment-gateways`
2. Configure available payment methods:
   - **Billplz**: Malaysian payment gateway
   - **ToyyibPay**: Alternative Malaysian solution
   - **Chip**: Credit/debit cards, e-wallets
   - **Stripe**: International payments

3. For each gateway, configure:
   - API keys and credentials
   - Test/live mode settings
   - Enable/disable status

### Payment Analytics

#### Revenue Tracking
- View total revenue by event
- Track payment success rates
- Monitor payment gateway performance

#### Registration Reports
- Export registration data with payment status
- Filter by payment status and date ranges
- Generate financial reports

## Payment Methods Supported

### Malaysian Payment Gateways

#### Billplz
- **FPX Online Banking**: All major Malaysian banks
- **Credit/Debit Cards**: Visa, Mastercard
- **E-wallets**: Touch 'n Go eWallet, Boost, GrabPay

#### ToyyibPay
- **FPX Online Banking**: Malaysian banks
- **Credit/Debit Cards**: Local and international cards

#### Chip
- **Credit/Debit Cards**: Visa, Mastercard, American Express
- **E-wallets**: Various supported wallets

### International Payments

#### Stripe
- **Credit/Debit Cards**: Global card support
- **Digital Wallets**: Apple Pay, Google Pay
- **Bank Transfers**: ACH, SEPA, and other local methods

## Troubleshooting

### Common Issues

#### Payment Not Processing
**Problem**: Clicked "Pay Now" but nothing happens
**Solution**:
- Check internet connection
- Ensure popup blockers are disabled
- Try refreshing the page and clicking again

#### Payment Completed but Status Not Updated
**Problem**: Paid at gateway but ticket still shows "Pending"
**Solution**:
- Wait 5-10 minutes for automatic verification
- Refresh the My Tickets page
- Contact support if issue persists

#### Cannot Access Payment Gateway
**Problem**: Error when redirected to payment page
**Solution**:
- Check if payment gateway is in maintenance
- Try a different payment method
- Contact event organizer

#### Payment Failed
**Problem**: Payment was declined or failed
**Solution**:
- Check card/account balance
- Verify payment details are correct
- Try alternative payment method
- Contact your bank if needed

### Getting Help

#### For Attendees
1. **Check FAQ**: Common payment questions answered
2. **Contact Event Organizer**: Use contact details on event page
3. **Support Ticket**: Submit through help center

#### For Organizers
1. **Payment Gateway Support**: Contact your gateway provider
2. **Platform Support**: Submit technical support ticket
3. **Documentation**: Check payment system documentation

## Security and Privacy

### Payment Security
- **PCI Compliance**: All payments processed securely
- **No Card Storage**: Card details never stored on our servers
- **Encrypted Transmission**: All data encrypted in transit
- **Fraud Protection**: Advanced fraud detection systems

### Data Privacy
- **Minimal Data Collection**: Only necessary payment information collected
- **Secure Storage**: Payment records encrypted and secured
- **Access Control**: Strict access controls on payment data
- **Compliance**: GDPR and local privacy law compliance

## Best Practices

### For Attendees
- **Pay Early**: Complete payment soon after registration
- **Keep Records**: Save receipts and tickets
- **Check Details**: Verify event details before payment
- **Secure Connection**: Only pay on secure networks

### For Organizers
- **Test Payments**: Test payment flow before event launch
- **Monitor Status**: Regularly check registration payment status
- **Clear Pricing**: Display clear pricing and payment terms
- **Support Ready**: Prepare to help attendees with payment issues

## Payment Policies

### Refunds
- **Event Cancellation**: Full refund if event cancelled by organizer
- **Attendee Cancellation**: Per event organizer's refund policy
- **Processing Time**: 5-10 business days for refunds
- **Method**: Refunds processed to original payment method

### Failed Payments
- **Retry**: Can retry payment multiple times
- **Expiry**: Registration may expire if payment not completed
- **Notification**: Email notifications for payment reminders

### Currency
- **Primary**: Malaysian Ringgit (MYR)
- **International**: USD for international events
- **Conversion**: Real-time exchange rates applied
