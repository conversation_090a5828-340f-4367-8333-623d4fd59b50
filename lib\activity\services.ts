import { logActivity } from './logger'
import { ActivityCategory, ACTIVITY_ACTIONS, ENTITY_TYPES } from './types'
import type { ActivityLogParams } from './types'

/**
 * High-level activity logging services for different domains
 */

/**
 * Authentication activity logging
 */
export class AuthActivityService {
  static async logLogin(userId: string, details: Record<string, any> = {}, ipAddress?: string, userAgent?: string) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.LOGIN,
      entityType: ENTITY_TYPES.AUTH,
      category: ActivityCategory.AUTH,
      details,
      ipAddress,
      userAgent,
    })
  }

  static async logLogout(userId: string, details: Record<string, any> = {}, ipAddress?: string, userAgent?: string) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.LOGOUT,
      entityType: ENTITY_TYPES.AUTH,
      category: ActivityCategory.AUTH,
      details,
      ipAddress,
      userAgent,
    })
  }

  static async logRegister(userId: string, details: Record<string, any> = {}, ipAddress?: string, userAgent?: string) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.REGISTER,
      entityType: ENTITY_TYPES.AUTH,
      category: ActivityCategory.AUTH,
      details,
      ipAddress,
      userAgent,
    })
  }

  static async logPasswordReset(userId?: string, details: Record<string, any> = {}, ipAddress?: string, userAgent?: string) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.PASSWORD_RESET,
      entityType: ENTITY_TYPES.AUTH,
      category: ActivityCategory.AUTH,
      details,
      ipAddress,
      userAgent,
    })
  }

  static async logPasswordChange(userId: string, details: Record<string, any> = {}, ipAddress?: string, userAgent?: string) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.PASSWORD_CHANGE,
      entityType: ENTITY_TYPES.AUTH,
      category: ActivityCategory.AUTH,
      details,
      ipAddress,
      userAgent,
    })
  }
}

/**
 * Event activity logging
 */
export class EventActivityService {
  static async logEventCreate(userId: string, eventId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.EVENT_CREATE,
      entityType: ENTITY_TYPES.EVENT,
      entityId: eventId,
      category: ActivityCategory.EVENT,
      details,
    })
  }

  static async logEventUpdate(userId: string, eventId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.EVENT_UPDATE,
      entityType: ENTITY_TYPES.EVENT,
      entityId: eventId,
      category: ActivityCategory.EVENT,
      details,
    })
  }

  static async logEventPublish(userId: string, eventId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.EVENT_PUBLISH,
      entityType: ENTITY_TYPES.EVENT,
      entityId: eventId,
      category: ActivityCategory.EVENT,
      details,
    })
  }

  static async logEventDelete(userId: string, eventId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.EVENT_DELETE,
      entityType: ENTITY_TYPES.EVENT,
      entityId: eventId,
      category: ActivityCategory.EVENT,
      details,
    })
  }
}

/**
 * Payment activity logging
 */
export class PaymentActivityService {
  static async logPaymentCreate(userId: string, paymentId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.PAYMENT_CREATE,
      entityType: ENTITY_TYPES.PAYMENT,
      entityId: paymentId,
      category: ActivityCategory.PAYMENT,
      details,
    })
  }

  static async logPaymentSuccess(userId: string, paymentId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.PAYMENT_SUCCESS,
      entityType: ENTITY_TYPES.PAYMENT,
      entityId: paymentId,
      category: ActivityCategory.PAYMENT,
      details,
    })
  }

  static async logPaymentFailed(userId: string, paymentId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.PAYMENT_FAILED,
      entityType: ENTITY_TYPES.PAYMENT,
      entityId: paymentId,
      category: ActivityCategory.PAYMENT,
      details,
    })
  }
}

/**
 * Certificate activity logging
 */
export class CertificateActivityService {
  static async logCertificateGenerate(userId: string, certificateId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.CERTIFICATE_GENERATE,
      entityType: ENTITY_TYPES.CERTIFICATE,
      entityId: certificateId,
      category: ActivityCategory.CERTIFICATE,
      details,
    })
  }

  static async logCertificateVerify(userId?: string, certificateId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.CERTIFICATE_VERIFY,
      entityType: ENTITY_TYPES.CERTIFICATE,
      entityId: certificateId,
      category: ActivityCategory.CERTIFICATE,
      details,
    })
  }
}

/**
 * Organization activity logging
 */
export class OrganizationActivityService {
  static async logOrganizationCreate(userId: string, organizationId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.ORGANIZATION_CREATE,
      entityType: ENTITY_TYPES.ORGANIZATION,
      entityId: organizationId,
      category: ActivityCategory.ORGANIZATION,
      details,
    })
  }

  static async logOrganizationUpdate(userId: string, organizationId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.ORGANIZATION_UPDATE,
      entityType: ENTITY_TYPES.ORGANIZATION,
      entityId: organizationId,
      category: ActivityCategory.ORGANIZATION,
      details,
    })
  }

  static async logOrganizationLink(userId: string, organizationId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.ORGANIZATION_LINK,
      entityType: ENTITY_TYPES.ORGANIZATION,
      entityId: organizationId,
      category: ActivityCategory.ORGANIZATION,
      details,
    })
  }
}

/**
 * User activity logging
 */
export class UserActivityService {
  static async logUserCreate(adminUserId: string, newUserId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId: adminUserId,
      action: ACTIVITY_ACTIONS.USER_CREATE,
      entityType: ENTITY_TYPES.USER,
      entityId: newUserId,
      category: ActivityCategory.USER,
      details,
    })
  }

  static async logUserUpdate(adminUserId: string, targetUserId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId: adminUserId,
      action: ACTIVITY_ACTIONS.USER_UPDATE,
      entityType: ENTITY_TYPES.USER,
      entityId: targetUserId,
      category: ActivityCategory.USER,
      details,
    })
  }

  static async logRoleChange(adminUserId: string, targetUserId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId: adminUserId,
      action: ACTIVITY_ACTIONS.ROLE_CHANGE,
      entityType: ENTITY_TYPES.USER,
      entityId: targetUserId,
      category: ActivityCategory.USER,
      details,
    })
  }

  static async logProfileUpdate(userId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.PROFILE_UPDATE,
      entityType: ENTITY_TYPES.USER,
      entityId: userId,
      category: ActivityCategory.USER,
      details,
    })
  }
}

/**
 * System activity logging
 */
export class SystemActivityService {
  static async logApiAccess(userId?: string, details: Record<string, any> = {}, ipAddress?: string, userAgent?: string) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.API_ACCESS,
      entityType: ENTITY_TYPES.SYSTEM,
      category: ActivityCategory.SYSTEM,
      details,
      ipAddress,
      userAgent,
    })
  }

  static async logWebhookTrigger(userId?: string, webhookId: string, details: Record<string, any> = {}) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.WEBHOOK_TRIGGER,
      entityType: ENTITY_TYPES.WEBHOOK,
      entityId: webhookId,
      category: ActivityCategory.SYSTEM,
      details,
    })
  }

  static async logError(userId?: string, details: Record<string, any> = {}, ipAddress?: string, userAgent?: string) {
    await logActivity({
      userId,
      action: ACTIVITY_ACTIONS.ERROR_OCCURRED,
      entityType: ENTITY_TYPES.SYSTEM,
      category: ActivityCategory.SYSTEM,
      details,
      ipAddress,
      userAgent,
    })
  }
}
