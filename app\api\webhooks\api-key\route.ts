import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"
import { getUserFromToken } from "@/lib/auth/token"
import { requireSubscriptionFeature } from "@/lib/subscription-utils"
import crypto from "crypto"

export async function GET(request: NextRequest) {
  try {
    // Get user from token
    const userToken = await getUserFromToken(request)
    if (!userToken) {
      return NextResponse.json(
        {
          success: false,
          error: "Authentication required",
        },
        { status: 401 }
      )
    }

    const supabase = createClient()

    // Get user details
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userToken.userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        {
          success: false,
          error: "User not found",
        },
        { status: 404 }
      )
    }

    // Check subscription feature access
    const featureCheck = await requireSubscriptionFeature(userToken.userId, 'webhooks_enabled', user.role_name)
    if (!featureCheck.hasAccess) {
      return NextResponse.json(
        {
          success: false,
          error: featureCheck.error,
        },
        { status: 403 }
      )
    }

    // Get or create API key for user
    let { data: apiKeyRecord, error: apiKeyError } = await supabase
      .from('api_keys')
      .select('*')
      .eq('user_id', userToken.userId)
      .single()

    if (apiKeyError && apiKeyError.code !== 'PGRST116') {
      console.error("Error fetching API key:", apiKeyError)
      return NextResponse.json(
        {
          success: false,
          error: "Failed to fetch API key",
        },
        { status: 500 }
      )
    }

    // If no API key exists, create one
    if (!apiKeyRecord) {
      const apiKey = `mtk_${crypto.randomBytes(32).toString('hex')}`

      const { data: newApiKey, error: createError } = await supabase
        .from('api_keys')
        .insert({
          user_id: userToken.userId,
          key: apiKey,
        })
        .select()
        .single()

      if (createError) {
        console.error("Error creating API key:", createError)
        return NextResponse.json(
          {
            success: false,
            error: "Failed to create API key",
          },
          { status: 500 }
        )
      }

      apiKeyRecord = newApiKey
    }

    return NextResponse.json({
      success: true,
      apiKey: apiKeyRecord.key,
    })

  } catch (error) {
    console.error("Error in API key endpoint:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get user from token
    const userToken = await getUserFromToken(request)
    if (!userToken) {
      return NextResponse.json(
        {
          success: false,
          error: "Authentication required",
        },
        { status: 401 }
      )
    }

    const supabase = createClient()

    // Get user details
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', userToken.userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        {
          success: false,
          error: "User not found",
        },
        { status: 404 }
      )
    }

    // Check subscription feature access
    const featureCheck = await requireSubscriptionFeature(userToken.userId, 'webhooks_enabled', user.role_name)
    if (!featureCheck.hasAccess) {
      return NextResponse.json(
        {
          success: false,
          error: featureCheck.error,
        },
        { status: 403 }
      )
    }

    // Delete existing API keys
    await supabase
      .from('api_keys')
      .delete()
      .eq('user_id', userToken.userId)

    // Generate new API key
    const apiKey = `mtk_${crypto.randomBytes(32).toString('hex')}`

    const { data: newApiKey, error: createError } = await supabase
      .from('api_keys')
      .insert({
        user_id: userToken.userId,
        key: apiKey,
      })
      .select()
      .single()

    if (createError) {
      console.error("Error creating new API key:", createError)
      return NextResponse.json(
        {
          success: false,
          error: "Failed to regenerate API key",
        },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      apiKey: newApiKey.key,
    })

  } catch (error) {
    console.error("Error regenerating API key:", error)
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    )
  }
}
