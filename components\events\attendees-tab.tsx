"use client"

import { useEffect, useState } from "react"
import { Download, Mail, Search, UserPlus } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"

interface AttendeesTabProps {
  eventSlug: string
}

export function AttendeesTab({ eventSlug }: AttendeesTabProps) {
  const [event, setEvent] = useState<any>(null)
  const [attendees, setAttendees] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const { toast } = useToast()

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      try {
        // Fetch event details by slug
        const { data: eventData, error: eventError } = await supabase
          .from("events")
          .select("*")
          .eq("slug", eventSlug)
          .single()

        if (eventError) {
          if (eventError.code === "PGRST116") {
            toast({
              title: "Event not found",
              description: "The event you're looking for doesn't exist or has been removed.",
              variant: "destructive",
            })
            return
          }
          throw eventError
        }

        setEvent(eventData)

        // Fetch attendees using the event ID
        const { data: attendeesData, error: attendeesError } = await supabase
          .from("registrations")
          .select("*")
          .eq("event_id", eventData.id)
          .order("created_at", { ascending: false })

        if (attendeesError) {
          throw attendeesError
        }

        // Transform the data to match the expected format
        const transformedAttendees = (attendeesData || []).map((attendee) => ({
          ...attendee,
          full_name: attendee.attendee_name,
          email: attendee.attendee_email,
          ticket_type: attendee.ticket_type || "General",
          payment_status: attendee.payment_status || "pending",
          attendance_status: attendee.status || "confirmed",
        }))

        setAttendees(transformedAttendees)
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: "Failed to fetch attendees data",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    if (eventSlug) {
      fetchData()
    }
  }, [eventSlug, toast])

  const handleExportCSV = () => {
    if (!attendees.length) return

    const csvHeaders = ["Name", "Email", "Phone", "Ticket Type", "Payment Status", "Attendance", "Registration Date"]
    const csvData = attendees.map((attendee) => [
      attendee.full_name || "",
      attendee.email || "",
      attendee.phone || "",
      attendee.ticket_type || "",
      attendee.payment_status || "",
      attendee.attendance_status || "",
      new Date(attendee.created_at).toLocaleDateString(),
    ])

    const csvContent = [csvHeaders, ...csvData]
      .map((row) => row.map((field) => `"${field}"`).join(","))
      .join("\n")

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
    const link = document.createElement("a")
    const url = URL.createObjectURL(blob)
    link.setAttribute("href", url)
    link.setAttribute("download", `${event?.title || "event"}-attendees.csv`)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    toast({
      title: "Success",
      description: "Attendees data exported successfully",
    })
  }

  const filteredAttendees = attendees.filter(
    (attendee) =>
      attendee.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      attendee.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      attendee.phone?.includes(searchQuery),
  )

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Manage Attendees</CardTitle>
          <CardDescription>View and manage attendees for this event</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-6 flex flex-col sm:flex-row items-center gap-4">
            <div className="relative flex-1 w-full">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search attendees..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2 w-full sm:w-auto">
              <Button variant="outline" onClick={handleExportCSV} disabled={!attendees.length}>
                <Download className="mr-2 h-4 w-4" />
                Export CSV
              </Button>
              <Button>
                <UserPlus className="mr-2 h-4 w-4" />
                Add Attendee
              </Button>
            </div>
          </div>

          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center gap-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-40" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                  <div className="ml-auto">
                    <Skeleton className="h-8 w-20" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Attendee</TableHead>
                    <TableHead>Ticket Type</TableHead>
                    <TableHead>Payment Status</TableHead>
                    <TableHead>Attendance</TableHead>
                    <TableHead>Registration Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAttendees.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="h-24 text-center">
                        No attendees found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredAttendees.map((attendee) => (
                      <TableRow key={attendee.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="font-medium">{attendee.full_name}</div>
                          </div>
                          <div className="text-sm text-muted-foreground">{attendee.email}</div>
                        </TableCell>
                        <TableCell>{attendee.ticket_type}</TableCell>
                        <TableCell>
                          <PaymentStatusBadge status={attendee.payment_status} />
                        </TableCell>
                        <TableCell>
                          <AttendanceStatusBadge status={attendee.attendance_status} />
                        </TableCell>
                        <TableCell>{new Date(attendee.created_at).toLocaleDateString()}</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" size="sm">
                            <Mail className="h-4 w-4" />
                            <span className="sr-only">Email</span>
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

function PaymentStatusBadge({ status }: { status: string }) {
  switch (status) {
    case "paid":
      return <Badge className="bg-green-100 text-green-800 border-green-200">Paid</Badge>
    case "pending":
      return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Pending</Badge>
    case "failed":
      return <Badge variant="destructive">Failed</Badge>
    case "refunded":
      return <Badge variant="outline">Refunded</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

function AttendanceStatusBadge({ status }: { status: string }) {
  switch (status) {
    case "confirmed":
      return <Badge variant="secondary">Confirmed</Badge>
    case "attended":
      return <Badge className="bg-green-100 text-green-800 border-green-200">Attended</Badge>
    case "no_show":
      return <Badge variant="destructive">No Show</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}
