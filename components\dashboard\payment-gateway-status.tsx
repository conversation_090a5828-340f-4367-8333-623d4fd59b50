import { CheckCircle, AlertCircle } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface PaymentGatewayStatusProps {
  gateways: {
    name: string
    type: string
    enabled: boolean
  }[]
}

export function PaymentGatewayStatus({ gateways }: PaymentGatewayStatusProps) {
  const enabledGateways = gateways.filter((gateway) => gateway.enabled)
  const hasEnabledGateways = enabledGateways.length > 0

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium">Payment Gateways</CardTitle>
        <CardDescription>Active payment methods</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {hasEnabledGateways ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <AlertCircle className="h-5 w-5 text-amber-500" />
            )}
            <span className="font-medium">{hasEnabledGateways ? "Gateways Active" : "No Active Gateways"}</span>
          </div>
          <div className="flex flex-wrap gap-1">
            {enabledGateways.map((gateway) => (
              <Badge key={gateway.name} variant="outline" className="text-xs">
                {gateway.name}
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
