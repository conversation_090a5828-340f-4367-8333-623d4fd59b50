import { NextResponse } from "next/server";
import { hashPassword, verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth";
import { supabase } from "@/lib/supabase";

export async function POST(request: Request) {
  try {
    // Check if we're in development mode
    const isDevelopment = process.env.NODE_ENV === "development";
    console.log("API auth/admin/update-password-rpc - Is development mode:", isDevelopment);

    // Get JWT token from request
    const token = getJWTTokenFromRequest(request);
    console.log("API auth/admin/update-password-rpc - Token present:", !!token);

    // In production, verify authentication and admin role
    // In development, allow access without authentication for easier testing
    if (!isDevelopment) {
      if (!token) {
        console.log("API auth/admin/update-password-rpc - Access denied: No token provided");
        return NextResponse.json(
          { error: "Unauthorized. Admin access required." },
          { status: 403 }
        );
      }

      // Verify the JWT token and get user data
      const authResult = await verifyJWTToken(token);
      if (!authResult?.user) {
        console.log("API auth/admin/update-password-rpc - Access denied: Invalid token");
        return NextResponse.json(
          { error: "Unauthorized. Invalid token." },
          { status: 403 }
        );
      }

      // Check if user has admin role (only "admin" role has full access)
      const userRole = authResult.user.role_name || authResult.user.role;

      if (userRole !== "admin") {
        console.log("API auth/admin/update-password-rpc - Access denied: Not admin role. User role:", userRole);
        return NextResponse.json(
          { error: "Unauthorized. Admin access required." },
          { status: 403 }
        );
      }

      console.log("API auth/admin/update-password-rpc - Access granted to admin user:", authResult.user.email);
    } else {
      console.log("API auth/admin/update-password-rpc - Development mode: Allowing access without authentication");
    }

    const { userId, password } = await request.json();

    // Validate input
    if (!userId || !password) {
      return NextResponse.json(
        { error: "User ID and password are required" },
        { status: 400 }
      );
    }

    // First, check if the user exists
    const { data: userData, error: userError } = await supabase
      .from("users")
      .select("email")
      .eq("id", userId)
      .single();

    if (userError || !userData) {
      console.error("User not found:", userError);
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Hash the password using our custom function
    const passwordHash = await hashPassword(password);

    // Call the RPC function to update the password
    // This function will be created in the database with appropriate permissions
    const { data, error } = await supabase.rpc('update_user_password', {
      user_id: userId,
      new_password_hash: passwordHash
    });

    if (error) {
      console.error("Error updating password:", error);
      return NextResponse.json(
        { error: "Failed to update password: " + error.message },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true, data });
  } catch (error: any) {
    console.error("Password update error:", error);
    return NextResponse.json(
      { error: error.message || "Password update failed" },
      { status: 500 }
    );
  }
}
