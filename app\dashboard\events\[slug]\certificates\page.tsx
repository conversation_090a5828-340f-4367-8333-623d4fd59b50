"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { ArrowLeft, Award, Download, FileText, Mail, Search, Settings } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"

export default function EventCertificatesPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [event, setEvent] = useState<any>(null)
  const [attendees, setAttendees] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const eventSlug = params.slug as string

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true)
      try {
        // Fetch event details by slug
        const { data: eventData, error: eventError } = await supabase
          .from("events")
          .select("*")
          .eq("slug", eventSlug)
          .single()

        if (eventError) {
          if (eventError.code === "PGRST116") {
            toast({
              title: "Event not found",
              description: "The event you're looking for doesn't exist or has been removed.",
              variant: "destructive",
            })
            router.push("/dashboard/events")
            return
          }
          throw eventError
        }

        setEvent(eventData)

        // Fetch all registrations for this event
        const { data: attendeesData, error: attendeesError } = await supabase
          .from("registrations")
          .select(`
            id,
            attendee_name,
            attendee_email,
            status,
            checked_in_at,
            created_at
          `)
          .eq("event_id", eventData.id)
          .order("created_at", { ascending: false })

        if (attendeesError) throw attendeesError

        // Fetch existing certificates for this event
        const { data: certificatesData, error: certificatesError } = await supabase
          .from("certificates")
          .select("*")
          .eq("event_id", eventData.id)

        if (certificatesError) {
          console.warn("Error fetching certificates:", certificatesError)
        }

        // Create a map of registration_id to certificate data
        const certificatesMap = new Map()
        if (certificatesData) {
          certificatesData.forEach((cert) => {
            certificatesMap.set(cert.registration_id, cert)
          })
        }

        // Transform the data to match the expected format
        const transformedAttendees = (attendeesData || []).map((attendee) => {
          const certificate = certificatesMap.get(attendee.id)
          return {
            ...attendee,
            full_name: attendee.attendee_name,
            email: attendee.attendee_email,
            certificate_id: certificate?.unique_code || null,
            certificate_status: certificate ? "issued" : "pending",
            certificate_url: certificate?.verification_url || null,
            certificate_generated_at: certificate?.generated_at || null,
          }
        })

        setAttendees(transformedAttendees)
      } catch (error) {
        console.error("Error fetching data:", error)
        toast({
          title: "Error",
          description: "Failed to fetch certificates data. Please try again later.",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    if (eventSlug) {
      fetchData()
    }
  }, [eventSlug, router, toast])

  const handleGenerateCertificates = async () => {
    try {
      setLoading(true)

      // Filter attendees who don't have certificates yet
      const attendeesWithoutCertificates = attendees.filter(
        (attendee) => !attendee.certificate_id
      )

      if (attendeesWithoutCertificates.length === 0) {
        toast({
          title: "Info",
          description: "All attendees already have certificates",
        })
        return
      }

      // Generate certificates for each attendee
      const certificatePromises = attendeesWithoutCertificates.map(async (attendee) => {
        const uniqueCode = Math.random().toString(36).substring(2, 10).toUpperCase()
        const verificationUrl = `${window.location.origin}/certificates/verify/${uniqueCode}`

        const certificateData = {
          registration_id: attendee.id,
          template_id: "default-template", // You can make this configurable
          unique_code: uniqueCode,
          file_path: null, // Will be generated later
          verification_url: verificationUrl,
          qr_code_data: verificationUrl,
          recipient_name: attendee.full_name,
          event_id: event.id,
          event_title: event.title,
          generated_at: new Date().toISOString(),
        }

        const { data, error } = await supabase
          .from("certificates")
          .insert([certificateData])
          .select()
          .single()

        if (error) throw error
        return data
      })

      await Promise.all(certificatePromises)

      // Refresh the attendees data
      const { data: updatedAttendeesData } = await supabase
        .from("registrations")
        .select(`
          id,
          attendee_name,
          attendee_email,
          status,
          checked_in_at,
          created_at
        `)
        .eq("event_id", event.id)
        .order("created_at", { ascending: false })

      // Fetch updated certificates
      const { data: updatedCertificatesData } = await supabase
        .from("certificates")
        .select("*")
        .eq("event_id", event.id)

      // Create updated certificates map
      const updatedCertificatesMap = new Map()
      if (updatedCertificatesData) {
        updatedCertificatesData.forEach((cert) => {
          updatedCertificatesMap.set(cert.registration_id, cert)
        })
      }

      // Transform updated data
      const updatedTransformedAttendees = (updatedAttendeesData || []).map((attendee) => {
        const certificate = updatedCertificatesMap.get(attendee.id)
        return {
          ...attendee,
          full_name: attendee.attendee_name,
          email: attendee.attendee_email,
          certificate_id: certificate?.unique_code || null,
          certificate_status: certificate ? "issued" : "pending",
          certificate_url: certificate?.verification_url || null,
          certificate_generated_at: certificate?.generated_at || null,
        }
      })

      setAttendees(updatedTransformedAttendees)

      toast({
        title: "Success",
        description: `Generated ${attendeesWithoutCertificates.length} certificates successfully`,
      })
    } catch (error) {
      console.error("Error generating certificates:", error)
      toast({
        title: "Error",
        description: "Failed to generate certificates",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleGenerateIndividualCertificate = async (attendee: any) => {
    try {
      const uniqueCode = Math.random().toString(36).substring(2, 10).toUpperCase()
      const verificationUrl = `${window.location.origin}/certificates/verify/${uniqueCode}`

      const certificateData = {
        registration_id: attendee.id,
        template_id: "default-template",
        unique_code: uniqueCode,
        file_path: null,
        verification_url: verificationUrl,
        qr_code_data: verificationUrl,
        recipient_name: attendee.full_name,
        event_id: event.id,
        event_title: event.title,
        generated_at: new Date().toISOString(),
      }

      const { data, error } = await supabase
        .from("certificates")
        .insert([certificateData])
        .select()
        .single()

      if (error) throw error

      // Update the attendee in the local state
      setAttendees(prev => prev.map(a =>
        a.id === attendee.id
          ? {
              ...a,
              certificate_id: uniqueCode,
              certificate_status: "issued",
              certificate_url: verificationUrl,
              certificate_generated_at: new Date().toISOString(),
            }
          : a
      ))

      toast({
        title: "Success",
        description: `Certificate generated for ${attendee.full_name}`,
      })
    } catch (error) {
      console.error("Error generating certificate:", error)
      toast({
        title: "Error",
        description: "Failed to generate certificate",
        variant: "destructive",
      })
    }
  }

  const handleSendIndividualCertificate = async (attendee: any) => {
    try {
      // In a real app, this would call an API to send certificate via email
      toast({
        title: "Success",
        description: `Certificate sent to ${attendee.full_name}`,
      })
    } catch (error) {
      console.error("Error sending certificate:", error)
      toast({
        title: "Error",
        description: "Failed to send certificate",
        variant: "destructive",
      })
    }
  }

  const handleSendCertificates = () => {
    try {
      // In a real app, this would call an API to send certificates via email
      const issuedCertificates = attendees.filter(a => a.certificate_status === "issued")
      toast({
        title: "Success",
        description: `Sent ${issuedCertificates.length} certificates successfully`,
      })
    } catch (error) {
      console.error("Error sending certificates:", error)
      toast({
        title: "Error",
        description: "Failed to send certificates",
        variant: "destructive",
      })
    }
  }

  const filteredAttendees = attendees.filter(
    (attendee) =>
      attendee.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      attendee.email?.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="mb-6 flex items-center justify-between">
        <Link href={`/dashboard/events/${eventSlug}`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Event
          </Button>
        </Link>
        <h1 className="text-2xl font-bold">{loading ? "Loading..." : `Certificates: ${event?.title}`}</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Manage Certificates</CardTitle>
          <CardDescription>Generate and send certificates to attendees</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-6 flex flex-col sm:flex-row items-center gap-4">
            <div className="relative flex-1 w-full">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search attendees..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2 w-full sm:w-auto">
              <Button
                variant="outline"
                onClick={handleSendCertificates}
                disabled={!attendees.some(a => a.certificate_status === "issued")}
              >
                <Mail className="mr-2 h-4 w-4" />
                Send All
              </Button>
              <Button variant="outline">
                <Settings className="mr-2 h-4 w-4" />
                Template
              </Button>
              <Button
                onClick={handleGenerateCertificates}
                disabled={!attendees.some(a => a.certificate_status === "pending")}
              >
                <Award className="mr-2 h-4 w-4" />
                Generate All
              </Button>
            </div>
          </div>

          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center gap-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-40" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                  <div className="ml-auto">
                    <Skeleton className="h-8 w-20" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Attendee</TableHead>
                    <TableHead>Certificate ID</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Issue Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAttendees.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="h-24 text-center">
                        No certificates found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredAttendees.map((attendee) => (
                      <TableRow key={attendee.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="font-medium">{attendee.full_name}</div>
                          </div>
                          <div className="text-sm text-muted-foreground">{attendee.email}</div>
                        </TableCell>
                        <TableCell>{attendee.certificate_id || "Not issued"}</TableCell>
                        <TableCell>
                          <CertificateStatusBadge status={attendee.certificate_status} />
                        </TableCell>
                        <TableCell>
                          {attendee.certificate_id ?
                            (attendee.certificate_generated_at ?
                              new Date(attendee.certificate_generated_at).toLocaleDateString() :
                              new Date(attendee.created_at).toLocaleDateString()
                            ) :
                            "Not issued"
                          }
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            {attendee.certificate_status === "issued" && (
                              <>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => window.open(attendee.certificate_url, '_blank')}
                                  title="View Certificate"
                                >
                                  <Download className="h-4 w-4" />
                                  <span className="sr-only">Download</span>
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleSendIndividualCertificate(attendee)}
                                  title="Send Certificate"
                                >
                                  <Mail className="h-4 w-4" />
                                  <span className="sr-only">Email</span>
                                </Button>
                              </>
                            )}
                            {attendee.certificate_status === "pending" && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleGenerateIndividualCertificate(attendee)}
                              >
                                <FileText className="h-4 w-4" />
                                <span className="sr-only">Generate</span>
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

function CertificateStatusBadge({ status }: { status: string }) {
  switch (status) {
    case "issued":
      return <Badge variant="success">Issued</Badge>
    case "pending":
      return <Badge variant="secondary">Pending</Badge>
    case "revoked":
      return <Badge variant="destructive">Revoked</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}
