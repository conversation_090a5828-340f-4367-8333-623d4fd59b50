import { supabase } from "@/lib/supabase"
import { getSupabaseAdmin } from "@/lib/supabase"

/**
 * Activity log categories
 */
export enum ActivityCategory {
  AUTH = "auth",
  USER = "user",
  EVENT = "event",
  REGISTRATION = "registration",
  PAYMENT = "payment",
  CERTIFICATE = "certificate",
  EXPORT = "export",
  SETTINGS = "settings",
  SYSTEM = "system",
  ORGANIZATION = "organization",
  WEBHOOK = "webhook",
  ATTENDANCE = "attendance",
}

/**
 * Parameters for logging an activity
 */
export type ActivityLogParams = {
  user_id?: string // Optional for system actions or anonymous users
  action: string // The action performed (e.g., 'login', 'create_event')
  entity_type: string // The type of entity (e.g., 'user', 'event')
  entity_id?: string // Optional ID of the entity
  category?: string // Category for filtering
  details?: Record<string, any> // Additional details about the action
  ip_address?: string // IP address of the user
  user_agent?: string // User agent of the browser
}

/**
 * Log an activity in the system
 *
 * @param params Activity log parameters
 * @returns Promise<void>
 */
export async function logActivity(params: ActivityLogParams): Promise<void> {
  try {
    const supabaseAdmin = getSupabaseAdmin()
    
    // Use provided IP and user agent
    const ip = params.ip_address || 'not_provided'
    const ua = params.user_agent || 'not_provided'

    // Determine category if not provided
    const logCategory = params.category || getCategoryFromEntityType(params.entity_type)

    // Insert activity log
    await supabaseAdmin.from("activity_logs").insert([
      {
        user_id: params.user_id,
        action: params.action,
        entity_type: params.entity_type,
        entity_id: params.entity_id,
        category: logCategory,
        details: params.details || {},
        ip_address: ip,
        user_agent: ua,
        created_at: new Date().toISOString(),
      },
    ])
  } catch (error) {
    console.error("Error logging activity:", error)
  }
}

/**
 * Helper function to determine category from entity type
 */
function getCategoryFromEntityType(entityType: string): string {
  switch (entityType.toLowerCase()) {
    case 'user':
    case 'profile':
      return ActivityCategory.USER
    case 'event':
      return ActivityCategory.EVENT
    case 'registration':
    case 'ticket':
      return ActivityCategory.REGISTRATION
    case 'payment':
    case 'transaction':
      return ActivityCategory.PAYMENT
    case 'certificate':
      return ActivityCategory.CERTIFICATE
    case 'auth':
    case 'login':
    case 'logout':
    case 'password':
      return ActivityCategory.AUTH
    case 'export':
    case 'report':
      return ActivityCategory.EXPORT
    case 'setting':
    case 'configuration':
    case 'payment_gateway':
      return ActivityCategory.SETTINGS
    case 'organization':
      return ActivityCategory.ORGANIZATION
    case 'webhook':
    case 'api':
      return ActivityCategory.WEBHOOK
    case 'attendance':
      return ActivityCategory.ATTENDANCE
    default:
      return ActivityCategory.SYSTEM
  }
}
