import { supabase } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

/**
 * Validates an API key from the request headers
 * @param apiKey The API key to validate
 * @returns True if the API key is valid, false otherwise
 */
export async function validateApiKey(apiKey: string): Promise<boolean> {
  try {
    if (!apiKey || !apiKey.startsWith("mtk_")) {
      return false;
    }

    // Check if the API key exists in the database
    const { data, error } = await supabase
      .from("api_keys")
      .select("*")
      .eq("key", apiKey)
      .single();

    if (error || !data) {
      console.error("API key validation error:", error);
      return false;
    }

    return true;
  } catch (error) {
    console.error("Error validating API key:", error);
    return false;
  }
}

/**
 * Middleware function to validate API key from request headers
 * @param request The Next.js request object
 * @returns A response if the API key is invalid, or null to continue
 */
export async function validateApiKeyMiddleware(request: NextRequest): Promise<NextResponse | null> {
  try {
    // Extract the API key from the Authorization header
    const authHeader = request.headers.get("Authorization");
    
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return NextResponse.json(
        { error: "Missing or invalid Authorization header" },
        { status: 401 }
      );
    }

    const apiKey = authHeader.substring(7); // Remove "Bearer " prefix
    
    // Validate the API key
    const isValid = await validateApiKey(apiKey);
    
    if (!isValid) {
      return NextResponse.json(
        { error: "Invalid API key" },
        { status: 401 }
      );
    }
    
    // API key is valid, continue with the request
    return null;
  } catch (error) {
    console.error("Error in API key middleware:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * Gets the user ID associated with an API key
 * @param apiKey The API key
 * @returns The user ID, or null if the API key is invalid
 */
export async function getUserIdFromApiKey(apiKey: string): Promise<string | null> {
  try {
    if (!apiKey || !apiKey.startsWith("mtk_")) {
      return null;
    }

    // Get the API key from the database
    const { data, error } = await supabase
      .from("api_keys")
      .select("user_id")
      .eq("key", apiKey)
      .single();

    if (error || !data) {
      console.error("Error getting user ID from API key:", error);
      return null;
    }

    return data.user_id;
  } catch (error) {
    console.error("Error getting user ID from API key:", error);
    return null;
  }
}
