import jsPD<PERSON> from 'jspdf'
import html2canvas from 'html2canvas'
import QRCode from 'qrcode'

export interface TicketData {
  id: string
  event: {
    title: string
    start_date: string
    end_date: string
    location: string
    image_url?: string
  }
  guest_name: string
  guest_email: string
  phone?: string
  payment_amount?: number
  payment_status: string
  registration_code: string
  created_at: string
  qr_code_data: string
}

export class TicketPDFGenerator {
  private pdf: jsPDF
  private pageWidth: number
  private pageHeight: number
  private margin: number

  constructor() {
    // A4 size in landscape for ticket format
    this.pdf = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4'
    })
    this.pageWidth = this.pdf.internal.pageSize.getWidth()
    this.pageHeight = this.pdf.internal.pageSize.getHeight()
    this.margin = 10
  }

  async generateTicketPDF(ticketData: TicketData): Promise<Blob> {
    try {
      // Create ticket HTML element
      const ticketElement = await this.createTicketElement(ticketData)

      // Add to DOM temporarily for rendering
      document.body.appendChild(ticketElement)

      try {
        // Wait a moment for the element to be fully rendered
        await new Promise(resolve => setTimeout(resolve, 100))

        // Convert HTML to canvas
        const canvas = await html2canvas(ticketElement, {
          scale: 2,
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          width: 800,
          height: 400,
          logging: false
        })

        // Convert canvas to image
        const imgData = canvas.toDataURL('image/png')

        // Add image to PDF
        const imgWidth = this.pageWidth - (this.margin * 2)
        const imgHeight = (canvas.height * imgWidth) / canvas.width

        this.pdf.addImage(imgData, 'PNG', this.margin, this.margin, imgWidth, imgHeight)

        // Add security watermark
        this.addSecurityWatermark()

        // Return PDF as blob
        return new Blob([this.pdf.output('blob')], { type: 'application/pdf' })

      } finally {
        // Clean up
        if (document.body.contains(ticketElement)) {
          document.body.removeChild(ticketElement)
        }
      }
    } catch (error) {
      console.error('Error generating ticket PDF:', error)
      throw new Error('Failed to generate ticket PDF')
    }
  }

  private async createTicketElement(ticketData: TicketData): Promise<HTMLElement> {
    const ticketDiv = document.createElement('div')
    ticketDiv.style.cssText = `
      width: 800px;
      height: 400px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 20px;
      padding: 30px;
      box-sizing: border-box;
      font-family: 'Arial', sans-serif;
      color: white;
      position: relative;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    `

    // Add decorative elements
    const decorativeCircle1 = document.createElement('div')
    decorativeCircle1.style.cssText = `
      position: absolute;
      width: 200px;
      height: 200px;
      background: rgba(255,255,255,0.1);
      border-radius: 50%;
      top: -100px;
      right: -100px;
    `

    const decorativeCircle2 = document.createElement('div')
    decorativeCircle2.style.cssText = `
      position: absolute;
      width: 150px;
      height: 150px;
      background: rgba(255,255,255,0.05);
      border-radius: 50%;
      bottom: -75px;
      left: -75px;
    `

    ticketDiv.appendChild(decorativeCircle1)
    ticketDiv.appendChild(decorativeCircle2)

    // Main content container
    const contentDiv = document.createElement('div')
    contentDiv.style.cssText = `
      display: flex;
      height: 100%;
      gap: 30px;
      position: relative;
      z-index: 1;
    `

    // Left section - Event details
    const leftSection = document.createElement('div')
    leftSection.style.cssText = `
      flex: 2;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    `

    // Header with logo and title
    const header = document.createElement('div')
    header.innerHTML = `
      <div style="display: flex; align-items: center; margin-bottom: 20px;">
        <div style="background: white; padding: 8px 16px; border-radius: 8px; margin-right: 15px;">
          <span style="color: #667eea; font-weight: bold; font-size: 16px;">mTicket.my</span>
        </div>
        <span style="font-size: 14px; opacity: 0.9;">Event Ticket</span>
      </div>
    `

    // Event details
    const eventDetails = document.createElement('div')
    const eventDate = new Date(ticketData.event.start_date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
    const eventTime = new Date(ticketData.event.start_date).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })

    eventDetails.innerHTML = `
      <h1 style="font-size: 28px; font-weight: bold; margin: 0 0 15px 0; line-height: 1.2;">${ticketData.event.title}</h1>
      <div style="font-size: 16px; opacity: 0.9; margin-bottom: 10px;">
        <div style="margin-bottom: 8px;">📅 ${eventDate}</div>
        <div style="margin-bottom: 8px;">🕐 ${eventTime}</div>
        <div style="margin-bottom: 8px;">📍 ${ticketData.event.location}</div>
      </div>
    `

    // Attendee info
    const attendeeInfo = document.createElement('div')
    attendeeInfo.innerHTML = `
      <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 10px; backdrop-filter: blur(10px);">
        <div style="font-size: 14px; opacity: 0.8; margin-bottom: 5px;">Attendee</div>
        <div style="font-size: 18px; font-weight: bold;">${ticketData.guest_name}</div>
        <div style="font-size: 12px; opacity: 0.7; margin-top: 5px;">Ticket #${ticketData.registration_code}</div>
      </div>
    `

    leftSection.appendChild(header)
    leftSection.appendChild(eventDetails)
    leftSection.appendChild(attendeeInfo)

    // Right section - QR code and verification
    const rightSection = document.createElement('div')
    rightSection.style.cssText = `
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
    `

    // QR Code section with real QR code
    const qrSection = document.createElement('div')
    const qrContainer = document.createElement('div')
    qrContainer.style.cssText = `
      background: white;
      padding: 20px;
      border-radius: 15px;
      margin-bottom: 15px;
      display: flex;
      justify-content: center;
      align-items: center;
    `

    // Generate QR code
    const qrCodeUrl = `https://mticket.my/verify?data=${btoa(ticketData.qr_code_data)}`
    const qrCodeDataURL = await QRCode.toDataURL(qrCodeUrl, {
      width: 120,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })

    // Create QR code image
    const qrImg = document.createElement('img')
    qrImg.src = qrCodeDataURL
    qrImg.style.cssText = 'width: 120px; height: 120px;'

    qrContainer.appendChild(qrImg)
    qrSection.appendChild(qrContainer)

    const qrLabels = document.createElement('div')
    qrLabels.innerHTML = `
      <div style="font-size: 12px; opacity: 0.8; text-align: center;">Scan at entrance</div>
      <div style="font-size: 10px; opacity: 0.6; margin-top: 5px; text-align: center;">Security verified</div>
    `
    qrSection.appendChild(qrLabels)

    rightSection.appendChild(qrSection)

    // Assembly
    contentDiv.appendChild(leftSection)
    contentDiv.appendChild(rightSection)
    ticketDiv.appendChild(contentDiv)

    // Add ticket perforation effect
    const perforation = document.createElement('div')
    perforation.style.cssText = `
      position: absolute;
      right: 200px;
      top: 0;
      bottom: 0;
      width: 2px;
      background: repeating-linear-gradient(
        to bottom,
        transparent 0px,
        transparent 8px,
        rgba(255,255,255,0.3) 8px,
        rgba(255,255,255,0.3) 12px
      );
    `
    ticketDiv.appendChild(perforation)

    return ticketDiv
  }

  private addSecurityWatermark(): void {
    // Add security watermark
    this.pdf.setTextColor(200, 200, 200)
    this.pdf.setFontSize(8)
    this.pdf.text('AUTHENTIC TICKET - mTicket.my', this.pageWidth - 50, this.pageHeight - 5)

    // Add generation timestamp
    const timestamp = new Date().toISOString()
    this.pdf.text(`Generated: ${timestamp}`, 10, this.pageHeight - 5)
  }

  async generateReceiptPDF(ticketData: TicketData): Promise<Blob> {
    // Reset PDF for receipt
    this.pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    })

    // Add receipt content
    this.pdf.setFontSize(20)
    this.pdf.text('PAYMENT RECEIPT', 20, 30)

    this.pdf.setFontSize(12)
    this.pdf.text(`Receipt ID: ${ticketData.registration_code}`, 20, 50)
    this.pdf.text(`Date: ${new Date(ticketData.created_at).toLocaleDateString()}`, 20, 60)
    this.pdf.text(`Event: ${ticketData.event.title}`, 20, 80)
    this.pdf.text(`Attendee: ${ticketData.guest_name}`, 20, 90)
    this.pdf.text(`Amount: RM ${(ticketData.payment_amount || 0).toFixed(2)}`, 20, 100)
    this.pdf.text(`Status: ${ticketData.payment_status}`, 20, 110)

    return new Blob([this.pdf.output('blob')], { type: 'application/pdf' })
  }
}

export const generateTicketPDF = async (ticketData: TicketData): Promise<Blob> => {
  const generator = new TicketPDFGenerator()
  return await generator.generateTicketPDF(ticketData)
}

export const generateReceiptPDF = async (ticketData: TicketData): Promise<Blob> => {
  const generator = new TicketPDFGenerator()
  return await generator.generateReceiptPDF(ticketData)
}
