"use client"

import React from "react"
import <PERSON>Nav from "@/components/main-nav"
import { CTASection } from "@/components/cta-section"
import { EnhancedFooter } from "@/components/enhanced-footer"

interface PageLayoutProps {
  children: React.ReactNode
  showFooter?: boolean
  showCTA?: boolean
  className?: string
}

export function PageLayout({ children, showFooter = true, showCTA = true, className = "" }: PageLayoutProps) {
  return (
    <div className={`flex min-h-screen flex-col ${className}`}>
      <MainNav />
      <main className="flex-1">
        {children}
      </main>
      {showCTA && <CTASection />}
      {showFooter && <EnhancedFooter />}
    </div>
  )
}
