"use client"

import React from "react"
import Image from "next/image"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { 
  CreditCard, 
  AlertCircle, 
  Shield, 
  Lock,
  CheckCircle,
  ArrowLeft
} from "lucide-react"

interface PaymentGateway {
  id: string
  name: string
  type: string
  description: string
  logo_url?: string | null
  enabled: boolean
}

interface PaymentStepProps {
  totalAmount: number
  paymentGateways: PaymentGateway[]
  selectedGateway: string | null
  onGatewaySelect: (gatewayId: string) => void
  paymentStatus: "idle" | "processing" | "success" | "failed"
  paymentGatewaysLoading: boolean
  onProcessPayment: () => void
  onBackToSummary: () => void
}

export function PaymentStep({
  totalAmount,
  paymentGateways,
  selectedGate<PERSON>,
  onGatewaySelect,
  paymentStatus,
  paymentGatewaysLoading,
  onProcessPayment,
  onBackToSummary
}: PaymentStepProps) {
  return (
    <div className="space-y-6">
      {/* Step Header */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-emerald-500 to-green-600 text-white rounded-full mb-4">
          <CreditCard className="h-8 w-8" />
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Secure Payment</h2>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Complete your registration by selecting a payment method. All transactions are secured with industry-standard encryption.
        </p>
      </div>

      {/* Security Badge */}
      <div className="flex justify-center">
        <div className="inline-flex items-center gap-2 bg-green-50 text-green-800 px-4 py-2 rounded-full border border-green-200">
          <Shield className="h-4 w-4" />
          <span className="text-sm font-medium">256-bit SSL Encrypted</span>
          <Lock className="h-4 w-4" />
        </div>
      </div>

      {/* Payment Amount */}
      <Card className="shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-100">
        <CardContent className="p-6 text-center">
          <p className="text-sm font-medium text-blue-900 mb-2">Total Amount</p>
          <p className="text-4xl font-bold text-blue-700">RM {totalAmount.toFixed(2)}</p>
          <Badge variant="secondary" className="mt-2 bg-blue-200 text-blue-800">
            Malaysian Ringgit
          </Badge>
        </CardContent>
      </Card>

      {/* Payment Methods */}
      <Card className="shadow-lg border-0 bg-white">
        <CardHeader className="bg-gradient-to-r from-gray-50 to-gray-100 border-b">
          <CardTitle className="text-xl text-gray-900">Select Payment Method</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          {paymentGatewaysLoading ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <div className="h-12 w-12 animate-spin rounded-full border-4 border-blue-600 border-t-transparent mb-4"></div>
              <p className="text-gray-600">Loading secure payment methods...</p>
            </div>
          ) : paymentGateways.length > 0 ? (
            <div className="space-y-4">
              <RadioGroup value={selectedGateway || ""} onValueChange={onGatewaySelect}>
                {paymentGateways.map((gateway) => (
                  <div
                    key={gateway.id}
                    className="flex items-center space-x-3 rounded-lg border-2 border-gray-200 p-4 hover:border-blue-300 hover:bg-blue-50/50 transition-all duration-200 cursor-pointer"
                  >
                    <RadioGroupItem value={gateway.id} id={gateway.id} />
                    <Label htmlFor={gateway.id} className="flex-1 cursor-pointer">
                      <div className="flex items-center gap-4">
                        {gateway.logo_url ? (
                          <div className="relative w-16 h-16 flex-shrink-0 bg-white rounded-lg border border-gray-200 p-2">
                            <Image
                              src={gateway.logo_url}
                              alt={`${gateway.name} logo`}
                              fill
                              className="object-contain"
                              sizes="64px"
                            />
                          </div>
                        ) : (
                          <div className="w-16 h-16 flex-shrink-0 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center border border-gray-200">
                            <CreditCard className="h-8 w-8 text-gray-500" />
                          </div>
                        )}
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h3 className="font-semibold text-gray-900">{gateway.name}</h3>
                            <Badge variant="outline" className="border-green-300 text-green-700">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Secure
                            </Badge>
                          </div>
                          {gateway.description && (
                            <p className="text-sm text-gray-600 mt-1">
                              {gateway.description}
                            </p>
                          )}
                        </div>
                      </div>
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <AlertCircle className="h-16 w-16 text-red-500 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Payment Methods Available</h3>
              <p className="text-gray-600 max-w-md">
                There are no payment gateways configured at the moment. Please contact our support team for assistance.
              </p>
            </div>
          )}

          {/* Payment Status */}
          {paymentStatus === "processing" && (
            <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
              <div className="h-12 w-12 animate-spin rounded-full border-4 border-blue-600 border-t-transparent mx-auto mb-4"></div>
              <h4 className="font-semibold text-blue-900 mb-2">Processing Payment</h4>
              <p className="text-blue-800">
                Please wait while we process your payment securely.
                <br />
                <strong>Do not close this window or navigate away.</strong>
              </p>
            </div>
          )}

          {paymentStatus === "failed" && (
            <div className="mt-6 bg-red-50 border border-red-200 rounded-lg p-6 text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h4 className="font-semibold text-red-900 mb-2">Payment Failed</h4>
              <p className="text-red-800">
                There was an error processing your payment. Please try again or contact support if the issue persists.
              </p>
            </div>
          )}
        </CardContent>
        
        <CardFooter className="bg-gray-50 border-t p-6">
          <div className="flex justify-between w-full gap-4">
            <Button variant="outline" onClick={onBackToSummary} className="flex-1">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Review
            </Button>
            <Button
              onClick={onProcessPayment}
              disabled={!selectedGateway || paymentStatus === "processing" || paymentGateways.length === 0 || paymentGatewaysLoading}
              className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
            >
              {paymentGatewaysLoading ? (
                "Loading..."
              ) : paymentStatus === "processing" ? (
                "Processing..."
              ) : (
                <>
                  <Lock className="h-4 w-4 mr-2" />
                  Pay RM {totalAmount.toFixed(2)}
                </>
              )}
            </Button>
          </div>
        </CardFooter>
      </Card>

      {/* Security Information */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
          <Shield className="h-4 w-4 text-green-600" />
          Your Payment is Secure
        </h4>
        <ul className="text-sm text-gray-700 space-y-1">
          <li>• All payment information is encrypted using 256-bit SSL technology</li>
          <li>• We never store your credit card details on our servers</li>
          <li>• Payments are processed by certified payment providers</li>
          <li>• Your personal information is protected according to industry standards</li>
        </ul>
      </div>
    </div>
  )
}
