"use client"

import { createContext, useContext, useState, type ReactNode } from "react"
import { v4 as uuidv4 } from "uuid"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { useSettings } from "@/contexts/settings-context"
import { supabase } from "@/lib/supabase"

// Types
export type CertificateTemplate = {
  id: string
  name: string
  html_template: string
  css_styles: string
  thumbnail_url: string
  is_premium: boolean
  created_by: string
  created_at: string
}

export type Certificate = {
  id: string
  event_id: string
  participant_id: string
  participant_name: string
  template_id: string
  issue_date: string
  certificate_url: string
  verification_code: string
  is_revoked: boolean
}

type CertificateContextType = {
  loading: boolean
  error: string | null
  createTemplate: (template: Omit<CertificateTemplate, "id" | "created_at">) => Promise<CertificateTemplate | null>
  getTemplates: (includePremium: boolean) => Promise<CertificateTemplate[]>
  generateCertificate: (
    eventId: string,
    participantId: string,
    participantName: string,
    templateId: string,
    customFields?: Record<string, string>,
  ) => Promise<Certificate | null>
  verifyCertificate: (verificationCode: string) => Promise<Certificate | null>
  revokeCertificate: (certificateId: string) => Promise<boolean>
  getEventCertificates: (eventId: string) => Promise<Certificate[]>
  getParticipantCertificates: (participantId: string) => Promise<Certificate[]>
}

const CertificateContext = createContext<CertificateContextType | undefined>(undefined)

export function CertificateProvider({ children }: { children: ReactNode }) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { toast } = useToast()
  const { user, isPaidUser } = useAuth()
  const { getCertificateTemplates } = useSettings()

  // Create certificate template
  const createTemplate = async (
    template: Omit<CertificateTemplate, "id" | "created_at">,
  ): Promise<CertificateTemplate | null> => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to create a template",
        variant: "destructive",
      })
      return null
    }

    setLoading(true)
    try {
      const newTemplate = {
        id: uuidv4(),
        ...template,
        created_at: new Date().toISOString(),
      }

      const { data, error } = await supabase.from("certificate_templates").insert([newTemplate]).select()

      if (error) throw error

      toast({
        title: "Success",
        description: "Certificate template created successfully",
      })

      return data[0] as CertificateTemplate
    } catch (err) {
      console.error("Error creating template:", err)
      setError("Failed to create certificate template")
      toast({
        title: "Error",
        description: "Failed to create certificate template",
        variant: "destructive",
      })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Get certificate templates
  const getTemplates = async (includePremium: boolean): Promise<CertificateTemplate[]> => {
    setLoading(true)
    try {
      let query = supabase.from("certificate_templates").select("*")

      if (!includePremium) {
        query = query.eq("is_premium", false)
      }

      const { data, error } = await query

      if (error) throw error

      return data as CertificateTemplate[]
    } catch (err) {
      console.error("Error fetching templates:", err)
      setError("Failed to fetch certificate templates")
      return []
    } finally {
      setLoading(false)
    }
  }

  // Generate certificate
  const generateCertificate = async (
    eventId: string,
    participantId: string,
    participantName: string,
    templateId: string,
    customFields?: Record<string, string>,
  ): Promise<Certificate | null> => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to generate a certificate",
        variant: "destructive",
      })
      return null
    }

    setLoading(true)
    try {
      // Check if template is premium and if user has access
      const { data: templateData, error: templateError } = await supabase
        .from("certificate_templates")
        .select("is_premium")
        .eq("id", templateId)
        .single()

      if (templateError) throw templateError

      if (templateData.is_premium && !isPaidUser()) {
        toast({
          title: "Error",
          description: "You need a paid subscription to use premium templates",
          variant: "destructive",
        })
        return null
      }

      // Generate verification code
      const verificationCode = uuidv4().slice(0, 8).toUpperCase()

      // In a real app, this would generate a PDF certificate
      // For demo purposes, we'll just create a record

      const certificate = {
        id: uuidv4(),
        event_id: eventId,
        participant_id: participantId,
        participant_name: participantName,
        template_id: templateId,
        issue_date: new Date().toISOString(),
        certificate_url: `https://example.com/certificates/${verificationCode}`,
        verification_code: verificationCode,
        is_revoked: false,
        custom_fields: customFields || {},
      }

      const { data, error } = await supabase.from("certificates").insert([certificate]).select()

      if (error) throw error

      toast({
        title: "Success",
        description: "Certificate generated successfully",
      })

      return data[0] as Certificate
    } catch (err) {
      console.error("Error generating certificate:", err)
      setError("Failed to generate certificate")
      toast({
        title: "Error",
        description: "Failed to generate certificate",
        variant: "destructive",
      })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Verify certificate
  const verifyCertificate = async (verificationCode: string): Promise<Certificate | null> => {
    setLoading(true)
    try {
      const { data, error } = await supabase
        .from("certificates")
        .select("*")
        .eq("verification_code", verificationCode)
        .single()

      if (error) throw error

      return data as Certificate
    } catch (err) {
      console.error("Error verifying certificate:", err)
      setError("Failed to verify certificate")
      return null
    } finally {
      setLoading(false)
    }
  }

  // Revoke certificate
  const revokeCertificate = async (certificateId: string): Promise<boolean> => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to revoke a certificate",
        variant: "destructive",
      })
      return false
    }

    setLoading(true)
    try {
      const { error } = await supabase.from("certificates").update({ is_revoked: true }).eq("id", certificateId)

      if (error) throw error

      toast({
        title: "Success",
        description: "Certificate revoked successfully",
      })

      return true
    } catch (err) {
      console.error("Error revoking certificate:", err)
      setError("Failed to revoke certificate")
      toast({
        title: "Error",
        description: "Failed to revoke certificate",
        variant: "destructive",
      })
      return false
    } finally {
      setLoading(false)
    }
  }

  // Get event certificates
  const getEventCertificates = async (eventId: string): Promise<Certificate[]> => {
    setLoading(true)
    try {
      const { data, error } = await supabase.from("certificates").select("*").eq("event_id", eventId)

      if (error) throw error

      return data as Certificate[]
    } catch (err) {
      console.error("Error fetching event certificates:", err)
      setError("Failed to fetch certificates")
      return []
    } finally {
      setLoading(false)
    }
  }

  // Get participant certificates
  const getParticipantCertificates = async (participantId: string): Promise<Certificate[]> => {
    setLoading(true)
    try {
      const { data, error } = await supabase.from("certificates").select("*").eq("participant_id", participantId)

      if (error) throw error

      return data as Certificate[]
    } catch (err) {
      console.error("Error fetching participant certificates:", err)
      setError("Failed to fetch certificates")
      return []
    } finally {
      setLoading(false)
    }
  }

  const value = {
    loading,
    error,
    createTemplate,
    getTemplates,
    generateCertificate,
    verifyCertificate,
    revokeCertificate,
    getEventCertificates,
    getParticipantCertificates,
  }

  return <CertificateContext.Provider value={value}>{children}</CertificateContext.Provider>
}

export function useCertificates() {
  const context = useContext(CertificateContext)
  if (context === undefined) {
    throw new Error("useCertificates must be used within a CertificateProvider")
  }
  return context
}
