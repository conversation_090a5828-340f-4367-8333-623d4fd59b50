import { NextRequest } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase";
import crypto from "crypto";

/**
 * GET /api/tickets/public?token=xxx&type=view|pdf
 * Public ticket access using secure token (no authentication required)
 * Token is generated during payment return and valid for limited time
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const token = url.searchParams.get('token');
    const type = url.searchParams.get('type') || 'view';

    console.log("Public Tickets API: Starting request for token:", token?.substring(0, 10) + "...", "type:", type);

    if (!token) {
      return new Response("Ticket token is required", { status: 400 });
    }

    // Verify and decode the ticket token
    const ticketData = verifyTicketToken(token);
    if (!ticketData) {
      console.error("Public Tickets API: Invalid or expired ticket token");
      console.error("Public Tickets API: Token length:", token.length);
      return new Response("Invalid or expired ticket token", { status: 401 });
    }

    const { registrationId, transactionId } = ticketData;

    console.log("Public Tickets API: Token verified for registration:", registrationId);

    const supabaseAdmin = getSupabaseAdmin();

    // Fetch registration data with event and transaction details
    const { data: registration, error: regError } = await supabaseAdmin
      .from("registrations")
      .select(`
        *,
        event:event_id (
          *
        ),
        transaction:transaction_id (
          id,
          status,
          amount,
          currency,
          gateway_transaction_id,
          invoice_number,
          receipt_number,
          processed_at,
          created_at
        )
      `)
      .eq("id", registrationId)
      .single();

    if (regError || !registration) {
      console.error("Public Tickets API: Registration not found:", regError);
      console.error("Public Tickets API: Searched for registration ID:", registrationId);
      return new Response(`Registration not found for ID: ${registrationId}. Error: ${regError?.message || 'Unknown error'}`, { status: 404 });
    }

    // Verify transaction matches
    if (registration.transaction_id !== transactionId) {
      console.error("Public Tickets API: Transaction mismatch");
      console.error("Public Tickets API: Expected transaction ID:", transactionId);
      console.error("Public Tickets API: Actual transaction ID:", registration.transaction_id);
      return new Response(`Transaction mismatch. Expected: ${transactionId}, Got: ${registration.transaction_id}`, { status: 401 });
    }

    // Only allow tickets for paid transactions
    if (registration.transaction?.status !== 'paid') {
      console.error("Public Tickets API: Payment not completed");
      console.error("Public Tickets API: Transaction status:", registration.transaction?.status);
      console.error("Public Tickets API: Registration payment status:", registration.payment_status);
      return new Response(`Ticket not available - payment not completed. Transaction status: ${registration.transaction?.status}, Registration status: ${registration.payment_status}`, { status: 400 });
    }

    if (type === 'pdf') {
      // Return PDF view of the ticket
      return generateTicketPDF(registration);
    } else {
      // Return JSON data for ticket viewing
      return Response.json({
        success: true,
        ticket: {
          id: registration.id,
          attendee_name: registration.attendee_name,
          attendee_email: registration.attendee_email,
          attendee_phone: registration.attendee_phone,
          ticket_type: registration.ticket_type,
          registration_date: registration.created_at,
          check_in_status: registration.check_in_status,
          check_in_time: registration.check_in_time,
          event: registration.event,
          transaction: registration.transaction,
          group_registration_id: registration.group_registration_id
        }
      });
    }

  } catch (error) {
    console.error("Error in public tickets API:", error);
    return new Response("Internal server error", { status: 500 });
  }
}

/**
 * Generate a secure ticket token
 */
export function generateTicketToken(registrationId: string, transactionId: string): string {
  const payload = {
    registrationId,
    transactionId,
    timestamp: Date.now(),
    expires: Date.now() + (24 * 60 * 60 * 1000) // 24 hours
  };

  const secret = process.env.JWT_SECRET || "your-secret-key";
  const data = JSON.stringify(payload);
  const signature = crypto.createHmac('sha256', secret).update(data).digest('hex');

  return Buffer.from(JSON.stringify({ data, signature })).toString('base64');
}

/**
 * Verify and decode a ticket token
 */
function verifyTicketToken(token: string): { registrationId: string; transactionId: string } | null {
  try {
    const decoded = JSON.parse(Buffer.from(token, 'base64').toString());
    const { data, signature } = decoded;

    const secret = process.env.JWT_SECRET || "your-secret-key";
    const expectedSignature = crypto.createHmac('sha256', secret).update(data).digest('hex');

    if (signature !== expectedSignature) {
      return null;
    }

    const payload = JSON.parse(data);

    // Check if token has expired
    if (Date.now() > payload.expires) {
      return null;
    }

    return {
      registrationId: payload.registrationId,
      transactionId: payload.transactionId
    };
  } catch (error) {
    return null;
  }
}

/**
 * Generate PDF view of the ticket
 */
function generateTicketPDF(registration: any) {
  const event = registration.event;
  const transaction = registration.transaction;

  const ticketHtml = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Ticket - ${event.title}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            color: #1e293b;
            line-height: 1.6;
        }
        .ticket-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 2px solid #e2e8f0;
        }
        .ticket-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .ticket-title {
            font-size: 28px;
            font-weight: bold;
            margin: 0 0 10px 0;
        }
        .ticket-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin: 0;
        }
        .ticket-body {
            padding: 30px;
        }
        .ticket-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        .info-group {
            background: #f8fafc;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .info-label {
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }
        .info-value {
            font-size: 16px;
            font-weight: 500;
            color: #1e293b;
        }
        .event-details {
            background: #f1f5f9;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .event-title {
            font-size: 20px;
            font-weight: bold;
            color: #1e293b;
            margin-bottom: 10px;
        }
        .event-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .qr-section {
            text-align: center;
            padding: 20px;
            background: #f8fafc;
            border-radius: 8px;
            margin-top: 20px;
        }
        .qr-placeholder {
            width: 120px;
            height: 120px;
            background: #e2e8f0;
            border: 2px dashed #94a3b8;
            border-radius: 8px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #64748b;
        }
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-active {
            background: #dcfce7;
            color: #166534;
        }
        .status-checked-in {
            background: #dbeafe;
            color: #1e40af;
        }
        .footer {
            text-align: center;
            padding: 20px;
            border-top: 1px solid #e2e8f0;
            background: #f8fafc;
        }
        @media print {
            body { background: white; }
            .ticket-container { box-shadow: none; border: 1px solid #ccc; }
        }
    </style>
</head>
<body>
    <div class="ticket-container">
        <div class="ticket-header">
            <h1 class="ticket-title">Event Ticket</h1>
            <p class="ticket-subtitle">mTicket.my - Event Management Platform</p>
        </div>

        <div class="ticket-body">
            <div class="event-details">
                <div class="event-title">${event.title}</div>
                <div class="event-info">
                    <div>
                        <div class="info-label">Date & Time</div>
                        <div class="info-value">
                            ${new Date(event.start_date).toLocaleDateString()} ${new Date(event.start_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            ${event.start_date !== event.end_date ? ` - ${new Date(event.end_date).toLocaleDateString()} ${new Date(event.end_date).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}` : ''}
                        </div>
                    </div>
                    <div>
                        <div class="info-label">Location</div>
                        <div class="info-value">${event.location || 'TBA'}</div>
                    </div>
                </div>
            </div>

            <div class="ticket-info">
                <div class="info-group">
                    <div class="info-label">Attendee Name</div>
                    <div class="info-value">${registration.attendee_name}</div>
                </div>
                <div class="info-group">
                    <div class="info-label">Ticket Type</div>
                    <div class="info-value">${registration.ticket_type || 'General Admission'}</div>
                </div>
                <div class="info-group">
                    <div class="info-label">Ticket ID</div>
                    <div class="info-value">${registration.id}</div>
                </div>
                <div class="info-group">
                    <div class="info-label">Status</div>
                    <div class="info-value">
                        <span class="status-badge ${registration.check_in_status === 'checked_in' ? 'status-checked-in' : 'status-active'}">
                            ${registration.check_in_status === 'checked_in' ? 'Checked In' : 'Active'}
                        </span>
                    </div>
                </div>
            </div>

            ${registration.attendee_email ? `
            <div class="info-group" style="grid-column: 1 / -1;">
                <div class="info-label">Contact Email</div>
                <div class="info-value">${registration.attendee_email}</div>
            </div>
            ` : ''}

            <div class="qr-section">
                <div class="qr-placeholder">
                    QR Code<br>
                    (Generated at check-in)
                </div>
                <p style="margin: 0; font-size: 12px; color: #64748b;">
                    Present this ticket at the event entrance
                </p>
            </div>
        </div>

        <div class="footer">
            <p style="margin: 0 0 8px 0; font-weight: bold;">Thank you for your registration!</p>
            <p style="margin: 0 0 5px 0;">This is your official event ticket.</p>
            <p style="font-size: 10px; color: #64748b; margin: 0;">
                Official ticket from mTicket.my | Generated: ${new Date().toLocaleString()}
            </p>
        </div>
    </div>

    <script>
        window.onload = function() {
            setTimeout(function() {
                window.print();
            }, 1000);
        }
    </script>
</body>
</html>
    `;

  return new Response(ticketHtml, {
    headers: {
      'Content-Type': 'text/html',
    },
  });
}
