// Verification script for required fields count and asterisks
console.log('🔍 Verifying Required Fields Implementation');
console.log('==========================================');

// Event 5nEh details from database query
const event5nEh = {
  id: "60fdcaec-49c7-4b3f-9498-6887b3d80276",
  title: "Ipoh Fun Run Day 2025",
  slug: "5nEh",
  custom_fields: [
    {
      id: "emergency_contact",
      type: "phone",
      label: "Emergency Contact Number",
      order: 1,
      required: true,
      validation: {
        pattern: "^[\\+]?[0-9\\s\\-\\(\\)]{8,}$",
        minLength: 8
      },
      placeholder: "e.g., +60123456789"
    },
    {
      id: "tshirt_size",
      type: "select",
      label: "T-Shirt Size",
      order: 2,
      options: ["XS", "S", "M", "L", "XL", "2XL", "3XL", "4XL"],
      required: true,
      placeholder: "Select your t-shirt size"
    }
  ]
};

// Calculate required fields count (same logic as in RegistrationStep component)
const standardRequiredFields = 4; // Name, IC/Passport, Phone, Email
const customRequiredFields = event5nEh.custom_fields?.filter(field => field.required).length || 0;
const totalRequiredFields = standardRequiredFields + customRequiredFields;

console.log('📊 Required Fields Analysis:');
console.log(`   Standard required fields: ${standardRequiredFields}`);
console.log(`   Custom required fields: ${customRequiredFields}`);
console.log(`   Total required fields: ${totalRequiredFields}`);
console.log('');

console.log('📝 Standard Fields (should all have red asterisks):');
console.log('   ✅ Full Name *');
console.log('   ✅ IC/Passport Number *');
console.log('   ✅ Phone Number *');
console.log('   ✅ Email *');
console.log('');

console.log('🔧 Custom Fields (already had red asterisks):');
event5nEh.custom_fields.forEach((field, index) => {
  const asterisk = field.required ? ' *' : '';
  console.log(`   ✅ ${field.label}${asterisk}`);
});
console.log('');

console.log('🎯 Expected Results:');
console.log(`   Required Fields Card: ${totalRequiredFields}`);
console.log('   All fields should show red asterisk (*)');
console.log('');

console.log('🧪 Testing Instructions:');
console.log('1. Open: http://localhost:3001/events/5nEh');
console.log('2. Select 1 General Admission ticket');
console.log('3. Click "Register" button');
console.log('4. Verify Required Fields card shows: 6');
console.log('5. Verify all 6 fields have red asterisks');
console.log('');

console.log('✅ Verification Complete!');
