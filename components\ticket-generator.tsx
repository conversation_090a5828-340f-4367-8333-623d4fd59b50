"use client"

import { useState } from "react"
import { QRCodeSVG } from "qrcode.react"
import { Download, Share2, Printer } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"

interface TicketGeneratorProps {
  registrationId: string
  eventTitle: string
  eventDate: string
  eventLocation: string
  attendeeName: string
  ticketNumber: string
}

export function TicketGenerator({
  registrationId,
  eventTitle,
  eventDate,
  eventLocation,
  attendeeName,
  ticketNumber,
}: TicketGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const { toast } = useToast()

  // Generate ticket URL with verification data
  const ticketData = JSON.stringify({
    id: registrationId,
    event: eventTitle,
    attendee: attendeeName,
    ticket: ticketNumber,
    timestamp: new Date().toISOString(),
  })

  // Base64 encode the ticket data for the QR code
  const ticketQrData = `https://mtickets.com/verify?data=${btoa(ticketData)}`

  // Handle ticket download
  const handleDownload = () => {
    setIsGenerating(true)
    try {
      // In a real app, this would generate a PDF ticket
      // For demo purposes, we'll just simulate a download
      setTimeout(() => {
        toast({
          title: "Success",
          description: "Ticket downloaded successfully",
        })
        setIsGenerating(false)
      }, 1500)
    } catch (error) {
      console.error("Error downloading ticket:", error)
      toast({
        title: "Error",
        description: "Failed to download ticket",
        variant: "destructive",
      })
      setIsGenerating(false)
    }
  }

  // Handle ticket printing
  const handlePrint = () => {
    window.print()
  }

  // Handle ticket sharing
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Ticket for ${eventTitle}`,
          text: `Here's my ticket for ${eventTitle} on ${eventDate}`,
          url: window.location.href,
        })
      } catch (error) {
        console.error("Error sharing ticket:", error)
      }
    } else {
      toast({
        title: "Info",
        description: "Sharing is not supported on this device",
      })
    }
  }

  return (
    <div className="space-y-6">
      <Card className="overflow-hidden print:shadow-none">
        <CardHeader className="bg-primary text-primary-foreground print:bg-white print:text-black">
          <CardTitle className="text-center">Event Ticket</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid gap-6 md:grid-cols-2">
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold text-lg">{eventTitle}</h3>
                <p className="text-muted-foreground">{eventDate}</p>
                <p className="text-muted-foreground">{eventLocation}</p>
              </div>
              <div>
                <p className="font-medium">Attendee</p>
                <p>{attendeeName}</p>
              </div>
              <div>
                <p className="font-medium">Ticket #</p>
                <p className="font-mono">{ticketNumber}</p>
              </div>
            </div>
            <div className="flex flex-col items-center justify-center">
              <div className="bg-white p-2 rounded-lg">
                <QRCodeSVG value={ticketQrData} size={150} />
              </div>
              <p className="mt-2 text-xs text-center text-muted-foreground">Scan this QR code at the event entrance</p>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between bg-muted/50 print:hidden">
          <Button variant="outline" size="sm" onClick={handlePrint}>
            <Printer className="mr-2 h-4 w-4" />
            Print
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleShare}>
              <Share2 className="mr-2 h-4 w-4" />
              Share
            </Button>
            <Button size="sm" onClick={handleDownload} disabled={isGenerating}>
              <Download className="mr-2 h-4 w-4" />
              {isGenerating ? "Generating..." : "Download"}
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}
