"use client"

import { useState, useEffect, useRef } from "react"
import { Html5Qrcode } from "html5-qrcode"
import { CheckCircle2, XCircle, Camera, CameraOff } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"

interface QrScannerProps {
  eventId: string
  onAttendanceMarked?: (attendeeData: any) => void
}

export function QrScanner({ eventId, onAttendanceMarked }: QrScannerProps) {
  const [scanning, setScanning] = useState(false)
  const [scanResult, setScanResult] = useState<any | null>(null)
  const [scanError, setScanError] = useState<string | null>(null)
  const [processing, setProcessing] = useState(false)
  const [permissionDenied, setPermissionDenied] = useState(false)
  const [cameraDevices, setCameraDevices] = useState<any[]>([])
  const [selectedCamera, setSelectedCamera] = useState<string>("")
  const [showManualInput, setShowManualInput] = useState(false)
  const [manualInput, setManualInput] = useState("")
  const scannerRef = useRef<Html5Qrcode | null>(null)
  const scannerContainerId = "qr-scanner-container"
  const { toast } = useToast()

  // Check camera permissions and get available cameras on mount
  useEffect(() => {
    checkCameraPermissions()
  }, [])

  // Clean up scanner on unmount
  useEffect(() => {
    return () => {
      if (scannerRef.current && scanning) {
        scannerRef.current.stop().catch(console.error)
      }
    }
  }, [scanning])

  // Check camera permissions and get available cameras
  const checkCameraPermissions = async () => {
    try {
      // Check if we can get camera devices
      const devices = await Html5Qrcode.getCameras()
      setCameraDevices(devices)

      // Set default camera (prefer back camera for mobile)
      const backCamera = devices.find(device =>
        device.label.toLowerCase().includes('back') ||
        device.label.toLowerCase().includes('rear') ||
        device.label.toLowerCase().includes('environment')
      )
      setSelectedCamera(backCamera?.id || devices[0]?.id || "")

      setPermissionDenied(false)
    } catch (error: any) {
      console.error("Camera permission error:", error)
      setPermissionDenied(true)

      // Show helpful error message based on error type
      let errorMessage = "Camera access denied. Please allow camera permissions and refresh the page."

      if (error.message?.includes("Permission denied")) {
        errorMessage = "Camera permission denied. Please allow camera access in your browser settings."
      } else if (error.message?.includes("not found")) {
        errorMessage = "No camera found. Please ensure your device has a camera."
      } else if (error.message?.includes("not supported")) {
        errorMessage = "Camera not supported in this browser. Please try a different browser."
      }

      setScanError(errorMessage)
    }
  }

  // Start scanning
  const startScanner = async () => {
    setScanResult(null)
    setScanError(null)

    if (permissionDenied) {
      await checkCameraPermissions()
      if (permissionDenied) {
        return
      }
    }

    try {
      scannerRef.current = new Html5Qrcode(scannerContainerId)

      // Use selected camera or fallback to environment facing mode
      const cameraConfig = selectedCamera
        ? selectedCamera
        : { facingMode: "environment" }

      await scannerRef.current.start(
        cameraConfig,
        {
          fps: 10,
          qrbox: { width: 250, height: 250 },
          aspectRatio: 1.0,
        },
        onScanSuccess,
        onScanFailure,
      )

      setScanning(true)
      setPermissionDenied(false)
    } catch (error: any) {
      console.error("Error starting scanner:", error)

      let errorMessage = "Failed to start camera. Please check permissions."

      if (error.message?.includes("Permission denied") || error.message?.includes("NotAllowedError")) {
        errorMessage = "Camera permission denied. Please allow camera access and try again."
        setPermissionDenied(true)
      } else if (error.message?.includes("NotFoundError")) {
        errorMessage = "No camera found. Please ensure your device has a camera."
      } else if (error.message?.includes("NotSupportedError")) {
        errorMessage = "Camera not supported. Please try a different browser."
      } else if (error.message?.includes("OverconstrainedError")) {
        errorMessage = "Camera constraints not supported. Trying with different settings..."
        // Try with basic constraints
        try {
          await scannerRef.current?.start(
            { facingMode: "user" },
            { fps: 5, qrbox: 200 },
            onScanSuccess,
            onScanFailure,
          )
          setScanning(true)
          return
        } catch (fallbackError) {
          errorMessage = "Unable to start camera with any configuration."
        }
      }

      setScanError(errorMessage)
      toast({
        title: "Camera Error",
        description: errorMessage,
        variant: "destructive",
      })
    }
  }

  // Stop scanning
  const stopScanner = async () => {
    if (scannerRef.current) {
      try {
        await scannerRef.current.stop()
        setScanning(false)
      } catch (error) {
        console.error("Error stopping scanner:", error)
      }
    }
  }

  // Handle successful scan
  const onScanSuccess = async (decodedText: string) => {
    try {
      // Stop scanner after successful scan
      await stopScanner()

      // Check if this is a secure QR code (new format)
      const url = new URL(decodedText)
      if (url.pathname === "/verify/secure" && url.searchParams.get("token")) {
        // Handle secure QR code
        await markAttendanceSecure(decodedText)
      } else {
        // Handle legacy QR code format
        const encodedData = url.searchParams.get("data")
        if (!encodedData) {
          throw new Error("Invalid QR code format")
        }

        // Decode the base64 data
        const decodedData = atob(encodedData)
        const ticketData = JSON.parse(decodedData)

        // Process attendance with legacy method
        await markAttendance(ticketData)
      }
    } catch (error: any) {
      console.error("Error processing QR code:", error)
      setScanError(error.message || "Invalid QR code")
      toast({
        title: "Error",
        description: "Failed to process QR code",
        variant: "destructive",
      })
    }
  }

  // Handle scan failure
  const onScanFailure = (error: string) => {
    // We don't need to show every scan failure, only when it's a persistent issue
    console.log("QR scan error:", error)
  }

  // Process manual QR code input
  const processManualInput = async () => {
    if (!manualInput.trim()) {
      setScanError("Please enter a QR code URL")
      return
    }

    try {
      await onScanSuccess(manualInput.trim())
      setManualInput("")
      setShowManualInput(false)
    } catch (error: any) {
      console.error("Error processing manual input:", error)
      setScanError(error.message || "Invalid QR code")
    }
  }

  // Mark attendance using secure token verification
  const markAttendanceSecure = async (qrData: string) => {
    setProcessing(true)
    try {
      const url = new URL(qrData)
      const token = url.searchParams.get("token")

      if (!token) {
        throw new Error("Invalid secure QR code - missing token")
      }

      // Call secure verification API
      const response = await fetch('/api/verify/secure', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token: qrData,
          eventId: eventId,
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Verification failed")
      }

      // Success - show result
      setScanResult({
        success: true,
        message: result.message,
        registration: result.registration,
        security: result.security,
      })

      toast({
        title: "Success",
        description: `${result.registration.guest_name} checked in successfully`,
      })

      if (onAttendanceMarked) {
        onAttendanceMarked(result.registration)
      }
    } catch (error: any) {
      console.error("Error marking secure attendance:", error)
      setScanError(error.message || "Failed to verify secure ticket")
      toast({
        title: "Error",
        description: error.message || "Failed to verify secure ticket",
        variant: "destructive",
      })
    } finally {
      setProcessing(false)
    }
  }

  // Mark attendance in the database (legacy method)
  const markAttendance = async (ticketData: any) => {
    setProcessing(true)
    try {
      // Verify the ticket is for this event
      const { data: registrationData, error: registrationError } = await supabase
        .from("registrations")
        .select("*")
        .eq("id", ticketData.id)
        .eq("event_id", eventId)
        .single()

      if (registrationError || !registrationData) {
        throw new Error("Invalid ticket or ticket not found for this event")
      }

      // Check if already checked in
      if (registrationData.checked_in) {
        throw new Error("Attendee has already been checked in")
      }

      // Update attendance status
      const { error: updateError } = await supabase
        .from("registrations")
        .update({
          status: "attended",
          checked_in: true,
          checked_in_at: new Date().toISOString()
        })
        .eq("id", ticketData.id)

      if (updateError) {
        throw updateError
      }

      // Log attendance
      await supabase.from("activity_logs").insert([
        {
          event_id: eventId,
          user_id: registrationData.user_id,
          action: "attendance_marked",
          details: {
            registration_id: ticketData.id,
            attendee_name: ticketData.attendee,
            marked_at: new Date().toISOString(),
          },
          created_at: new Date().toISOString(),
        },
      ])

      // Set scan result
      setScanResult({
        ...ticketData,
        registrationData,
      })

      // Call callback if provided
      if (onAttendanceMarked) {
        onAttendanceMarked({
          ...ticketData,
          registrationData,
        })
      }

      toast({
        title: "Success",
        description: "Attendance marked successfully",
      })
    } catch (error: any) {
      console.error("Error marking attendance:", error)
      setScanError(error.message || "Failed to mark attendance")
      toast({
        title: "Error",
        description: error.message || "Failed to mark attendance",
        variant: "destructive",
      })
    } finally {
      setProcessing(false)
    }
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Scan Ticket QR Code</CardTitle>
        <CardDescription>Scan attendee tickets to mark attendance</CardDescription>
      </CardHeader>
      <CardContent>
        {/* Camera Selection */}
        {!scanning && cameraDevices.length > 1 && !permissionDenied && (
          <div className="mb-4">
            <label className="text-sm font-medium mb-2 block">Select Camera:</label>
            <Select value={selectedCamera} onValueChange={setSelectedCamera}>
              <SelectTrigger>
                <SelectValue placeholder="Choose camera" />
              </SelectTrigger>
              <SelectContent>
                {cameraDevices.map((device) => (
                  <SelectItem key={device.id} value={device.id}>
                    {device.label || `Camera ${device.id}`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {scanning ? (
          <div className="relative aspect-square max-w-md mx-auto">
            <div id={scannerContainerId} className="w-full h-full"></div>
            <div className="absolute inset-0 pointer-events-none border-2 border-dashed border-primary/50 rounded-lg flex items-center justify-center">
              <div className="w-64 h-64 border-2 border-primary rounded-lg"></div>
            </div>
          </div>
        ) : (
          <div className="aspect-square max-w-md mx-auto bg-muted rounded-lg flex flex-col items-center justify-center">
            {scanResult ? (
              <div className="text-center p-6">
                <CheckCircle2 className="h-16 w-16 text-green-500 mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">Attendance Marked!</h3>
                <p className="mb-1">
                  <span className="font-medium">Attendee:</span> {scanResult.attendee}
                </p>
                <p className="mb-1">
                  <span className="font-medium">Ticket:</span> {scanResult.ticket}
                </p>
                <p className="text-sm text-muted-foreground mt-4">Scan another ticket to continue</p>
              </div>
            ) : scanError ? (
              <div className="text-center p-6">
                <XCircle className="h-16 w-16 text-destructive mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">Scan Failed</h3>
                <p className="text-muted-foreground">{scanError}</p>
              </div>
            ) : (
              <div className="text-center p-6">
                {permissionDenied ? (
                  <>
                    <CameraOff className="h-16 w-16 text-destructive mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Camera Access Denied</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Please allow camera permissions in your browser to use the QR scanner.
                    </p>
                    <div className="text-xs text-muted-foreground space-y-1">
                      <p><strong>Chrome/Edge:</strong> Click the camera icon in the address bar</p>
                      <p><strong>Firefox:</strong> Click the shield icon and allow camera</p>
                      <p><strong>Safari:</strong> Go to Settings → Websites → Camera</p>
                    </div>
                  </>
                ) : (
                  <>
                    <Camera className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Camera is off</h3>
                    <p className="text-sm text-muted-foreground">Click the button below to start scanning</p>
                  </>
                )}
              </div>
            )}
          </div>
        )}

        {/* Manual QR Code Input */}
        {showManualInput && !scanning && (
          <div className="mt-4 p-4 border rounded-lg bg-muted/50">
            <Label htmlFor="manual-qr" className="text-sm font-medium">
              Enter QR Code URL Manually
            </Label>
            <div className="flex gap-2 mt-2">
              <Input
                id="manual-qr"
                placeholder="Paste QR code URL here..."
                value={manualInput}
                onChange={(e) => setManualInput(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && processManualInput()}
              />
              <Button onClick={processManualInput} disabled={processing || !manualInput.trim()}>
                Process
              </Button>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowManualInput(false)}
              className="mt-2"
            >
              Cancel
            </Button>
          </div>
        )}

        {scanResult && (
          <Alert className="mt-6">
            <CheckCircle2 className="h-4 w-4" />
            <AlertTitle>Attendance confirmed</AlertTitle>
            <AlertDescription>{scanResult.attendee} has been marked as attended for this event.</AlertDescription>
          </Alert>
        )}

        {scanError && (
          <Alert variant="destructive" className="mt-6">
            <XCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{scanError}</AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter className="flex flex-col gap-2">
        {scanning ? (
          <Button variant="outline" className="w-full" onClick={stopScanner}>
            <CameraOff className="mr-2 h-4 w-4" />
            Stop Scanning
          </Button>
        ) : (
          <>
            <Button
              className="w-full"
              onClick={startScanner}
              disabled={processing || (permissionDenied && cameraDevices.length === 0)}
            >
              <Camera className="mr-2 h-4 w-4" />
              {processing ? "Processing..." : permissionDenied ? "Request Camera Access" : "Start Scanning"}
            </Button>

            {permissionDenied && (
              <Button
                variant="outline"
                className="w-full"
                onClick={checkCameraPermissions}
                size="sm"
              >
                Retry Camera Access
              </Button>
            )}

            {/* Manual Input Toggle */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowManualInput(!showManualInput)}
              className="w-full"
            >
              {showManualInput ? "Hide Manual Input" : "Enter QR Code Manually"}
            </Button>
          </>
        )}
      </CardFooter>
    </Card>
  )
}
