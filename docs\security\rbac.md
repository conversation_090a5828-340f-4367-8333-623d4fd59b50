# Granular Role-Based Access Control (RBAC) Implementation

This document provides a comprehensive guide to the role-based access control system implemented in the mTicket.my application, including database schema, role definitions, permission structures, access control patterns, and detailed function-level access control.

## Table of Contents

1. [Database Schema](#database-schema)
2. [Role Definitions](#role-definitions)
3. [Permission Structure](#permission-structure)
4. [Access Control Patterns](#access-control-patterns)
5. [API Endpoint Access Control](#api-endpoint-access-control)
6. [Frontend Access Control](#frontend-access-control)
7. [Function-Level Access Control](#function-level-access-control)
8. [Server Actions Access Control](#server-actions-access-control)
9. [Component-Level Access Control](#component-level-access-control)
10. [Activity Logging Integration](#activity-logging-integration)
11. [Implementation Examples](#implementation-examples)
12. [Security Considerations](#security-considerations)

## Database Schema

### Users Table
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  password_hash TEXT,
  role TEXT CHECK (role IN ('free', 'paid', 'manager', 'admin')), -- Legacy field
  role_id UUID REFERENCES user_roles(id), -- New granular role system
  subscription_status TEXT DEFAULT 'none',
  subscription_end_date TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  organization TEXT,
  organization_id UUID,
  profile_image_url TEXT,
  events_created INTEGER DEFAULT 0,
  total_earnings DECIMAL(10,2) DEFAULT 0,
  available_balance DECIMAL(10,2) DEFAULT 0
);
```

### User Roles Table
```sql
CREATE TABLE user_roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  role_name TEXT UNIQUE NOT NULL,
  description TEXT,
  parent_role_id UUID REFERENCES user_roles(id),
  permissions JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Role Definitions

### Current Role Hierarchy

The system defines exactly 5 roles in the `user_roles` table:

#### 1. **admin** - Full System Administrator
- Access to all systems and functions
- Complete user management capabilities
- System configuration and settings access
- Complete override of subscription limitations
- Can manage all events, users, and system data

#### 2. **user** - Registered Users/Participants
- Can register for events as participants
- Basic profile management
- Limited feature access
- Subscription-based limitations apply
- Cannot create events until upgraded to manager role

#### 3. **manager** - Event Organizers
- Users who created organizations and can manage their own events and participants
- Access to their own event participant management
- Organization management capabilities
- Subscription-based feature access
- Upgraded from "user" role when first organization is created

#### 4. **supermanager** - Enhanced Manager with Commission Access
- Same access as manager role
- Additional commission access (future feature)
- Can manage their own events and participants
- Enhanced financial reporting capabilities

#### 5. **event_admin** - System-wide Event Administrator
- Can manage ALL events created in the system (not just their own)
- Event oversight and administration capabilities
- Cross-organization event management
- Does not have full system admin access like "admin" role

### Role Compatibility

The system checking `role_name` fields:

```typescript
const userRole = authResult.user.role_name;
```

## Permission Structure

### Permission Categories

The granular permission system is organized into categories:

```typescript
const permissionCategories = [
  {
    id: "users",
    name: "User Management",
    permissions: ["view", "create", "edit", "delete", "manage_roles"]
  },
  {
    id: "events",
    name: "Event Management",
    permissions: ["view", "create", "edit", "delete", "publish", "manage_registrations"]
  },
  {
    id: "payments",
    name: "Payment Management",
    permissions: ["view", "process", "refund", "configure_gateways"]
  },
  {
    id: "reports",
    name: "Reports & Analytics",
    permissions: ["view", "export", "advanced_analytics"]
  },
  {
    id: "settings",
    name: "System Settings",
    permissions: ["view", "edit", "manage_integrations"]
  },
  {
    id: "roles",
    name: "Role Management",
    permissions: ["view", "create", "edit", "delete", "assign"]
  }
];
```

### Permission Storage

Permissions are stored as JSONB in the `user_roles.permissions` field:

```json
{
  "users": {
    "view": true,
    "create": true,
    "edit": true,
    "delete": false,
    "manage_roles": false
  },
  "events": {
    "view": true,
    "create": true,
    "edit": true,
    "delete": true,
    "publish": true,
    "manage_registrations": true
  },
  "payments": {
    "view": true,
    "process": false,
    "refund": false,
    "configure_gateways": false
  }
}
```

## Access Control Patterns

### 1. API Endpoint Access Control

#### Standard Admin Access Pattern
```typescript
// Check if user has admin role (only "admin" role has full access)
const userRole = authResult.user.role_name || authResult.user.role;

if (userRole !== "admin") {
  return NextResponse.json(
    { error: "Unauthorized. Admin access required." },
    { status: 403 }
  );
}
```

#### Extended Access Pattern (Admin + Manager)
```typescript
// Check if user has admin or manager role (admin has full access, others have elevated permissions)
const userRole = authResult.user.role_name || authResult.user.role;
const allowedRoles = ["admin", "manager", "super_admin", "supermanager", "event_admin"];

if (!allowedRoles.includes(userRole)) {
  return NextResponse.json(
    { error: "Unauthorized. Admin or manager access required." },
    { status: 403 }
  );
}
```

### 2. JWT Token Verification

All API endpoints use JWT token verification:

```typescript
import { verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth";

// Get JWT token from request
const token = getJWTTokenFromRequest(request);

// Verify the JWT token and get user data
const authResult = await verifyJWTToken(token);
if (!authResult?.user) {
  return NextResponse.json(
    { error: "Unauthorized. Invalid token." },
    { status: 403 }
  );
}
```

### 3. Development Mode Bypass

All endpoints include development mode bypass for easier testing:

```typescript
const isDevelopment = process.env.NODE_ENV === "development";

if (!isDevelopment) {
  // Perform authentication checks
} else {
  console.log("Development mode: Allowing access without authentication");
}
```

## API Endpoint Access Control

For detailed information about all API endpoints and their access requirements, see:
- **[API Endpoints Reference](./api-endpoints-reference.md)** - Complete API documentation with access levels

### Access Level Summary
- **Admin-Only**: User management, role management, system settings
- **Admin/Manager**: Payment gateway management
- **Authenticated**: User-specific data and operations
- **Public**: Event listings, categories, authentication

### Role Mapping for Legacy Compatibility

The system maps granular roles to legacy role constraints:

```typescript
// Map the role name to one of the allowed values in the check constraint
// The constraint only allows 'free', 'paid', 'manager', 'admin'
let mappedRole = roleName;
if (!['free', 'paid', 'manager', 'admin'].includes(roleName)) {
  // More specific mappings for common roles
  if (roleName.includes('admin')) {
    mappedRole = 'admin';
  } else if (roleName.includes('manager')) {
    mappedRole = 'manager';
  } else if (roleName.includes('paid')) {
    mappedRole = 'paid';
  } else {
    mappedRole = 'free';
  }
}
```

## Frontend Access Control

### 1. Role-Based UI Components

Components check user roles to show/hide functionality:

```typescript
// Check if user has admin role for UI display
const isAdmin = user?.role === 'admin' ||
                user?.role === 'super_admin' ||
                user?.role === 'supermanager' ||
                user?.role === 'event_admin';

// Show admin menu only to admin users
{isAdmin && (
  <AdminMenuComponent />
)}
```

### 2. Role Badge Display

Different roles have distinct visual representations:

```typescript
const getRoleBadgeColor = (role: string) => {
  switch (role.toLowerCase()) {
    case 'admin':
    case 'super_admin':
      return 'bg-red-100 text-red-800';
    case 'supermanager':
    case 'event_admin':
      return 'bg-purple-100 text-purple-800'; // Distinct color for Event_admin
    case 'manager':
      return 'bg-blue-100 text-blue-800';
    case 'paid':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};
```

### 3. Navigation Access Control

Navigation items are conditionally rendered based on user roles:

```typescript
// Dashboard navigation with role-based access
const navigationItems = [
  { name: 'Dashboard', href: '/dashboard', roles: ['all'] },
  { name: 'Events', href: '/dashboard/events', roles: ['all'] },
  { name: 'Users', href: '/dashboard/users', roles: ['admin', 'super_admin', 'supermanager', 'event_admin'] },
  { name: 'Roles', href: '/dashboard/admin/roles', roles: ['admin', 'super_admin', 'supermanager'] },
  { name: 'Activity Logs', href: '/dashboard/activity-logs', roles: ['admin', 'super_admin', 'supermanager', 'event_admin'] },
  { name: 'Settings', href: '/dashboard/settings', roles: ['admin', 'super_admin', 'supermanager', 'manager'] }
];
```

## Function-Level Access Control

### 1. Authentication Functions

#### JWT Token Verification (`lib/auth.ts`)
```typescript
// Function: verifyJWTToken
// Access: Internal system function
// Purpose: Verify JWT tokens and fetch user data with role information
export async function verifyJWTToken(token: string): Promise<{ user: any; error?: string } | null>

// Role Data Fetching:
// - Fetches user data with role_id relationship
// - Includes user_roles table data (role_name, description)
// - Used by all API endpoints for authentication
```

#### Auth Context Functions (`contexts/auth-context.tsx`)
```typescript
// Function: isAdmin()
// Access: All authenticated users (returns boolean)
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin"]
// Usage: Frontend role checking for admin features

// Function: isManager()
// Access: All authenticated users (returns boolean)
// Required Roles: ["manager", "admin", "super_admin", "supermanager", "event_admin"]
// Usage: Frontend role checking for manager+ features

// Function: login(email, password)
// Access: Public
// Activity Logging: Yes (login attempts, success/failure)

// Function: logout()
// Access: Authenticated users
// Activity Logging: Yes (logout events)
```

### 2. User Management Functions

#### User Context Functions (`contexts/user-context.tsx`)
```typescript
// Function: canCreateEvent()
// Access: All authenticated users (returns boolean)
// Logic:
// - Admin/Manager: Always true
// - Paid users with active subscription: Always true
// - Free users: True if events_created < 1
// - Others: False

// Function: getSubscriptionTiers()
// Access: All authenticated users
// Purpose: Returns available subscription tiers based on user role
```

#### User Management API Functions
```typescript
// Function: GET /api/admin/users/list
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin"]
// Purpose: List all users with role information
// Activity Logging: Implicit (access logging)

// Function: POST /api/admin/users/update-role
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin"]
// Purpose: Update user roles
// Activity Logging: Yes (role changes with before/after values)

// Function: POST /api/admin/users/create
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin"]
// Purpose: Create new user accounts
// Activity Logging: Yes (user creation)
```

### 3. Role Management Functions

#### Role Management API Functions
```typescript
// Function: GET /api/admin/roles/list
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin"]
// Purpose: List all available roles
// Activity Logging: Implicit (access logging)
```

#### Role Server Actions (`app/actions/roles.ts`)
```typescript
// Function: createRole(role: Partial<Role>)
// Access: Server-side only (called from admin pages)
// Required Roles: Admin roles (enforced at page level)
// Purpose: Create new roles with permissions
// Validation: Checks for duplicate role names

// Function: updateRole(role: Role)
// Access: Server-side only (called from admin pages)
// Required Roles: Admin roles (enforced at page level)
// Purpose: Update existing roles and permissions

// Function: deleteRole(roleId: string)
// Access: Server-side only (called from admin pages)
// Required Roles: Admin roles (enforced at page level)
// Purpose: Delete roles (with safety checks)
```

### 4. Payment Gateway Functions

#### Payment Gateway API Functions
```typescript
// Function: GET /api/admin/payment-gateways/list
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin", "manager"]
// Purpose: List payment gateways
// Activity Logging: Implicit (access logging)

// Function: POST /api/admin/payment-gateways/create
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin"]
// Purpose: Create new payment gateways
// Activity Logging: Yes (gateway creation)

// Function: PUT /api/admin/payment-gateways/update
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin"]
// Purpose: Update payment gateway settings
// Activity Logging: Yes (gateway updates)

// Function: PATCH /api/admin/payment-gateways/toggle
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin", "manager"]
// Purpose: Enable/disable payment gateways
// Activity Logging: Yes (status changes)
```

### 5. Event Management Functions

#### Event API Functions
```typescript
// Function: GET /api/events
// Access: Public (no authentication required)
// Purpose: List published events
// Filters: Only published events (is_published = true)

// Function: GET /api/dashboard/events
// Access: Authenticated users
// Purpose: List user's own events
// Filters: Events created by authenticated user
```

### 6. Activity Logging Functions

#### Activity Logger Service (`lib/activity-logger-service.ts`)
```typescript
// Function: logAuthActivity(userId, action, details, ipAddress, userAgent)
// Access: Internal system function
// Purpose: Log authentication-related activities
// Categories: AUTH

// Function: logUserActivity(userId, action, targetUserId, details)
// Access: Internal system function
// Purpose: Log user management activities
// Categories: USER

// Function: logEventActivity(userId, action, eventId, details)
// Access: Internal system function
// Purpose: Log event-related activities
// Categories: EVENT

// Function: logSettingsActivity(userId, action, settingType, details)
// Access: Internal system function
// Purpose: Log settings changes
// Categories: SETTINGS

// Function: logSystemActivity(action, details)
// Access: Internal system function
// Purpose: Log system-level activities
// Categories: SYSTEM
```

#### Activity Logger Utility (`utils/activity-logger.ts`)
```typescript
// Function: logActivity(params: ActivityLogParams)
// Access: Internal system function
// Purpose: Direct activity logging with flexible parameters
// Usage: Used throughout the application for activity tracking
```

## Server Actions Access Control

### 1. Role Management Server Actions

#### File: `app/actions/roles.ts`
```typescript
// All server actions in this file require admin-level access
// Access control is enforced at the page/component level
// Functions are only called from admin pages that check isAdmin()

// createRole(role: Partial<Role>)
// - Validates role name uniqueness
// - Creates role with permissions structure
// - Uses Supabase admin client for database operations

// updateRole(role: Role)
// - Updates existing role data
// - Preserves permission structure integrity
// - Handles role hierarchy if applicable

// deleteRole(roleId: string)
// - Safely removes roles
// - Checks for role dependencies
// - Prevents deletion of system-critical roles
```

### 2. Authentication Server Actions

#### Password Management
```typescript
// Function: POST /api/auth/admin/update-password
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin"]
// Purpose: Admin-initiated password updates
// Activity Logging: Yes (password changes)

// Function: POST /api/auth/admin/update-password-rpc
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin"]
// Purpose: Admin password updates via RPC
// Activity Logging: Yes (password changes)
```

## Component-Level Access Control

### 1. Dashboard Pages

#### Roles Management Page (`app/dashboard/admin/roles/page.tsx`)
```typescript
// Access Control: isAdmin() check
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin"]
// Fallback: Permission denied card
// Features:
// - Role creation/editing
// - Permission management
// - Role deletion
// - Permission category organization

// Permission Categories:
// - Events: ["view", "create", "edit", "delete", "manage"]
// - Users: ["view", "create", "edit", "delete", "manage"]
// - Reports: ["view", "export", "manage"]
// - Settings: ["view", "edit"]
// - Roles: ["view", "create", "edit", "delete"]
```

#### Users Management Page (`app/dashboard/users/page.tsx`)
```typescript
// Access Control: isAdmin() check
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin"]
// Fallback: Permission denied card
// Features:
// - User listing with role information
// - Role updates
// - User creation
// - User details viewing
// - Password reset functionality
```

#### Activity Logs Page (`app/dashboard/activity-logs/page.tsx`)
```typescript
// Access Control: isAdmin() check (implied from navigation)
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin"]
// Features:
// - Activity log viewing
// - Filtering by category, user, date
// - Real-time activity display
// - User activity correlation
```

### 2. Settings Pages

#### Payment Gateway Settings (`app/dashboard/settings/payment-gateways/page.tsx`)
```typescript
// Access Control: role !== "admin" && role !== "manager" check
// Required Roles: ["admin", "manager", "super_admin", "supermanager", "event_admin"]
// Fallback: Permission denied card
// Features:
// - Payment gateway configuration
// - Gateway enable/disable
// - Gateway creation/editing
```

### 3. Navigation Components

#### Dashboard Sidebar (`components/dashboard/sidebar.tsx`)
```typescript
// Access Control: Role-based navigation item filtering
// Navigation Items with Role Requirements:
// - Dashboard: All authenticated users
// - Events: All authenticated users
// - Users: ["admin", "super_admin", "supermanager", "event_admin"]
// - Roles: ["admin", "super_admin", "supermanager"]
// - Activity Logs: ["admin", "super_admin", "supermanager", "event_admin"]
// - Settings: ["admin", "super_admin", "supermanager", "manager"]

// Functions:
// - isAdmin(): Uses auth context admin check
// - isManager(): Uses auth context manager check
// - Dynamic menu rendering based on user role
```

### 4. Admin Components

#### User Management Component (`components/admin/user-management.tsx`)
```typescript
// Access Control: isAdmin() check
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin"]
// Fallback: Permission denied card
// Features:
// - User table with role badges
// - Role editing dropdown
// - User action menu (edit, view, disable)
// - User search and filtering
```

#### Settings Panel Component (`components/admin/settings-panel.tsx`)
```typescript
// Access Control: isAdmin() check in onSubmit
// Required Roles: ["admin", "super_admin", "supermanager", "event_admin"]
// Features:
// - System settings configuration
// - Fee and limit management
// - Payment gateway settings
// - Certificate template management
```

## Activity Logging Integration

### 1. Authentication Activity Logging

```typescript
// Login Activities
// - Successful login: User ID, timestamp, IP, user agent
// - Failed login: Email attempt, reason, IP, user agent
// - Logout: User ID, session duration, timestamp

// Registration Activities
// - New user registration: User details, role assigned
// - Email verification: User ID, verification status
```

### 2. User Management Activity Logging

```typescript
// Role Changes (API: /api/admin/users/update-role)
// - Previous role ID and name
// - New role ID and name
// - User performing the change
// - Target user details
// - Timestamp and IP address

// User Creation
// - New user details
// - Initial role assignment
// - Creator information
```

### 3. Settings Activity Logging

```typescript
// Payment Gateway Changes
// - Gateway creation/updates
// - Status changes (enable/disable)
// - Configuration modifications
// - User performing changes

// System Settings Changes
// - Setting category and specific changes
// - Previous and new values
// - Administrator performing changes
```

### 4. Event Management Activity Logging

```typescript
// Event Updates (contexts/event-context.tsx)
// - Comprehensive change tracking
// - Before/after values for all fields
// - User performing updates
// - Event identification details
```

## Implementation Examples

### 1. Role Management Page

The roles management page (`app/dashboard/admin/roles/page.tsx`) demonstrates:
- Granular permission management
- Permission category organization
- Role creation and editing
- Permission toggling interface

### 2. User Management Integration

User management integrates both legacy and granular role systems:
- Displays role information from `user_roles` table
- Maintains compatibility with legacy `role` field
- Supports role updates through admin endpoints

### 3. Authentication Flow

The authentication system:
- Generates JWT tokens with user and role information
- Includes both `role` and `role_name` for compatibility
- Fetches role information from `user_roles` table during login

## Security Considerations

### 1. Authentication Security

#### JWT Token Security
```typescript
// Token Verification Pattern (used in all API endpoints)
const token = getJWTTokenFromRequest(request);
const authResult = await verifyJWTToken(token);

// Security Features:
// - Token signature verification
// - Expiration checking
// - Fresh user data fetching from database
// - Role information validation
```

#### Development Mode Bypass
```typescript
// All API endpoints include development bypass
const isDevelopment = process.env.NODE_ENV === "development";

// Security Implications:
// - Only active in development environment
// - Allows testing without authentication
// - Must be disabled in production
// - Includes logging for development access
```

### 2. Role-Based Security

#### Server-Side Role Verification
```typescript
// Pattern used across all protected endpoints (admin-only access)
const userRole = authResult.user.role_name || authResult.user.role;

if (userRole !== "admin") {
  return NextResponse.json(
    { error: "Unauthorized. Admin access required." },
    { status: 403 }
  );
}

// Security Features:
// - Never trust client-side role information
// - Always verify roles on server side
// - Only "admin" role has full access to all functions
// - Proper HTTP status codes (403 for forbidden)
```

#### Frontend Role Checking
```typescript
// Auth context provides centralized role checking
const { isAdmin, isManager } = useAuth();

// Security Features:
// - Centralized role logic
// - Consistent role definitions
// - UI-level access control
// - Fallback to server-side verification
```

### 3. Database Security

#### Row Level Security (RLS)
```sql
-- Events table policies
CREATE POLICY "Enable read access for own events"
ON events FOR SELECT
USING (auth.uid() = created_by);

CREATE POLICY "Enable update for event creators"
ON events FOR UPDATE
USING (auth.uid() = created_by)
WITH CHECK (auth.uid() = created_by);

-- Security Features:
-- - User can only access their own events
-- - Admin access through service role
-- - Automatic user ID verification
```

#### Admin Client Usage
```typescript
// Supabase admin client for privileged operations
const supabaseAdmin = getSupabaseAdmin();

// Security Features:
// - Bypasses RLS policies when needed
// - Used only in server-side operations
// - Requires service role key
// - Logged and monitored access
```

### 4. Activity Logging Security

#### Comprehensive Audit Trail
```typescript
// All sensitive operations are logged
await logActivity({
  userId: authResult.user.id,
  action: "update_user_role",
  entityType: "user",
  entityId: targetUserId,
  category: ActivityCategory.USER,
  details: {
    previous_role: oldRole,
    new_role: newRole,
    admin_user: authResult.user.email
  }
});

// Security Features:
// - Immutable audit trail
// - Detailed change tracking
// - User identification
// - IP address and user agent logging
```

### 5. API Security Patterns

#### Consistent Error Handling
```typescript
// Standardized error responses
return NextResponse.json(
  { error: "Unauthorized. Admin access required." },
  { status: 403 }
);

// Security Features:
// - Consistent error messages
// - Appropriate HTTP status codes
// - No information leakage
// - Logged security events
```

#### Input Validation
```typescript
// Role validation in user updates
if (!['free', 'paid', 'manager', 'admin'].includes(roleName)) {
  // Map to allowed values or reject
  mappedRole = mapRoleToLegacyConstraint(roleName);
}

// Security Features:
// - Input sanitization
// - Role constraint enforcement
// - Legacy compatibility
// - Validation logging
```

## Best Practices

### 1. Role Checking
- Always check both `role` and `role_name` fields for compatibility
- Use consistent role arrays across endpoints
- Implement fallback role mapping for legacy compatibility
- Centralize role checking logic in auth context

### 2. Permission Granularity
- Use granular permissions for fine-grained access control
- Organize permissions into logical categories
- Implement permission inheritance where appropriate
- Document permission requirements for each function

### 3. Security Implementation
- Never trust client-side role information
- Always verify roles on the server side
- Use JWT tokens for stateless authentication
- Implement proper token expiration and refresh
- Log all security-related events

### 4. Development Workflow
- Use development mode bypass for easier testing
- Implement comprehensive logging for access control
- Test role-based access thoroughly before deployment
- Maintain security documentation
- Regular security audits of role assignments

### 5. Error Handling
- Use consistent error messages and status codes
- Avoid information leakage in error responses
- Log security violations for monitoring
- Implement rate limiting for authentication endpoints

## Future Enhancements

### 1. Dynamic Permission System
- Implement runtime permission checking
- Add permission inheritance from parent roles
- Create permission-based API access control

### 2. Role Hierarchy
- Implement proper role hierarchy with inheritance
- Add role delegation capabilities
- Create role-based data filtering

### 3. Audit and Compliance
- Enhanced activity logging for role changes
- Permission change tracking
- Compliance reporting for access control

## Function Reference Table

### API Endpoints

| Endpoint | Method | Required Roles | Purpose | Activity Logging |
|----------|--------|----------------|---------|------------------|
| `/api/admin/users/list` | GET | Admin | List all users | Implicit |
| `/api/admin/users/create` | POST | Admin | Create new users | Yes |
| `/api/admin/users/update-role` | POST | Admin | Update user roles | Yes |
| `/api/admin/roles/list` | GET | Admin | List all roles | Implicit |
| `/api/admin/payment-gateways/list` | GET | Admin + Manager | List payment gateways | Implicit |
| `/api/admin/payment-gateways/create` | POST | Admin | Create payment gateways | Yes |
| `/api/admin/payment-gateways/update` | PUT | Admin | Update payment gateways | Yes |
| `/api/admin/payment-gateways/toggle` | PATCH | Admin + Manager | Toggle gateway status | Yes |
| `/api/auth/admin/update-password` | POST | Admin | Admin password updates | Yes |
| `/api/auth/admin/update-password-rpc` | POST | Admin | Admin password updates (RPC) | Yes |
| `/api/dashboard/events` | GET | Authenticated | List user's events | No |
| `/api/events` | GET | Public | List published events | No |
| `/api/auth/login` | POST | Public | User authentication | Yes |

**Role Definitions:**
- **Admin**: `"admin"` (only "admin" role has full access to all functions)
- **Admin + Manager**: `["admin", "manager", "supermanager", "event_admin"]` (elevated permissions)
- **Event Admin**: `"event_admin"` (can manage all events but not full system access)
- **Manager**: `["manager", "supermanager"]` (can manage own events and participants)
- **Authenticated**: Valid JWT token required
- **Public**: No authentication required

### Frontend Components

For detailed information about all components and their access requirements, see:
- **[Components Reference](./components-reference.md)** - Complete component documentation
- **[Application Structure](./application-structure.md)** - Page and route documentation

### Component Access Summary
- **Admin Components**: Role management, user management, activity logs, system settings
- **Manager Components**: Payment gateway settings (Admin + Manager access)
- **Authenticated Components**: Dashboard, profile, user-specific features
- **Public Components**: Event listings, authentication forms, public pages
| Settings Panel | `components/admin/settings-panel.tsx` | Admin | `isAdmin()` check |

### Server Actions

| Function | File Path | Access Control | Purpose |
|----------|-----------|----------------|---------|
| `createRole()` | `app/actions/roles.ts` | Page-level (Admin) | Create new roles |
| `updateRole()` | `app/actions/roles.ts` | Page-level (Admin) | Update existing roles |
| `deleteRole()` | `app/actions/roles.ts` | Page-level (Admin) | Delete roles |

### Context Functions

| Function | Context | Access Level | Purpose |
|----------|---------|--------------|---------|
| `isAdmin()` | `contexts/auth-context.tsx` | All users | Check admin privileges |
| `isManager()` | `contexts/auth-context.tsx` | All users | Check manager+ privileges |
| `login()` | `contexts/auth-context.tsx` | Public | User authentication |
| `logout()` | `contexts/auth-context.tsx` | Authenticated | User logout |
| `canCreateEvent()` | `contexts/user-context.tsx` | Authenticated | Check event creation rights |

### Activity Logging Functions

| Function | File Path | Access Level | Purpose |
|----------|-----------|--------------|---------|
| `logActivity()` | `utils/activity-logger.ts` | Internal | Direct activity logging |
| `logAuthActivity()` | `lib/activity-logger-service.ts` | Internal | Authentication logging |
| `logUserActivity()` | `lib/activity-logger-service.ts` | Internal | User management logging |
| `logEventActivity()` | `lib/activity-logger-service.ts` | Internal | Event management logging |
| `logSettingsActivity()` | `lib/activity-logger-service.ts` | Internal | Settings change logging |
| `logSystemActivity()` | `lib/activity-logger-service.ts` | Internal | System-level logging |

### Utility Functions

| Function | File Path | Access Level | Purpose |
|----------|-----------|--------------|---------|
| `verifyJWTToken()` | `lib/auth.ts` | Internal | JWT token verification |
| `getJWTTokenFromRequest()` | `lib/auth.ts` | Internal | Extract JWT from request |
| `getSupabaseAdmin()` | `lib/supabase-admin.ts` | Internal | Get admin Supabase client |

---

**Last Updated:** January 2025
**Version:** 2.0
**Maintainer:** Development Team
