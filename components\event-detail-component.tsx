"use client"

import { useState, useEffect } from "react"
import * as React from "react"
import { useRouter } from "next/navigation"
import { Calendar, Clock, MapPin, ArrowRight } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { type EventType } from "@/contexts/event-context"
import MainNav from "@/components/main-nav"
import { CTASection } from "@/components/cta-section"
import { EnhancedFooter } from "@/components/enhanced-footer"
import { EventHeroImage } from "@/components/event-hero-image"
import { EventImageCarousel } from "@/components/event-image-carousel"
import { InlineTicketSelection } from "@/components/inline-ticket-selection"
import { EventOrganizerTab } from "@/components/event-organizer-tab"
import { EventActionButtons } from "@/components/event-actions"
import { formatEventDateTime } from "@/lib/utils"

interface EventDetailComponentProps {
  slug: string
}

export function EventDetailComponent({ slug }: EventDetailComponentProps) {
  const router = useRouter()
  const [event, setEvent] = useState<EventType | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Get current participants from event data (provided by API)
  const currentParticipants = event?.current_participants || 0

  // Check if event has ended
  const eventEnded = event ? new Date(event.end_date) < new Date() : false

  // Check if registration is closed
  const registrationClosed = event ? (
    eventEnded ||
    (event.registration_deadline && new Date(event.registration_deadline) < new Date())
  ) : false

  useEffect(() => {
    if (!slug) {
      setError("No event specified")
      setLoading(false)
      return
    }

    const fetchEvent = async () => {
      setLoading(true)
      try {
        console.log("Fetching event with slug:", slug)

        // Use API endpoint instead of context to ensure we get organizer data
        const response = await fetch(`/api/events/${slug}`)

        if (!response.ok) {
          if (response.status === 404) {
            setError("Event not found")
            return
          }
          throw new Error("Failed to fetch event")
        }

        const eventData = await response.json()

        console.log("Event data loaded successfully:", eventData)
        console.log("Organization data:", eventData.organizations)
        setEvent(eventData)

        // Update page title with event name
        if (eventData?.title) {
          document.title = `${eventData.title} | mTicket.my - Event Management Platform`
        }
      } catch (err: any) {
        console.error("Error fetching event:", err)
        setError(err.message || "Failed to load event details")
      } finally {
        setLoading(false)
      }
    }

    fetchEvent()
  }, [slug])

  // Cleanup: Reset title when component unmounts
  useEffect(() => {
    return () => {
      document.title = "mTicket.my - Event Management Platform"
    }
  }, [])

  // Show loading state
  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <MainNav />
        <main className="flex-1">
          <div className="container mx-auto px-4 py-8">
            <div className="flex flex-col items-center justify-center min-h-[50vh]">
              <div className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
              <p className="mt-4 text-lg">Loading event details...</p>
            </div>
          </div>
        </main>
      </div>
    )
  }

  // Show error state
  if (error || !event) {
    return (
      <div className="flex min-h-screen flex-col">
        <MainNav />
        <main className="flex-1">
          <div className="container mx-auto px-4 py-8">
            <div className="flex flex-col items-center justify-center min-h-[50vh]">
              <div className="text-destructive text-5xl mb-4">⚠️</div>
              <h1 className="text-2xl font-bold mb-4">{error || "Event not found"}</h1>
              <p className="text-muted-foreground mb-6">
                The event you're looking for doesn't exist or has been removed.
              </p>
              <Button onClick={() => router.push("/")}>Return to Home</Button>
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="flex min-h-screen flex-col">
      <MainNav />
      <main className="flex-1">
        {/* Event Hero Image */}
        <EventHeroImage
          images={event.images}
          fallbackImageUrl={event.image_url}
          eventTitle={event.title}
          startDate={event.start_date}
          showCountdown={true}
          className="w-full"
        />

        <div className="container px-4 md:px-6 py-8">
          <div className="mx-auto max-w-7xl">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              <h1 className="text-3xl font-bold mb-4">{event.title}</h1>

              {(() => {
                const dateTimeInfo = formatEventDateTime(event.start_date, event.end_date)

                return (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                    {/* Date and Time Information */}
                    {dateTimeInfo.isSingleDay ? (
                      // Single day event: Start Date & Time, End Date & Time, Location
                      <>
                        <div className="flex items-start">
                          <Calendar className="h-5 w-5 mr-2 text-primary" />
                          <div>
                            <p className="text-sm font-medium">Start Date & Time</p>
                            <p className="text-sm text-muted-foreground">{dateTimeInfo.startDateFormatted}</p>
                            <p className="text-sm text-muted-foreground">{dateTimeInfo.startTimeFormatted}</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <Clock className="h-5 w-5 mr-2 text-primary" />
                          <div>
                            <p className="text-sm font-medium">End Date & Time</p>
                            <p className="text-sm text-muted-foreground">{dateTimeInfo.endDateFormatted}</p>
                            <p className="text-sm text-muted-foreground">{dateTimeInfo.endTimeFormatted}</p>
                          </div>
                        </div>
                      </>
                    ) : (
                      // Multi-day event: Start Date & Time, End Date & Time, Total Days
                      <>
                        <div className="flex items-start">
                          <Calendar className="h-5 w-5 mr-2 text-primary" />
                          <div>
                            <p className="text-sm font-medium">Start Date & Time</p>
                            <p className="text-sm text-muted-foreground">{dateTimeInfo.startDateFormatted}</p>
                            <p className="text-sm text-muted-foreground">{dateTimeInfo.startTimeFormatted}</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <Clock className="h-5 w-5 mr-2 text-primary" />
                          <div>
                            <p className="text-sm font-medium">End Date & Time</p>
                            <p className="text-sm text-muted-foreground">{dateTimeInfo.endDateFormatted}</p>
                            <p className="text-sm text-muted-foreground">{dateTimeInfo.endTimeFormatted}</p>
                          </div>
                        </div>
                        <div className="flex items-start">
                          <Calendar className="h-5 w-5 mr-2 text-primary" />
                          <div>
                            <p className="text-sm font-medium">Total Days</p>
                            <p className="text-sm text-muted-foreground">{dateTimeInfo.totalDays} days</p>
                          </div>
                        </div>
                      </>
                    )}

                    {/* Location */}
                    <div className="flex items-start">
                      <MapPin className="h-5 w-5 mr-2 text-primary" />
                      <div>
                        <p className="text-sm font-medium">Location</p>
                        <p className="text-sm text-muted-foreground">{event.location}</p>
                      </div>
                    </div>
                  </div>
                )
              })()}

              {/* Image Carousel - Above About Tab */}
              {event.images && event.images.length > 1 && (
                <div className="mb-6">
                  <h3 className="text-lg font-semibold mb-3">Event Gallery</h3>
                  <EventImageCarousel
                    images={event.images}
                    eventTitle={event.title}
                  />
                </div>
              )}

              <Tabs defaultValue="about">
                <div className="flex justify-between items-center mb-4">
                  <TabsList>
                    <TabsTrigger value="about">About</TabsTrigger>
                    <TabsTrigger value="organizer">Organizer</TabsTrigger>
                  </TabsList>
                  <div className="flex items-center">
                    <EventActionButtons
                      eventSlug={slug}
                      eventTitle={event.title}
                      className="flex items-center"
                    />
                  </div>
                </div>
                <TabsContent value="about" className="space-y-4">
                  <div className="prose max-w-none">
                    {event.description_html ? (
                      <div
                        dangerouslySetInnerHTML={{ __html: event.description_html }}
                        className="wysiwyg-content"
                      />
                    ) : (
                      <p className="text-justify">{event.description}</p>
                    )}
                  </div>
                </TabsContent>
                <TabsContent value="organizer" className="space-y-4">
                  <EventOrganizerTab organizer={event.organizations || null} />
                </TabsContent>
              </Tabs>

              {/* Ticket Selection Section */}
              <div id="select-tickets" className="mt-8">
                <InlineTicketSelection event={event} />
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Event Info Card */}
              <Card>
                <CardContent className="p-6">
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-4">
                      <p className="text-lg font-bold">Event Details</p>
                      {/* Published Status Badge */}
                      <Badge
                        className={
                          (event.status === 'published' || event.is_published)
                            ? 'bg-green-100 text-green-800 border-green-200'
                            : 'bg-yellow-100 text-yellow-800 border-yellow-200'
                        }
                      >
                        {(event.status === 'published' || event.is_published) ? 'Published' : 'Draft'}
                      </Badge>
                    </div>

                    {/* Event Status Information */}
                    <div className="space-y-2 mb-4">

                      {/* Registration Status */}
                      <div className="flex items-center text-sm">
                        <div className={`w-2 h-2 rounded-full mr-2 ${registrationClosed ? 'bg-red-500' : 'bg-green-500'}`}></div>
                        <span className={registrationClosed ? 'text-muted-foreground' : 'text-foreground'}>
                          {registrationClosed ? 'Registration closed' : 'Registration open'}
                        </span>
                      </div>

                      {/* Registration Deadline */}
                      {event.registration_deadline && (
                        <div className="flex items-center text-sm">
                          <div className="w-2 h-2 rounded-full mr-2 bg-blue-500"></div>
                          <span className="text-foreground">
                            Registration deadline: {new Date(event.registration_deadline).toLocaleDateString()}
                          </span>
                        </div>
                      )}

                      {/* Attendance and Certificate Info */}
                      <div className="flex items-center text-sm">
                        <div className={`w-2 h-2 rounded-full mr-2 ${event.enable_attendance ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                        <span className={event.enable_attendance ? 'text-foreground' : 'text-muted-foreground'}>
                          {event.enable_attendance ? 'Attendance tracking enabled' : 'No attendance tracking'}
                        </span>
                      </div>
                      {event.enable_attendance && (
                        <div className="flex items-center text-sm">
                          <div className={`w-2 h-2 rounded-full mr-2 ${event.enable_certificates ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                          <span className={event.enable_certificates ? 'text-foreground' : 'text-muted-foreground'}>
                            {event.enable_certificates ? 'Digital certificate provided' : 'No certificate'}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-sm text-muted-foreground">
                        {event.max_participants
                          ? `Limited to ${event.max_participants} participants`
                          : "Unlimited participants"}
                      </p>
                      <p className="text-sm font-medium">
                        {currentParticipants}{event.max_participants ? ` / ${event.max_participants}` : ' registered'}
                      </p>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2.5">
                      <div
                        className="bg-primary h-2.5 rounded-full transition-all duration-300"
                        style={{
                          width: `${
                            event.max_participants
                              ? Math.min((currentParticipants / event.max_participants) * 100, 100)
                              : Math.min((currentParticipants / 100) * 100, 100) // Show progress for unlimited events based on 100 as reference
                          }%`,
                        }}
                      ></div>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {event.max_participants
                        ? `${Math.max(0, event.max_participants - currentParticipants)} spots remaining`
                        : `${currentParticipants} people registered`
                      }
                    </p>
                  </div>

                  {eventEnded ? (
                    <Button
                      className="w-full"
                      size="lg"
                      variant="secondary"
                      disabled
                    >
                      Event Ended
                    </Button>
                  ) : registrationClosed ? (
                    <Button
                      className="w-full"
                      size="lg"
                      variant="secondary"
                      disabled
                    >
                      Registration Closed
                    </Button>
                  ) : (
                    <Button
                      className="w-full"
                      size="lg"
                      onClick={() => {
                        const ticketSection = document.getElementById('select-tickets')
                        ticketSection?.scrollIntoView({ behavior: 'smooth' })
                      }}
                    >
                      Select Tickets
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  )}
                </CardContent>
              </Card>
            </div>
            </div>
          </div>
        </div>
      </main>
      <CTASection />
      <EnhancedFooter />
    </div>
  )
}
