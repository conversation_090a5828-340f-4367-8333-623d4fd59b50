-- Add payment_callback_data column to registrations table for storing payment gateway callback data
-- This field stores the complete callback data from payment gateways (ToyyibPay, etc.) for debugging and verification

DO $$ 
BEGIN
  -- Add payment_callback_data column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'registrations' AND column_name = 'payment_callback_data') THEN
    ALTER TABLE registrations ADD COLUMN payment_callback_data JSONB DEFAULT '{}';
  END IF;
END $$;

-- Create index for better performance when querying callback data
CREATE INDEX IF NOT EXISTS idx_registrations_payment_callback_data ON registrations USING GIN (payment_callback_data);

-- Add comment to document the purpose
COMMENT ON COLUMN registrations.payment_callback_data IS 'Complete payment gateway callback data (status_id, billcode, order_id, msg, transaction_id, etc.) for debugging and verification';
