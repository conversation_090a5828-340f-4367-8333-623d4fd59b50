"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON>er } from "next/navigation"
import Link from "next/link"
import { ArrowLeft } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { supabase } from "@/lib/supabase"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { EventForm } from "@/components/event-form"
import { useAuth } from "@/contexts/auth-context"

export default function EditEventPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const { user } = useAuth()
  const [event, setEvent] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const eventSlug = params.slug as string

  useEffect(() => {
    const fetchEvent = async () => {
      setLoading(true)
      try {
        // Fetch event by slug with all related data
        const { data, error } = await supabase
          .from("events")
          .select(`
            *,
            event_categories!category_id (
              id,
              name,
              color,
              icon
            ),
            organizations (
              id,
              name,
              logo_url
            )
          `)
          .eq("slug", eventSlug)
          .single()

        if (error) {
          if (error.code === "PGRST116") {
            toast({
              title: "Event not found",
              description: "The event you're trying to edit doesn't exist or has been removed.",
              variant: "destructive",
            })
            router.push("/dashboard/events")
            return
          }
          throw error
        }

        if (data) {
          setEvent(data)
        } else {
          toast({
            title: "Event not found",
            description: "The event you're trying to edit doesn't exist or has been removed.",
            variant: "destructive",
          })
          router.push("/dashboard/events")
          return
        }
      } catch (error: any) {
        console.error("Error fetching event:", error)
        toast({
          title: "Error",
          description: error.message || "Something went wrong",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    if (eventSlug) {
      fetchEvent()
    }
  }, [eventSlug, router, toast])

  const handleSuccess = (updatedEvent: any) => {
    toast({
      title: "Success",
      description: "Event updated successfully",
    })
    router.push(`/dashboard/events/${updatedEvent.slug}`)
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6 px-4">
        <div className="mb-6">
          <Skeleton className="h-8 w-40" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-3/4 mb-2" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent className="space-y-6">
            <Skeleton className="h-40 w-full" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!event) {
    return (
      <div className="container mx-auto py-6 px-4">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-2">Event Not Found</h2>
          <p className="text-muted-foreground mb-6">The event you're trying to edit doesn't exist or has been removed.</p>
          <Link href="/dashboard/events">
            <Button>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Events
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="mb-6">
        <Link href={`/dashboard/events/${eventSlug}`}>
          <Button variant="outline" size="sm">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Event
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Edit Event</CardTitle>
          <CardDescription>
            Update your event details. All changes will be saved immediately.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {user && (
            <EventForm
              event={event}
              userId={user.id}
              onSuccess={handleSuccess}
            />
          )}
        </CardContent>
      </Card>
    </div>
  )
}
