import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { validateApiKeyMiddleware, getUserIdFromApiKey } from "@/lib/api-key-validator";
import { logActivity, ActivityCategory } from "@/utils/activity-logger";
import crypto from "crypto";

/**
 * Validates an API key
 */
export async function GET(request: NextRequest) {
  try {
    // Validate API key
    const apiKeyResponse = await validateApiKeyMiddleware(request);
    if (apiKeyResponse) {
      return apiKeyResponse; // Return the error response if API key is invalid
    }

    // Extract the API key from the Authorization header
    const authHeader = request.headers.get("Authorization") || "";
    const apiKey = authHeader.substring(7); // Remove "Bearer " prefix

    // Get the user ID associated with the API key
    const userId = await getUserIdFromApiKey(apiKey);

    // Log the API key validation
    await logActivity({
      action: "api_key_validated",
      entityType: "api_key",
      category: ActivityCategory.SECURITY,
      details: {
        user_id: userId,
        timestamp: new Date().toISOString(),
      },
    });

    return NextResponse.json(
      {
        valid: true,
        message: "API key is valid",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error validating API key:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * Creates a new API key for the authenticated user
 */
export async function POST(request: NextRequest) {
  try {
    // Validate API key
    const apiKeyResponse = await validateApiKeyMiddleware(request);
    if (apiKeyResponse) {
      return apiKeyResponse; // Return the error response if API key is invalid
    }

    // Extract the API key from the Authorization header
    const authHeader = request.headers.get("Authorization") || "";
    const apiKey = authHeader.substring(7); // Remove "Bearer " prefix

    // Get the user ID associated with the API key
    const userId = await getUserIdFromApiKey(apiKey);
    if (!userId) {
      return NextResponse.json(
        { error: "Invalid API key" },
        { status: 401 }
      );
    }

    // Generate a new API key
    const newKey = "mtk_" + crypto.randomBytes(16).toString("hex");

    // Store the new API key in the database
    const { data, error } = await supabase
      .from("api_keys")
      .insert({
        key: newKey,
        user_id: userId,
        created_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error("Error creating API key:", error);
      return NextResponse.json(
        { error: "Failed to create API key" },
        { status: 500 }
      );
    }

    // Log the API key creation
    await logActivity({
      action: "api_key_created",
      entityType: "api_key",
      category: ActivityCategory.SECURITY,
      details: {
        user_id: userId,
        api_key_id: data.id,
        timestamp: new Date().toISOString(),
      },
    });

    return NextResponse.json(
      {
        message: "API key created successfully",
        key: newKey,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating API key:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
