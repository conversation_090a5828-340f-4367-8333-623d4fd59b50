import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/lib/supabase";
import { validateApiKeyMiddleware } from "@/lib/api-key-validator";
import { logActivity, ActivityCategory } from "@/utils/activity-logger";

/**
 * Handles webhook event triggers
 * This endpoint allows external services to trigger webhook events
 */
export async function POST(request: NextRequest) {
  try {
    // Validate API key
    const apiKeyResponse = await validateApiKeyMiddleware(request);
    if (apiKeyResponse) {
      return apiKeyResponse; // Return the error response if API key is invalid
    }

    // Parse the request body
    const { event, data } = await request.json();

    // Validate required fields
    if (!event) {
      return NextResponse.json(
        { error: "Event type is required" },
        { status: 400 }
      );
    }

    // Log the webhook event
    await logActivity({
      action: "webhook_event_received",
      entityType: "webhook",
      category: ActivityCategory.SYSTEM,
      details: {
        event,
        data,
        timestamp: new Date().toISOString(),
      },
    });

    // Find all active webhooks for this event
    const { data: webhooks, error } = await supabase
      .from("webhooks")
      .select("*")
      .filter("active", "eq", true)
      .filter("events", "cs", `{"${event}"}`)
      .order("created_at", { ascending: false });

    if (error) {
      console.error("Error fetching webhooks:", error);
      return NextResponse.json(
        { error: "Error fetching webhooks" },
        { status: 500 }
      );
    }

    if (!webhooks || webhooks.length === 0) {
      return NextResponse.json(
        { message: "No webhooks found for this event", count: 0 },
        { status: 200 }
      );
    }

    // Prepare the webhook payload
    const payload = {
      event,
      data,
      timestamp: new Date().toISOString(),
    };

    // Send the webhook notifications in parallel
    const webhookPromises = webhooks.map(async (webhook) => {
      try {
        // Send the webhook notification
        const response = await fetch(webhook.url, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        });

        // Update the webhook stats
        const success = response.ok;
        const updateData = {
          last_triggered_at: new Date().toISOString(),
          ...(success
            ? { success_count: (webhook.success_count || 0) + 1 }
            : { failure_count: (webhook.failure_count || 0) + 1 }),
        };

        await supabase.from("webhooks").update(updateData).eq("id", webhook.id);

        return {
          id: webhook.id,
          name: webhook.name,
          success,
          status: response.status,
        };
      } catch (error) {
        console.error(`Error sending webhook ${webhook.id} for event ${event}:`, error);

        // Update the failure count
        await supabase
          .from("webhooks")
          .update({
            last_triggered_at: new Date().toISOString(),
            failure_count: (webhook.failure_count || 0) + 1,
          })
          .eq("id", webhook.id);

        return {
          id: webhook.id,
          name: webhook.name,
          success: false,
          error: (error as Error).message,
        };
      }
    });

    // Wait for all webhook notifications to be processed
    const results = await Promise.all(webhookPromises);

    return NextResponse.json(
      {
        message: "Webhooks processed",
        count: webhooks.length,
        results,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error processing webhook event:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
