# Android Implementation Examples for mTicket.my

## Project Setup and Dependencies

### build.gradle (Module: app)
```gradle
dependencies {
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.7.0'
    implementation 'androidx.activity:activity-compose:1.8.2'
    implementation 'androidx.compose.ui:ui:1.5.8'
    implementation 'androidx.compose.ui:ui-tooling-preview:1.5.8'
    implementation 'androidx.compose.material3:material3:1.1.2'
    
    // Navigation
    implementation 'androidx.navigation:navigation-compose:2.7.6'
    
    // ViewModel
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0'
    
    // Networking
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    
    // Local Database
    implementation 'androidx.room:room-runtime:2.6.1'
    implementation 'androidx.room:room-ktx:2.6.1'
    kapt 'androidx.room:room-compiler:2.6.1'
    
    // QR Code
    implementation 'com.journeyapps:zxing-android-embedded:4.3.0'
    implementation 'com.google.zxing:core:3.5.2'
    
    // Image Loading
    implementation 'io.coil-kt:coil-compose:2.5.0'
    
    // Security
    implementation 'androidx.security:security-crypto:1.1.0-alpha06'
    
    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3'
    
    // JSON
    implementation 'com.google.code.gson:gson:2.10.1'
}
```

## Network Layer Implementation

### 1. API Service Interface
```kotlin
interface MTicketApiService {
    
    @POST("auth/login")
    suspend fun login(@Body request: LoginRequest): Response<LoginResponse>
    
    @GET("auth/verify")
    suspend fun verifyToken(@Header("Authorization") token: String): Response<TokenVerificationResponse>
    
    @GET("dashboard/tickets")
    suspend fun getUserTickets(@Header("Authorization") token: String): Response<List<TicketResponse>>
    
    @POST("tickets/secure-qr")
    suspend fun generateSecureQR(
        @Header("Authorization") token: String,
        @Body request: SecureQRRequest
    ): Response<SecureQRResponse>
}
```

### 2. Network Module (Dependency Injection)
```kotlin
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    
    @Provides
    @Singleton
    fun provideOkHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .addInterceptor(HttpLoggingInterceptor().apply {
                level = if (BuildConfig.DEBUG) {
                    HttpLoggingInterceptor.Level.BODY
                } else {
                    HttpLoggingInterceptor.Level.NONE
                }
            })
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
    }
    
    @Provides
    @Singleton
    fun provideRetrofit(okHttpClient: OkHttpClient): Retrofit {
        return Retrofit.Builder()
            .baseUrl(BuildConfig.API_BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
    
    @Provides
    @Singleton
    fun provideMTicketApiService(retrofit: Retrofit): MTicketApiService {
        return retrofit.create(MTicketApiService::class.java)
    }
}
```

### 3. Repository Implementation
```kotlin
@Singleton
class TicketRepository @Inject constructor(
    private val apiService: MTicketApiService,
    private val ticketDao: TicketDao,
    private val tokenManager: TokenManager
) {
    
    suspend fun getUserTickets(): Result<List<TicketResponse>> {
        return try {
            val token = tokenManager.getToken()
            if (token == null) {
                return Result.failure(Exception("No authentication token"))
            }
            
            val response = apiService.getUserTickets("Bearer $token")
            if (response.isSuccessful) {
                val tickets = response.body() ?: emptyList()
                // Cache tickets locally
                cacheTickets(tickets)
                Result.success(tickets)
            } else {
                Result.failure(Exception("Failed to fetch tickets: ${response.code()}"))
            }
        } catch (e: Exception) {
            // Return cached tickets if network fails
            val cachedTickets = getCachedTickets()
            if (cachedTickets.isNotEmpty()) {
                Result.success(cachedTickets)
            } else {
                Result.failure(e)
            }
        }
    }
    
    suspend fun generateSecureQR(ticketId: String, eventId: String, guestName: String): Result<String> {
        return try {
            val token = tokenManager.getToken()
            if (token == null) {
                return Result.failure(Exception("No authentication token"))
            }
            
            val request = SecureQRRequest(
                ticketData = TicketData(
                    id = ticketId,
                    event_id = eventId,
                    guest_name = guestName
                )
            )
            
            val response = apiService.generateSecureQR("Bearer $token", request)
            if (response.isSuccessful) {
                val qrResponse = response.body()
                if (qrResponse?.success == true) {
                    Result.success(qrResponse.qrData)
                } else {
                    Result.failure(Exception("Failed to generate QR code"))
                }
            } else {
                Result.failure(Exception("QR generation failed: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    private suspend fun cacheTickets(tickets: List<TicketResponse>) {
        val entities = tickets.map { it.toEntity() }
        ticketDao.insertTickets(entities)
    }
    
    private suspend fun getCachedTickets(): List<TicketResponse> {
        return ticketDao.getAllTickets().map { it.toResponse() }
    }
}
```

## Authentication Implementation

### 1. Secure Token Storage
```kotlin
@Singleton
class SecureTokenManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    private val masterKey = MasterKey.Builder(context)
        .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
        .build()
    
    private val encryptedSharedPreferences = EncryptedSharedPreferences.create(
        context,
        "secure_prefs",
        masterKey,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
    )
    
    fun saveToken(token: String) {
        encryptedSharedPreferences.edit()
            .putString(KEY_JWT_TOKEN, token)
            .putLong(KEY_TOKEN_SAVED_TIME, System.currentTimeMillis())
            .apply()
    }
    
    fun getToken(): String? {
        val token = encryptedSharedPreferences.getString(KEY_JWT_TOKEN, null)
        return if (token != null && !isTokenExpired(token)) {
            token
        } else {
            clearToken()
            null
        }
    }
    
    fun clearToken() {
        encryptedSharedPreferences.edit()
            .remove(KEY_JWT_TOKEN)
            .remove(KEY_TOKEN_SAVED_TIME)
            .apply()
    }
    
    private fun isTokenExpired(token: String): Boolean {
        return try {
            val parts = token.split(".")
            if (parts.size != 3) return true
            
            val payload = String(Base64.decode(parts[1], Base64.URL_SAFE))
            val json = Gson().fromJson(payload, JsonObject::class.java)
            val exp = json.get("exp")?.asLong ?: return true
            
            val currentTime = System.currentTimeMillis() / 1000
            currentTime >= exp
        } catch (e: Exception) {
            true
        }
    }
    
    companion object {
        private const val KEY_JWT_TOKEN = "jwt_token"
        private const val KEY_TOKEN_SAVED_TIME = "token_saved_time"
    }
}
```

### 2. Authentication ViewModel
```kotlin
@HiltViewModel
class AuthViewModel @Inject constructor(
    private val authRepository: AuthRepository,
    private val tokenManager: SecureTokenManager
) : ViewModel() {
    
    private val _authState = MutableStateFlow<AuthState>(AuthState.Initial)
    val authState: StateFlow<AuthState> = _authState.asStateFlow()
    
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()
    
    fun login(email: String, password: String) {
        viewModelScope.launch {
            _isLoading.value = true
            _authState.value = AuthState.Loading
            
            try {
                val result = authRepository.login(email, password)
                result.fold(
                    onSuccess = { loginResponse ->
                        tokenManager.saveToken(loginResponse.token)
                        _authState.value = AuthState.Success(loginResponse.user)
                    },
                    onFailure = { error ->
                        _authState.value = AuthState.Error(error.message ?: "Login failed")
                    }
                )
            } catch (e: Exception) {
                _authState.value = AuthState.Error(e.message ?: "Unknown error")
            } finally {
                _isLoading.value = false
            }
        }
    }
    
    fun logout() {
        tokenManager.clearToken()
        _authState.value = AuthState.Initial
    }
    
    fun checkAuthStatus() {
        viewModelScope.launch {
            val token = tokenManager.getToken()
            if (token != null) {
                // Verify token with server
                val result = authRepository.verifyToken()
                result.fold(
                    onSuccess = { user ->
                        _authState.value = AuthState.Success(user)
                    },
                    onFailure = {
                        tokenManager.clearToken()
                        _authState.value = AuthState.Initial
                    }
                )
            } else {
                _authState.value = AuthState.Initial
            }
        }
    }
}

sealed class AuthState {
    object Initial : AuthState()
    object Loading : AuthState()
    data class Success(val user: User) : AuthState()
    data class Error(val message: String) : AuthState()
}
```

## QR Code Implementation

### 1. QR Code Generator
```kotlin
class QRCodeGenerator {
    
    fun generateQRCodeBitmap(data: String, size: Int = 512): Bitmap? {
        return try {
            val writer = QRCodeWriter()
            val bitMatrix = writer.encode(data, BarcodeFormat.QR_CODE, size, size)
            val width = bitMatrix.width
            val height = bitMatrix.height
            val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565)
            
            for (x in 0 until width) {
                for (y in 0 until height) {
                    bitmap.setPixel(
                        x, y, 
                        if (bitMatrix[x, y]) Color.BLACK else Color.WHITE
                    )
                }
            }
            bitmap
        } catch (e: Exception) {
            Log.e("QRCodeGenerator", "Error generating QR code", e)
            null
        }
    }
}
```

### 2. QR Code Auto-Refresh Manager
```kotlin
@Singleton
class QRRefreshManager @Inject constructor(
    private val ticketRepository: TicketRepository
) {
    
    private val refreshJobs = mutableMapOf<String, Job>()
    
    fun startAutoRefresh(
        ticketId: String,
        eventId: String,
        guestName: String,
        scope: CoroutineScope,
        onQRUpdated: (String) -> Unit,
        onError: (String) -> Unit
    ) {
        // Cancel existing job for this ticket
        refreshJobs[ticketId]?.cancel()
        
        refreshJobs[ticketId] = scope.launch {
            while (isActive) {
                try {
                    val result = ticketRepository.generateSecureQR(ticketId, eventId, guestName)
                    result.fold(
                        onSuccess = { qrData ->
                            onQRUpdated(qrData)
                        },
                        onFailure = { error ->
                            onError(error.message ?: "Failed to refresh QR code")
                        }
                    )
                    
                    // Wait 25 seconds before next refresh (5-second buffer before 30s expiry)
                    delay(25_000)
                } catch (e: CancellationException) {
                    break
                } catch (e: Exception) {
                    onError(e.message ?: "QR refresh error")
                    delay(5_000) // Retry after 5 seconds on error
                }
            }
        }
    }
    
    fun stopAutoRefresh(ticketId: String) {
        refreshJobs[ticketId]?.cancel()
        refreshJobs.remove(ticketId)
    }
    
    fun stopAllRefresh() {
        refreshJobs.values.forEach { it.cancel() }
        refreshJobs.clear()
    }
}
```

## UI Implementation with Jetpack Compose

### 1. Login Screen
```kotlin
@Composable
fun LoginScreen(
    viewModel: AuthViewModel = hiltViewModel(),
    onLoginSuccess: () -> Unit
) {
    val authState by viewModel.authState.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var passwordVisible by remember { mutableStateOf(false) }
    
    LaunchedEffect(authState) {
        if (authState is AuthState.Success) {
            onLoginSuccess()
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // Logo
        Image(
            painter = painterResource(id = R.drawable.mticket_logo),
            contentDescription = "mTicket Logo",
            modifier = Modifier.size(120.dp)
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // Email Field
        OutlinedTextField(
            value = email,
            onValueChange = { email = it },
            label = { Text("Email") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
            modifier = Modifier.fillMaxWidth(),
            enabled = !isLoading
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Password Field
        OutlinedTextField(
            value = password,
            onValueChange = { password = it },
            label = { Text("Password") },
            visualTransformation = if (passwordVisible) VisualTransformation.None else PasswordVisualTransformation(),
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
            trailingIcon = {
                IconButton(onClick = { passwordVisible = !passwordVisible }) {
                    Icon(
                        imageVector = if (passwordVisible) Icons.Filled.Visibility else Icons.Filled.VisibilityOff,
                        contentDescription = if (passwordVisible) "Hide password" else "Show password"
                    )
                }
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = !isLoading
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // Login Button
        Button(
            onClick = { viewModel.login(email, password) },
            modifier = Modifier.fillMaxWidth(),
            enabled = !isLoading && email.isNotBlank() && password.isNotBlank()
        ) {
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    color = MaterialTheme.colorScheme.onPrimary
                )
            } else {
                Text("Login")
            }
        }
        
        // Error Message
        if (authState is AuthState.Error) {
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = authState.message,
                color = MaterialTheme.colorScheme.error,
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}
```

### 2. Ticket List Screen
```kotlin
@Composable
fun TicketListScreen(
    viewModel: TicketViewModel = hiltViewModel(),
    onTicketClick: (String) -> Unit
) {
    val tickets by viewModel.tickets.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    
    val pullRefreshState = rememberPullRefreshState(
        refreshing = isLoading,
        onRefresh = { viewModel.refreshTickets() }
    )
    
    LaunchedEffect(Unit) {
        viewModel.loadTickets()
    }
    
    Box(
        modifier = Modifier
            .fillMaxSize()
            .pullRefresh(pullRefreshState)
    ) {
        when {
            tickets.isEmpty() && !isLoading -> {
                EmptyTicketsMessage()
            }
            else -> {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(16.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(tickets) { ticket ->
                        TicketCard(
                            ticket = ticket,
                            onClick = { onTicketClick(ticket.id) }
                        )
                    }
                }
            }
        }
        
        PullRefreshIndicator(
            refreshing = isLoading,
            state = pullRefreshState,
            modifier = Modifier.align(Alignment.TopCenter)
        )
        
        error?.let { errorMessage ->
            Snackbar(
                modifier = Modifier.align(Alignment.BottomCenter),
                action = {
                    TextButton(onClick = { viewModel.clearError() }) {
                        Text("Dismiss")
                    }
                }
            ) {
                Text(errorMessage)
            }
        }
    }
}
```

### 3. QR Code Display Screen
```kotlin
@Composable
fun QRCodeScreen(
    ticketId: String,
    viewModel: QRCodeViewModel = hiltViewModel()
) {
    val qrData by viewModel.qrData.collectAsState()
    val ticket by viewModel.ticket.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    
    LaunchedEffect(ticketId) {
        viewModel.loadTicket(ticketId)
        viewModel.startQRRefresh(ticketId)
    }
    
    DisposableEffect(Unit) {
        onDispose {
            viewModel.stopQRRefresh()
        }
    }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        ticket?.let { ticketData ->
            Text(
                text = ticketData.event.title,
                style = MaterialTheme.typography.headlineMedium,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = "Attendee: ${ticketData.attendee_name}",
                style = MaterialTheme.typography.bodyLarge
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            // QR Code Display
            Box(
                modifier = Modifier.size(300.dp),
                contentAlignment = Alignment.Center
            ) {
                when {
                    isLoading -> {
                        CircularProgressIndicator()
                    }
                    qrData != null -> {
                        QRCodeImage(
                            qrData = qrData!!,
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                    error != null -> {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "Failed to generate QR code",
                                color = MaterialTheme.colorScheme.error
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Button(
                                onClick = { viewModel.retryQRGeneration() }
                            ) {
                                Text("Retry")
                            }
                        }
                    }
                }
            }
            
            Spacer(modifier = Modifier.height(24.dp))
            
            Text(
                text = "Show this QR code at the event entrance",
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Auto-refresh indicator
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "Auto refresh",
                    modifier = Modifier.size(16.dp),
                    tint = MaterialTheme.colorScheme.primary
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = "QR code refreshes automatically",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

@Composable
fun QRCodeImage(
    qrData: String,
    modifier: Modifier = Modifier
) {
    val qrBitmap = remember(qrData) {
        QRCodeGenerator().generateQRCodeBitmap(qrData)
    }
    
    qrBitmap?.let { bitmap ->
        Image(
            bitmap = bitmap.asImageBitmap(),
            contentDescription = "Ticket QR Code",
            modifier = modifier
        )
    }
}
```

This implementation provides a solid foundation for the Android app with secure authentication, efficient ticket management, and automatic QR code refresh functionality. The code follows Android best practices and uses modern libraries for optimal performance and security.
