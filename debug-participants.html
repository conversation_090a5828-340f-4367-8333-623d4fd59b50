<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Participants Issue - Event 5nEh</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
        .error-section {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        .ticket-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .ticket-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        input[type="number"] {
            width: 60px;
            padding: 5px;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn.debug {
            background: #28a745;
            margin-left: 10px;
        }
        .btn.debug:hover {
            background: #1e7e34;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 Debug Participants Issue</h1>
        <p>This page helps debug why selecting 3 tickets only shows 1 participant form.</p>

        <div class="error-section">
            <h3>🚨 Issue Description</h3>
            <p><strong>Problem:</strong> When selecting 3 tickets, only 1 participant form is displayed instead of 3.</p>
            <p><strong>Expected:</strong> 3 participant forms should be generated (one for each ticket).</p>
        </div>

        <div class="debug-section">
            <h3>🎫 Test Ticket Selection</h3>
            <div class="ticket-item">
                <div>
                    <strong>General Admission</strong><br>
                    <small>Standard ticket for the fun run</small><br>
                    <span style="color: #28a745; font-weight: bold;">RM 30.00</span>
                </div>
                <div class="ticket-controls">
                    <label for="general-qty">Quantity:</label>
                    <input type="number" id="general-qty" min="0" max="10" value="3">
                </div>
            </div>
            
            <div style="margin-top: 15px;">
                <button class="btn" onclick="proceedToRegistration()">
                    🚀 Test Registration (3 tickets)
                </button>
                <button class="btn debug" onclick="debugTicketData()">
                    🔍 Debug Ticket Data
                </button>
            </div>
        </div>

        <div class="debug-section">
            <h3>📊 Debug Information</h3>
            <div id="debug-output">
                <p>Click "Debug Ticket Data" to see the ticket structure that will be sent to the registration form.</p>
            </div>
        </div>

        <div class="debug-section">
            <h3>🔧 Potential Causes</h3>
            <ul>
                <li><strong>Session Storage Issue:</strong> Tickets not properly stored/retrieved</li>
                <li><strong>generateParticipants() Logic:</strong> Not correctly iterating through ticket quantities</li>
                <li><strong>useEffect Dependencies:</strong> Form not updating when selectedTickets change</li>
                <li><strong>Field Array Issue:</strong> replace() function not working correctly</li>
            </ul>
        </div>

        <div class="debug-section">
            <h3>🧪 Testing Steps</h3>
            <ol>
                <li>Set quantity to 3 tickets</li>
                <li>Click "Debug Ticket Data" to verify structure</li>
                <li>Click "Test Registration" to proceed to form</li>
                <li>Check browser console for logs</li>
                <li>Verify if 3 participant forms are displayed</li>
            </ol>
        </div>
    </div>

    <script>
        const ticketTypes = {
            general: {
                id: 'general',
                name: 'General Admission',
                price: 30,
                description: 'Standard ticket for the fun run'
            }
        };

        function debugTicketData() {
            const quantity = parseInt(document.getElementById('general-qty').value) || 0;
            
            const selectedTickets = [{
                ticketType: ticketTypes.general,
                quantity: quantity
            }];

            // Calculate total tickets (same logic as in RegistrationForm)
            const totalTickets = selectedTickets.reduce((total, ticket) => total + ticket.quantity, 0);

            // Simulate generateParticipants logic
            const participants = [];
            selectedTickets.forEach((selectedTicket) => {
                for (let i = 0; i < selectedTicket.quantity; i++) {
                    participants.push({
                        name: "",
                        ic: "",
                        phone: "",
                        email: "",
                        ticketTypeId: selectedTicket.ticketType.id,
                        ticketTypeName: selectedTicket.ticketType.name,
                        custom_field_responses: {}
                    });
                }
            });

            const debugInfo = {
                selectedTickets,
                totalTickets,
                expectedParticipants: participants.length,
                participantsStructure: participants
            };

            document.getElementById('debug-output').innerHTML = `
                <h4>🔍 Debug Results:</h4>
                <p><strong>Total Tickets:</strong> ${totalTickets}</p>
                <p><strong>Expected Participants:</strong> ${participants.length}</p>
                <p><strong>Ticket Structure:</strong></p>
                <pre>${JSON.stringify(selectedTickets, null, 2)}</pre>
                <p><strong>Generated Participants:</strong></p>
                <pre>${JSON.stringify(participants, null, 2)}</pre>
            `;

            console.log('🐛 Debug Information:', debugInfo);
        }

        function proceedToRegistration() {
            const quantity = parseInt(document.getElementById('general-qty').value) || 0;
            
            if (quantity === 0) {
                alert('Please select at least one ticket.');
                return;
            }

            const selectedTickets = [{
                ticketType: ticketTypes.general,
                quantity: quantity
            }];

            // Store selected tickets in session storage
            sessionStorage.setItem('selectedTickets', JSON.stringify(selectedTickets));
            
            console.log('🎫 Stored tickets in session storage:', selectedTickets);
            console.log('🎫 Total tickets:', selectedTickets.reduce((total, ticket) => total + ticket.quantity, 0));
            
            alert(`✅ ${quantity} tickets selected! Redirecting to registration form...`);
            
            // Redirect to registration form after a short delay
            setTimeout(() => {
                window.location.href = 'http://localhost:3001/events/5nEh/register';
            }, 1500);
        }

        // Auto-debug on page load
        window.addEventListener('load', () => {
            debugTicketData();
        });
    </script>
</body>
</html>
