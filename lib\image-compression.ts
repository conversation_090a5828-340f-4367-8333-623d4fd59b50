import imageCompression from 'browser-image-compression';

export interface CompressionOptions {
  maxSizeMB?: number;
  maxWidthOrHeight?: number;
  useWebWorker?: boolean;
  quality?: number;
  fileType?: string;
}

/**
 * Compress an image file to reduce its size while maintaining quality
 * @param file - The image file to compress
 * @param options - Compression options
 * @returns Promise<File> - The compressed image file
 */
export async function compressImage(
  file: File,
  options: CompressionOptions = {}
): Promise<File> {
  const defaultOptions = {
    maxSizeMB: 0.5, // Maximum file size in MB (500KB)
    maxWidthOrHeight: 800, // Maximum width or height in pixels
    useWebWorker: true, // Use web worker for better performance
    quality: 0.8, // Image quality (0.1 to 1.0)
    fileType: 'image/jpeg', // Output format
  };

  const compressionOptions = { ...defaultOptions, ...options };

  try {
    console.log('Original file size:', (file.size / 1024 / 1024).toFixed(2), 'MB');

    const compressedFile = await imageCompression(file, compressionOptions);

    console.log('Compressed file size:', (compressedFile.size / 1024 / 1024).toFixed(2), 'MB');
    console.log('Compression ratio:', ((1 - compressedFile.size / file.size) * 100).toFixed(1), '%');

    return compressedFile;
  } catch (error) {
    console.error('Error compressing image:', error);
    throw new Error('Failed to compress image');
  }
}

/**
 * Compress a profile image with optimized settings for avatars
 * @param file - The image file to compress
 * @returns Promise<File> - The compressed image file
 */
export async function compressProfileImage(file: File): Promise<File> {
  return compressImage(file, {
    maxSizeMB: 0.3, // 300KB max for profile images
    maxWidthOrHeight: 400, // 400px max for profile images (sufficient for avatars)
    quality: 0.85, // Higher quality for profile images
    fileType: 'image/jpeg', // JPEG for better compression
  });
}

/**
 * Compress an event image with optimized settings for event banners
 * @param file - The image file to compress
 * @returns Promise<File> - The compressed image file
 */
export async function compressEventImage(file: File): Promise<File> {
  return compressImage(file, {
    maxSizeMB: 1, // 1MB max for event images
    maxWidthOrHeight: 1200, // 1200px max for event banners
    quality: 0.8, // Good quality for event images
    fileType: 'image/jpeg',
  });
}

/**
 * Compress a certificate image with optimized settings for certificate backgrounds and logos
 * @param file - The image file to compress
 * @returns Promise<File> - The compressed image file
 */
export async function compressCertificateImage(file: File): Promise<File> {
  return compressImage(file, {
    maxSizeMB: 0.8, // 800KB max for certificate images
    maxWidthOrHeight: 1000, // 1000px max for certificate images
    quality: 0.85, // High quality for certificate images
    fileType: 'image/jpeg',
  });
}

/**
 * Check if a file is a valid image
 * @param file - The file to check
 * @returns boolean - True if the file is a valid image
 */
export function isValidImageFile(file: File): boolean {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
  return validTypes.includes(file.type);
}

/**
 * Get human-readable file size
 * @param bytes - File size in bytes
 * @returns string - Human-readable file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Create a preview URL for an image file
 * @param file - The image file
 * @returns Promise<string> - The preview URL
 */
export function createImagePreview(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}
