"use client"

import React from "react"
import { Search, Filter, ChevronDown } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"

interface Column<T> {
  key: keyof T | string
  header: string
  render?: (item: T) => React.ReactNode
  className?: string
}

interface DataTableProps<T> {
  data: T[]
  columns: Column<T>[]
  searchQuery: string
  onSearchChange: (query: string) => void
  searchPlaceholder?: string
  emptyMessage?: string
  showFilter?: boolean
  onFilterClick?: () => void
  className?: string
  onRowClick?: (item: T) => void
}

export function DataTable<T extends Record<string, any>>({
  data,
  columns,
  searchQuery,
  onSearchChange,
  searchPlaceholder = "Search...",
  emptyMessage = "No data found",
  showFilter = true,
  onFilterClick,
  className = "",
  onRowClick
}: DataTableProps<T>) {
  const getValue = (item: T, key: keyof T | string): any => {
    if (typeof key === 'string' && key.includes('.')) {
      // Handle nested properties like 'user.name'
      return key.split('.').reduce((obj, prop) => obj?.[prop], item)
    }
    return item[key as keyof T]
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Search and Filter Bar */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder={searchPlaceholder}
            className="w-full pl-8"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>
        {showFilter && (
          <Button
            variant="outline"
            size="sm"
            className="h-9 gap-1 shrink-0"
            onClick={onFilterClick}
          >
            <Filter className="h-4 w-4" />
            <span className="hidden sm:inline">Filter</span>
            <ChevronDown className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Table - Mobile responsive with horizontal scroll */}
      <div className="rounded-md border overflow-x-auto">
        <Table className="min-w-full">
          <TableHeader>
            <TableRow>
              {columns.map((column, index) => (
                <TableHead key={index} className={`${column.className} whitespace-nowrap`}>
                  {column.header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  {emptyMessage}
                </TableCell>
              </TableRow>
            ) : (
              data.map((item, index) => (
                <TableRow
                  key={index}
                  className={onRowClick ? "cursor-pointer hover:bg-muted/50" : ""}
                  onClick={() => onRowClick?.(item)}
                >
                  {columns.map((column, colIndex) => (
                    <TableCell key={colIndex} className={`${column.className} whitespace-nowrap`}>
                      {column.render
                        ? column.render(item)
                        : getValue(item, column.key)?.toString() || '-'
                      }
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}
