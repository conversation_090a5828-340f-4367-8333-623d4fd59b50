import { NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase"
import { verifyJWTToken, getJWTTokenFromRequest } from "@/lib/auth"

/**
 * POST /api/organizations/create
 * Creates a new organization
 * Requires authentication
 */
export async function POST(request: Request) {
  try {
    // Get JWT token from request
    const token = getJWTTokenFromRequest(request)

    if (!token) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Verify the token
    const authResult = await verifyJWTToken(token)
    if (!authResult || !authResult.user) {
      return NextResponse.json(
        { error: "Invalid or expired token" },
        { status: 401 }
      )
    }

    const userId = authResult.user.id
    const body = await request.json()

    // Validate required fields
    const { name, ssmNumber, picName, picPhone, picEmail, address, website } = body

    if (!name || !ssmNumber || !picName || !picPhone || !picEmail) {
      return NextResponse.json(
        { error: "Missing required fields: name, ssmNumber, picName, picPhone, picEmail" },
        { status: 400 }
      )
    }

    console.log("Organizations Create API: Creating organization", { name, userId })

    // Get the Supabase admin client
    const supabaseAdmin = getSupabaseAdmin()

    // Check if organization with same name already exists
    const { data: existingOrg, error: checkError } = await supabaseAdmin
      .from("organizations")
      .select("id, name")
      .eq("name", name)
      .single()

    if (checkError && checkError.code !== "PGRST116") { // PGRST116 = no rows returned
      console.error("Error checking existing organization:", checkError)
      return NextResponse.json(
        { error: "Failed to check existing organization" },
        { status: 500 }
      )
    }

    if (existingOrg) {
      return NextResponse.json(
        { error: "Organization with this name already exists" },
        { status: 409 }
      )
    }

    // Create the organization
    const { data: orgData, error: createError } = await supabaseAdmin
      .from("organizations")
      .insert({
        name,
        ssm_number: ssmNumber,
        pic_name: picName,
        pic_phone: picPhone,
        pic_email: picEmail,
        address: address || null,
        website: website || null,
        created_by: userId,
      })
      .select()
      .single()

    if (createError) {
      console.error("Error creating organization:", createError)
      return NextResponse.json(
        { error: "Failed to create organization" },
        { status: 500 }
      )
    }

    // Get the manager role ID from user_roles table
    const { data: managerRole, error: roleError } = await supabaseAdmin
      .from("user_roles")
      .select("id")
      .eq("role_name", "manager")
      .single()

    if (roleError || !managerRole) {
      console.error("Error fetching manager role:", roleError)
      return NextResponse.json(
        { error: "Manager role not found in database" },
        { status: 500 }
      )
    }

    // Update user role to manager and link to organization
    const { error: userUpdateError } = await supabaseAdmin
      .from("users")
      .update({
        role_id: managerRole.id,
        organization_id: orgData.id,
      })
      .eq("id", userId)

    if (userUpdateError) {
      console.error("Error updating user role:", userUpdateError)
      return NextResponse.json(
        { error: "Failed to update user role" },
        { status: 500 }
      )
    }

    // Log activity
    await supabaseAdmin.from("activity_logs").insert([
      {
        user_id: userId,
        action: "create_organization",
        entity_type: "organization",
        entity_id: orgData.id,
        details: { organization_name: name },
      },
    ])

    console.log("Organizations Create API: Successfully created organization", orgData.id)

    return NextResponse.json({
      success: true,
      organization: orgData
    })
  } catch (error: any) {
    console.error("Error in organizations create API:", error)
    return NextResponse.json(
      { error: error.message || "An unexpected error occurred" },
      { status: 500 }
    )
  }
}
