// This file is auto-generated to reflect your Supabase public schema tables and columns.
// Update this file if your schema changes.

export const TABLES = [
  {
    name: 'system_settings',
    columns: [
      { name: 'id', type: 'uuid', isPrimary: true },
      { name: 'event_fee_percentage', type: 'numeric' },
      { name: 'withdrawal_fee_percentage', type: 'numeric' },
      { name: 'min_withdrawal_amount', type: 'numeric' },
      { name: 'payment_gateways', type: 'jsonb' },
      { name: 'certificate_templates', type: 'jsonb' },
      { name: 'updated_at', type: 'timestamptz' },
      { name: 'updated_by', type: 'uuid' },
    ],
  },
  {
    name: 'events',
    columns: [
      { name: 'id', type: 'uuid', isPrimary: true },
      { name: 'title', type: 'text' },
      { name: 'slug', type: 'text', isUnique: true },
      { name: 'description', type: 'text' },
      // ...add all other columns as needed
    ],
  },
] as const;

export type TableName = typeof TABLES[number]['name'];

export const TABLE_NAMES = TABLES.map(t => t.name) as TableName[];
