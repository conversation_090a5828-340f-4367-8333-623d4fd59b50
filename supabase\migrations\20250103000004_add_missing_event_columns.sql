-- Add missing columns to events table that the application expects

-- Add short_description column
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'short_description') THEN
    ALTER TABLE events ADD COLUMN short_description TEXT;
  END IF;
END $$;

-- Add venue_details column
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'venue_details') THEN
    ALTER TABLE events ADD COLUMN venue_details JSONB;
  END IF;
END $$;

-- Add registration_deadline column
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'registration_deadline') THEN
    ALTER TABLE events ADD COLUMN registration_deadline TIMESTAMP WITH TIME ZONE;
  END IF;
END $$;

-- Add images column for multiple image support
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'images') THEN
    ALTER TABLE events ADD COLUMN images JSONB DEFAULT '[]';
  END IF;
END $$;

-- Add comments to document the columns
COMMENT ON COLUMN events.short_description IS 'Brief summary for event cards and previews';
COMMENT ON COLUMN events.venue_details IS 'Additional venue information, directions, parking, etc.';
COMMENT ON COLUMN events.registration_deadline IS 'When registration closes for this event';
COMMENT ON COLUMN events.images IS 'Array of event images with metadata';

-- Add custom_fields column for event registration custom fields
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'custom_fields') THEN
    ALTER TABLE events ADD COLUMN custom_fields JSONB DEFAULT '[]';
  END IF;
END $$;

-- Add custom_field_responses column to registrations table
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'registrations' AND column_name = 'custom_field_responses') THEN
    ALTER TABLE registrations ADD COLUMN custom_field_responses JSONB DEFAULT '{}';
  END IF;
END $$;

-- Add comments to document the new columns
COMMENT ON COLUMN events.custom_fields IS 'Array of custom field configurations for event registration';
COMMENT ON COLUMN registrations.custom_field_responses IS 'Participant responses to event custom fields';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_events_registration_deadline ON events(registration_deadline);
CREATE INDEX IF NOT EXISTS idx_events_images ON events USING GIN(images);
CREATE INDEX IF NOT EXISTS idx_events_custom_fields ON events USING GIN(custom_fields);
CREATE INDEX IF NOT EXISTS idx_registrations_custom_field_responses ON registrations USING GIN(custom_field_responses);
